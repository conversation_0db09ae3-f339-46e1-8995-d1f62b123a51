import os
import networkx as nx
import matplotlib.pyplot as plt

G = nx.DiGraph()
files = [f for f in os.listdir() if f.endswith(('.py', '.ts', '.tsx', '.js'))]
G.add_nodes_from(files)

imports = {f: [] for f in files}

for f in files:
    with open(f, 'r') as file:
        content = file.read()
    
    # 找到所有import语句
    deps = []
    for line in content.split('\n'):
        if line.startswith('import') or line.startswith('from'):
            for other_f in files:
                if other_f in line:
                    deps.append(other_f)
    
    imports[f] = deps
    for d in deps:
        G.add_edge(f, d)

# 绘制依赖关系图
plt.figure(figsize=(12, 8))
nx.draw(G, with_labels=True, node_color='lightblue', edge_color='gray', font_size=10, node_size=2000, alpha=0.8, linewidths=2)
plt.title('Project File Dependencies')
plt.savefig('dependency_graph.png')
plt.close()

def get_files_with_extensions(root_dir, extensions):
    """获取指定目录及其子目录中具有特定扩展名的文件列表"""
    files = []
    for dirpath, _, filenames in os.walk(root_dir):
        for filename in filenames:
            if any(filename.endswith(ext) for ext in extensions):
                files.append(os.path.join(dirpath, filename))
    return files

def parse_imports(file_path, files):
    """解析给定文件中的导入关系"""
    deps = []
    try:
        with open(file_path, 'r') as file:
            content = file.read()

        for line in content.split('\n'):
            if line.startswith('import') or line.startswith('from'):
                for other_f in files:
                    if other_f in line:
                        deps.append(other_f)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    return deps

def build_dependency_graph(files):
    """构建依赖关系图"""
    G = nx.DiGraph()
    G.add_nodes_from(files)

    imports = {f: [] for f in files}

    for f in files:
        deps = parse_imports(f, files)
        imports[f] = deps
        for d in deps:
            G.add_edge(f, d)

    return G, imports

def generate_dependency_report(imports):
    """生成依赖关系文本报告"""
    report_lines = []
    for file, deps in imports.items():
        report_lines.append(f"{file}:")
        for dep in deps:
            report_lines.append(f"  - {dep}")
        report_lines.append("")
    return "\n".join(report_lines)

def visualize_dependency_graph(G):
    """可视化依赖关系图"""
    plt.figure(figsize=(12, 8))
    nx.draw(G, with_labels=True, node_color='lightblue', edge_color='gray',
            font_size=10, node_size=2000, alpha=0.8, linewidths=2)
    plt.title('Project File Dependencies')
    plt.savefig('dependency_graph.png')
    plt.close()

def main():
    root_dir = '.'  # 当前目录
    extensions = ('.py', '.ts', '.tsx', '.js')
    files = get_files_with_extensions(root_dir, extensions)

    if not files:
        print("No files found with the specified extensions.")
        return

    G, imports = build_dependency_graph(files)

    # 输出依赖关系报告
    report = generate_dependency_report(imports)
    with open('dependency_report.txt', 'w') as report_file:
        report_file.write(report)

    # 可视化依赖关系图
    visualize_dependency_graph(G)

if __name__ == "__main__":
    main()