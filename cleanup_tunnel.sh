#!/bin/bash

# 清理Cloudflare隧道进程脚本

echo "🧹 清理Cloudflare隧道进程..."

# 查找所有cloudflared进程
PROCESSES=$(ps aux | grep cloudflared | grep -v grep)

if [ -z "$PROCESSES" ]; then
    echo "✅ 没有发现运行中的cloudflared进程"
else
    echo "🔍 发现以下cloudflared进程:"
    echo "$PROCESSES"
    echo ""
    
    # 只杀死当前用户的进程
    USER_PROCESSES=$(ps aux | grep cloudflared | grep -v grep | grep "$(whoami)")
    if [ ! -z "$USER_PROCESSES" ]; then
        echo "🛑 停止当前用户的cloudflared进程..."
        pkill -f cloudflared
        sleep 2
        echo "✅ 已停止当前用户的cloudflared进程"
    fi
    
    # 检查是否还有root进程
    ROOT_PROCESSES=$(ps aux | grep cloudflared | grep -v grep | grep root)
    if [ ! -z "$ROOT_PROCESSES" ]; then
        echo "⚠️  发现root用户的cloudflared进程仍在运行:"
        echo "$ROOT_PROCESSES"
        echo ""
        echo "请手动停止这些进程，或者重启系统"
        echo "如果需要停止root进程，请运行:"
        echo "sudo pkill -f cloudflared"
    fi
fi

echo ""
echo "🔍 当前cloudflared进程状态:"
ps aux | grep cloudflared | grep -v grep || echo "✅ 没有cloudflared进程在运行"
