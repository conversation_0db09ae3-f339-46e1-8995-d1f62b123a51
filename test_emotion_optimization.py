#!/usr/bin/env python3
"""
情绪预测优化效果测试脚本

用于测试和验证针对neutral偏差的优化效果
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('emotion_optimization_test.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

async def test_emotion_prediction_optimization():
    """测试情绪预测优化效果"""
    
    logger.info("="*60)
    logger.info("开始测试情绪预测优化效果")
    logger.info("="*60)
    
    try:
        # 导入所需模块
        from emotionai.core.ml.emotion_ensemble import EmotionEnsemble
        from emotionai.core.config.emotion_config import MODEL_WEIGHTS, EMOTION_CONFIDENCE_THRESHOLD
        
        logger.info(f"当前模型权重配置: {MODEL_WEIGHTS}")
        logger.info(f"情绪置信度阈值: {EMOTION_CONFIDENCE_THRESHOLD}")
        
        # 创建情绪集成模型
        ensemble = EmotionEnsemble()
        
        # 获取当前权重
        current_weights = ensemble.get_weights()
        logger.info(f"实际使用的权重: {current_weights}")
        
        # 使用一个测试图像进行预测
        import cv2
        import numpy as np
        
        # 创建一个测试图像（模拟人脸图像）
        test_image = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
        
        logger.info("开始进行测试预测...")
        
        # 进行预测
        result = await ensemble.analyze(test_image)
        
        logger.info("预测结果:")
        logger.info(f"- 成功: {result.get('success', False)}")
        logger.info(f"- 主要情绪: {result.get('dominant_emotion', 'unknown')}")
        
        # 显示情绪分布
        emotions = result.get('emotions', {})
        if emotions:
            logger.info("情绪分布:")
            sorted_emotions = sorted(emotions.items(), key=lambda x: x[1], reverse=True)
            for emotion, confidence in sorted_emotions:
                logger.info(f"  {emotion}: {confidence:.4f} ({confidence*100:.1f}%)")
        
        # 显示模型结果详情
        model_results = result.get('model_results', {})
        if model_results:
            logger.info("\n各模型预测结果:")
            for model_name, model_result in model_results.items():
                success = model_result.get('success', False)
                dominant = model_result.get('dominant_emotion', 'unknown')
                logger.info(f"  {model_name}: 成功={success}, 主要情绪={dominant}")
        
        # 检查是否仍然倾向于预测neutral
        neutral_confidence = emotions.get('neutral', 0.0)
        if result.get('dominant_emotion') == 'neutral':
            logger.warning(f"⚠️  仍然预测为neutral (置信度: {neutral_confidence:.4f})")
            
            # 分析其他情绪的置信度
            other_emotions = {k: v for k, v in emotions.items() if k != 'neutral'}
            if other_emotions:
                max_other = max(other_emotions.items(), key=lambda x: x[1])
                logger.info(f"最高的非neutral情绪: {max_other[0]} (置信度: {max_other[1]:.4f})")
                
                # 计算neutral与其他情绪的差距
                confidence_gap = neutral_confidence - max_other[1]
                logger.info(f"Neutral优势: {confidence_gap:.4f}")
                
                if confidence_gap < 0.1:
                    logger.info("✅ 情绪分布较为均衡，优化有效")
                else:
                    logger.warning("❌ Neutral仍然有明显优势，需要进一步优化")
        else:
            logger.info(f"✅ 预测为非neutral情绪: {result.get('dominant_emotion')}")
        
        return result
        
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return None

def test_configuration_loading():
    """测试配置文件加载"""
    
    logger.info("-"*40)
    logger.info("测试配置文件加载")
    logger.info("-"*40)
    
    try:
        from emotionai.core.config.emotion_config import (
            MODEL_WEIGHTS,
            EMOTION_CONFIDENCE_THRESHOLD,
            NEUTRAL_CONFIDENCE_PENALTY,
            ALTERNATIVE_EMOTION_THRESHOLD,
            EMOTION_LABELS,
            EMOTION_DISPLAY_NAMES
        )
        
        logger.info("✅ 配置文件加载成功")
        logger.info(f"模型权重: {MODEL_WEIGHTS}")
        logger.info(f"置信度阈值: {EMOTION_CONFIDENCE_THRESHOLD}")
        logger.info(f"Neutral惩罚因子: {NEUTRAL_CONFIDENCE_PENALTY}")
        logger.info(f"替代情绪阈值: {ALTERNATIVE_EMOTION_THRESHOLD}")
        logger.info(f"情绪标签数量: {len(EMOTION_LABELS)}")
        logger.info(f"情绪显示名称数量: {len(EMOTION_DISPLAY_NAMES)}")
        
        # 验证权重总和
        total_weight = sum(MODEL_WEIGHTS.values())
        logger.info(f"权重总和: {total_weight:.3f}")
        
        if abs(total_weight - 1.0) < 0.01:
            logger.info("✅ 权重归一化正确")
        else:
            logger.warning(f"⚠️  权重总和不为1: {total_weight}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ 配置文件加载失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"❌ 配置测试出错: {str(e)}")
        return False

async def main():
    """主函数"""
    
    logger.info("开始情绪预测优化测试")
    
    # 测试配置文件加载
    config_ok = test_configuration_loading()
    
    if not config_ok:
        logger.error("配置文件测试失败，退出测试")
        return
    
    # 测试情绪预测优化
    result = await test_emotion_prediction_optimization()
    
    if result:
        logger.info("="*60)
        logger.info("测试完成")
        logger.info("="*60)
        
        # 输出测试总结
        logger.info("优化效果总结:")
        logger.info("1. 降低了DeepFace模型权重（从40%降至20%）")
        logger.info("2. 提高了专业情绪模型权重（Trakov VIT和Rajaram各40%）")
        logger.info("3. 添加了置信度阈值检查机制")
        logger.info("4. 实现了智能回退策略")
        logger.info("5. 增强了预测日志，便于调试")
        
        dominant_emotion = result.get('dominant_emotion', 'unknown')
        if dominant_emotion != 'neutral':
            logger.info("✅ 成功减少了对neutral的偏向")
        else:
            logger.info("⚠️  仍需进一步调优参数")
    else:
        logger.error("测试失败")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main()) 