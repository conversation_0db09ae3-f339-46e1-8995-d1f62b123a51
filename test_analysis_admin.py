#!/usr/bin/env python3
"""
测试分析管理页面功能
验证EmotionAnalysis模型的user关系是否正常工作
"""

import asyncio
import sys
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select
from sqlalchemy.orm import selectinload

# 添加项目路径
sys.path.append('/Volumes/acasis/ema2_20250417/backend')

from emotionai.core.config import settings
from emotionai.models.ml.emotion import EmotionAnalysis
from emotionai.models.user.user import User

async def test_emotion_analysis_user_relationship():
    """测试EmotionAnalysis模型的user关系"""
    print("🧪 测试EmotionAnalysis模型的user关系...")
    
    try:
        # 创建异步数据库引擎
        engine = create_async_engine(settings.DATABASE_URL)
        async_session = sessionmaker(engine, class_=AsyncSession)
        
        async with async_session() as session:
            # 测试查询分析记录并加载用户信息
            statement = select(EmotionAnalysis).options(selectinload(EmotionAnalysis.user)).limit(5)
            result = await session.execute(statement)
            analyses = result.scalars().all()
            
            print(f"✅ 成功查询到 {len(analyses)} 条分析记录")
            
            for analysis in analyses:
                print(f"   - 分析ID: {analysis.id}")
                print(f"     情绪: {analysis.emotion}")
                print(f"     置信度: {analysis.confidence}")
                print(f"     用户ID: {analysis.user_id}")
                if hasattr(analysis, 'user') and analysis.user:
                    print(f"     用户名: {analysis.user.username}")
                else:
                    print(f"     用户信息: 未加载")
                print()
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await engine.dispose()

async def test_crud_get_multi():
    """测试CRUD的get_multi方法"""
    print("🧪 测试CRUD的get_multi方法...")
    
    try:
        # 创建异步数据库引擎
        engine = create_async_engine(settings.DATABASE_URL)
        async_session = sessionmaker(engine, class_=AsyncSession)
        
        async with async_session() as session:
            from emotionai.crud.crud_emotion_analysis import cemotion_analysis
            
            # 测试get_multi方法
            analyses = await cemotion_analysis.get_multi(session, skip=0, limit=5)
            
            print(f"✅ CRUD get_multi成功，查询到 {len(analyses)} 条记录")
            
            for analysis in analyses:
                print(f"   - 分析ID: {analysis.id}")
                print(f"     情绪: {analysis.emotion}")
                if hasattr(analysis, 'user') and analysis.user:
                    print(f"     用户名: {analysis.user.username}")
                else:
                    print(f"     用户信息: 未加载")
                print()
            
            return True
            
    except Exception as e:
        print(f"❌ CRUD测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await engine.dispose()

async def main():
    """主函数"""
    print("=" * 60)
    print("🎯 测试分析管理页面功能")
    print("=" * 60)
    
    tests = [
        ("EmotionAnalysis用户关系测试", test_emotion_analysis_user_relationship),
        ("CRUD get_multi方法测试", test_crud_get_multi),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        success = await test_func()
        results.append((test_name, success))
        print()
    
    # 总结结果
    print("=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    all_passed = True
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if not success:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！分析管理页面功能正常！")
        print("✅ EmotionAnalysis模型的user关系正常工作")
        print("✅ CRUD操作正常")
        print("✅ 分析管理页面应该可以正常显示")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main()) 