#!/usr/bin/env python3
"""
测试分析详情API是否正确包含反馈信息
"""
import asyncio
import sys
import uuid
from datetime import datetime

sys.path.append('backend')

from backend.emotionai.core.database.session import get_async_db
from backend.emotionai.crud.crud_emotion_analysis import cemotion_analysis
from backend.emotionai.crud.crud_emotion_feedback import cemotion_feedback
from backend.emotionai.schemas.admin.emotion_analysis import EmotionAnalysis
from sqlalchemy import text

async def test_analysis_detail_with_feedback():
    """测试分析详情API包含反馈信息"""
    
    async for db in get_async_db():
        print("=== 测试分析详情API包含反馈信息 ===")
        
        # 1. 获取一个有反馈的分析记录
        print("\n1. 查找有反馈的分析记录...")
        result = await db.execute(
            text("""
                SELECT DISTINCT ea.id, ea.emotion, ea.confidence, ea.input_path, ea.created_at
                FROM emotion_analysis ea 
                JOIN emotion_feedback ef ON ea.id = ef.analysis_id 
                WHERE ea.deleted_at IS NULL 
                ORDER BY ea.created_at DESC 
                LIMIT 1
            """)
        )
        row = result.fetchone()
        
        if not row:
            print("❌ 没有找到有反馈的分析记录")
            return
        
        analysis_id = row[0]
        emotion = row[1]
        confidence = row[2]
        input_path = row[3]
        
        print(f"✅ 找到有反馈的分析记录:")
        print(f"   ID: {analysis_id}")
        print(f"   情绪: {emotion}")
        print(f"   置信度: {confidence}")
        print(f"   输入路径: {input_path}")
        
        # 2. 使用CRUD获取分析记录
        print("\n2. 使用CRUD获取分析记录...")
        analysis_orm = await cemotion_analysis.get(db, id=analysis_id)
        if not analysis_orm:
            print("❌ 无法获取分析记录")
            return
        
        print(f"✅ 成功获取分析记录: {analysis_orm.id}")
        
        # 3. 获取反馈记录
        print("\n3. 获取反馈记录...")
        feedbacks_orm = await cemotion_feedback.get_by_analysis_id_with_user(db, analysis_id)
        print(f"✅ 找到 {len(feedbacks_orm)} 条反馈记录")
        
        for i, feedback in enumerate(feedbacks_orm, 1):
            print(f"   反馈 {i}:")
            print(f"     ID: {feedback.id}")
            print(f"     情绪: {feedback.emotion}")
            print(f"     评论: {feedback.comment}")
            print(f"     创建时间: {feedback.created_at}")
            
            # 检查用户关系
            try:
                if hasattr(feedback, 'user') and feedback.user:
                    print(f"     用户: {feedback.user.username}")
                else:
                    print(f"     用户: 未加载用户信息")
            except Exception as e:
                print(f"     用户: 加载用户信息失败 - {e}")
        
        # 4. 模拟API逻辑构建完整的分析数据
        print("\n4. 模拟API逻辑构建完整的分析数据...")
        
        # 构建分析记录数据
        analysis_data = analysis_orm.__dict__.copy()
        
        # 添加用户名
        if analysis_orm.user:
            analysis_data["username"] = analysis_orm.user.username
        else:
            analysis_data["username"] = "未知用户"
        
        # 处理图片缩略图URL
        analysis_data["image_thumbnail_url"] = None
        if analysis_orm.input_path:
            absolute_image_path = str(analysis_orm.input_path)
            possible_prefixes = [
                "/Volumes/acasis/ema2_20250417/backend/uploads/",
                "/Volumes/acasis/ema2_20250417/uploads/",
                "uploads/",
            ]
            
            relative_path = None
            for prefix in possible_prefixes:
                if absolute_image_path.startswith(prefix):
                    relative_path = absolute_image_path[len(prefix):]
                    break
            
            if not relative_path and absolute_image_path.startswith("original/"):
                relative_path = absolute_image_path
            
            if not relative_path and "original/" in absolute_image_path:
                relative_path = absolute_image_path[absolute_image_path.index("original/"):]
            
            if relative_path:
                analysis_data["image_thumbnail_url"] = f"/static/{relative_path}"
            else:
                if absolute_image_path.endswith(".jpg") and "-" in absolute_image_path:
                    analysis_data["image_thumbnail_url"] = f"/static/original/{absolute_image_path}"
                else:
                    analysis_data["image_thumbnail_url"] = f"/static/{absolute_image_path}"
        
        # 构建反馈数据
        feedbacks_data = []
        for feedback_orm in feedbacks_orm:
            feedback_data = feedback_orm.__dict__.copy()
            # 添加反馈用户名
            try:
                if hasattr(feedback_orm, 'user') and feedback_orm.user:
                    feedback_data["username"] = feedback_orm.user.username
                else:
                    feedback_data["username"] = "未知用户"
            except Exception:
                feedback_data["username"] = "未知用户"
            feedbacks_data.append(feedback_data)
        
        # 添加反馈信息到分析数据
        analysis_data["feedbacks"] = feedbacks_data
        analysis_data["feedback_count"] = len(feedbacks_data)
        
        print(f"✅ 构建完成，包含 {len(feedbacks_data)} 条反馈")
        
        # 5. 使用Pydantic模型验证
        print("\n5. 使用Pydantic模型验证...")
        try:
            validated_analysis = EmotionAnalysis.model_validate(analysis_data)
            print("✅ Pydantic模型验证成功")
            
            print(f"\n📊 验证结果:")
            print(f"   分析ID: {validated_analysis.id}")
            print(f"   用户名: {validated_analysis.username}")
            print(f"   情绪: {validated_analysis.emotion}")
            print(f"   置信度: {validated_analysis.confidence}")
            print(f"   图片URL: {validated_analysis.image_thumbnail_url}")
            print(f"   反馈数量: {validated_analysis.feedback_count}")
            
            if validated_analysis.feedbacks:
                print(f"\n📝 反馈详情:")
                for i, feedback in enumerate(validated_analysis.feedbacks, 1):
                    print(f"   反馈 {i}:")
                    print(f"     用户: {feedback.username}")
                    print(f"     情绪: {feedback.emotion}")
                    print(f"     评论: {feedback.comment}")
                    print(f"     时间: {feedback.created_at}")
            
            return True
            
        except Exception as e:
            print(f"❌ Pydantic模型验证失败: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = asyncio.run(test_analysis_detail_with_feedback())
    if success:
        print("\n🎉 测试成功！分析详情API可以正确包含反馈信息")
    else:
        print("\n❌ 测试失败！需要检查代码") 