#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试使用Gemini API分析音频情绪
"""

import os
import sys
import google.generativeai as genai

# 设置API密钥
API_KEY = "AIzaSyALEVpVMStS25KRI4fLFHlxzvPYJd3GMdQ"
genai.configure(api_key=API_KEY)

def analyze_audio_emotion(audio_file_path):
    """
    使用Gemini API分析音频情绪

    Args:
        audio_file_path: 音频文件路径

    Returns:
        分析结果
    """
    print(f"正在分析音频文件: {audio_file_path}")

    # 检查文件是否存在
    if not os.path.exists(audio_file_path):
        print(f"错误: 文件 {audio_file_path} 不存在")
        return

    # 检查文件扩展名
    _, ext = os.path.splitext(audio_file_path)
    if ext.lower() not in ['.mp3', '.wav', '.ogg', '.flac', '.aac', '.aiff']:
        print(f"警告: 文件扩展名 {ext} 可能不受支持。支持的格式包括: .mp3, .wav, .ogg, .flac, .aac, .aiff")

    try:
        # 创建Gemini客户端
        # 直接使用genai模块，不需要创建客户端

        # 读取音频文件
        print("正在读取音频文件...")
        with open(audio_file_path, "rb") as f:
            audio_data = f.read()

        # 构建提示词，专注于情绪分析
        prompt = """
        请分析这段音频中说话者的情绪状态。

        请考虑以下因素:
        1. 语调和音高变化
        2. 语速和节奏
        3. 音量变化
        4. 停顿和犹豫
        5. 声音颤抖或其他情绪指标

        请提供详细分析，并给出主要情绪类别(如快乐、悲伤、愤怒、恐惧、惊讶、中性等)，
        以及情绪强度的估计(1-10分，其中1表示几乎不可察觉，10表示非常强烈)。

        如果音频中有多个说话者，请分别分析每个说话者的情绪。
        """

        # 调用Gemini API进行分析
        print("正在使用Gemini API分析音频...")

        # 创建多模态内容
        import base64

        # 将音频数据转换为Base64编码
        audio_base64 = base64.b64encode(audio_data).decode('utf-8')

        # 创建音频部分 - 使用内联数据
        audio_part = {
            "inline_data": {
                "data": audio_base64,
                "mime_type": "audio/wav"
            }
        }

        # 生成内容
        model = genai.GenerativeModel('gemini-2.0-flash')
        response = model.generate_content(
            contents=[prompt, audio_part]
        )

        # 打印分析结果
        print("\n===== 音频情绪分析结果 =====\n")
        print(response.text)
        return response.text

    except Exception as e:
        print(f"分析过程中出错: {str(e)}")
        return None

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python test_audio_emotion.py <音频文件路径>")
        return

    audio_file_path = sys.argv[1]
    analyze_audio_emotion(audio_file_path)

if __name__ == "__main__":
    main()
