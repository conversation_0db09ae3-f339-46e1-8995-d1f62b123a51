#!/usr/bin/env python3
"""
OpenCV版本清理脚本
解决多个OpenCV版本冲突问题，统一使用opencv-python-headless
"""

import subprocess
import sys
from typing import List, <PERSON><PERSON>


def run_command(cmd: List[str]) -> Tuple[int, str, str]:
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, capture_output=True, text=True, check=False
        )
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return 1, "", str(e)


def get_installed_opencv_packages() -> List[str]:
    """获取已安装的OpenCV包"""
    code, stdout, stderr = run_command(["pip", "list", "--format=freeze"])
    if code != 0:
        print(f"❌ 获取包列表失败: {stderr}")
        return []
    
    opencv_packages = []
    for line in stdout.split('\n'):
        if line.startswith('opencv-'):
            package_name = line.split('==')[0]
            opencv_packages.append(package_name)
    
    return opencv_packages


def uninstall_opencv_packages(packages: List[str]) -> bool:
    """卸载指定的OpenCV包"""
    if not packages:
        print("✅ 没有需要卸载的OpenCV包")
        return True
    
    print(f"🗑️  准备卸载以下OpenCV包: {', '.join(packages)}")
    
    for package in packages:
        print(f"   正在卸载 {package}...")
        code, stdout, stderr = run_command(["pip", "uninstall", "-y", package])
        if code != 0:
            print(f"❌ 卸载 {package} 失败: {stderr}")
            return False
        else:
            print(f"✅ 成功卸载 {package}")
    
    return True


def install_opencv_headless() -> bool:
    """安装opencv-python-headless"""
    print("📦 正在安装 opencv-python-headless==*********...")
    
    code, stdout, stderr = run_command([
        "pip", "install", "opencv-python-headless==*********"
    ])
    
    if code != 0:
        print(f"❌ 安装失败: {stderr}")
        return False
    else:
        print("✅ 成功安装 opencv-python-headless==*********")
        return True


def verify_opencv_installation() -> bool:
    """验证OpenCV安装"""
    print("🔍 验证OpenCV安装...")
    
    try:
        import cv2
        version = cv2.__version__
        print(f"✅ OpenCV版本: {version}")
        
        # 测试基本功能
        import numpy as np
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        print("✅ OpenCV基本功能测试通过")
        
        return True
    except ImportError as e:
        print(f"❌ OpenCV导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ OpenCV功能测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 OpenCV版本清理工具")
    print("=" * 50)
    
    # 1. 检查当前安装的OpenCV包
    print("1️⃣ 检查当前安装的OpenCV包...")
    opencv_packages = get_installed_opencv_packages()
    
    if opencv_packages:
        print(f"   发现以下OpenCV包: {', '.join(opencv_packages)}")
        
        # 检查是否需要清理
        target_package = "opencv-python-headless"
        if len(opencv_packages) > 1 or target_package not in opencv_packages:
            print("⚠️  检测到OpenCV版本冲突，需要清理")
            
            # 2. 卸载所有OpenCV包
            print("\n2️⃣ 卸载现有OpenCV包...")
            if not uninstall_opencv_packages(opencv_packages):
                print("❌ 清理失败，请手动处理")
                sys.exit(1)
            
            # 3. 安装目标版本
            print("\n3️⃣ 安装目标OpenCV版本...")
            if not install_opencv_headless():
                print("❌ 安装失败")
                sys.exit(1)
        else:
            print("✅ OpenCV版本正确，无需清理")
    else:
        print("   未发现OpenCV包，将安装目标版本")
        
        # 直接安装目标版本
        print("\n2️⃣ 安装目标OpenCV版本...")
        if not install_opencv_headless():
            print("❌ 安装失败")
            sys.exit(1)
    
    # 4. 验证安装
    print("\n4️⃣ 验证安装...")
    if verify_opencv_installation():
        print("\n🎉 OpenCV清理完成！")
        print("✅ 当前使用: opencv-python-headless==*********")
    else:
        print("\n❌ 验证失败，请检查安装")
        sys.exit(1)


if __name__ == "__main__":
    main() 