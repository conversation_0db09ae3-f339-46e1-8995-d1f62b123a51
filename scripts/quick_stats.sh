#!/bin/bash

# EMA2 项目快速代码统计脚本
# 快速查看项目代码统计概览

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}📊 EMA2 项目快速代码统计${NC}"
echo -e "${YELLOW}================================${NC}"

# 项目总体统计
echo -e "\n${CYAN}🎯 项目总体统计:${NC}"
cloc . --exclude-dir=node_modules,.mypy_cache,.pytest_cache,.ruff_cache,.pnpm-store,__pycache__,dist,build,.git,htmlcov,logs,uploads,test_data,test_images,test_results --quiet

# 前端统计
echo -e "\n${CYAN}🎨 前端代码:${NC}"
cloc frontend/ --exclude-dir=node_modules,.pnpm-store,dist,build --quiet

# 后端统计
echo -e "\n${CYAN}🔧 后端代码:${NC}"
cloc backend/ --exclude-dir=.mypy_cache,.pytest_cache,.ruff_cache,__pycache__,htmlcov,logs --quiet

# 核心业务逻辑
echo -e "\n${CYAN}🧠 核心业务逻辑:${NC}"
cloc backend/emotionai/ --exclude-dir=__pycache__,temp,tests,htmlcov --quiet

echo -e "\n${GREEN}✅ 统计完成!${NC}" 