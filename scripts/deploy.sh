#!/bin/bash

# ===================================
# 情感分析AI系统 - 部署脚本
# ===================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 环境配置
setup_environment() {
    log_info "设置环境配置..."
    
    if [ ! -f .env ]; then
        if [ -f env.example ]; then
            cp env.example .env
            log_warning "已从 env.example 复制环境配置，请检查并修改 .env 文件"
        else
            log_error "未找到环境配置文件，请创建 .env 文件"
            exit 1
        fi
    fi
    
    log_success "环境配置完成"
}

# 构建镜像
build_images() {
    log_info "构建 Docker 镜像..."
    
    # 设置构建目标
    export BUILD_TARGET=${BUILD_TARGET:-production}
    
    # 构建镜像
    docker-compose build --no-cache
    
    log_success "镜像构建完成"
}

# 数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 启动数据库服务
    docker-compose up -d postgres redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 运行迁移
    docker-compose run --rm backend alembic upgrade head
    
    log_success "数据库迁移完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动所有服务
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 60
    
    # 检查服务状态
    docker-compose ps
    
    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查后端服务
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "后端服务健康"
    else
        log_error "后端服务不健康"
        return 1
    fi
    
    # 检查前端服务 (通过Nginx)
    # NGINX_HTTP_PORT 环境变量应在 .env 文件中定义，如果未定义则默认为80
    FRONTEND_HEALTH_URL="http://localhost:${NGINX_HTTP_PORT:-80}/health"
    log_info "检查前端服务健康状态: ${FRONTEND_HEALTH_URL}"
    if curl -f ${FRONTEND_HEALTH_URL} > /dev/null 2>&1; then
        log_success "前端服务健康"
    else
        log_warning "前端服务检查失败 (URL: ${FRONTEND_HEALTH_URL})，可能正在启动中或配置错误"
    fi
    
    log_success "健康检查完成"
}

# 清理函数
cleanup() {
    log_info "清理资源..."
    docker-compose down
    docker system prune -f
    log_success "清理完成"
}

# 主函数
main() {
    log_info "开始部署情感分析AI系统..."
    
    # 解析命令行参数
    case "${1:-deploy}" in
        "deploy")
            check_dependencies
            setup_environment
            build_images
            run_migrations
            start_services
            health_check
            ;;
        "build")
            check_dependencies
            build_images
            ;;
        "start")
            start_services
            health_check
            ;;
        "stop")
            docker-compose down
            ;;
        "restart")
            docker-compose restart
            health_check
            ;;
        "logs")
            docker-compose logs -f
            ;;
        "cleanup")
            cleanup
            ;;
        "help")
            echo "用法: $0 [deploy|build|start|stop|restart|logs|cleanup|help]"
            echo "  deploy  - 完整部署（默认）"
            echo "  build   - 仅构建镜像"
            echo "  start   - 启动服务"
            echo "  stop    - 停止服务"
            echo "  restart - 重启服务"
            echo "  logs    - 查看日志"
            echo "  cleanup - 清理资源"
            echo "  help    - 显示帮助"
            exit 0
            ;;
        *)
            log_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助"
            exit 1
            ;;
    esac
    
    log_success "部署完成！"
    log_info "访问地址："
    log_info "  前端: http://localhost:3000"
    log_info "  后端API: http://localhost:8000"
    log_info "  API文档: http://localhost:8000/docs"
}

# 捕获中断信号
trap cleanup INT TERM

# 执行主函数
main "$@" 