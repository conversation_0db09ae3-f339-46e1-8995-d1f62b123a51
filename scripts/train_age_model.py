#!/usr/bin/env python
"""
年龄识别模型训练脚本

支持多种训练策略：
1. 单任务年龄回归训练
2. 多任务训练（年龄回归 + 年龄组分类）
3. 迁移学习训练
4. 集成学习训练

使用方法:
python scripts/train_age_model.py --config configs/age_training.yaml
"""

import argparse
import logging
import os
import sys
from pathlib import Path

import torch
import yaml
from torch.utils.data import DataLoader, random_split

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.emotionai.core.ml.datasets.age_dataset import (
    AgeDatasetFactory,
    create_age_transforms,
)
from backend.emotionai.core.ml.trainers.age_trainer import (
    AgeModelTrainer,
    AgeRegressionModel,
    MultiTaskAgeModel,
    MultiTaskAgeTrainer,
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_config(config_path: str) -> dict:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def create_data_loaders(config: dict) -> tuple:
    """创建数据加载器"""
    dataset_config = config['dataset']
    training_config = config['training']
    
    # 创建训练和验证变换
    train_transform = create_age_transforms(
        target_size=tuple(dataset_config['target_size']),
        is_training=True,
        augmentation_strength=dataset_config.get('augmentation_strength', 0.5)
    )
    
    val_transform = create_age_transforms(
        target_size=tuple(dataset_config['target_size']),
        is_training=False
    )
    
    # 创建完整数据集
    full_dataset = AgeDatasetFactory.create_dataset(
        dataset_type=dataset_config['type'],
        data_dir=dataset_config['data_dir'],
        csv_file=dataset_config.get('csv_file'),
        transform=train_transform,
        target_size=tuple(dataset_config['target_size']),
        age_range=tuple(dataset_config['age_range']),
        mode=dataset_config.get('mode', 'regression'),
        num_age_groups=dataset_config.get('num_age_groups', 8)
    )
    
    # 分割数据集
    total_size = len(full_dataset)
    train_size = int(training_config['train_split'] * total_size)
    val_size = total_size - train_size
    
    train_dataset, val_dataset = random_split(
        full_dataset, 
        [train_size, val_size],
        generator=torch.Generator().manual_seed(training_config.get('seed', 42))
    )
    
    # 为验证集设置不同的变换
    val_dataset.dataset.transform = val_transform
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=training_config['batch_size'],
        shuffle=True,
        num_workers=training_config.get('num_workers', 4),
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=training_config['batch_size'],
        shuffle=False,
        num_workers=training_config.get('num_workers', 4),
        pin_memory=True
    )
    
    logger.info(f"数据集加载完成 - 训练集: {len(train_dataset)}, 验证集: {len(val_dataset)}")
    
    return train_loader, val_loader


def create_model(config: dict) -> torch.nn.Module:
    """创建模型"""
    model_config = config['model']
    model_type = model_config['type']
    
    if model_type == 'single_task':
        model = AgeRegressionModel(
            backbone=model_config['backbone'],
            pretrained=model_config.get('pretrained', True),
            num_classes=1,
            dropout=model_config.get('dropout', 0.5)
        )
    elif model_type == 'multi_task':
        model = MultiTaskAgeModel(
            backbone=model_config['backbone'],
            pretrained=model_config.get('pretrained', True),
            num_age_groups=model_config.get('num_age_groups', 8),
            dropout=model_config.get('dropout', 0.5)
        )
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")
    
    logger.info(f"创建模型: {model_type}, backbone: {model_config['backbone']}")
    
    return model


def create_trainer(model: torch.nn.Module, train_loader: DataLoader, val_loader: DataLoader, config: dict):
    """创建训练器"""
    model_config = config['model']
    training_config = config['training']
    
    if model_config['type'] == 'single_task':
        trainer = AgeModelTrainer(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            device=training_config.get('device'),
            save_dir=training_config['save_dir'],
            log_dir=training_config['log_dir'],
            loss_type=training_config.get('loss_type', 'mse'),
            age_range=tuple(config['dataset']['age_range'])
        )
    elif model_config['type'] == 'multi_task':
        trainer = MultiTaskAgeTrainer(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            device=training_config.get('device'),
            save_dir=training_config['save_dir'],
            log_dir=training_config['log_dir'],
            regression_weight=training_config.get('regression_weight', 1.0),
            classification_weight=training_config.get('classification_weight', 0.5)
        )
    else:
        raise ValueError(f"不支持的模型类型: {model_config['type']}")
    
    return trainer


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='年龄识别模型训练')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    parser.add_argument('--resume', type=str, help='恢复训练的检查点路径')
    parser.add_argument('--device', type=str, help='训练设备 (cuda/cpu)')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 覆盖设备设置
    if args.device:
        config['training']['device'] = args.device
    
    # 设置随机种子
    seed = config['training'].get('seed', 42)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    
    # 创建保存目录
    save_dir = Path(config['training']['save_dir'])
    save_dir.mkdir(parents=True, exist_ok=True)
    
    log_dir = Path(config['training']['log_dir'])
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存配置文件
    config_save_path = save_dir / 'training_config.yaml'
    with open(config_save_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    logger.info(f"配置文件已保存到: {config_save_path}")
    
    try:
        # 创建数据加载器
        train_loader, val_loader = create_data_loaders(config)
        
        # 创建模型
        model = create_model(config)
        
        # 创建训练器
        trainer = create_trainer(model, train_loader, val_loader, config)
        
        # 恢复训练（如果指定）
        if args.resume:
            if trainer.load_checkpoint(args.resume):
                logger.info(f"从检查点恢复训练: {args.resume}")
            else:
                logger.error(f"无法加载检查点: {args.resume}")
                return
        
        # 开始训练
        training_config = config['training']
        trainer.train(
            epochs=training_config['epochs'],
            learning_rate=training_config['learning_rate'],
            patience=training_config.get('patience', 10),
            save_every=training_config.get('save_every', 5),
            optimizer=training_config.get('optimizer', 'adamw'),
            weight_decay=training_config.get('weight_decay', 1e-4)
        )
        
        logger.info("训练完成！")
        
    except Exception as e:
        logger.error(f"训练过程中发生错误: {e}")
        raise


if __name__ == '__main__':
    main() 