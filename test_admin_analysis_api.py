#!/usr/bin/env python3
"""
测试管理员分析详情API是否正确返回反馈信息
"""
import asyncio
import aiohttp
import json
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append('backend')

# API配置
API_BASE_URL = "http://localhost:8000"
ADMIN_USERNAME = "admin"  # 管理员用户名
ADMIN_PASSWORD = "admin123"  # 管理员密码

async def get_admin_token():
    """获取管理员访问令牌"""
    async with aiohttp.ClientSession() as session:
        # 登录获取token
        login_data = {
            "username": ADMIN_USERNAME,
            "password": ADMIN_PASSWORD
        }
        
        async with session.post(
            f"{API_BASE_URL}/api/v1/auth/login",
            data=login_data
        ) as response:
            if response.status == 200:
                result = await response.json()
                return result.get("access_token")
            else:
                print(f"❌ 登录失败: {response.status}")
                text = await response.text()
                print(f"错误信息: {text}")
                return None

async def get_analysis_with_feedback():
    """获取有反馈的分析记录ID"""
    # 从数据库查询中我们知道有反馈的分析记录ID
    return "4b8c8cee-f731-4414-8829-4d7766591489"

async def test_admin_analysis_detail_api():
    """测试管理员分析详情API"""
    print("=== 测试管理员分析详情API ===")
    
    # 1. 获取管理员token
    print("\n1. 获取管理员访问令牌...")
    token = await get_admin_token()
    if not token:
        print("❌ 无法获取管理员令牌")
        return False
    
    print("✅ 成功获取管理员令牌")
    
    # 2. 获取有反馈的分析记录ID
    print("\n2. 获取测试用的分析记录ID...")
    analysis_id = await get_analysis_with_feedback()
    print(f"✅ 使用分析记录ID: {analysis_id}")
    
    # 3. 调用分析详情API
    print("\n3. 调用分析详情API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.get(
            f"{API_BASE_URL}/api/v1/admin/analysis/{analysis_id}",
            headers=headers
        ) as response:
            print(f"API响应状态: {response.status}")
            
            if response.status == 200:
                result = await response.json()
                print("✅ API调用成功")
                
                # 4. 验证响应数据
                print("\n4. 验证响应数据...")
                
                # 检查基本字段
                required_fields = ["id", "emotion", "confidence", "username", "created_at"]
                for field in required_fields:
                    if field in result:
                        print(f"✅ 包含字段 '{field}': {result[field]}")
                    else:
                        print(f"❌ 缺少字段 '{field}'")
                
                # 检查反馈相关字段
                feedback_fields = ["feedbacks", "feedback_count"]
                for field in feedback_fields:
                    if field in result:
                        print(f"✅ 包含反馈字段 '{field}': {result[field]}")
                    else:
                        print(f"❌ 缺少反馈字段 '{field}'")
                
                # 5. 详细检查反馈数据
                print("\n5. 详细检查反馈数据...")
                
                if "feedbacks" in result and result["feedbacks"]:
                    feedbacks = result["feedbacks"]
                    print(f"✅ 找到 {len(feedbacks)} 条反馈记录")
                    
                    for i, feedback in enumerate(feedbacks, 1):
                        print(f"\n   反馈 {i}:")
                        print(f"     ID: {feedback.get('id', 'N/A')}")
                        print(f"     用户: {feedback.get('username', 'N/A')}")
                        print(f"     情绪: {feedback.get('emotion', 'N/A')}")
                        print(f"     评论: {feedback.get('comment', 'N/A')}")
                        print(f"     时间: {feedback.get('created_at', 'N/A')}")
                        
                        # 检查反馈必需字段
                        feedback_required = ["id", "emotion", "created_at", "analysis_id"]
                        for field in feedback_required:
                            if field in feedback:
                                print(f"       ✅ 反馈包含字段 '{field}'")
                            else:
                                print(f"       ❌ 反馈缺少字段 '{field}'")
                else:
                    print("⚠️  没有找到反馈数据")
                
                # 6. 检查图片URL
                print("\n6. 检查图片URL...")
                if "image_thumbnail_url" in result:
                    image_url = result["image_thumbnail_url"]
                    print(f"✅ 图片URL: {image_url}")
                else:
                    print("❌ 缺少图片URL")
                
                # 7. 输出完整的响应数据（格式化）
                print("\n7. 完整响应数据:")
                print("=" * 50)
                print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
                print("=" * 50)
                
                return True
                
            else:
                print(f"❌ API调用失败: {response.status}")
                error_text = await response.text()
                print(f"错误信息: {error_text}")
                return False

async def test_admin_analysis_list_api():
    """测试管理员分析列表API（验证列表中是否也包含反馈信息）"""
    print("\n\n=== 测试管理员分析列表API ===")
    
    # 获取管理员token
    token = await get_admin_token()
    if not token:
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.get(
            f"{API_BASE_URL}/api/v1/admin/analysis/?limit=5",
            headers=headers
        ) as response:
            if response.status == 200:
                result = await response.json()
                print("✅ 列表API调用成功")
                
                if "items" in result and result["items"]:
                    print(f"✅ 获取到 {len(result['items'])} 条记录")
                    
                    # 检查第一条记录是否包含反馈字段
                    first_item = result["items"][0]
                    if "feedbacks" in first_item or "feedback_count" in first_item:
                        print("✅ 列表API也包含反馈信息")
                    else:
                        print("ℹ️  列表API不包含反馈信息（这是正常的，反馈信息通常只在详情API中提供）")
                
                return True
            else:
                print(f"❌ 列表API调用失败: {response.status}")
                return False

if __name__ == "__main__":
    async def main():
        print("🚀 开始测试管理员分析API...")
        
        # 测试详情API
        detail_success = await test_admin_analysis_detail_api()
        
        # 测试列表API
        list_success = await test_admin_analysis_list_api()
        
        print("\n" + "=" * 60)
        if detail_success:
            print("🎉 分析详情API测试成功！反馈信息已正确集成")
        else:
            print("❌ 分析详情API测试失败")
        
        if list_success:
            print("✅ 分析列表API测试成功")
        else:
            print("❌ 分析列表API测试失败")
        
        return detail_success and list_success
    
    success = asyncio.run(main())
    if success:
        print("\n🎊 所有测试通过！用户反馈功能已成功集成到分析详情中")
    else:
        print("\n⚠️  部分测试失败，请检查相关配置") 