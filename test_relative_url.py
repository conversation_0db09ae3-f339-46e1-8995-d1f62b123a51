#!/usr/bin/env python
# -*- coding: utf-8 -*-

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import datetime
import logging
import sys

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def test_smtp_relative_url():
    """测试使用相对URL的邮件发送"""
    logger.info("开始测试使用相对URL的邮件发送...")
    
    # 配置信息
    smtp_host = "smtp.qq.com"
    smtp_port = 465  # SSL端口
    sender = "<EMAIL>"
    password = "qweamijhnidebaji"  # 授权码
    receiver = "<EMAIL>"  # 修改为您的邮箱
    
    # 使用相对URL
    relative_url = "/reset-password?token=test_token_12345"
    
    # 创建邮件
    message = MIMEMultipart()
    message['From'] = sender
    message['To'] = receiver
    message['Subject'] = "测试相对URL的密码重置链接"
    
    # 创建HTML内容，使用相对URL
    html_body = f"""
    <html>
    <body>
        <p>您好，</p>
        <p>这是一封测试邮件，用于测试相对URL的密码重置链接。</p>
        <p>请点击以下链接重置密码（相对URL）：</p>
        <p><a href='{relative_url}'>{relative_url}</a></p>
        <p>注意：由于这是相对URL，您需要在浏览器中手动添加域名。</p>
        <p>例如：http://localhost:3000{relative_url} 或 https://cxyai.shenzhuo.vip{relative_url}</p>
    </body>
    </html>
    """
    
    # 添加HTML内容，明确指定UTF-8编码
    part = MIMEText(html_body, 'html', 'utf-8')
    message.attach(part)
    
    try:
        # 使用SSL连接
        logger.info(f"尝试连接到{smtp_host}:{smtp_port}...")
        context = ssl.create_default_context()
        
        with smtplib.SMTP_SSL(smtp_host, smtp_port, context=context) as server:
            logger.info(f"连接成功，尝试登录...")
            server.login(sender, password)
            logger.info(f"登录成功，尝试发送邮件到{receiver}...")
            
            # 尝试发送邮件
            server.sendmail(sender, receiver, message.as_string())
            logger.info("邮件发送成功！")
            return True
            
    except Exception as e:
        logger.error(f"邮件发送失败: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    if test_smtp_relative_url():
        print("\n✅ 测试成功！邮件已发送。")
    else:
        print("\n❌ 测试失败！请检查错误信息。")
