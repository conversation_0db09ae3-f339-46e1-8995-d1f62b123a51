#!/usr/bin/env python3
"""
直接测试API逻辑
"""
import asyncio
import sys
import uuid

sys.path.append('backend')

from backend.emotionai.core.database.session import get_async_db
from backend.emotionai.crud.crud_emotion_analysis import cemotion_analysis
from backend.emotionai.crud.crud_emotion_feedback import cemotion_feedback
from backend.emotionai.schemas.admin.emotion_analysis import EmotionAnalysis

async def test_api_logic_directly():
    """直接测试API逻辑"""
    
    async for db in get_async_db():
        print("=== 直接测试API逻辑 ===")
        
        # 测试分析记录ID
        analysis_id = uuid.UUID("4b8c8cee-f731-4414-8829-4d7766591489")
        
        print(f"\n1. 获取分析记录...")
        analysis = await cemotion_analysis.get(db, id=analysis_id)
        if not analysis:
            print("❌ 分析记录不存在")
            return
        
        print(f"✅ 找到分析记录: {analysis.id}")
        print(f"   用户ID: {analysis.user_id}")
        print(f"   用户: {analysis.user.username if hasattr(analysis, 'user') and analysis.user else '未加载'}")
        
        print(f"\n2. 获取反馈记录...")
        feedbacks_orm = await cemotion_feedback.get_by_analysis_id_with_user(db, analysis_id)
        print(f"✅ 找到 {len(feedbacks_orm)} 条反馈记录")
        
        for i, feedback in enumerate(feedbacks_orm, 1):
            print(f"   反馈 {i}:")
            print(f"     ID: {feedback.id}")
            print(f"     情绪: {feedback.emotion}")
            print(f"     评论: {feedback.comment}")
            print(f"     用户: {feedback.user.username if hasattr(feedback, 'user') and feedback.user else '未加载'}")
        
        print(f"\n3. 模拟API数据构建...")
        
        # 构建分析记录数据（模拟API逻辑）
        analysis_data = {}
        
        # 复制基本属性
        for attr in ['id', 'input_type', 'input_path', 'emotion', 'confidence', 'gender', 'age', 'race', 
                     'user_id', 'session_id', 'device_info', 'analysis_time', 'model_version', 'additional_info',
                     'created_at', 'updated_at', 'deleted_at']:
            if hasattr(analysis, attr):
                analysis_data[attr] = getattr(analysis, attr)
        
        # 添加用户名
        if hasattr(analysis, 'user') and analysis.user:
            analysis_data["username"] = analysis.user.username
        else:
            analysis_data["username"] = "未知用户"
        
        # 处理图片缩略图URL
        analysis_data["image_thumbnail_url"] = None
        if analysis.input_path:
            absolute_image_path = str(analysis.input_path)
            if "original/" in absolute_image_path:
                relative_path = absolute_image_path[absolute_image_path.index("original/"):]
                analysis_data["image_thumbnail_url"] = f"/static/{relative_path}"
        
        # 构建反馈数据
        feedbacks_data = []
        for feedback_orm in feedbacks_orm:
            feedback_data = {}
            
            # 复制反馈基本属性
            for attr in ['id', 'analysis_id', 'emotion', 'comment', 'created_by', 'created_at', 'updated_at']:
                if hasattr(feedback_orm, attr):
                    feedback_data[attr] = getattr(feedback_orm, attr)
            
            # 添加反馈用户名
            if hasattr(feedback_orm, 'user') and feedback_orm.user:
                feedback_data["username"] = feedback_orm.user.username
            else:
                feedback_data["username"] = "未知用户"
            feedbacks_data.append(feedback_data)
        
        # 添加反馈信息到分析数据
        analysis_data["feedbacks"] = feedbacks_data
        analysis_data["feedback_count"] = len(feedbacks_data)
        
        print(f"✅ 构建完成，包含 {len(feedbacks_data)} 条反馈")
        
        print(f"\n4. 验证Pydantic模型...")
        try:
            validated_analysis = EmotionAnalysis.model_validate(analysis_data)
            print("✅ Pydantic模型验证成功")
            
            print(f"\n📊 最终结果:")
            print(f"   分析ID: {validated_analysis.id}")
            print(f"   用户名: {validated_analysis.username}")
            print(f"   情绪: {validated_analysis.emotion}")
            print(f"   反馈数量: {validated_analysis.feedback_count}")
            
            if validated_analysis.feedbacks:
                print(f"\n📝 反馈详情:")
                for i, feedback in enumerate(validated_analysis.feedbacks, 1):
                    print(f"   反馈 {i}:")
                    print(f"     用户: {feedback.username}")
                    print(f"     情绪: {feedback.emotion}")
                    print(f"     评论: {feedback.comment}")
            
            return True
            
        except Exception as e:
            print(f"❌ Pydantic模型验证失败: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = asyncio.run(test_api_logic_directly())
    if success:
        print("\n🎉 API逻辑测试成功！")
    else:
        print("\n❌ API逻辑测试失败！") 