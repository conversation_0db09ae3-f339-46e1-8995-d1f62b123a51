#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
生成测试语音文件
"""

import os
from gtts import gTTS

def generate_speech_file(text, filename, lang='zh-cn'):
    """
    使用Google Text-to-Speech生成语音文件
    
    Args:
        text: 要转换为语音的文本
        filename: 输出文件名
        lang: 语言代码
    """
    print(f"正在生成语音文件: {filename}")
    print(f"文本内容: {text}")
    
    # 创建gTTS对象
    tts = gTTS(text=text, lang=lang, slow=False)
    
    # 保存到文件
    tts.save(filename)
    
    print(f"语音文件已生成: {filename}")
    
if __name__ == "__main__":
    # 生成一个包含不同情绪的语音文件
    happy_text = "我今天非常开心，因为我收到了一个好消息。这真是太棒了！"
    generate_speech_file(happy_text, "happy_speech.mp3")
    
    # 生成一个悲伤的语音文件
    sad_text = "我感到很难过，因为我失去了一个重要的东西。这让我很伤心。"
    generate_speech_file(sad_text, "sad_speech.mp3")
