#!/usr/bin/env python3
"""
测试反馈CRUD方法
"""
import asyncio
import sys
import uuid

sys.path.append('backend')

from backend.emotionai.core.database.session import get_async_db
from backend.emotionai.crud.crud_emotion_feedback import cemotion_feedback

async def test_feedback_crud():
    """测试反馈CRUD方法"""
    
    async for db in get_async_db():
        print("=== 测试反馈CRUD方法 ===")
        
        # 测试分析记录ID
        analysis_id = uuid.UUID("4b8c8cee-f731-4414-8829-4d7766591489")
        
        print(f"\n1. 测试 get_by_analysis_id 方法...")
        try:
            feedbacks = await cemotion_feedback.get_by_analysis_id(db, analysis_id)
            print(f"✅ 找到 {len(feedbacks)} 条反馈记录")
            
            for i, feedback in enumerate(feedbacks, 1):
                print(f"   反馈 {i}:")
                print(f"     ID: {feedback.id}")
                print(f"     情绪: {feedback.emotion}")
                print(f"     评论: {feedback.comment}")
                print(f"     创建时间: {feedback.created_at}")
                print(f"     分析ID: {feedback.analysis_id}")
                print(f"     创建者: {feedback.created_by}")
                
        except Exception as e:
            print(f"❌ get_by_analysis_id 方法失败: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"\n2. 测试 get_by_analysis_id_with_user 方法...")
        try:
            feedbacks_with_user = await cemotion_feedback.get_by_analysis_id_with_user(db, analysis_id)
            print(f"✅ 找到 {len(feedbacks_with_user)} 条反馈记录（包含用户信息）")
            
            for i, feedback in enumerate(feedbacks_with_user, 1):
                print(f"   反馈 {i}:")
                print(f"     ID: {feedback.id}")
                print(f"     情绪: {feedback.emotion}")
                print(f"     评论: {feedback.comment}")
                print(f"     创建时间: {feedback.created_at}")
                
                # 检查用户关系
                try:
                    if hasattr(feedback, 'user') and feedback.user:
                        print(f"     用户: {feedback.user.username}")
                    else:
                        print(f"     用户: 未加载用户信息")
                except Exception as e:
                    print(f"     用户: 加载用户信息失败 - {e}")
                
        except Exception as e:
            print(f"❌ get_by_analysis_id_with_user 方法失败: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"\n3. 测试 count_by_analysis_id 方法...")
        try:
            count = await cemotion_feedback.count_by_analysis_id(db, analysis_id)
            print(f"✅ 反馈数量: {count}")
        except Exception as e:
            print(f"❌ count_by_analysis_id 方法失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_feedback_crud()) 