#!/usr/bin/env python3
"""
检查特定分析记录的反馈数据
"""
import psycopg2
import os
from dotenv import load_dotenv

load_dotenv()

DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': os.getenv('DB_PORT', '5432'),
    'database': os.getenv('DB_NAME', 'emotion_ai'),
    'user': os.getenv('DB_USER', 'dom'),
    'password': os.getenv('DB_PASSWORD', '')
}

def check_feedback_for_analysis():
    conn = psycopg2.connect(**DB_CONFIG)
    cursor = conn.cursor()

    # 检查特定分析记录的反馈
    analysis_id = '4b8c8cee-f731-4414-8829-4d7766591489'
    
    print(f"=== 检查分析记录 {analysis_id} 的反馈数据 ===")
    
    # 1. 检查分析记录是否存在
    cursor.execute('''
        SELECT id, emotion, confidence, user_id, created_at
        FROM emotion_analysis 
        WHERE id = %s AND deleted_at IS NULL
    ''', (analysis_id,))
    
    analysis = cursor.fetchone()
    if analysis:
        print(f"✅ 分析记录存在:")
        print(f"   ID: {analysis[0]}")
        print(f"   情绪: {analysis[1]}")
        print(f"   置信度: {analysis[2]}")
        print(f"   用户ID: {analysis[3]}")
        print(f"   创建时间: {analysis[4]}")
    else:
        print("❌ 分析记录不存在")
        return

    # 2. 检查该分析记录的反馈
    cursor.execute('''
        SELECT ef.id, ef.emotion, ef.comment, ef.created_at, ef.analysis_id, ef.created_by, ef.deleted_at
        FROM emotion_feedback ef 
        WHERE ef.analysis_id = %s
        ORDER BY ef.created_at DESC
    ''', (analysis_id,))

    feedbacks = cursor.fetchall()
    print(f"\n📝 反馈记录数量: {len(feedbacks)}")

    for i, feedback in enumerate(feedbacks, 1):
        print(f"\n反馈 {i}:")
        print(f"   ID: {feedback[0]}")
        print(f"   情绪: {feedback[1]}")
        print(f"   评论: {feedback[2]}")
        print(f"   创建时间: {feedback[3]}")
        print(f"   分析ID: {feedback[4]}")
        print(f"   创建者: {feedback[5]}")
        print(f"   删除时间: {feedback[6]}")
        
        if feedback[6] is not None:
            print("   ⚠️  这条反馈已被软删除")
        else:
            print("   ✅ 这条反馈是活跃的")

    # 3. 检查未删除的反馈
    cursor.execute('''
        SELECT ef.id, ef.emotion, ef.comment, ef.created_at
        FROM emotion_feedback ef 
        WHERE ef.analysis_id = %s AND ef.deleted_at IS NULL
        ORDER BY ef.created_at DESC
    ''', (analysis_id,))

    active_feedbacks = cursor.fetchall()
    print(f"\n✅ 活跃反馈数量: {len(active_feedbacks)}")

    # 4. 检查所有反馈记录（不限制分析ID）
    cursor.execute('''
        SELECT COUNT(*) FROM emotion_feedback WHERE deleted_at IS NULL
    ''')
    total_active_feedbacks = cursor.fetchone()[0]
    print(f"\n📊 数据库中总的活跃反馈数量: {total_active_feedbacks}")

    # 5. 检查有反馈的分析记录
    cursor.execute('''
        SELECT DISTINCT ef.analysis_id, ea.emotion
        FROM emotion_feedback ef
        JOIN emotion_analysis ea ON ef.analysis_id = ea.id
        WHERE ef.deleted_at IS NULL AND ea.deleted_at IS NULL
        ORDER BY ef.analysis_id
    ''')
    
    analyses_with_feedback = cursor.fetchall()
    print(f"\n🔗 有反馈的分析记录:")
    for analysis_with_feedback in analyses_with_feedback:
        print(f"   分析ID: {analysis_with_feedback[0]}, 情绪: {analysis_with_feedback[1]}")

    cursor.close()
    conn.close()

if __name__ == "__main__":
    check_feedback_for_analysis() 