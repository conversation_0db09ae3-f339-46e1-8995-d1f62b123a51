// 第一处情绪颜色映射（用于情绪分析历史记录表格）
render: (emotion) => {
  // 情绪对应的颜色和中英文映射
  const emotionColors: Record<string, string> = {
    // 中文情绪
    '高兴': 'green',
    '快乐': 'green',
    'Happy': 'green',
    '悲伤': 'blue',
    'Sad': 'blue',
    '愤怒': 'red',
    'Angry': 'red',
    '恐惧': 'purple',
    'Fear': 'purple',
    '厌恶': 'orange',
    'Disgust': 'orange',
    '惊喜': 'cyan',
    'Surprise': 'cyan',
    '中立': 'default',
    'Neutral': 'default',
    '惊讯': 'cyan',
    '蔑视': 'magenta',
    'Contempt': 'magenta',
  };
  return <Tag color={emotionColors[emotion] || 'default'}>{emotion}</Tag>;
},

// 第二处情绪颜色映射（用于情绪分布表格）
render: (emotion) => {
  // 情绪对应的颜色和中英文映射
  const emotionColors: Record<string, string> = {
    // 中文情绪
    '高兴': 'green',
    '快乐': 'green',
    'Happy': 'green',
    '悲伤': 'blue',
    'Sad': 'blue',
    '愤怒': 'red',
    'Angry': 'red',
    '恐惧': 'purple',
    'Fear': 'purple',
    '厌恶': 'orange',
    'Disgust': 'orange',
    '惊喜': 'cyan',
    'Surprise': 'cyan',
    '中立': 'default',
    'Neutral': 'default',
    '惊讯': 'cyan',
    '蔑视': 'magenta',
    'Contempt': 'magenta',
  };
  return <Tag color={emotionColors[emotion] || 'default'}>{emotion}</Tag>;
},

// 置信度显示为整数百分比
render: (confidence) => `${Math.round(confidence)}%`,
