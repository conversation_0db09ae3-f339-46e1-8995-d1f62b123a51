<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>反馈输入框测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .button-group {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
            position: relative;
        }
        
        .feedback-button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            transition: all 0.3s;
        }
        
        .good-btn { background-color: #52c41a; }
        .average-btn { background-color: #faad14; }
        .bad-btn { background-color: #f05654; }
        
        .feedback-button:hover {
            opacity: 0.8;
        }
        
        .input-box {
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 90%;
            max-width: 400px;
            background: white;
            border: 2px solid #faad14;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.3);
            z-index: 1000;
            display: none;
        }
        
        .input-box.visible {
            display: block;
        }
        
        .input-box.bad {
            border-color: #f05654;
        }
        
        .prompt-text {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            text-align: center;
        }
        
        .textarea {
            width: 100%;
            min-height: 80px;
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            resize: vertical;
            font-family: inherit;
            margin-bottom: 8px;
        }
        
        .char-count {
            font-size: 12px;
            color: #999;
            text-align: right;
            margin-bottom: 12px;
        }
        
        .button-row {
            display: flex;
            gap: 8px;
        }
        
        .cancel-btn, .submit-btn {
            flex: 1;
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .cancel-btn {
            background: #f5f5f5;
            color: #333;
        }
        
        .submit-btn {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .log {
            margin-top: 20px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>反馈输入框测试</h1>
        <p>点击"一般"或"不好"按钮测试输入框显示功能</p>
        
        <div class="button-group" id="buttonGroup">
            <button class="feedback-button good-btn" onclick="handleClick('good')">
                好评<br><small>Good</small>
            </button>
            <button class="feedback-button average-btn" onclick="handleClick('average')">
                一般<br><small>Average</small>
            </button>
            <button class="feedback-button bad-btn" onclick="handleClick('bad')">
                不好<br><small>Bad</small>
            </button>
            
            <!-- 输入框 -->
            <div class="input-box" id="inputBox">
                <div class="prompt-text" id="promptText">
                    请告诉我们如何改进：
                </div>
                <textarea class="textarea" id="textarea" placeholder="请输入您的建议..." maxlength="200"></textarea>
                <div class="char-count" id="charCount">0/200</div>
                <div class="button-row">
                    <button class="cancel-btn" onclick="hideInputBox()">取消</button>
                    <button class="submit-btn" onclick="submitFeedback()">提交反馈</button>
                </div>
            </div>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let currentType = null;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function handleClick(type) {
            log(`点击了 ${type} 按钮`);
            currentType = type;
            
            if (type === 'good') {
                log('好评直接提交，不显示输入框');
                hideInputBox();
            } else {
                log(`显示输入框，类型: ${type}`);
                showInputBox(type);
            }
        }
        
        function showInputBox(type) {
            const inputBox = document.getElementById('inputBox');
            const promptText = document.getElementById('promptText');
            const textarea = document.getElementById('textarea');
            
            // 设置提示文字
            if (type === 'average') {
                promptText.textContent = '您觉得分析结果一般，请告诉我们如何改进：';
                inputBox.className = 'input-box visible';
            } else if (type === 'bad') {
                promptText.textContent = '您觉得分析结果不好，请告诉我们哪里需要改进：';
                inputBox.className = 'input-box visible bad';
            }
            
            // 清空输入框
            textarea.value = '';
            updateCharCount();
            
            // 自动聚焦
            setTimeout(() => {
                textarea.focus();
            }, 100);
            
            log('输入框已显示');
        }
        
        function hideInputBox() {
            const inputBox = document.getElementById('inputBox');
            inputBox.className = 'input-box';
            currentType = null;
            log('输入框已隐藏');
        }
        
        function submitFeedback() {
            const textarea = document.getElementById('textarea');
            const comment = textarea.value.trim();
            
            log(`提交反馈: 类型=${currentType}, 内容="${comment}"`);
            hideInputBox();
        }
        
        function updateCharCount() {
            const textarea = document.getElementById('textarea');
            const charCount = document.getElementById('charCount');
            charCount.textContent = `${textarea.value.length}/200`;
        }
        
        // 绑定输入事件
        document.getElementById('textarea').addEventListener('input', updateCharCount);
        
        // 绑定键盘事件
        document.getElementById('textarea').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                submitFeedback();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                hideInputBox();
            }
        });
        
        log('测试页面已加载');
    </script>
</body>
</html> 