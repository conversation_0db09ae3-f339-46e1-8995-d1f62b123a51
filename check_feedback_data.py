#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psycopg2
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接参数
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': os.getenv('DB_PORT', '5432'),
    'database': os.getenv('DB_NAME', 'emotion_ai'),
    'user': os.getenv('DB_USER', 'dom'),
    'password': os.getenv('DB_PASSWORD', '')
}

try:
    # 连接数据库
    conn = psycopg2.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('=== 检查情绪分析和反馈数据 ===\n')
    
    # 1. 检查emotion_analysis表
    print('1. 情绪分析记录:')
    cursor.execute("""
        SELECT COUNT(*) FROM emotion_analysis 
        WHERE deleted_at IS NULL;
    """)
    analysis_count = cursor.fetchone()[0]
    print(f'   总记录数: {analysis_count}')
    
    if analysis_count > 0:
        # 显示最近的几条记录
        cursor.execute("""
            SELECT id, emotion, confidence, input_type, created_at 
            FROM emotion_analysis 
            WHERE deleted_at IS NULL 
            ORDER BY created_at DESC 
            LIMIT 5;
        """)
        recent_analyses = cursor.fetchall()
        print('   最近的分析记录:')
        for record in recent_analyses:
            print(f'     ID: {record[0][:8]}... | 情绪: {record[1]} | 置信度: {record[2]:.2f} | 类型: {record[3]} | 时间: {record[4]}')
    
    # 2. 检查emotion_feedback表
    print('\n2. 情绪反馈记录:')
    cursor.execute("SELECT COUNT(*) FROM emotion_feedback;")
    feedback_count = cursor.fetchone()[0]
    print(f'   总反馈数: {feedback_count}')
    
    if feedback_count > 0:
        # 显示最近的几条反馈
        cursor.execute("""
            SELECT ef.id, ef.emotion, ef.comment, ef.created_at, ea.emotion as original_emotion
            FROM emotion_feedback ef
            LEFT JOIN emotion_analysis ea ON ef.analysis_id = ea.id
            ORDER BY ef.created_at DESC 
            LIMIT 5;
        """)
        recent_feedbacks = cursor.fetchall()
        print('   最近的反馈记录:')
        for record in recent_feedbacks:
            comment = record[2][:50] + '...' if record[2] and len(record[2]) > 50 else record[2] or '无评论'
            print(f'     反馈情绪: {record[1]} | 原始情绪: {record[4]} | 评论: {comment} | 时间: {record[3]}')
    
    # 3. 检查反馈与分析的关联
    print('\n3. 反馈与分析的关联:')
    cursor.execute("""
        SELECT COUNT(DISTINCT ef.analysis_id) 
        FROM emotion_feedback ef
        JOIN emotion_analysis ea ON ef.analysis_id = ea.id
        WHERE ea.deleted_at IS NULL;
    """)
    linked_analyses = cursor.fetchone()[0]
    print(f'   有反馈的分析记录数: {linked_analyses}')
    
    if analysis_count > 0:
        feedback_rate = (linked_analyses / analysis_count) * 100
        print(f'   反馈覆盖率: {feedback_rate:.1f}%')
    
    # 4. 检查反馈情绪分布
    print('\n4. 反馈情绪分布:')
    cursor.execute("""
        SELECT emotion, COUNT(*) as count 
        FROM emotion_feedback 
        GROUP BY emotion 
        ORDER BY count DESC;
    """)
    emotion_distribution = cursor.fetchall()
    if emotion_distribution:
        for emotion, count in emotion_distribution:
            print(f'   {emotion}: {count} 次')
    else:
        print('   暂无反馈数据')
    
    # 5. 检查用户反馈活跃度
    print('\n5. 用户反馈活跃度:')
    cursor.execute("""
        SELECT created_by, COUNT(*) as feedback_count
        FROM emotion_feedback 
        GROUP BY created_by 
        ORDER BY feedback_count DESC 
        LIMIT 5;
    """)
    user_activity = cursor.fetchall()
    if user_activity:
        print('   最活跃的反馈用户:')
        for user_id, count in user_activity:
            print(f'     用户 {str(user_id)[:8]}...: {count} 次反馈')
    else:
        print('   暂无用户反馈数据')
    
    cursor.close()
    conn.close()
    
    print('\n=== 检查完成 ===')
    
    if analysis_count == 0:
        print('\n⚠️  没有找到情绪分析记录，请先进行情绪分析')
    elif feedback_count == 0:
        print('\n⚠️  没有找到反馈记录，用户输入可能没有成功保存到数据库')
        print('   建议检查:')
        print('   1. 前端反馈提交逻辑')
        print('   2. 后端API端点')
        print('   3. 数据库连接和权限')
    else:
        print('\n✅ 数据库中有情绪分析和反馈数据')
    
except Exception as e:
    print(f'数据库连接或查询错误: {e}') 