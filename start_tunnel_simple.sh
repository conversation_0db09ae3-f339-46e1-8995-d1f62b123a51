#!/bin/bash

# 简化的Cloudflare隧道启动脚本
# 使用最基本的配置来避免连接问题

echo "🚀 启动Cloudflare隧道 (简化模式)..."
echo "📍 项目路径: $(pwd)"
echo ""

# 清理现有进程
./cleanup_tunnel.sh

echo ""
echo "🔍 检查本地服务状态..."

# 检查后端服务 (8000端口)
if lsof -i :8000 > /dev/null 2>&1; then
    echo "✅ 后端服务 (8000端口) 正在运行"
else
    echo "⚠️  警告: 后端服务 (8000端口) 未运行"
    echo "   请先启动后端服务: ./start_server.sh"
fi

# 检查前端服务 (3000端口)
if lsof -i :3000 > /dev/null 2>&1; then
    echo "✅ 前端服务 (3000端口) 正在运行"
else
    echo "⚠️  警告: 前端服务 (3000端口) 未运行"
    echo "   请先启动前端服务: cd frontend && pnpm dev"
fi

echo ""
echo "🌐 启动隧道连接 (简化模式)..."
echo "📝 域名: www.cxyai.net"
echo "🔗 所有流量: www.cxyai.net/* -> localhost:3000"
echo ""
echo "注意: 这是简化模式，所有流量都会转发到前端"
echo "如果需要API路由，请使用完整配置文件"
echo ""
echo "按 Ctrl+C 停止隧道"
echo "----------------------------------------"

# 使用最简单的方式启动隧道 - 直接转发到前端
cloudflared tunnel --url http://localhost:3000 run ema-dev
