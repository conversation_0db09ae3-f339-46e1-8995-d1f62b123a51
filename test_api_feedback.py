#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import uuid
from datetime import datetime
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接参数
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': os.getenv('DB_PORT', '5432'),
    'database': os.getenv('DB_NAME', 'emotion_ai'),
    'user': os.getenv('DB_USER', 'dom'),
    'password': os.getenv('DB_PASSWORD', '')
}

# 异步数据库连接
DATABASE_URL = f"postgresql+asyncpg://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"

async def test_api_feedback():
    """测试API层面的反馈功能"""
    
    # 创建异步引擎和会话
    engine = create_async_engine(DATABASE_URL)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    try:
        async with async_session() as db:
            print("=== 测试API层面的反馈功能 ===")
            
            # 1. 测试保存反馈功能
            print("\n1. 测试保存反馈功能...")
            
            # 使用已知的分析记录ID和用户ID
            analysis_id = "799e7d78-14cd-44af-ab28-f41a3b89a7e0"
            user_id = "386c9d5c-4174-4811-91bf-595bfeb7e841"
            
            # 创建一个简化的反馈记录
            from emotionai.models.ml.emotion_feedback import EmotionFeedback
            
            feedback = EmotionFeedback(
                analysis_id=uuid.UUID(analysis_id),
                emotion="sad",
                comment="API测试反馈 - 这个分析结果不太准确",
                created_by=uuid.UUID(user_id),
                created_at=datetime.now(),
            )
            
            # 保存到数据库
            db.add(feedback)
            await db.commit()
            await db.refresh(feedback)
            
            print(f"✅ API反馈保存成功，ID: {feedback.id}")
            
            # 2. 测试查询反馈历史
            print("\n2. 测试查询反馈历史...")
            
            from sqlalchemy import select, desc
            
            query = (
                select(EmotionFeedback)
                .where(
                    EmotionFeedback.analysis_id == uuid.UUID(analysis_id),
                    EmotionFeedback.created_by == uuid.UUID(user_id),
                )
                .order_by(desc(EmotionFeedback.created_at))
            )
            
            result = await db.execute(query)
            feedbacks = result.scalars().all()
            
            print(f"✅ 找到 {len(feedbacks)} 条反馈记录:")
            for i, feedback in enumerate(feedbacks):
                print(f"  反馈 #{i+1}:")
                print(f"    ID: {feedback.id}")
                print(f"    Analysis ID: {feedback.analysis_id}")
                print(f"    Emotion: {feedback.emotion}")
                print(f"    Comment: {feedback.comment}")
                print(f"    Created At: {feedback.created_at}")
                print(f"    Created By: {feedback.created_by}")
                print("    ---")
            
            print("\n🎉 API层面的反馈功能测试完成！")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await engine.dispose()

if __name__ == "__main__":
    asyncio.run(test_api_feedback()) 