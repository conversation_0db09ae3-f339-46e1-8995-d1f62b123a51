import os
import sys
import psycopg2
from psycopg2 import sql

def main():
    try:
        # 从环境变量或使用默认值获取数据库连接信息
        db_host = os.environ.get("POSTGRES_SERVER", "localhost")
        db_user = os.environ.get("POSTGRES_USER", "dom")
        db_password = os.environ.get("POSTGRES_PASSWORD", "")
        db_name = os.environ.get("POSTGRES_DB", "emotion_ai")
        
        print(f"尝试连接到 PostgreSQL 数据库: {db_name} (用户: {db_user}, 主机: {db_host})")
        
        # 连接到数据库
        conn = psycopg2.connect(
            host=db_host,
            user=db_user,
            password=db_password,
            dbname=db_name
        )
        
        # 创建游标
        cursor = conn.cursor()
        
        # 查询 emotion_analysis 表中的情绪值
        print("查询 emotion_analysis 表中的情绪值...")
        cursor.execute("SELECT DISTINCT emotion FROM emotion_analysis WHERE deleted_at IS NULL")
        emotions = cursor.fetchall()
        
        if emotions:
            print(f"数据库中的情绪值: {emotions}")
            
            # 查询每种情绪的记录数
            for emotion in emotions:
                emotion_value = emotion[0]
                cursor.execute(
                    sql.SQL("SELECT COUNT(*) FROM emotion_analysis WHERE emotion = %s AND deleted_at IS NULL"),
                    (emotion_value,)
                )
                count = cursor.fetchone()[0]
                print(f"情绪 '{emotion_value}' 的记录数: {count}")
                
                # 查询大小写不敏感的情绪值
                cursor.execute(
                    sql.SQL("SELECT COUNT(*) FROM emotion_analysis WHERE LOWER(emotion) = LOWER(%s) AND deleted_at IS NULL"),
                    (emotion_value,)
                )
                case_insensitive_count = cursor.fetchone()[0]
                print(f"情绪 '{emotion_value}' 的记录数 (不区分大小写): {case_insensitive_count}")
        else:
            print("数据库中没有情绪分析记录或 emotion_analysis 表不存在")
            
            # 检查表是否存在
            cursor.execute("SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'emotion_analysis')")
            table_exists = cursor.fetchone()[0]
            
            if table_exists:
                print("emotion_analysis 表存在，但没有记录或所有记录已被标记为删除")
                
                # 检查表中是否有任何记录（包括已删除的）
                cursor.execute("SELECT COUNT(*) FROM emotion_analysis")
                total_count = cursor.fetchone()[0]
                print(f"emotion_analysis 表中的总记录数: {total_count}")
                
                if total_count > 0:
                    # 检查已删除的记录
                    cursor.execute("SELECT COUNT(*) FROM emotion_analysis WHERE deleted_at IS NOT NULL")
                    deleted_count = cursor.fetchone()[0]
                    print(f"已删除的记录数: {deleted_count}")
                    
                    # 获取一些示例记录
                    cursor.execute("SELECT id, emotion, created_at, deleted_at FROM emotion_analysis LIMIT 5")
                    sample_records = cursor.fetchall()
                    print(f"示例记录: {sample_records}")
            else:
                print("emotion_analysis 表不存在")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"查询数据库时出错: {e}")

if __name__ == "__main__":
    main()
