absl-py==2.2.2
aiofiles @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_f56ag8l7kr/croot/aiofiles_1683773599608/work
aiosmtplib==3.0.2
albumentations==1.3.1
alembic==1.15.2
annotated-types @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_1fa2djihwb/croot/annotated-types_1709542925772/work
anyio @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_d9l4uro_qv/croot/anyio_1745334654441/work
argcomplete==3.5.3
arrow==1.3.0
asttokens==3.0.0
astunparse==1.6.3
async-timeout @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_0fagvkecl5/croot/async-timeout_1732662297241/work
asyncpg @ file:///Users/<USER>/miniforge3/conda-bld/asyncpg_1729845850049/work
attrs==25.3.0
bandit==1.7.5
bcrypt==4.3.0
beautifulsoup4==4.13.4
binaryornot==0.4.4
black @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_86c3dop3qh/croot/black_1712698506071/work
blinker==1.9.0
Bottleneck @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_55txi4fy1u/croot/bottleneck_1731058642212/work
Brotli @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_f7i0oxypt6/croot/brotli-split_1736182464088/work
build==1.2.2.post1
cachetools==5.5.2
certifi @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_53iribkvo4/croot/certifi_1738623743875/work/certifi
cffi @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_e4xd9yd9i2/croot/cffi_1736182819442/work
cfgv @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_80pd4bhsl2/croot/cfgv_1694505980983/work
chardet==5.2.0
charset-normalizer==3.4.1
click @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_d2rtg2ejqc/croot/click_1744271604580/work
code2flow==2.5.1
colorama==0.4.6
coloredlogs==15.0.1
commitizen==4.6.0
contourpy @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_2cvjf0v4ux/croot/contourpy_1732540055997/work
cookiecutter==2.6.0
copier==9.7.1
cryptography @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_fdxcx92j10/croot/cryptography_1740577852661/work
cycler @ file:///tmp/build/80754af9/cycler_1637851556182/work
Cython==3.0.12
decli==0.6.2
decorator==5.2.1
deepface==0.0.93
Deprecated==1.2.18
diffusers==0.32.2
distlib @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_601vai9os4/croot/distlib_1714716995800/work
dnspython @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_ac52k4vqs2/croot/dnspython_1703096969321/work
dunamai==1.23.1
easydict==1.13
ecdsa==0.19.1
email_validator @ file:///home/<USER>/feedstock_root/build_artifacts/email-validator-meta_1733300719943/work
-e git+https://github.com/changxiaoyangbrain/emotionAI.git@9d9b129757a04b797f43f0ff93fd223adf396384#egg=emotionai
exceptiongroup @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_b2258scr33/croot/exceptiongroup_1706031391815/work
executing==2.2.0
fastapi==0.115.12
fastapi-cli @ file:///home/<USER>/feedstock_root/build_artifacts/fastapi-cli_1734302308128/work
fastapi-mail==1.5.0
filelock @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_f6dmuewahq/croot/filelock_1744281400993/work
fire==0.7.0
flake8==7.2.0
Flask==3.1.0
flask-cors==5.0.1
flatbuffers==25.2.10
fonttools @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_ce1jt_55vl/croot/fonttools_1737039388732/work
fsspec==2025.3.2
funcy==2.0
gast==0.4.0
gdown==5.2.0
gitdb==4.0.12
GitPython==3.1.44
google-ai-generativelanguage==0.6.15
google-api-core==2.25.0rc1
google-api-python-client==2.169.0
google-auth==2.40.1
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.0.0
google-generativeai==0.8.5
google-pasta==0.2.0
googleapis-common-protos==1.70.0
greenlet @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_3fdrsxa1og/croot/greenlet_1733860082149/work
grpcio==1.71.0
gunicorn==23.0.0
h11 @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_110bmw2coo/croot/h11_1706652289620/work
h5py==3.13.0
httpcore @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_fcxiho9nv7/croot/httpcore_1706728465004/work
httplib2==0.22.0
httptools @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_15flooa1ra/croot/httptools_1732564161042/work
httpx @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_cc4egw1482/croot/httpx_1723474826664/work
huggingface-hub==0.30.2
humanfriendly==10.0
identify @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_9b1fejv0gu/croots/recipe/identify_1663193182471/work
idna @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_a12xpo84t2/croot/idna_1714398852854/work
imageio==2.37.0
importlib_metadata==8.7.0
iniconfig @ file:///home/<USER>/recipes/ci/iniconfig_1610983019677/work
insightface==0.7.3
isort @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_3fj5_dtsoq/croot/isort_1741284893939/work
itsdangerous==2.2.0
jax==0.4.34
jaxlib==0.4.34
jedi==0.19.2
Jinja2 @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_2cnn4kenrm/croot/jinja2_1741710859444/work
jinja2-ansible-filters==1.3.2
joblib==1.4.2
jsonpickle==4.0.5
keras==3.9.2
kiwisolver @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_cc2l_z_0ri/croot/kiwisolver_1737039586949/work
lazy_loader==0.4
libclang==18.1.1
limits==5.1.0
lz4==4.4.4
Mako @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_e36bw9_e01/croot/mako_1665472433821/work
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_1f_uj4vxik/croot/markupsafe_1738584045311/work
matplotlib==3.10.1
matplotlib-inline==0.1.7
mccabe @ file:///opt/conda/conda-bld/mccabe_1644221741721/work
mdurl==0.1.2
mediapipe==0.10.21
ml-dtypes==0.3.2
mpmath==1.3.0
mtcnn==1.0.0
mypy @ file:///Users/<USER>/miniforge3/conda-bld/mypy-split_1713327168159/work
mypy-extensions @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_98zqpuwvro/croot/mypy_extensions_1695130957675/work
namex==0.0.9
networkx==3.4.2
nodeenv @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_ab2gvw03cp/croot/nodeenv_1741859502434/work
numexpr @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_b3kvvt6tc6/croot/numexpr_1730215947700/work
numpy==1.26.4
oauthlib==3.2.2
onnx==1.17.0
onnxruntime==1.21.1
opencv-contrib-python==*********
opencv-python==********
opencv-python-headless==*********
opt_einsum==3.4.0
optree==0.15.0
packaging @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_a6_qk3qyg7/croot/packaging_1734472142254/work
pandas==2.0.3
parso==0.8.4
passlib==1.7.4
pathspec @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_f954ebvbxx/croot/pathspec_1674681573969/work
pbr==6.1.1
pexpect==4.9.0
pgvector==0.4.1
pillow==11.2.1
pip-tools==7.4.1
pipdeptree==2.26.1
platformdirs @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_4euggz8v1n/croot/platformdirs_1744273055841/work
pluggy @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_bei6q1cvvt/croot/pluggy_1733169621717/work
plumbum==1.9.0
pre_commit @ file:///home/<USER>/feedstock_root/build_artifacts/pre-commit_1742475552107/work
prettytable==3.16.0
prometheus-fastapi-instrumentator==7.1.0
prometheus_client==0.21.1
prompt_toolkit==3.0.51
proto-plus==1.26.1
protobuf==4.25.7
psutil==7.0.0
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyan3==1.2.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycodestyle==2.13.0
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
pydantic==2.11.4
pydantic-settings==2.9.1
pydantic_core==2.33.2
pyflakes==3.3.2
Pygments @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_f0f10r98sf/croot/pygments_1744664126614/work
PyJWT==2.10.1
pyparsing @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_65qfw6vkxg/croot/pyparsing_1731445528142/work
pyproject_hooks==1.2.0
PySocks==1.7.1
pytest==8.3.5
python-dateutil @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_66ud1l42_h/croot/python-dateutil_1716495741162/work
python-dotenv @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_0bqseadoho/croot/python-dotenv_1745610246418/work
python-jose==3.4.0
python-multipart @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_43t75u1qxn/croot/python-multipart_1743526711393/work
python-slugify==8.0.4
pytz @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_a4b76c83ik/croot/pytz_1713974318928/work
pyvis==0.3.2
PyYAML @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_faoex52hrr/croot/pyyaml_1728657970485/work
qudida==0.0.4
questionary==2.1.0
redis==6.0.0
redislite==6.2.912183
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
retina-face==0.0.17
rich @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_034myiu7pr/croot/rich_1732638981241/work
rich-toolkit==0.14.6
rsa==4.9.1
ruff @ file:///Users/<USER>/miniforge3/conda-bld/ruff_1745528860383/work
safetensors==0.5.3
scikit-image==0.25.2
scikit-learn==1.6.1
scipy @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_cc_7w74fto/croot/scipy_1743153255057/work/dist/scipy-1.15.2-cp310-cp310-macosx_11_0_arm64.whl#sha256=ee07a0e6b02a203db8a258a51b5822186b365c46a776093594d3a9a290ecd92d
seaborn==0.13.2
sentencepiece==0.2.0
shellingham @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_9bf5wowles/croot/shellingham_1669142181600/work
simsimd==6.2.1
six==1.15.0
slowapi==0.1.9
smmap==5.0.2
sniffio @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_1573pknjrg/croot/sniffio_1705431298885/work
sounddevice==0.5.1
soundfile==0.13.1
soupsieve==2.7
SQLAlchemy==2.0.41
stack-data==0.6.3
starlette @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_ae9pkrh7rr/croot/starlette-recipe_1745338537544/work
stevedore==5.4.1
stringzilla==3.12.5
sympy==1.13.1
tensorboard==2.16.2
tensorboard-data-server==0.7.2
tensorboard-plugin-wit==1.8.1
tensorflow==2.16.2
tensorflow-estimator==2.13.0
tensorflow-io-gcs-filesystem==0.37.1
tensorflow-metal==1.1.0
termcolor==2.5.0
text-unidecode==1.3
tf_keras==2.16.0
threadpoolctl==3.6.0
tifffile==2025.3.30
timm==1.0.15
tokenizers==0.21.1
tomli @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_d0e5ffbf-5cf1-45be-8693-c5dff8108a2awhthtjlq/croots/recipe/tomli_1657175508477/work
tomlkit==0.13.2
torch==2.6.0
torchaudio==2.6.0
torchvision==0.21.0
tornado @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_0axef5a0m0/croot/tornado_1733960501260/work
tqdm @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_5642tzrprr/croot/tqdm_1738943467095/work
traitlets==5.14.3
transformers==4.51.3
typer==0.15.3
typer-slim==0.15.3
types-python-dateutil==2.9.0.20241206
types-pytz==2025.2.0.20250326
types-requests==2.32.0.20250328
types-setuptools==80.0.0.20250429
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata @ file:///croot/python-tzdata_1690578112552/work
ukkonen @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_5cm70exp3d/croot/ukkonen_1736541927576/work
ultralytics==8.3.120
ultralytics-thop==2.0.14
unicodedata2 @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_b99hn8t9lq/croot/unicodedata2_1736541774279/work
uritemplate==4.1.1
urllib3==2.4.0
uvicorn @ file:///home/<USER>/feedstock_root/build_artifacts/uvicorn_1745173431065/work
uvloop @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_1539mh1ss3/croot/uvloop_1732545180417/work
virtualenv @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_e03g4e9gx7/croot/virtualenv_1733440914292/work
watchfiles @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_e8ok395flg/croot/watchfiles_1732647865032/work
wcwidth==0.2.13
websockets @ file:///Users/<USER>/miniforge3/conda-bld/websockets_1741285557173/work
Werkzeug==3.1.3
wrapt==1.17.2
zipp==3.21.0
