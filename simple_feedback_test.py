#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psycopg2
import uuid
from datetime import datetime
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接参数
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': os.getenv('DB_PORT', '5432'),
    'database': os.getenv('DB_NAME', 'emotion_ai'),
    'user': os.getenv('DB_USER', 'dom'),
    'password': os.getenv('DB_PASSWORD', '')
}

def test_feedback_with_sql():
    """使用直接SQL语句测试反馈功能"""
    
    try:
        # 连接数据库
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("=== 使用SQL直接测试反馈功能 ===")
        
        # 1. 获取一个有效的emotion_analysis记录
        print("\n1. 查找有效的emotion_analysis记录...")
        
        cursor.execute("""
            SELECT id, user_id, emotion, confidence 
            FROM emotion_analysis 
            WHERE deleted_at IS NULL 
            AND user_id IS NOT NULL
            ORDER BY created_at DESC 
            LIMIT 1;
        """)
        
        analysis_record = cursor.fetchone()
        if not analysis_record:
            print("❌ 没有找到有效的emotion_analysis记录")
            return
        
        analysis_id, user_id, emotion, confidence = analysis_record
        print(f"✅ 找到分析记录: ID={analysis_id}, User={user_id}, Emotion={emotion}")
        
        # 2. 直接插入反馈记录
        print("\n2. 直接插入反馈记录...")
        
        feedback_id = str(uuid.uuid4())
        feedback_emotion = "happy"
        feedback_comment = "测试反馈 - 使用SQL直接插入"
        created_at = datetime.now()
        
        cursor.execute("""
            INSERT INTO emotion_feedback (
                id, analysis_id, emotion, comment, created_by, created_at
            ) VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            feedback_id,
            analysis_id,
            feedback_emotion,
            feedback_comment,
            user_id,
            created_at
        ))
        
        conn.commit()
        print(f"✅ 反馈记录插入成功，ID: {feedback_id}")
        
        # 3. 验证插入的记录
        print("\n3. 验证插入的反馈记录...")
        
        cursor.execute("""
            SELECT id, analysis_id, emotion, comment, created_by, created_at
            FROM emotion_feedback 
            WHERE id = %s;
        """, (feedback_id,))
        
        feedback_record = cursor.fetchone()
        if feedback_record:
            print("✅ 在数据库中找到反馈记录:")
            print(f"  ID: {feedback_record[0]}")
            print(f"  Analysis ID: {feedback_record[1]}")
            print(f"  Emotion: {feedback_record[2]}")
            print(f"  Comment: {feedback_record[3]}")
            print(f"  Created By: {feedback_record[4]}")
            print(f"  Created At: {feedback_record[5]}")
        else:
            print("❌ 在数据库中没有找到反馈记录")
        
        # 4. 查询该分析记录的所有反馈
        print("\n4. 查询该分析记录的所有反馈...")
        
        cursor.execute("""
            SELECT id, emotion, comment, created_at
            FROM emotion_feedback 
            WHERE analysis_id = %s
            ORDER BY created_at DESC;
        """, (analysis_id,))
        
        all_feedbacks = cursor.fetchall()
        print(f"✅ 找到 {len(all_feedbacks)} 条反馈记录:")
        for i, feedback in enumerate(all_feedbacks):
            print(f"  反馈 #{i+1}:")
            print(f"    ID: {feedback[0]}")
            print(f"    Emotion: {feedback[1]}")
            print(f"    Comment: {feedback[2]}")
            print(f"    Created At: {feedback[3]}")
        
        # 5. 检查emotion_feedback表的总记录数
        print("\n5. 检查emotion_feedback表的总记录数...")
        
        cursor.execute("SELECT COUNT(*) FROM emotion_feedback;")
        total_count = cursor.fetchone()[0]
        print(f"✅ emotion_feedback表总记录数: {total_count}")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 反馈功能测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_feedback_with_sql() 