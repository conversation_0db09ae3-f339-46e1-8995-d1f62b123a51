#!/usr/bin/env python3
"""
测试反馈功能的完整流程
"""
import asyncio
import sys
import uuid
from datetime import datetime

sys.path.append('backend')

from backend.emotionai.core.database.session import get_async_db
from backend.emotionai.models.ml.emotion import EmotionAnalysis
from backend.emotionai.models.ml.emotion_feedback import EmotionFeedback
from backend.emotionai.services.emotion_service import EmotionService
from sqlalchemy import select, desc, text

async def test_feedback_functionality():
    """测试反馈功能的完整流程"""
    
    async for db in get_async_db():
        print("=== 测试反馈功能完整流程 ===")
        
        # 1. 获取一个现有的情绪分析记录
        print("\n1. 获取现有的情绪分析记录...")
        result = await db.execute(
            text("SELECT id, user_id, emotion FROM emotion_analysis WHERE deleted_at IS NULL ORDER BY created_at DESC LIMIT 1")
        )
        row = result.fetchone()
        
        if not row:
            print("❌ 没有找到情绪分析记录，请先进行情绪分析")
            return
        
        analysis_id = str(row[0])
        user_id = str(row[1])
        emotion = row[2]
        
        print(f"✅ 找到分析记录: ID={analysis_id[:8]}..., 用户={user_id[:8]}..., 情绪={emotion}")
        
        # 2. 测试保存反馈功能
        print("\n2. 测试保存反馈功能...")
        
        emotion_service = EmotionService()
        
        # 测试保存反馈
        feedback_result = await emotion_service.save_feedback(
            analysis_id=analysis_id,
            user_id=user_id,
            emotion="happy",
            comment="测试反馈 - 这个分析结果很准确！",
            db=db
        )
        
        if feedback_result.get("success"):
            print(f"✅ 反馈保存成功: ID={feedback_result['feedback_id'][:8]}...")
        else:
            print(f"❌ 反馈保存失败: {feedback_result}")
            return
        
        # 3. 测试获取反馈历史
        print("\n3. 测试获取反馈历史...")
        
        history_result = await emotion_service.get_feedback_history(
            analysis_id=analysis_id,
            user_id=user_id,
            db=db
        )
        
        print(f"✅ 反馈历史获取成功，共 {len(history_result)} 条记录:")
        for i, feedback in enumerate(history_result, 1):
            comment = feedback['comment'][:30] + '...' if feedback['comment'] and len(feedback['comment']) > 30 else feedback['comment'] or '无评论'
            print(f"  {i}. 情绪: {feedback['emotion']}, 评论: {comment}, 时间: {feedback['created_at']}")
        
        # 4. 直接查询数据库验证
        print("\n4. 直接查询数据库验证...")
        
        result = await db.execute(
            text("SELECT COUNT(*) FROM emotion_feedback WHERE analysis_id = :analysis_id"),
            {"analysis_id": analysis_id}
        )
        count = result.scalar()
        print(f"✅ 数据库中该分析记录的反馈数量: {count}")
        
        # 5. 测试不同类型的反馈
        print("\n5. 测试不同类型的反馈...")
        
        test_feedbacks = [
            {"emotion": "neutral", "comment": "一般般的分析结果"},
            {"emotion": "sad", "comment": "分析结果不太准确"},
            {"emotion": "happy", "comment": ""},  # 空评论
        ]
        
        for i, feedback_data in enumerate(test_feedbacks, 1):
            print(f"  测试反馈 {i}: {feedback_data['emotion']}")
            
            result = await emotion_service.save_feedback(
                analysis_id=analysis_id,
                user_id=user_id,
                emotion=feedback_data["emotion"],
                comment=feedback_data["comment"],
                db=db
            )
            
            if result.get("success"):
                print(f"    ✅ 反馈 {i} 保存成功")
            else:
                print(f"    ❌ 反馈 {i} 保存失败: {result}")
        
        # 6. 最终验证
        print("\n6. 最终验证...")
        
        final_history = await emotion_service.get_feedback_history(
            analysis_id=analysis_id,
            user_id=user_id,
            db=db
        )
        
        print(f"✅ 最终反馈历史，共 {len(final_history)} 条记录:")
        for i, feedback in enumerate(final_history, 1):
            comment = feedback['comment'][:30] + '...' if feedback['comment'] and len(feedback['comment']) > 30 else feedback['comment'] or '无评论'
            print(f"  {i}. 情绪: {feedback['emotion']}, 评论: {comment}")
        
        # 7. 测试前端数据格式
        print("\n7. 测试前端数据格式...")
        
        # 模拟前端发送的数据格式
        frontend_data = {
            "analysis_id": analysis_id,
            "emotion": "happy",  # 前端映射: good -> happy
            "comment": "前端测试反馈"
        }
        
        result = await emotion_service.save_feedback(
            analysis_id=frontend_data["analysis_id"],
            user_id=user_id,
            emotion=frontend_data["emotion"],
            comment=frontend_data["comment"],
            db=db
        )
        
        if result.get("success"):
            print("✅ 前端数据格式测试通过")
        else:
            print(f"❌ 前端数据格式测试失败: {result}")
        
        print("\n=== 反馈功能测试完成 ===")
        print("✅ 所有测试都通过")
        print("✅ 用户输入内容能够正确保存到数据库")
        print("✅ 反馈功能工作正常")
        
        break

if __name__ == "__main__":
    asyncio.run(test_feedback_functionality()) 