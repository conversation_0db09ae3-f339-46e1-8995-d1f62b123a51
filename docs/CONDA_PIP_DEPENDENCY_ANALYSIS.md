# Conda + Pip 混合依赖管理分析

## 📋 概述

本项目使用 **conda + pip 混合依赖管理**策略：
- **Conda包**: 100个
- **Pip包**: 148个
- **总计**: 248个

## 🔍 依赖来源分析

### 通过 Conda 安装的包

| 包名 | 版本 | 渠道 | 说明 |
|------|------|------|------|
| aiofiles | 22.1.0 |  |  |
| alembic | 1.15.2 | conda-forge | 数据库迁移 |
| annotated-types | 0.6.0 |  |  |
| anyio | 4.7.0 |  |  |
| async-timeout | 5.0.1 |  |  |
| asyncpg | 0.30.0 | conda-forge | 异步PostgreSQL驱动 |
| black | 24.3.0 | tuna镜像 | 代码格式化 |
| blas | 1.0 |  |  |
| bottleneck | 1.4.2 |  |  |
| brotli-python | 1.0.9 |  |  |
| bzip2 | 1.0.8 |  |  |
| ca-certificates | 2025.2.25 |  |  |
| certifi | 2025.1.31 |  |  |
| cffi | 1.17.1 |  |  |
| cfgv | 3.4.0 |  |  |
| click | 8.1.8 |  |  |
| contourpy | 1.3.1 |  |  |
| cryptography | 44.0.1 |  |  |
| cycler | 0.11.0 |  |  |
| distlib | 0.3.8 |  |  |
| dnspython | 2.4.2 |  |  |
| email-validator | 2.2.0 |  |  |
| email_validator | 2.2.0 |  |  |
| exceptiongroup | 1.2.0 |  |  |
| fastapi | 0.115.12 | conda-forge | Web框架 |
| fastapi-cli | 0.0.7 |  |  |
| filelock | 3.17.0 |  |  |
| fonttools | 4.55.3 |  |  |
| freetype | 2.13.3 |  |  |
| greenlet | 3.1.1 |  |  |
| h11 | 0.14.0 |  |  |
| httpcore | 1.0.2 |  |  |
| httptools | 0.6.4 |  |  |
| httpx | 0.27.0 |  |  |
| icu | 75.1 |  |  |
| identify | 2.5.5 |  |  |
| idna | 3.7 |  |  |
| iniconfig | 1.1.1 |  |  |
| isort | 6.0.1 | tuna镜像 | 导入排序 |
| jinja2 | 3.1.6 |  |  |
| kiwisolver | 1.4.8 |  |  |
| lcms2 | 2.17 |  |  |
| lerc | 4.0.0 |  |  |
| libcxx | 20.1.3 |  |  |
| libdeflate | 1.23 |  |  |
| libexpat | 2.7.0 |  |  |
| libffi | 3.4.4 |  |  |
| libfreetype | 2.13.3 |  |  |
| libfreetype6 | 2.13.3 |  |  |
| libgfortran | 5.0.0 |  |  |
| libgfortran5 | 14.2.0 |  |  |
| libjpeg-turbo | 3.0.3 | tuna镜像 | JPEG处理库 |
| liblzma | 5.8.1 |  |  |
| libopenblas | 0.3.27 | conda-forge | OpenBLAS库 |
| libpng | 1.6.47 |  |  |
| libsqlite | 3.49.1 |  |  |
| libtiff | 4.7.0 |  |  |
| libuv | 1.50.0 |  |  |
| libwebp-base | 1.5.0 |  |  |
| libxcb | 1.17.0 |  |  |
| libzlib | 1.3.1 |  |  |
| llvm-openmp | 20.1.3 |  |  |
| mako | 1.2.3 |  |  |
| markupsafe | 3.0.2 |  |  |
| mccabe | 0.7.0 |  |  |
| mypy | 1.9.0 | conda-forge | 类型检查 |
| mypy_extensions | 1.0.0 |  |  |
| ncurses | 6.5 |  |  |
| nodeenv | 1.9.1 |  |  |
| nodejs | 22.13.0 | conda-forge | Node.js运行时 |
| numexpr | 2.10.1 |  |  |
| openblas | 0.3.27 | conda-forge | 线性代数库 |
| openjpeg | 2.5.3 |  |  |
| openssl | 3.5.0 |  |  |
| packaging | 24.2 |  |  |
| pathspec | 0.10.3 |  |  |
| platformdirs | 4.3.7 |  |  |
| pluggy | 1.5.0 |  |  |
| pre-commit | 4.2.0 |  |  |
| pthread-stubs | 0.3 |  |  |
| pycparser | 2.21 |  |  |
| pygments | 2.19.1 |  |  |
| pyparsing | 3.2.0 |  |  |
| python | 3.10.16 | conda-forge | Python解释器 |
| python-dateutil | 2.9.0post0 |  |  |
| python-dotenv | 1.1.0 | tuna镜像 | 环境变量 |
| python-multipart | 0.0.20 | tuna镜像 | 文件上传 |
| python-tzdata | 2023.3 |  |  |
| python_abi | 3.10 |  |  |
| pytz | 2024.1 |  |  |
| pyyaml | 6.0.2 |  |  |
| readline | 8.2 |  |  |
| rich | 13.9.4 |  |  |
| ruff | 0.11.7 | conda-forge | 代码检查 |
| scipy | 1.15.2 | tuna镜像 | 科学计算 |
| setuptools | 75.8.0 |  |  |
| shellingham | 1.5.0 |  |  |
| sniffio | 1.3.0 |  |  |
| starlette | 0.46.2 |  |  |
| tk | 8.6.13 |  |  |
| tomli | 2.0.1 |  |  |
| tornado | 6.4.2 |  |  |
| tqdm | 4.67.1 |  |  |
| typer | 0.15.3 |  |  |
| typer-slim | 0.15.3 |  |  |
| typer-slim-standard | 0.15.3 |  |  |
| typing-inspection | 0.4.0 |  |  |
| tzdata | 2025a |  |  |
| ukkonen | 1.0.1 |  |  |
| unicodedata2 | 15.1.0 |  |  |
| uvicorn | 0.34.2 | conda-forge | ASGI服务器 |
| uvicorn-standard | 0.34.2 | conda-forge | Uvicorn标准版 |
| uvloop | 0.21.0 |  |  |
| virtualenv | 20.28.0 |  |  |
| watchfiles | 0.24.0 |  |  |
| websockets | 15.0.1 |  |  |
| wheel | 0.45.1 |  |  |
| xorg-libxau | 1.0.12 |  |  |
| xorg-libxdmcp | 1.1.5 |  |  |
| yaml | 0.2.5 |  |  |
| zlib | 1.3.1 |  |  |
| zstd | 1.5.7 |  |  |

### 通过 Pip 安装的包

| 包名 | 版本 | 说明 |
|------|------|------|
| absl-py | 2.2.2 |  |
| aiohappyeyeballs | 2.6.1 |  |
| aiohttp | 3.12.2 |  |
| aiosignal | 1.3.2 |  |
| aiosmtplib | 3.0.2 |  |
| albumentations | 1.3.1 | 图像增强 |
| argcomplete | 3.5.3 |  |
| arrow | 1.3.0 |  |
| asttokens | 3.0.0 |  |
| astunparse | 1.6.3 |  |
| attrs | 25.3.0 |  |
| bandit | 1.7.5 |  |
| bcrypt | 4.3.0 |  |
| beautifulsoup4 | 4.13.4 |  |
| binaryornot | 0.4.4 |  |
| blinker | 1.9.0 |  |
| build | 1.2.2.post1 |  |
| cachetools | 5.5.2 |  |
| chardet | 5.2.0 |  |
| charset-normalizer | 3.4.1 |  |
| code2flow | 2.5.1 |  |
| colorama | 0.4.6 |  |
| coloredlogs | 15.0.1 |  |
| commitizen | 4.6.0 |  |
| cookiecutter | 2.6.0 |  |
| copier | 9.7.1 |  |
| cython | 3.0.12 |  |
| decli | 0.6.2 |  |
| decorator | 5.2.1 |  |
| deepface | 0.0.93 | 人脸分析 |
| deprecated | 1.2.18 |  |
| diffusers | 0.32.2 |  |
| dunamai | 1.23.1 |  |
| easydict | 1.13 |  |
| ecdsa | 0.19.1 |  |
| emotionai | 0.1.0 |  |
| executing | 2.2.0 |  |
| fastapi-mail | 1.5.0 |  |
| fire | 0.7.0 |  |
| flake8 | 7.2.0 |  |
| flask | 3.1.0 |  |
| flask-cors | 5.0.1 |  |
| flatbuffers | 25.2.10 |  |
| frozenlist | 1.6.0 |  |
| fsspec | 2025.3.2 |  |
| funcy | 2.0 |  |
| gast | 0.4.0 |  |
| gdown | 5.2.0 |  |
| gitdb | 4.0.12 |  |
| gitpython | 3.1.44 |  |
| google-ai-generativelanguage | 0.6.15 |  |
| google-api-core | 2.25.0rc1 |  |
| google-api-python-client | 2.169.0 |  |
| google-auth | 2.40.1 |  |
| google-auth-httplib2 | 0.2.0 |  |
| google-auth-oauthlib | 1.0.0 |  |
| google-generativeai | 0.8.5 |  |
| google-pasta | 0.2.0 |  |
| googleapis-common-protos | 1.70.0 |  |
| grpcio | 1.71.0 |  |
| gunicorn | 23.0.0 |  |
| h5py | 3.13.0 |  |
| httplib2 | 0.22.0 |  |
| huggingface-hub | 0.30.2 |  |
| humanfriendly | 10.0 |  |
| imageio | 2.37.0 |  |
| importlib-metadata | 8.7.0 |  |
| insightface | 0.7.3 |  |
| itsdangerous | 2.2.0 |  |
| jax | 0.4.34 |  |
| jaxlib | 0.4.34 |  |
| jedi | 0.19.2 |  |
| jinja2-ansible-filters | 1.3.2 |  |
| joblib | 1.4.2 |  |
| jsonpickle | 4.0.5 |  |
| keras | 3.9.2 |  |
| lazy-loader | 0.4 |  |
| libclang | 18.1.1 |  |
| limits | 5.1.0 |  |
| lz4 | 4.4.4 |  |
| markdown | 3.8 |  |
| markdown-it-py | 3.0.0 |  |
| matplotlib | 3.10.1 |  |
| matplotlib-inline | 0.1.7 |  |
| mdurl | 0.1.2 |  |
| mediapipe | 0.10.21 | 多媒体处理 |
| ml-dtypes | 0.3.2 |  |
| mpmath | 1.3.0 |  |
| mtcnn | 1.0.0 |  |
| multidict | 6.4.4 |  |
| namex | 0.0.9 |  |
| networkx | 3.4.2 |  |
| numpy | 1.26.4 | 数值计算 |
| oauthlib | 3.2.2 |  |
| onnx | 1.17.0 |  |
| onnxruntime | 1.21.1 |  |
| opencv-python-headless | ********* | OpenCV无头版 |
| opt-einsum | 3.4.0 |  |
| optree | 0.15.0 |  |
| pandas | 2.0.3 |  |
| parso | 0.8.4 |  |
| passlib | 1.7.4 |  |
| pbr | 6.1.1 |  |
| pexpect | 4.9.0 |  |
| pgvector | 0.4.1 |  |
| pillow | 11.2.1 |  |
| pip | 25.1.1 | Python包管理器 |
| pip-tools | 7.4.1 |  |
| pipdeptree | 2.26.1 |  |
| plumbum | 1.9.0 |  |
| prettytable | 3.16.0 |  |
| prometheus-client | 0.21.1 |  |
| prometheus-fastapi-instrumentator | 7.1.0 |  |
| prompt-toolkit | 3.0.51 |  |
| propcache | 0.3.1 |  |
| proto-plus | 1.26.1 |  |
| protobuf | 4.25.7 |  |
| psutil | 7.0.0 |  |
| psycopg2-binary | 2.9.10 |  |
| ptyprocess | 0.7.0 |  |
| pure-eval | 0.2.3 |  |
| py-cpuinfo | 9.0.0 |  |
| pyan3 | 1.2.0 |  |
| pyasn1 | 0.6.1 |  |
| pyasn1-modules | 0.4.2 |  |
| pycodestyle | 2.13.0 |  |
| pydantic | 2.11.4 | 数据验证 |
| pydantic-core | 2.33.2 |  |
| pydantic-settings | 2.9.1 | 配置管理 |
| pyflakes | 3.3.2 |  |
| pyjwt | 2.10.1 |  |
| pyproject-hooks | 1.2.0 |  |
| pysocks | 1.7.1 |  |
| pytest | 8.3.5 |  |
| python-jose | 3.4.0 |  |
| python-slugify | 8.0.4 |  |
| pyvis | 0.3.2 |  |
| qudida | 0.0.4 |  |
| questionary | 2.1.0 |  |
| redis | 6.0.0 | 缓存数据库 |
| redislite | 6.2.912183 |  |
| regex | 2024.11.6 |  |
| requests | 2.32.3 |  |
| requests-oauthlib | 2.0.0 |  |
| retina-face | 0.0.17 |  |
| rich-toolkit | 0.14.6 |  |
| rsa | 4.9.1 |  |
| safetensors | 0.5.3 |  |
| scikit-image | 0.25.2 |  |
| scikit-learn | 1.6.1 |  |
| seaborn | 0.13.2 |  |
| sentencepiece | 0.2.0 |  |
| simsimd | 6.2.1 |  |
| six | 1.15.0 |  |
| slowapi | 0.1.9 |  |
| smmap | 5.0.2 |  |
| sounddevice | 0.5.1 |  |
| soundfile | 0.13.1 |  |
| soupsieve | 2.7 |  |
| sqlalchemy | 2.0.41 | ORM框架 |
| stack-data | 0.6.3 |  |
| stevedore | 5.4.1 |  |
| stringzilla | 3.12.5 |  |
| sympy | 1.13.1 |  |
| tensorboard | 2.16.2 |  |
| tensorboard-data-server | 0.7.2 |  |
| tensorboard-plugin-wit | 1.8.1 |  |
| tensorflow | 2.16.2 |  |
| tensorflow-estimator | 2.13.0 |  |
| tensorflow-io-gcs-filesystem | 0.37.1 |  |
| tensorflow-metal | 1.1.0 |  |
| termcolor | 2.5.0 |  |
| text-unidecode | 1.3 |  |
| tf-keras | 2.16.0 |  |
| threadpoolctl | 3.6.0 |  |
| tifffile | 2025.3.30 |  |
| timm | 1.0.15 |  |
| tokenizers | 0.21.1 |  |
| tomlkit | 0.13.2 |  |
| torch | 2.6.0 | PyTorch深度学习框架 |
| torchaudio | 2.6.0 | 音频处理 |
| torchvision | 0.21.0 | 计算机视觉 |
| traitlets | 5.14.3 |  |
| transformers | 4.51.3 | Transformer模型 |
| types-python-dateutil | 2.9.0.20241206 |  |
| types-pytz | 2025.2.0.20250326 |  |
| types-requests | 2.32.0.20250328 |  |
| types-setuptools | 80.0.0.20250429 |  |
| typing-extensions | 4.13.2 |  |
| ultralytics | 8.3.120 | YOLO目标检测 |
| ultralytics-thop | 2.0.14 |  |
| uritemplate | 4.1.1 |  |
| urllib3 | 2.4.0 |  |
| wcwidth | 0.2.13 |  |
| werkzeug | 3.1.3 |  |
| wrapt | 1.17.2 |  |
| yarl | 1.20.0 |  |
| zipp | 3.21.0 |  |

## 🎯 混合管理策略分析

### ✅ 优势
1. **性能优化**: Conda管理系统级依赖，确保最佳性能
2. **依赖解析**: Conda处理复杂的C/C++库依赖关系
3. **环境隔离**: 完整的环境管理，包括Python版本
4. **专业库支持**: Pip安装最新的深度学习库

### ⚠️ 潜在问题
1. **版本冲突**: 同一包可能同时存在conda和pip版本
2. **依赖重复**: 某些包可能被重复安装
3. **更新复杂**: 需要同时维护两套依赖文件

### 🔍 发现的问题

#### 1. OpenCV版本冲突
```bash
opencv-python         ********     # pip安装 (旧状态，当前已由opencv-python-headless统一)
opencv-contrib-python *********    # pip安装 (旧状态)
opencv-python-headless *********   # pip安装 (当前统一版本)
```
**建议**: 统一使用 `opencv-python-headless==*********` (已在当前Pip列表体现)

#### 2. 依赖来源不一致
- `fastapi`: conda安装 (0.115.12)
- `pydantic`: pip安装 (2.11.4)
- `uvicorn`: conda安装 (0.34.2)

#### 3. environmentMACOS.yml 与实际环境不符
- 配置文件: `pydantic-settings=2.2.1` (旧状态)
- 实际环境: `pydantic-settings=2.9.1` (pip, 当前状态)

## 🛠️ 建议的优化方案

### 1. 更新 environmentMACOS.yml
```yaml
name: ema-macos-m4

channels:
  - conda-forge
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
  - defaults

dependencies:
  # 基础环境 (conda管理)
  - python=3.10.16
  - pip>=25.1.0 # pip本身通过conda管理，其安装的包通过pip list查看
  - nodejs=22.13.0
  
  # 系统库 (conda管理)
  - openblas=0.3.27
  - libjpeg-turbo=3.0.3
  - libopenblas=0.3.27
  
  # Web框架核心 (conda管理)
  - fastapi=0.115.12
  - uvicorn=0.34.2 # uvicorn-standard 通常包含在uvicorn中或通过其依赖解决
  
  # 数据库核心 (conda管理)
  - asyncpg=0.30.0
  - alembic=1.15.2
  
  # 开发工具 (conda管理)
  - black=24.3.0
  - isort=6.0.1
  - mypy=1.9.0
  - ruff=0.11.7
  
  # 工具库 (conda管理)
  - python-dotenv=1.1.0
  - python-multipart=0.0.20
  - scipy=1.15.2
  
  # pip依赖 (通过 conda env export -n ema-macos-m4 --no-builds | grep -A 1000 "pip:" 获取)
  # 注意：以下列表应与上方通过Pip安装的包列表同步，或直接引用 requirements 文件
  - pip:
    # 示例，实际应从conda env export的pip部分获取或指向requirements文件
    - absl-py==2.2.2
    - albumentations==1.3.1
    # ... (此处仅为示例，实际列表很长)
    - torch==2.6.0
    - transformers==4.51.3
    - pydantic==2.11.4
    - pydantic-settings==2.9.1
    # ... 其他 pip 包
```

### 2. 清理 requirements-core.txt
移除已由conda管理的包：
```txt
# 移除这些包 (已由conda管理)
# fastapi==0.115.12
# uvicorn[standard]==0.34.2  
# asyncpg==0.30.0
# alembic==1.15.2
# python-dotenv>=1.0.1
# python-multipart>=0.0.20
# scipy>=1.15.2 

# 保留这些包 (由pip管理)
pydantic==2.11.4
pydantic-settings==2.9.1
sqlalchemy==2.0.41
torch==2.6.0
transformers==4.51.3
mediapipe==0.10.21
opencv-python-headless==*********
# ... 其他深度学习库和pip管理的包
```

### 3. 统一OpenCV版本
```txt
# 只保留一个OpenCV版本 (已在上方Pip列表体现)
opencv-python-headless==*********
```

## 📝 部署建议

### 开发环境设置
```bash
# 1. 创建conda环境 (如果 environmentMACOS.yml 已更新并包含pip部分)
conda env create -f environmentMACOS.yml

# 或者分步：
# conda env create -f environmentMACOS_conda_only.yml 
# conda activate ema-macos-m4
# pip install -r requirements_pip_only.txt

# 2. 激活环境
conda activate ema-macos-m4

# 3. 验证安装
python -c "import torch, transformers, mediapipe, fastapi, pydantic; print('Key packages imported successfully')"
```

### Docker环境
```dockerfile
# 使用conda基础镜像
FROM continuumio/miniconda3:latest

# 复制环境文件
COPY environmentMACOS.yml .
# COPY requirements-core.txt . # 如果 environmentMACOS.yml 的 pip 部分引用了它

# 创建环境
RUN conda env create -f environmentMACOS.yml

# 如果 environmentMACOS.yml 的 pip 部分没有列出所有包，而是引用了 requirements 文件:
# RUN conda run -n ema-macos-m4 pip install -r requirements-core.txt

# 设置默认激活环境的shell
SHELL ["conda", "run", "-n", "ema-macos-m4", "/bin/bash", "-c"]

# 验证
RUN python -c "import torch; print('PyTorch version:', torch.__version__)"
```

## 🔄 迁移计划

1. **阶段1**: 基于 `conda env export` 更新本文档中的依赖列表。
2. **阶段2**: 仔细审查 `environmentMACOS.yml`，确保其 `dependencies` 部分准确反映Conda管理的包，其 `pip:` 部分准确反映Pip管理的包（或指向一个干净的 `requirements.txt`）。
3. **阶段3**: 清理所有 `requirements-*.txt` 文件，移除由Conda管理的包，确保只有一个权威的Pip依赖列表。
4. **阶段4**: 统一OpenCV版本（已通过仅保留 `opencv-python-headless` 实现）。
5. **阶段5**: 测试完整环境，确保功能正常。
6. **阶段6**: 更新Docker配置，以反映新的依赖管理策略。

## ⚠️ 注意事项

1. **不要混合安装**: 同一个包不要同时用conda和pip安装。如果必须，优先conda。
2. **优先级**: Conda环境解析时，conda包优先。
3. **环境重建**: 重大依赖更改后建议重新创建环境 (`conda env remove -n ema-macos-m4` 后再 `create`)。
4. **版本锁定**: 生产环境和共享开发环境应使用精确版本号。
5. **定期清理**: 定期使用 `conda list` 和 `pip list` 检查，并对比 `environmentMACOS.yml` 和 `requirements.txt`，清理重复或冲突依赖。