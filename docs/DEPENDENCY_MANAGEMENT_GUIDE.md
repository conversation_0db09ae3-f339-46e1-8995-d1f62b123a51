# 依赖管理指南

## 📋 概述

本项目采用 **Conda + Pip 混合依赖管理**策略，确保系统级依赖的稳定性和深度学习库的最新性。

## 🏗️ 架构设计

### 依赖分层策略

```
┌─────────────────────────────────────┐
│           Conda 管理层              │
├─────────────────────────────────────┤
│ • 基础环境 (Python, Node.js)       │
│ • 系统库 (OpenBLAS, JPEG)          │
│ • Web框架 (FastAPI, Uvicorn)       │
│ • 数据库核心 (AsyncPG, Alembic)    │
│ • 开发工具 (Black, MyPy, Ruff)     │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│            Pip 管理层               │
├─────────────────────────────────────┤
│ • 深度学习 (PyTorch, Transformers) │
│ • 计算机视觉 (MediaPipe, OpenCV)   │
│ • 数据验证 (Pydantic)              │
│ • 专业库 (DeepFace, Ultralytics)   │
└─────────────────────────────────────┘
```

## 📁 文件结构

```
ema2_20250417/
├── environmentMACOS.yml          # Conda环境配置
├── requirements-core.txt         # Pip核心依赖
├── requirements-dev.txt          # 开发环境依赖
├── requirements-production.txt   # 生产环境依赖
├── requirements-vector.txt       # 向量数据库依赖
└── scripts/
    └── cleanup_opencv.py         # OpenCV版本清理脚本
```

## 🔧 环境设置

### 1. 创建Conda环境

```bash
# 创建环境
conda env create -f environmentMACOS.yml

# 激活环境
conda activate ema-macos-m4

# 验证安装
python -c "import torch, transformers, mediapipe; print('✅ 环境设置成功')"
```

### 2. 解决OpenCV冲突

```bash
# 运行清理脚本
python scripts/cleanup_opencv.py

# 手动清理（如果脚本失败）
pip uninstall opencv-python opencv-contrib-python opencv-python-headless -y
pip install opencv-python-headless==*********
```

### 3. 验证环境

```bash
# 检查关键包版本
python -c "
import torch, transformers, mediapipe, cv2, fastapi
print(f'PyTorch: {torch.__version__}')
print(f'Transformers: {transformers.__version__}')
print(f'MediaPipe: {mediapipe.__version__}')
print(f'OpenCV: {cv2.__version__}')
print(f'FastAPI: {fastapi.__version__}')
"
```

## 📦 依赖管理规则

### Conda管理的包

| 类别 | 包名 | 版本 | 原因 |
|------|------|------|------|
| **基础环境** | python | 3.10.16 | 环境基础 |
| **系统库** | openblas | 0.3.27 | 性能优化 |
| **Web框架** | fastapi | 0.115.12 | 稳定性 |
| **数据库** | asyncpg | 0.30.0 | 兼容性 |
| **开发工具** | black, mypy, ruff | 固定版本 | 一致性 |

### Pip管理的包

| 类别 | 包名 | 版本 | 原因 |
|------|------|------|------|
| **深度学习** | torch | 2.6.0 | 最新特性 |
| **计算机视觉** | mediapipe | 0.10.21 | 专业功能 |
| **数据验证** | pydantic | 2.11.4 | 快速更新 |
| **AI模型** | transformers | 4.51.3 | 模型支持 |

## 🚨 常见问题解决

### 1. OpenCV版本冲突

**问题**: 多个OpenCV版本导致导入错误
```bash
ImportError: cannot import name 'gapi_wip_gst_GStreamerPipeline' from 'cv2'
```

**解决方案**:
```bash
python scripts/cleanup_opencv.py
```

### 2. Conda vs Pip版本不一致

**问题**: 同一包在conda和pip中版本不同
```bash
# 检查包来源
conda list | grep package_name
pip show package_name
```

**解决方案**:
```bash
# 优先使用conda版本
conda install package_name=version
# 或移除pip版本
pip uninstall package_name
```

### 3. Apple Silicon兼容性

**问题**: TensorFlow在M1/M2芯片上运行缓慢

**解决方案**:
```bash
# 确保安装Apple Silicon优化版本
pip install tensorflow-macos==2.16.2
pip install tensorflow-metal==1.1.0
```

## 🔄 更新策略

### 定期更新流程

1. **备份当前环境**
```bash
conda env export > environment_backup.yml
pip freeze > requirements_backup.txt
```

2. **更新conda包**
```bash
conda update --all
```

3. **更新pip包**
```bash
pip list --outdated
pip install --upgrade package_name
```

4. **测试功能**
```bash
python -m pytest tests/
```

5. **更新配置文件**
```bash
conda env export > environmentMACOS.yml
pip freeze > requirements-current-snapshot.txt
```

## 🐳 Docker支持

### 多阶段构建

```dockerfile
# 开发环境
FROM continuumio/miniconda3:latest as development
COPY environmentMACOS.yml requirements-core.txt ./
RUN conda env create -f environmentMACOS.yml
RUN conda run -n ema-macos-m4 pip install -r requirements-core.txt

# 生产环境
FROM continuumio/miniconda3:latest as production
COPY environmentMACOS.yml requirements-production.txt ./
RUN conda env create -f environmentMACOS.yml
RUN conda run -n ema-macos-m4 pip install -r requirements-production.txt
```

## 📊 性能监控

### 依赖大小分析

```bash
# 查看包大小
pip list --format=freeze | xargs pip show | grep -E "Name|Size"

# 查看conda包大小
conda list --show-channel-urls
```

### 启动时间优化

```bash
# 分析导入时间
python -X importtime -c "import torch, transformers"

# 优化建议
# 1. 延迟导入非关键包
# 2. 使用conda管理系统级依赖
# 3. 定期清理未使用的包
```

## ⚠️ 最佳实践

1. **不要混合安装**: 同一包不要同时用conda和pip安装
2. **优先级顺序**: conda > pip > 源码编译
3. **版本锁定**: 生产环境使用精确版本号
4. **定期清理**: 移除未使用的依赖
5. **环境隔离**: 不同项目使用不同环境
6. **备份策略**: 定期备份工作环境
7. **测试验证**: 更新后充分测试功能

## 📝 维护日志

- **2025-01-17**: 初始版本，建立混合依赖管理策略
- **版本锁定**: 确保与当前运行环境完全一致
- **OpenCV清理**: 解决多版本冲突问题
- **Apple Silicon**: 优化M1/M2芯片兼容性 