{
  // 项目基本信息
  "name": "ema-frontend",
  "type": "module", // 使用ES模块规范
  "private": true, // 私有包，不发布到npm

  // 脚本命令
  "scripts": {
    "dev": "vite", // 启动开发服务器
    "build": "vite build", // 生产环境构建
    "preview": "vite preview", // 预览生产构建
    "type-check": "tsc --noEmit" // 类型检查
  },

  // 生产依赖
  "dependencies": {
    // UI组件库
    "@ant-design/icons": "^5.6.1", // Ant Design图标库
    "@tanstack/react-query": "^5.66.0", // 数据请求管理
    "react": "18.3.1",
    "react-dom": "18.3.1",
    "@use-gesture/react": "^10.3.1", // 手势交互
    "react-helmet-async": "2.0.5",
    "antd": "^5.23.4", // Ant Design组件库

    // 状态管理
    "zustand": "^5.0.3", // 轻量状态管理

    // 路由与导航
    "react-router-dom": "^7.1.5", // React路由

    // 网络请求
    "axios": "^1.7.9", // HTTP客户端

    // 国际化
    "i18next": "^24.2.2", // 国际化核心
    "react-i18next": "^15.4.0", // React国际化集成
    "i18next-browser-languagedetector": "^8.0.2", // 浏览器语言检测

    // 动画与交互
    "framer-motion": "^12.4.1", // 动画库
    "use-debounce": "^10.0.4", // 防抖钩子

    // React核心
    "react": "18.3.1",
    "react-dom": "18.3.1",
    "react-helmet-async": "^2.0.5", // SEO管理

    // 构建工具
    "vite": "^6.1.0", // 构建工具
    "typescript": "^5.7.3" // TypeScript
  },

  // 开发依赖
  "devDependencies": {
    // 类型定义
    "@types/node": "22.14.0",
    "@types/react": "18.3.20",
    "@types/react-dom": "18.2.18",
    "@types/lodash": "4.17.16",

    // ESLint相关
    "eslint": "^8.57.1", // 代码检查
    "@typescript-eslint/eslint-plugin": "^7.18.0", // TS ESLint插件
    "@typescript-eslint/parser": "^7.18.0", // TS解析器
    "eslint-config-airbnb-typescript": "^18.0.0", // Airbnb风格配置
    "eslint-plugin-import": "2.31.0", // import规则
    "eslint-plugin-react": "7.37.4", // React规则
    "eslint-plugin-react-hooks": "5.1.0", // Hooks规则

    // 测试相关
    "jest-environment-jsdom": "^29.7.0", // 测试环境

    // 样式处理
    "less": "4.2.2", // CSS预处理器
    "@vitejs/plugin-react": "^4.3.4", // Vite React插件

    // 可视化
    "echarts": "^5.6.0" // 图表库
  },

  // PNPM配置
  "pnpm": {
    "allowedDeprecatedVersions": {
      "esbuild": "0.19.2" // 允许特定弃用版本
    },
    "peerDependencyRules": {
      "ignoreMissing": ["esbuild"] // 忽略缺失的peer依赖
    },
    "onlyBuiltDependencies": ["esbuild"] // 仅构建指定依赖
  },

  // ESLint配置扩展
  "eslintConfig": {
    "extends": [
      "airbnb",
      "airbnb-typescript",
      "prettier" // 集成Prettier
    ]
  },

  // 浏览器兼容性配置
  "browserslist": {
    "production": [">0.2%", "not dead", "not op_mini all"],
    "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]
  }
}
