# 情感分析AI系统 - 部署指南

## 📋 目录

- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [配置说明](#配置说明)
- [部署方式](#部署方式)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 🔧 系统要求

### 硬件要求

**最低配置：**
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 50GB 可用空间
- 网络: 稳定的互联网连接

**推荐配置：**
- CPU: 8核心或更多
- 内存: 16GB RAM 或更多
- 存储: 100GB SSD
- GPU: NVIDIA GPU（可选，用于加速推理）

### 软件要求

- Docker 20.10+
- Docker Compose 2.0+
- Git
- curl（用于健康检查）

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd emotion-ai-system
```

### 2. 配置环境

```bash
# 复制环境配置文件
cp env.example .env

# 编辑环境配置（重要！）
nano .env
```

### 3. 一键部署

```bash
# 使用部署脚本
./scripts/deploy.sh

# 或者手动部署
docker-compose up -d
```

### 4. 验证部署

访问以下地址验证部署：

- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## ⚙️ 配置说明

### 环境变量配置

主要配置项说明：

```bash
# 应用基础配置
ENVIRONMENT=production          # 环境类型
DEBUG=false                    # 调试模式
PROJECT_NAME="Emotion AI"      # 项目名称

# 数据库配置
POSTGRES_SERVER=postgres       # 数据库服务器
POSTGRES_USER=your_user        # 数据库用户
POSTGRES_PASSWORD=your_pass    # 数据库密码
POSTGRES_DB=emotion_ai         # 数据库名称

# 安全配置
SECRET_KEY=your-secret-key     # 应用密钥
JWT_SECRET_KEY=your-jwt-key    # JWT密钥

# 深度学习配置
MEDIAPIPE_GPU=0               # 禁用GPU
MEDIAPIPE_DISABLE_GPU=1       # 强制禁用GPU
```

### 依赖管理

项目使用分层依赖管理：

```
requirements-core.txt      # 核心运行时依赖
requirements-dev.txt       # 开发环境依赖
requirements-production.txt # 生产环境依赖
requirements-vector.txt    # 向量数据库依赖
```

## 🐳 部署方式

### 开发环境部署

```bash
# 使用开发配置
docker-compose -f docker-compose.dev.yml up -d

# 或设置环境变量
export BUILD_TARGET=development
docker-compose up -d
```

### 生产环境部署

```bash
# 使用生产配置
export BUILD_TARGET=production
docker-compose up -d

# 或使用Nginx代理
docker-compose --profile production up -d
```

### 云平台部署

#### AWS ECS

```bash
# 构建并推送镜像
docker build -t your-registry/emotion-ai-backend:latest ./backend
docker build -t your-registry/emotion-ai-frontend:latest ./frontend

docker push your-registry/emotion-ai-backend:latest
docker push your-registry/emotion-ai-frontend:latest
```

#### Kubernetes

```yaml
# 使用提供的Kubernetes配置
kubectl apply -f k8s/
```

## 📊 监控和维护

### 健康检查

```bash
# 检查服务状态
./scripts/deploy.sh health

# 查看日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
```

### 性能监控

系统集成了Prometheus监控：

- 指标端点: http://localhost:8000/metrics
- 健康检查: http://localhost:8000/health

### 备份策略

```bash
# 数据库备份
docker-compose exec postgres pg_dump -U dom emotion_ai > backup.sql

# 文件备份
tar -czf uploads_backup.tar.gz backend/uploads/
```

## 🔧 故障排除

### 常见问题

#### 1. 容器启动失败

```bash
# 查看容器状态
docker-compose ps

# 查看错误日志
docker-compose logs backend
```

#### 2. 数据库连接失败

```bash
# 检查数据库服务
docker-compose exec postgres pg_isready

# 重启数据库
docker-compose restart postgres
```

#### 3. 内存不足

```bash
# 检查内存使用
docker stats

# 清理未使用的镜像
docker system prune -f
```

#### 4. 端口冲突

```bash
# 检查端口占用
netstat -tulpn | grep :8000

# 修改端口配置
# 编辑 .env 文件中的端口设置
```

### 日志分析

```bash
# 查看应用日志
tail -f backend/logs/app.log

# 查看错误日志
tail -f backend/logs/error.log

# 查看访问日志
docker-compose logs nginx
```

### 性能优化

#### 后端优化

```bash
# 调整worker数量
export GUNICORN_WORKERS=4

# 启用缓存
export REDIS_ENABLED=true
```

#### 前端优化

```bash
# 启用生产构建
export NODE_ENV=production

# 启用CDN
export VITE_CDN_URL=https://your-cdn.com
```

## 📝 维护任务

### 定期维护

```bash
# 更新依赖
pip-compile requirements.in

# 清理日志
find backend/logs -name "*.log" -mtime +30 -delete

# 数据库维护
docker-compose exec postgres psql -U dom -d emotion_ai -c "VACUUM ANALYZE;"
```

### 安全更新

```bash
# 更新基础镜像
docker pull python:3.10-slim
docker pull node:18-alpine
docker pull postgres:15-alpine
docker pull redis:7-alpine

# 重新构建镜像
docker-compose build --no-cache
```

## 📞 支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 联系技术支持团队

---

**注意**: 在生产环境中，请确保：
- 修改默认密码
- 启用HTTPS
- 配置防火墙
- 定期备份数据
- 监控系统性能 