# 依赖版本分析报告

## 📋 概述

本文档记录了当前运行环境与新配置依赖文件的版本对比分析，确保部署时的版本一致性。

## 🔍 关键依赖版本对比

### Web框架
| 包名 | 当前版本 | 新配置版本 | 状态 | 说明 |
|------|----------|------------|------|------|
| fastapi | 0.115.12 | 0.115.12 | ✅ 已同步 | 核心Web框架 |
| uvicorn | 0.34.2 | 0.34.2 | ✅ 已同步 | ASGI服务器 |
| starlette | - | >=0.46.0 | ⚠️ 需检查 | FastAPI依赖 |

### 数据库
| 包名 | 当前版本 | 新配置版本 | 状态 | 说明 |
|------|----------|------------|------|------|
| sqlalchemy | 2.0.41 | 2.0.41 | ✅ 已同步 | ORM框架 |
| alembic | 1.15.2 | 1.15.2 | ✅ 已同步 | 数据库迁移 |
| psycopg2-binary | 2.9.10 | 2.9.10 | ✅ 已同步 | PostgreSQL驱动 |
| asyncpg | 0.30.0 | 0.30.0 | ✅ 已同步 | 异步PostgreSQL驱动 |

### 数据验证
| 包名 | 当前版本 | 新配置版本 | 状态 | 说明 |
|------|----------|------------|------|------|
| pydantic | 2.11.4 | 2.11.4 | ✅ 已同步 | 数据验证 |
| pydantic-settings | 2.9.1 | 2.9.1 | ✅ 已同步 | 配置管理 |

### 深度学习
| 包名 | 当前版本 | 新配置版本 | 状态 | 说明 |
|------|----------|------------|------|------|
| torch | 2.6.0 | 2.6.0 | ✅ 已同步 | 深度学习框架 |
| torchvision | 0.21.0 | 0.21.0 | ✅ 已同步 | 计算机视觉 |
| torchaudio | 2.6.0 | 2.6.0 | ✅ 已同步 | 音频处理 |
| transformers | 4.51.3 | 4.51.3 | ✅ 已同步 | Transformer模型 |

### 计算机视觉
| 包名 | 当前版本 | 新配置版本 | 状态 | 说明 |
|------|----------|------------|------|------|
| mediapipe | 0.10.21 | 0.10.21 | ✅ 已同步 | 多媒体处理 |
| opencv-python-headless | ********* | ********* | ✅ 已同步 | 计算机视觉 |
| deepface | 0.0.93 | 0.0.93 | ✅ 已同步 | 人脸分析 |
| ultralytics | 8.3.120 | 8.3.120 | ✅ 已同步 | YOLO目标检测 |

### 重要修正
| 包名 | 原配置版本 | 当前版本 | 修正后版本 | 原因 |
|------|------------|----------|------------|------|
| aiofiles | >=23.2.1 | 22.1.0 | 22.1.0 | 保持当前稳定版本 |
| albumentations | >=2.0.6 | 1.3.1 | 1.3.1 | 避免破坏性更新 |
| redis | >=6.0.0 | 6.0.0 | 6.0.0 | 锁定稳定版本 |

## 🎯 版本锁定策略

### 1. 精确锁定 (==)
对于以下关键依赖使用精确版本锁定：
- **Web框架**: fastapi, uvicorn
- **数据库**: sqlalchemy, alembic, psycopg2-binary
- **深度学习**: torch, transformers, mediapipe
- **数据验证**: pydantic, pydantic-settings

### 2. 兼容性范围 (>=, <)
对于以下依赖使用兼容性版本范围：
- **工具库**: tqdm, psutil, rich
- **HTTP客户端**: httpx
- **科学计算**: scipy, matplotlib

### 3. Apple Silicon 特殊处理
```python
tensorflow-macos==2.16.2; sys_platform == "darwin"
tensorflow-metal==1.1.0; sys_platform == "darwin"
tf-keras==2.16.0; sys_platform == "darwin"
```

## 🚀 部署建议

### 开发环境
```bash
pip install -r requirements-core.txt
pip install -r requirements-dev.txt
```

### 生产环境
```bash
pip install -r requirements-production.txt
```

### Docker环境
使用多阶段构建，确保开发和生产环境的依赖隔离：
```dockerfile
# 开发阶段
FROM python:3.10-slim as development
COPY requirements-core.txt requirements-dev.txt ./
RUN pip install -r requirements-dev.txt

# 生产阶段
FROM python:3.10-slim as production
COPY requirements-production.txt ./
RUN pip install -r requirements-production.txt
```

## ⚠️ 注意事项

1. **版本兼容性**: 当前配置基于 Python 3.10 和 macOS ARM64 环境
2. **依赖冲突**: 避免同时安装多个OpenCV版本
3. **Apple Silicon**: 特定依赖仅在macOS上安装
4. **定期更新**: 建议每月检查安全更新
5. **测试验证**: 版本更新前必须在开发环境充分测试

## 📝 更新日志

- **2025-01-17**: 初始版本，基于当前运行环境创建
- **版本锁定**: 确保与当前稳定运行环境完全一致
- **Apple Silicon**: 添加特定平台依赖支持 