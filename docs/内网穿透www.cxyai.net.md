您需要同时打开两个终端窗口。

在第一个终端中，启动您的前端服务以加载最新的配置：
cd /Volumes/acasis/ema2_20250417/frontend
./start-dev.sh

bash


在第二个终端中，启动您的Cloudflare隧道：
cloudflared tunnel run --url http://localhost:3000 ema-dev


cloudflared tunnel run --url http://127.0.0.1:3000 ema-dev

cloudflared tunnel --config /Volumes/acasis/ema2_20250417/cloudflare-config.yml run
bash


最终架构执行方案
第一步：启动您的后端服务

在一个终端中，运行您修改后的后端启动脚本：
./start_server.sh

bash


第二步：启动您的前端服务

在第二个终端中，启动Vite开发服务器：
cd /Volumes/acasis/ema2_20250417/frontend
./start-dev.sh

bash


第三步：使用配置文件启动Cloudflare隧道

在第三个终端中，使用以下命令来启动隧道：
cloudflared tunnel --config /Volumes/acasis/ema2_20250417/cloudflare-config.yml run

bash


完成这三个步骤后，请确保三个进程都保持运行。我们已经从根源上解决了SSE连接被后端中断的问题。现在，您的所有访问都应该能正常工作，并且不会再有任何连接中断的问题。



在一个终端中，用 ./start_server.sh 启动后端服务。
在第二个终端中，用 ./start-dev.sh 启动前端服务。
在第三个终端中，用 cloudflared tunnel --config /path/to/your/cloudflare-config.yml run 启动Cloudflare隧道。
