# Cloudflare 免费加速指南

通过以下步骤，您可以利用Cloudflare的免费功能显著提升 `www.cxyai.net` 的访问速度。

## 登录并选择您的网站

1.  访问 [Cloudflare 控制台](https://dash.cloudflare.com/)。
2.  点击选择您的域名 `cxyai.net`。

---

## 第一步：开启自动内容压缩 (Auto Minify)

这个功能会自动压缩您的代码文件，减小它们的体积。

1.  在左侧菜单中，选择 **Speed -> Optimization**。
2.  在 **Content Optimization** 部分，找到 **Auto Minify**。
3.  勾选 **JavaScript**、**CSS** 和 **HTML** 这三个选项。

---

## 第二步：配置缓存规则

这是加速的核心。我们要告诉Cloudflare尽可能多地缓存静态内容。

1.  在左侧菜单中，选择 **Caching -> Configuration**。
2.  找到 **Caching Level**，确保它被设置为 **Standard**。这会缓存大部分静态资源。
3.  找到 **Browser Cache TTL**，将其设置为一个较长的时间，例如 **4 hours** 或 **1 day**。这会告诉用户的浏览器在一段时间内直接使用本地缓存，而不是重复请求相同的文件。

---

## 第三步：开启 Brotli 压缩

Brotli是一种比传统Gzip更高效的压缩算法，可以进一步减小文件大小。

1.  在 **Speed -> Optimization** 页面的 **Content Optimization** 部分。
2.  找到 **Brotli** 选项，并确保它的开关是 **开启 (On)** 状态。

---

完成以上三个步骤后，您的网站访问速度应该会有明显的提升。当您再次访问 `https://www.cxyai.net` 时，Cloudflare会开始缓存您的资源，并提供经过优化的内容。


第一部分：在 Cloudflare 网站上创建和配置隧道
登录 Cloudflare 控制台:

打开浏览器，访问 https://dash.cloudflare.com/
进入 Zero Trust 界面:

在左侧菜单中，找到并点击 “Zero Trust”。
创建隧道:

在 Zero Trust 界面中，导航到 Access -> Tunnels。
点击 “Create a tunnel” 按钮。
为您的隧道命名，例如 ema-dev，然后点击 “Save tunnel”。
安装连接器 (Connector):

在接下来的页面，Cloudflare 会为您生成一个安装命令，类似 cloudflared service install <一长串密钥>。
请复制这个命令，并在您的终端中运行一次。这会在您的系统上安装并认证隧道服务。
设置公共主机名 (Public Hostname):

安装完连接器后，在 Cloudflare 的同一个页面上，点击 “Next”。
现在您需要将您的域名指向本地服务：
Subdomain: www
Domain: cxyai.net
Service Type: HTTP
URL: localhost:3000
点击 “Save hostname”。 