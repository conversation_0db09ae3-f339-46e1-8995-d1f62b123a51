#!/usr/bin/env python
# -*- coding: utf-8 -*-

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import datetime
import logging
import sys

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,  # 使用DEBUG级别获取更详细的信息
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def test_smtp_with_https_url():
    """测试包含HTTPS URL的邮件发送"""
    logger.info("开始测试包含HTTPS URL的邮件发送...")
    
    # 配置信息
    smtp_host = "smtp.qq.com"
    smtp_port = 465  # SSL端口
    sender = "<EMAIL>"
    password = "qweamijhnidebaji"  # 授权码
    receiver = "<EMAIL>"  # 修改为您的邮箱
    
    # HTTPS URL
    https_url = "https://cxyai.shenzhuo.vip/reset-password?token=test_token_12345"
    
    # 创建邮件
    message = MIMEMultipart()
    message['From'] = f"EmotionAI系统 <{sender}>"
    message['To'] = receiver
    message['Subject'] = "EmotionAI密码重置测试"
    
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 创建HTML内容，包含HTTPS URL
    html_body = f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
            <h2 style="color: #333; text-align: center;">密码重置测试</h2>
            <p>您好，</p>
            <p>这是一封测试邮件，用于验证包含HTTPS URL的邮件是否能正常发送。</p>
            <p>请点击以下链接重置密码：</p>
            <p style="text-align: center;">
                <a href='{https_url}' style="display: inline-block; padding: 10px 20px; background-color: #1890ff; color: white; text-decoration: none; border-radius: 4px;">重置密码</a>
            </p>
            <p>如果上面的按钮不起作用，请复制以下链接到浏览器地址栏：</p>
            <p style="background-color: #f5f5f5; padding: 10px; word-break: break-all;">{https_url}</p>
            <p>发送时间：{current_time}</p>
        </div>
    </body>
    </html>
    """
    
    # 添加HTML内容，明确指定UTF-8编码
    part = MIMEText(html_body, 'html', 'utf-8')
    message.attach(part)
    
    # 添加纯文本备用内容
    text_body = f"""
    密码重置测试
    
    您好，
    
    这是一封测试邮件，用于验证包含HTTPS URL的邮件是否能正常发送。
    
    请访问以下链接重置密码：{https_url}
    
    发送时间：{current_time}
    """
    message.attach(MIMEText(text_body, 'plain', 'utf-8'))
    
    try:
        # 使用SSL连接
        logger.info(f"尝试使用SSL连接到{smtp_host}:{smtp_port}...")
        context = ssl.create_default_context()
        
        with smtplib.SMTP_SSL(smtp_host, smtp_port, context=context, timeout=30) as server:
            server.set_debuglevel(2)  # 启用最详细的日志
            logger.info(f"连接成功，尝试登录...")
            server.login(sender, password)
            logger.info(f"登录成功，尝试发送邮件到{receiver}...")
            
            # 尝试发送邮件
            server.sendmail(sender, receiver, message.as_string())
            logger.info("邮件发送成功！")
            return True
            
    except Exception as e:
        logger.error(f"邮件发送失败: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    if test_smtp_with_https_url():
        print("\n✅ 测试成功！邮件已发送。")
    else:
        print("\n❌ 测试失败！请检查错误信息。")
