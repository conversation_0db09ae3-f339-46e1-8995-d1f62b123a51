#!/usr/bin/env python3
"""
批量修复所有模型的Base导入
将所有模型统一使用 emotionai.core.database.base_unified.Base
"""

import os
import re
from pathlib import Path

def fix_base_imports():
    """修复所有模型文件的Base导入"""
    
    # 需要修复的文件模式
    patterns_to_fix = [
        "from emotionai.models.base.base import Base",
        "from emotionai.core.database.base_class import Base"
    ]
    
    # 新的导入语句
    new_import = "from emotionai.core.database.base_unified import Base"
    
    # 搜索所有Python文件
    backend_dir = Path("/Volumes/acasis/ema2_20250417/backend")
    python_files = list(backend_dir.rglob("*.py"))
    
    fixed_files = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 替换所有旧的导入语句
            for pattern in patterns_to_fix:
                if pattern in content:
                    content = content.replace(pattern, new_import)
                    print(f"修复文件: {file_path}")
                    print(f"  替换: {pattern}")
                    print(f"  为: {new_import}")
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_files.append(str(file_path))
                
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
    
    return fixed_files

def restore_relationships():
    """恢复被注释掉的关系定义"""
    
    # User模型的关系恢复
    user_file = "/Volumes/acasis/ema2_20250417/backend/emotionai/models/user/user.py"
    
    relationships_to_restore = [
        {
            "file": user_file,
            "old": """    # 暂时移除Annotation关系定义，避免循环导入问题
    # created_annotations = relationship(
    #     "Annotation", foreign_keys="[Annotation.created_by]", back_populates="creator"
    # )
    # reviewed_annotations = relationship(
    #     "Annotation", foreign_keys="[Annotation.reviewer_id]", back_populates="reviewer"
    # )""",
            "new": """    # Annotation关系定义
    created_annotations = relationship(
        "Annotation", foreign_keys="[Annotation.created_by]", back_populates="creator"
    )
    reviewed_annotations = relationship(
        "Annotation", foreign_keys="[Annotation.reviewer_id]", back_populates="reviewer"
    )"""
        },
        {
            "file": user_file,
            "old": """    # 暂时移除密码重置令牌关联关系，避免循环导入问题
    # password_reset_tokens: Mapped[List["PasswordResetToken"]] = relationship(
    #     "PasswordResetToken", back_populates="user", cascade="all, delete-orphan"
    # )""",
            "new": """    # 密码重置令牌关联关系
    password_reset_tokens: Mapped[List["PasswordResetToken"]] = relationship(
        "PasswordResetToken", back_populates="user", cascade="all, delete-orphan"
    )"""
        }
    ]
    
    # MLModel的关系恢复
    model_file = "/Volumes/acasis/ema2_20250417/backend/emotionai/models/ml/model.py"
    relationships_to_restore.append({
        "file": model_file,
        "old": """    # 暂时移除TrainingJob关系定义，避免循环导入问题
    # training_jobs = relationship(
    #     "TrainingJob",
    #     back_populates="model",
    #     cascade="all, delete-orphan",
    #     lazy="dynamic",
    # )""",
        "new": """    # TrainingJob关系定义
    training_jobs = relationship(
        "TrainingJob",
        back_populates="model",
        cascade="all, delete-orphan",
        lazy="dynamic",
    )"""
    })
    
    restored_files = []
    
    for item in relationships_to_restore:
        file_path = item["file"]
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if item["old"] in content:
                content = content.replace(item["old"], item["new"])
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                restored_files.append(file_path)
                print(f"恢复关系定义: {file_path}")
                
        except Exception as e:
            print(f"恢复文件 {file_path} 时出错: {e}")
    
    return restored_files

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 修复SQLAlchemy模型Base导入和关系定义")
    print("=" * 60)
    
    print("\n1. 修复Base导入...")
    fixed_files = fix_base_imports()
    print(f"✅ 修复了 {len(fixed_files)} 个文件的Base导入")
    
    print("\n2. 恢复关系定义...")
    restored_files = restore_relationships()
    print(f"✅ 恢复了 {len(restored_files)} 个文件的关系定义")
    
    print("\n🎉 修复完成！")
    print("现在所有模型都使用统一的Base类，关系定义也已恢复")
    print("=" * 60)

if __name__ == "__main__":
    main() 