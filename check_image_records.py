#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psycopg2
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接参数
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': os.getenv('DB_PORT', '5432'),
    'database': os.getenv('DB_NAME', 'emotion_ai'),
    'user': os.getenv('DB_USER', 'dom'),
    'password': os.getenv('DB_PASSWORD', '')
}

try:
    # 连接数据库
    conn = psycopg2.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('=== 评估反馈数据用于训练数据集的适用性 ===\n')
    
    # 1. 总体数据统计
    print('1. 总体数据统计:')
    
    # 总的分析记录数
    cursor.execute("SELECT COUNT(*) FROM emotion_analysis WHERE deleted_at IS NULL;")
    total_analysis = cursor.fetchone()[0]
    print(f'   总分析记录数: {total_analysis}')
    
    # 有反馈的分析记录数
    cursor.execute("""
        SELECT COUNT(DISTINCT ea.id) 
        FROM emotion_analysis ea 
        JOIN emotion_feedback ef ON ea.id = ef.analysis_id 
        WHERE ea.deleted_at IS NULL;
    """)
    analysis_with_feedback = cursor.fetchone()[0]
    print(f'   有反馈的分析记录数: {analysis_with_feedback}')
    
    # 反馈覆盖率
    feedback_coverage = (analysis_with_feedback / total_analysis * 100) if total_analysis > 0 else 0
    print(f'   反馈覆盖率: {feedback_coverage:.2f}%')
    
    # 总反馈数
    cursor.execute("SELECT COUNT(*) FROM emotion_feedback;")
    total_feedback = cursor.fetchone()[0]
    print(f'   总反馈数: {total_feedback}')
    
    # 2. 情绪分布分析
    print('\n2. 原始分析情绪分布:')
    cursor.execute("""
        SELECT emotion, COUNT(*) as count 
        FROM emotion_analysis 
        WHERE deleted_at IS NULL AND emotion IS NOT NULL
        GROUP BY emotion 
        ORDER BY count DESC;
    """)
    
    original_emotions = cursor.fetchall()
    for emotion, count in original_emotions:
        print(f'   {emotion}: {count} 条记录')
    
    print('\n3. 反馈情绪分布:')
    cursor.execute("""
        SELECT emotion, COUNT(*) as count 
        FROM emotion_feedback 
        WHERE emotion IS NOT NULL
        GROUP BY emotion 
        ORDER BY count DESC;
    """)
    
    feedback_emotions = cursor.fetchall()
    for emotion, count in feedback_emotions:
        print(f'   {emotion}: {count} 条反馈')
    
    # 3. 数据质量分析
    print('\n4. 数据质量分析:')
    
    # 检查有效图片文件
    cursor.execute("""
        SELECT ea.input_path, ea.emotion, ea.confidence
        FROM emotion_analysis ea
        JOIN emotion_feedback ef ON ea.id = ef.analysis_id
        WHERE ea.input_path IS NOT NULL AND ea.deleted_at IS NULL;
    """)
    
    image_records = cursor.fetchall()
    valid_images = 0
    invalid_images = 0
    
    for input_path, emotion, confidence in image_records:
        full_path = os.path.join('/Volumes/acasis/ema2_20250417/backend', input_path)
        if os.path.exists(full_path):
            valid_images += 1
        else:
            invalid_images += 1
    
    print(f'   有效图片文件: {valid_images}')
    print(f'   无效图片文件: {invalid_images}')
    
    # 检查反馈质量
    cursor.execute("""
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN ef.emotion IS NOT NULL THEN 1 END) as with_emotion,
            COUNT(CASE WHEN ef.comment IS NOT NULL AND ef.comment != '' THEN 1 END) as with_comment
        FROM emotion_feedback ef;
    """)
    
    feedback_quality = cursor.fetchone()
    total, with_emotion, with_comment = feedback_quality
    
    print(f'   反馈完整性:')
    print(f'     有情绪标签: {with_emotion}/{total} ({with_emotion/total*100:.1f}%)')
    print(f'     有评论内容: {with_comment}/{total} ({with_comment/total*100:.1f}%)')
    
    # 4. 训练数据集适用性评估
    print('\n5. 训练数据集适用性评估:')
    
    # 检查是否有足够的数据
    min_samples_per_class = 100  # 每个情绪类别至少需要的样本数
    
    cursor.execute("""
        SELECT ef.emotion, COUNT(*) as count
        FROM emotion_feedback ef
        JOIN emotion_analysis ea ON ef.analysis_id = ea.id
        WHERE ef.emotion IS NOT NULL 
        AND ea.input_path IS NOT NULL 
        AND ea.deleted_at IS NULL
        GROUP BY ef.emotion;
    """)
    
    feedback_class_distribution = cursor.fetchall()
    
    print('   各情绪类别样本数:')
    sufficient_classes = 0
    total_classes = len(feedback_class_distribution)
    
    for emotion, count in feedback_class_distribution:
        status = "✅ 充足" if count >= min_samples_per_class else "❌ 不足"
        print(f'     {emotion}: {count} 样本 {status}')
        if count >= min_samples_per_class:
            sufficient_classes += 1
    
    # 5. 数据质量问题
    print('\n6. 发现的数据质量问题:')
    
    issues = []
    
    # 检查数据量
    if total_feedback < 1000:
        issues.append(f"反馈数据量太少 ({total_feedback} < 1000)")
    
    # 检查类别平衡
    if feedback_class_distribution:
        max_count = max(count for _, count in feedback_class_distribution)
        min_count = min(count for _, count in feedback_class_distribution)
        imbalance_ratio = max_count / min_count if min_count > 0 else float('inf')
        
        if imbalance_ratio > 10:
            issues.append(f"类别不平衡严重 (最大/最小比例: {imbalance_ratio:.1f})")
    
    # 检查覆盖率
    if feedback_coverage < 5:
        issues.append(f"反馈覆盖率太低 ({feedback_coverage:.2f}%)")
    
    # 检查重复标注
    cursor.execute("""
        SELECT analysis_id, COUNT(*) as feedback_count
        FROM emotion_feedback
        GROUP BY analysis_id
        HAVING COUNT(*) > 1;
    """)
    
    duplicate_annotations = cursor.fetchall()
    if duplicate_annotations:
        issues.append(f"存在重复标注的图片 ({len(duplicate_annotations)} 张)")
    
    if issues:
        for issue in issues:
            print(f'   ❌ {issue}')
    else:
        print('   ✅ 未发现明显的数据质量问题')
    
    # 6. 建议
    print('\n7. 建议:')
    
    if total_feedback < 100:
        print('   🔴 当前数据不适合用于训练模型')
        print('      - 数据量太少，建议至少收集1000+条反馈')
        print('      - 每个情绪类别至少需要100+样本')
    elif total_feedback < 1000:
        print('   🟡 数据量有限，可用于初步实验')
        print('      - 建议继续收集更多反馈数据')
        print('      - 可以先用于模型微调或验证')
    else:
        print('   🟢 数据量充足，适合用于训练')
    
    print('\n   具体建议:')
    print('   1. 增加反馈数据收集：')
    print('      - 鼓励用户提供反馈')
    print('      - 实施反馈奖励机制')
    print('      - 简化反馈流程')
    
    print('   2. 提高数据质量：')
    print('      - 确保每个情绪类别有足够样本')
    print('      - 处理类别不平衡问题')
    print('      - 验证反馈的一致性')
    
    print('   3. 数据预处理：')
    print('      - 清理无效或错误的反馈')
    print('      - 标准化情绪标签')
    print('      - 处理重复标注')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'数据库连接或查询错误: {e}')
