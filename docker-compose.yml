# version: '3.8' # Obsolete, will be ignored

services:
  # PostgreSQL 数据库 (已注释掉，将使用本地数据库)
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: ema-postgres
  #   environment:
  #     POSTGRES_DB: ${POSTGRES_DB:-emotion_ai}
  #     POSTGRES_USER: ${POSTGRES_USER:-dom}
  #     POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your_password}
  #     POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #     - ./config/database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
  #   ports:
  #     - "${POSTGRES_PORT:-5432}:5432"
  #   networks:
  #     - ema-network
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-dom} -d ${POSTGRES_DB:-emotion_ai}"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 30s
  #   restart: unless-stopped

  # Redis 缓存 (已移除，假设使用内置 Redis 或 redislite)
  # redis:
  #   image: redis:7-alpine
  #   container_name: ema-redis
  #   command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
  #   volumes:
  #     - redis_data:/data
  #     - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
  #   ports:
  #     - "${REDIS_PORT:-6379}:6379"
  #   networks:
  #     - ema-network
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 10s
  #   restart: unless-stopped

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      # target: ${BUILD_TARGET:-development} # 新的 backend/Dockerfile 直接构建生产镜像，或其内部有明确的 production target
    container_name: ema-backend
    environment:
      # 数据库配置 (连接到本地/宿主机数据库)
      - POSTGRES_SERVER=host.docker.internal # 对于 Docker Desktop (Mac/Windows)
      # - POSTGRES_SERVER=********** # 对于 Linux Docker，可能是这个IP，或根据实际情况调整
      - POSTGRES_USER=${LOCAL_DB_USER:-dom} # 请替换为您的本地数据库用户名或通过.env设置
      - POSTGRES_PASSWORD=${LOCAL_DB_PASSWORD:-your_local_password} # 请替换为您的本地数据库密码或通过.env设置
      - POSTGRES_DB=${LOCAL_DB_NAME:-emotion_ai} # 请替换为您的本地数据库名或通过.env设置
      - POSTGRES_PORT=${LOCAL_DB_PORT:-5432} # 请替换为您的本地数据库端口或通过.env设置
      
      # Redis配置 (已移除，假设使用内置 Redis 或 redislite)
      # - REDIS_HOST=redis
      # - REDIS_PORT=6379
      # - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      
      # 应用配置
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEBUG=${DEBUG:-true}
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-jwt-secret}
      
      # CORS配置
      - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS:-http://localhost:3000,http://127.0.0.1:3000}
      
      # 文件上传配置
      - MAX_UPLOAD_SIZE=${MAX_UPLOAD_SIZE:-52428800}
      - UPLOAD_DIR=/app/uploads
      
      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_DIR=/app/logs
      
      # 深度学习模型配置
      - MEDIAPIPE_GPU=0
      - MEDIAPIPE_USE_CUSTOM_OPENCV=0
      - MEDIAPIPE_DISABLE_GPU=1
      - HF_DATASETS_OFFLINE=1
      - TRANSFORMERS_OFFLINE=1
      - NO_GIT=1
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - ./models:/app/models
      - ./data:/app/data
    ports:
      - "${BACKEND_PORT:-8008}:8000"
    command: uvicorn emotionai.main:app --host 0.0.0.0 --port 8000 --workers 4 --timeout-keep-alive 300
    networks:
      - ema-network
    # depends_on: # 后端服务不再依赖于由 docker-compose 管理的其他服务
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-development}
      args:
        - VITE_API_URL=${VITE_API_URL:-http://backend:8000}
        - VITE_PUBLIC_URL=${VITE_PUBLIC_URL:-}
    container_name: ema-frontend
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - VITE_API_URL=${VITE_API_URL:-http://backend:8000}
      - VITE_PUBLIC_URL=${VITE_PUBLIC_URL:-}
    ports:
      - "${FRONTEND_PORT:-3003}:3000"
    networks:
      - ema-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped

  # Nginx 反向代理（生产环境）
  nginx:
    image: nginx:alpine
    container_name: ema-nginx
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./frontend/dist:/usr/share/nginx/html:ro
      - ./backend/uploads:/usr/share/nginx/uploads:ro
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    networks:
      - ema-network
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    profiles:
      - production

# volumes: # 移除了 postgres_data 和 redis_data 卷定义, 整个 volumes 部分不再需要
#   # redis_data: # 已移除
#   #   driver: local

networks:
  ema-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16 