#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import uuid
from datetime import datetime
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接参数
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': os.getenv('DB_PORT', '5432'),
    'database': os.getenv('DB_NAME', 'emotion_ai'),
    'user': os.getenv('DB_USER', 'dom'),
    'password': os.getenv('DB_PASSWORD', '')
}

# 异步数据库连接
DATABASE_URL = f"postgresql+asyncpg://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"

async def test_service_feedback():
    """测试服务层的反馈功能"""
    
    # 创建异步引擎和会话
    engine = create_async_engine(DATABASE_URL)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    try:
        async with async_session() as db:
            print("=== 测试服务层的反馈功能 ===")
            
            # 使用已知的分析记录ID和用户ID
            analysis_id = "799e7d78-14cd-44af-ab28-f41a3b89a7e0"
            user_id = "386c9d5c-4174-4811-91bf-595bfeb7e841"
            
            # 1. 测试直接使用SQL插入反馈
            print("\n1. 测试直接使用SQL插入反馈...")
            
            feedback_id = str(uuid.uuid4())
            feedback_emotion = "neutral"
            feedback_comment = "服务层测试反馈 - 分析结果一般"
            created_at = datetime.now()
            
            # 使用原生SQL插入
            from sqlalchemy import text
            
            await db.execute(
                text("""
                INSERT INTO emotion_feedback (
                    id, analysis_id, emotion, comment, created_by, created_at
                ) VALUES (:id, :analysis_id, :emotion, :comment, :created_by, :created_at)
                """),
                {
                    "id": feedback_id,
                    "analysis_id": analysis_id,
                    "emotion": feedback_emotion,
                    "comment": feedback_comment,
                    "created_by": user_id,
                    "created_at": created_at
                }
            )
            
            await db.commit()
            print(f"✅ 服务层反馈保存成功，ID: {feedback_id}")
            
            # 2. 测试查询反馈历史
            print("\n2. 测试查询反馈历史...")
            
            result = await db.execute(
                text("""
                SELECT id, analysis_id, emotion, comment, created_by, created_at
                FROM emotion_feedback 
                WHERE analysis_id = :analysis_id AND created_by = :user_id
                ORDER BY created_at DESC
                """),
                {"analysis_id": analysis_id, "user_id": user_id}
            )
            
            feedbacks = result.fetchall()
            
            print(f"✅ 找到 {len(feedbacks)} 条反馈记录:")
            for i, feedback in enumerate(feedbacks):
                print(f"  反馈 #{i+1}:")
                print(f"    ID: {feedback[0]}")
                print(f"    Analysis ID: {feedback[1]}")
                print(f"    Emotion: {feedback[2]}")
                print(f"    Comment: {feedback[3]}")
                print(f"    Created By: {feedback[4]}")
                print(f"    Created At: {feedback[5]}")
                print("    ---")
            
            # 3. 测试获取总反馈数量
            print("\n3. 测试获取总反馈数量...")
            
            result = await db.execute(text("SELECT COUNT(*) FROM emotion_feedback"))
            total_count = result.scalar()
            print(f"✅ emotion_feedback表总记录数: {total_count}")
            
            print("\n🎉 服务层的反馈功能测试完成！")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await engine.dispose()

if __name__ == "__main__":
    asyncio.run(test_service_feedback()) 