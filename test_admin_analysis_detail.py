#!/usr/bin/env python3
"""
测试管理员分析详情API，验证反馈信息是否正确返回
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_admin_analysis_detail():
    """测试管理员分析详情API"""
    
    # 测试的分析ID（已知有反馈数据的记录）
    analysis_id = "4b8c8cee-f731-4414-8829-4d7766591489"
    
    # API端点
    url = f"http://localhost:8000/api/v1/admin/analysis/{analysis_id}"
    
    # 模拟管理员token（实际使用时需要真实的token）
    headers = {
        "Authorization": "Bearer admin_token_here",
        "Content-Type": "application/json"
    }
    
    print(f"🔍 测试分析详情API")
    print(f"📍 URL: {url}")
    print(f"🆔 分析ID: {analysis_id}")
    print("-" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                print(f"📊 响应状态码: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    
                    print("✅ API调用成功!")
                    print(f"📝 响应数据结构:")
                    
                    # 检查基本信息
                    print(f"  - 分析ID: {data.get('id', 'N/A')}")
                    print(f"  - 用户名: {data.get('username', 'N/A')}")
                    print(f"  - 情绪: {data.get('emotion', 'N/A')}")
                    print(f"  - 置信度: {data.get('confidence', 'N/A')}")
                    print(f"  - 创建时间: {data.get('created_at', 'N/A')}")
                    
                    # 检查反馈信息
                    feedback_count = data.get('feedback_count', 0)
                    feedbacks = data.get('feedbacks', [])
                    
                    print(f"\n💬 反馈信息:")
                    print(f"  - 反馈数量: {feedback_count}")
                    print(f"  - 反馈列表长度: {len(feedbacks)}")
                    
                    if feedbacks:
                        print(f"\n📋 反馈详情:")
                        for i, feedback in enumerate(feedbacks, 1):
                            print(f"  反馈 #{i}:")
                            print(f"    - ID: {feedback.get('id', 'N/A')}")
                            print(f"    - 情绪评价: {feedback.get('emotion', 'N/A')}")
                            print(f"    - 评论: {feedback.get('comment', 'N/A')}")
                            print(f"    - 反馈用户: {feedback.get('username', 'N/A')}")
                            print(f"    - 创建时间: {feedback.get('created_at', 'N/A')}")
                            print()
                    else:
                        print("  ⚠️  没有反馈数据")
                    
                    # 检查图片信息
                    image_url = data.get('image_thumbnail_url')
                    if image_url:
                        print(f"🖼️  图片缩略图: {image_url}")
                    else:
                        print("🖼️  无图片信息")
                    
                    print("\n" + "=" * 50)
                    print("🎉 测试完成！反馈信息已成功集成到分析详情中")
                    
                elif response.status == 404:
                    print("❌ 分析记录未找到")
                elif response.status == 401:
                    print("❌ 未授权访问（需要管理员权限）")
                else:
                    error_text = await response.text()
                    print(f"❌ API调用失败: {response.status}")
                    print(f"错误信息: {error_text}")
                    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")

async def test_without_auth():
    """测试不带认证的API调用（应该返回401）"""
    analysis_id = "4b8c8cee-f731-4414-8829-4d7766591489"
    url = f"http://localhost:8000/api/v1/admin/analysis/{analysis_id}"
    
    print(f"\n🔒 测试无认证访问（预期返回401）")
    print(f"📍 URL: {url}")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                print(f"📊 响应状态码: {response.status}")
                if response.status == 401:
                    print("✅ 正确返回401未授权")
                else:
                    print(f"⚠️  预期401，实际返回{response.status}")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始测试管理员分析详情API")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 运行测试
    asyncio.run(test_admin_analysis_detail())
    asyncio.run(test_without_auth()) 