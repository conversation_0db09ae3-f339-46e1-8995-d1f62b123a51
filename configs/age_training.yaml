# 年龄识别模型训练配置文件

# 数据集配置
dataset:
  type: "custom"  # utkface, imdb_wiki, fairface, custom
  data_dir: "data/age_datasets"  # 数据集目录
  csv_file: null  # CSV标注文件路径（可选）
  target_size: [224, 224]  # 目标图像尺寸
  age_range: [0, 100]  # 年龄范围
  mode: "regression"  # regression 或 classification
  num_age_groups: 8  # 年龄组数量（分类模式下使用）
  augmentation_strength: 0.6  # 数据增强强度 (0.0-1.0)

# 模型配置
model:
  type: "single_task"  # single_task 或 multi_task
  backbone: "resnet50"  # resnet50, efficientnet_b0
  pretrained: true  # 是否使用预训练权重
  dropout: 0.5  # Dropout率
  num_age_groups: 8  # 多任务模型的年龄组数量

# 训练配置
training:
  epochs: 100  # 训练轮数
  batch_size: 32  # 批次大小
  learning_rate: 0.001  # 学习率
  weight_decay: 0.0001  # 权重衰减
  optimizer: "adamw"  # adamw, adam, sgd
  loss_type: "smooth_l1"  # mse, mae, smooth_l1, focal_mse
  
  # 多任务训练权重
  regression_weight: 1.0  # 回归损失权重
  classification_weight: 0.3  # 分类损失权重
  
  # 数据分割
  train_split: 0.8  # 训练集比例
  
  # 早停和保存
  patience: 15  # 早停耐心值
  save_every: 5  # 每隔多少轮保存一次
  
  # 其他设置
  num_workers: 4  # 数据加载器工作进程数
  device: null  # 训练设备，null表示自动选择
  seed: 42  # 随机种子
  
  # 保存路径
  save_dir: "models/age_recognition/checkpoints"
  log_dir: "logs/age_recognition"

# 高级训练配置
advanced:
  # 学习率调度
  use_scheduler: true
  scheduler_type: "cosine"  # cosine, step, plateau
  scheduler_params:
    T_max: 100  # cosine调度器的最大轮数
    eta_min: 0.00001  # 最小学习率
  
  # 混合精度训练
  use_amp: true  # 是否使用自动混合精度
  
  # 梯度裁剪
  max_grad_norm: 1.0  # 梯度裁剪阈值
  
  # 模型集成
  use_ema: false  # 是否使用指数移动平均
  ema_decay: 0.999  # EMA衰减率
  
  # 知识蒸馏
  use_distillation: false  # 是否使用知识蒸馏
  teacher_model_path: null  # 教师模型路径
  distillation_alpha: 0.7  # 蒸馏损失权重
  distillation_temperature: 4.0  # 蒸馏温度

# 验证和测试配置
evaluation:
  # 验证指标
  metrics:
    - "MAE"  # 平均绝对误差
    - "MSE"  # 均方误差
    - "RMSE"  # 均方根误差
    - "Accuracy@1"  # 1岁误差内准确率
    - "Accuracy@3"  # 3岁误差内准确率
    - "Accuracy@5"  # 5岁误差内准确率
  
  # 测试时增强
  test_time_augmentation: false
  tta_num_crops: 5  # TTA裁剪数量 