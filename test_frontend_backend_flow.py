#!/usr/bin/env python3
"""
测试前端到后端的完整数据流
模拟前端发送反馈数据到后端API的过程
"""
import asyncio
import sys
import json
from datetime import datetime

sys.path.append('backend')

from backend.emotionai.core.database.session import get_async_db
from backend.emotionai.services.emotion_service import EmotionService
from sqlalchemy import text

async def test_frontend_backend_flow():
    """测试前端到后端的完整数据流"""
    
    async for db in get_async_db():
        print("=== 测试前端到后端的完整数据流 ===")
        
        # 1. 获取一个现有的情绪分析记录
        print("\n1. 获取现有的情绪分析记录...")
        result = await db.execute(
            text("SELECT id, user_id, emotion FROM emotion_analysis ORDER BY created_at DESC LIMIT 1")
        )
        row = result.fetchone()
        
        if not row:
            print("❌ 没有找到情绪分析记录，请先进行情绪分析")
            return
        
        analysis_id = str(row[0])
        user_id = str(row[1])
        emotion = row[2]
        
        print(f"✅ 找到分析记录: ID={analysis_id}, 用户={user_id}, 情绪={emotion}")
        
        # 2. 模拟前端发送的不同类型反馈数据
        print("\n2. 模拟前端发送的不同类型反馈数据...")
        
        emotion_service = EmotionService()
        
        # 测试场景1: 好评反馈（用户点击"好评"按钮）
        print("\n  场景1: 用户点击'好评'按钮")
        good_feedback = {
            "analysis_id": analysis_id,
            "emotion": "happy",  # 前端映射: good -> happy
            "comment": ""  # 好评通常没有评论
        }
        
        result1 = await emotion_service.save_feedback(
            analysis_id=good_feedback["analysis_id"],
            user_id=user_id,
            emotion=good_feedback["emotion"],
            comment=good_feedback["comment"],
            db=db
        )
        
        if result1.get("success"):
            print(f"    ✅ 好评反馈保存成功: {result1['feedback_id']}")
        else:
            print(f"    ❌ 好评反馈保存失败: {result1}")
        
        # 测试场景2: 一般评价反馈（用户点击"一般"按钮并输入评论）
        print("\n  场景2: 用户点击'一般'按钮并输入评论")
        average_feedback = {
            "analysis_id": analysis_id,
            "emotion": "neutral",  # 前端映射: average -> neutral
            "comment": "分析结果还可以，但是年龄预测不太准确，希望能改进一下。"
        }
        
        result2 = await emotion_service.save_feedback(
            analysis_id=average_feedback["analysis_id"],
            user_id=user_id,
            emotion=average_feedback["emotion"],
            comment=average_feedback["comment"],
            db=db
        )
        
        if result2.get("success"):
            print(f"    ✅ 一般评价反馈保存成功: {result2['feedback_id']}")
            print(f"    📝 评论内容: {average_feedback['comment']}")
        else:
            print(f"    ❌ 一般评价反馈保存失败: {result2}")
        
        # 测试场景3: 差评反馈（用户点击"不好"按钮并输入详细评论）
        print("\n  场景3: 用户点击'不好'按钮并输入详细评论")
        bad_feedback = {
            "analysis_id": analysis_id,
            "emotion": "sad",  # 前端映射: bad -> sad
            "comment": "情绪分析完全不准确！我明明是开心的表情，却被识别成了悲伤。而且性别识别也错了。建议重新训练模型。"
        }
        
        result3 = await emotion_service.save_feedback(
            analysis_id=bad_feedback["analysis_id"],
            user_id=user_id,
            emotion=bad_feedback["emotion"],
            comment=bad_feedback["comment"],
            db=db
        )
        
        if result3.get("success"):
            print(f"    ✅ 差评反馈保存成功: {result3['feedback_id']}")
            print(f"    📝 评论内容: {bad_feedback['comment']}")
        else:
            print(f"    ❌ 差评反馈保存失败: {result3}")
        
        # 3. 验证所有反馈都已保存到数据库
        print("\n3. 验证所有反馈都已保存到数据库...")
        
        history = await emotion_service.get_feedback_history(
            analysis_id=analysis_id,
            user_id=user_id,
            db=db
        )
        
        print(f"✅ 该分析记录的反馈历史，共 {len(history)} 条记录:")
        for i, feedback in enumerate(history, 1):
            print(f"  {i}. 情绪: {feedback['emotion']}")
            print(f"     评论: {feedback['comment'] if feedback['comment'] else '(无评论)'}")
            print(f"     时间: {feedback['created_at']}")
            print()
        
        # 4. 测试前端数据格式兼容性
        print("\n4. 测试前端数据格式兼容性...")
        
        # 模拟前端可能发送的各种数据格式
        test_cases = [
            {
                "name": "空评论",
                "data": {"analysis_id": analysis_id, "emotion": "happy", "comment": ""}
            },
            {
                "name": "None评论",
                "data": {"analysis_id": analysis_id, "emotion": "neutral", "comment": None}
            },
            {
                "name": "长评论",
                "data": {"analysis_id": analysis_id, "emotion": "sad", "comment": "这是一个很长的评论" * 10}
            },
            {
                "name": "特殊字符",
                "data": {"analysis_id": analysis_id, "emotion": "happy", "comment": "包含特殊字符：😊🎉👍💯"}
            }
        ]
        
        for test_case in test_cases:
            print(f"  测试 {test_case['name']}...")
            data = test_case['data']
            
            result = await emotion_service.save_feedback(
                analysis_id=data["analysis_id"],
                user_id=user_id,
                emotion=data["emotion"],
                comment=data.get("comment", ""),
                db=db
            )
            
            if result.get("success"):
                print(f"    ✅ {test_case['name']} 测试通过")
            else:
                print(f"    ❌ {test_case['name']} 测试失败: {result}")
        
        # 5. 最终统计
        print("\n5. 最终统计...")
        
        final_result = await db.execute(
            text("SELECT COUNT(*) FROM emotion_feedback WHERE analysis_id = :analysis_id"),
            {"analysis_id": analysis_id}
        )
        total_count = final_result.scalar()
        
        print(f"✅ 该分析记录的总反馈数量: {total_count}")
        
        # 按情绪类型统计
        emotion_stats = await db.execute(
            text("""
                SELECT emotion, COUNT(*) as count 
                FROM emotion_feedback 
                WHERE analysis_id = :analysis_id 
                GROUP BY emotion 
                ORDER BY count DESC
            """),
            {"analysis_id": analysis_id}
        )
        
        print("📊 反馈情绪分布:")
        for row in emotion_stats.fetchall():
            print(f"  {row[0]}: {row[1]} 条")
        
        print("\n=== 前端到后端数据流测试完成 ===")
        print("✅ 所有测试场景都已验证")
        print("✅ 用户输入内容能够正确传递到数据库")
        print("✅ 前端反馈功能工作正常")
        
        break

if __name__ == "__main__":
    asyncio.run(test_frontend_backend_flow()) 