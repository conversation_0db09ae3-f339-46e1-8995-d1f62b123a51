#!/usr/bin/env python3
"""
测试分析记录的用户关系加载
"""
import asyncio
import sys
import uuid

sys.path.append('backend')

from backend.emotionai.core.database.session import get_async_db
from backend.emotionai.crud.crud_emotion_analysis import cemotion_analysis

async def test_analysis_user_relation():
    """测试分析记录的用户关系加载"""
    
    async for db in get_async_db():
        print("=== 测试分析记录的用户关系加载 ===")
        
        # 测试分析记录ID
        analysis_id = uuid.UUID("4b8c8cee-f731-4414-8829-4d7766591489")
        
        print(f"\n1. 获取分析记录...")
        try:
            analysis = await cemotion_analysis.get(db, id=analysis_id)
            if analysis:
                print(f"✅ 找到分析记录: {analysis.id}")
                print(f"   情绪: {analysis.emotion}")
                print(f"   置信度: {analysis.confidence}")
                print(f"   用户ID: {analysis.user_id}")
                
                # 检查用户关系
                try:
                    if hasattr(analysis, 'user') and analysis.user:
                        print(f"   用户名: {analysis.user.username}")
                        print(f"   用户邮箱: {analysis.user.email}")
                    else:
                        print(f"   ❌ 用户关系未加载")
                except Exception as e:
                    print(f"   ❌ 用户关系加载失败: {e}")
                    
                # 检查分析记录的所有属性
                print(f"\n2. 分析记录的所有属性:")
                for attr_name in dir(analysis):
                    if not attr_name.startswith('_'):
                        try:
                            attr_value = getattr(analysis, attr_name)
                            if not callable(attr_value):
                                print(f"   {attr_name}: {attr_value}")
                        except Exception as e:
                            print(f"   {attr_name}: 获取失败 - {e}")
                            
            else:
                print("❌ 分析记录不存在")
                
        except Exception as e:
            print(f"❌ 获取分析记录失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_analysis_user_relation()) 