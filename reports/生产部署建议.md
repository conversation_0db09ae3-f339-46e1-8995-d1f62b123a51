# 生产环境部署建议
_Exported on 2025/5/27 at GMT+8 5:50:29 from Cursor (0.50.7)_

---

**User**

如果要部署本项目到生产环境，请提出建议

---

**Cursor**

好的，部署一个包含前端、后端以及深度学习模型的复杂项目到生产环境，需要周全的考虑。以下是我为你提出的部署建议，分为几个关键方面：

**我的解决思路：**

1.  **基础设施与容器化**:
    *   **云平台选择**: 考虑使用 AWS、Azure 或 Google Cloud Platform (GCP)。它们提供托管数据库、缓存、容器编排、CDN 等服务，可以简化部署和管理。
    *   **容器化**:
        *   **Docker**: 为前端、后端和可能的独立模型服务分别创建 `Dockerfile`。这能确保环境一致性，简化依赖管理和部署流程。
        *   **容器编排**:
            *   **Docker Compose**: 适用于单机或小规模部署，可以方便地定义和运行多容器应用。
            *   **Kubernetes (K8s)**: 对于需要高可用、自动伸缩和复杂管理的大规模应用，K8s 是更合适的选择。云平台通常提供托管的 K8s 服务 (如 AWS EKS, Azure AKS, GCP GKE)。

2.  **后端部署 (FastAPI 应用)**:
    *   **ASGI 服务器**: 在 Docker 容器中，使用 `Gunicorn` 作为进程管理器，并指定 `Uvicorn` 作为 ASGI worker 来运行 FastAPI 应用。例如：`gunicorn -w 4 -k uvicorn.workers.UvicornWorker backend.main:app -b 0.0.0.0:8000`。
    *   **反向代理**: 部署 `Nginx` 或 `Traefik` 作为反向代理。
        *   处理 SSL/TLS 终止 (HTTPS)。
        *   负载均衡到后端的多个实例。
        *   可以配置缓存策略。
        *   服务前端静态文件（如果选择此方案）。
    *   **数据库 (PostgreSQL)**:
        *   使用云服务商提供的托管 PostgreSQL 服务（如 AWS RDS, Azure Database for PostgreSQL, Google Cloud SQL）。它们通常包含自动备份、故障转移、扩展等功能。
        *   配置强密码，限制网络访问。
        *   通过环境变量向后端应用提供数据库连接字符串。
    *   **缓存 (Redis)**:
        *   使用云服务商提供的托管 Redis 服务（如 AWS ElastiCache, Azure Cache for Redis, Google Cloud Memorystore）。
        *   通过环境变量向后端应用提供 Redis 连接信息。
    *   **配置管理**:
        *   **环境变量**: 所有敏感信息（数据库凭证、API 密钥、JWT 密钥等）和环境特定配置都应通过环境变量注入到容器中，而不是硬编码。
        *   **Secrets Management**: 对于高度敏感的信息，考虑使用云平台的秘密管理服务 (如 AWS Secrets Manager, Azure Key Vault, Google Secret Manager)。
    *   **深度学习模型 (`emotionai`)**:
        *   **集成部署**: 如果模型推理速度快、资源占用不高，且与核心业务逻辑紧密耦合，可以将模型加载逻辑直接集成在 FastAPI 应用中。启动应用时加载模型到内存。需要注意并发请求对模型推理的影响，可能需要限制并发数或使用异步处理。
        *   **独立模型服务**: 如果模型较大、推理耗时较长、或需要独立扩展 GPU 资源，建议将模型部署为一个独立的推理服务。
            *   **NVIDIA Triton Inference Server**: 支持多种框架 (PyTorch, TensorFlow, ONNX), HTTP/gRPC 接口, 动态批处理, 模型版本管理。
            *   **TorchServe**: PyTorch 官方的模型服务库。
            *   **BentoML / Seldon Core**: 更高层次的模型部署框架。
            *   这个独立服务可以通过内部网络被 FastAPI 后端调用。模型文件可以存储在对象存储 (如 S3) 中，并在服务启动时下载。

3.  **前端部署 (React 应用)**:
    *   **构建**: 在 CI/CD 流程中运行 `pnpm run build` (或等效命令) 来生成优化后的静态 HTML, CSS, JavaScript 文件。
    *   **托管**:
        *   **CDN + 对象存储**: 将构建好的静态文件上传到对象存储服务 (如 AWS S3, Azure Blob Storage, Google Cloud Storage)，并配置 CDN (如 AWS CloudFront, Azure CDN, Google Cloud CDN) 指向这些文件。这是推荐的做法，可以提供低延迟和高可用性。
        *   **Nginx**: 也可以配置后端的 Nginx 反向代理来服务这些静态文件。适用于较简单的部署。
    *   **API 地址配置**: 前端应用需要知道后端 API 的地址。这通常通过构建时的环境变量注入，或者在部署后通过配置文件注入到前端的某个 JavaScript 文件中。

4.  **CI/CD (持续集成/持续部署)**:
    *   利用项目中的 `.github/workflows` (GitHub Actions) 或其他 CI/CD 工具 (如 Jenkins, GitLab CI, CircleCI)。
    *   **流程**:
        1.  **代码提交/合并到主分支**: 触发 CI/CD 流水线。
        2.  **代码检查与测试**: 运行 linter (Ruff), 类型检查 (mypy), 单元测试 (pytest), 前端测试 (Jest)。
        3.  **构建**:
            *   后端: 构建 Docker 镜像，包含所有依赖和应用代码。
            *   前端: 构建静态资源文件。
        4.  **推送**: 将 Docker 镜像推送到容器镜像仓库 (如 Docker Hub, AWS ECR, Azure CR, Google Artifact Registry)。将前端静态文件推送到对象存储。
        5.  **部署**:
            *   **暂存环境 (Staging)**: 自动部署到与生产环境类似的暂存环境，进行最终测试和验证。
            *   **生产环境 (Production)**: 可以配置为手动触发部署，或在暂存验证通过后自动部署。部署过程可能涉及更新 Kubernetes deployments, ECS services, 或直接重启 Docker 容器（取决于编排方式）。数据库迁移 (`alembic upgrade head`) 应作为部署流程的一部分，在应用更新前或更新后执行，并做好回滚计划。

5.  **监控与日志**:
    *   **日志管理**:
        *   **后端**: 配置 FastAPI 应用将日志输出到 `stdout/stderr`。容器日志由编排系统收集，并转发到集中的日志管理系统 (如 ELK Stack - Elasticsearch, Logstash, Kibana; Grafana Loki; Splunk; Datadog; 或云平台提供的日志服务如 AWS CloudWatch Logs, Azure Monitor Logs, Google Cloud Logging)。
        *   **前端**: 可以捕获运行时错误并发送到后端 API 端点或第三方错误跟踪服务 (如 Sentry, Bugsnag)。
    *   **应用性能监控 (APM)**:
        *   使用 Prometheus + Grafana, Datadog, New Relic, Dynatrace 或云平台自带的 APM 工具。
        *   监控关键指标：API 延迟、错误率、吞吐量、数据库查询时间、Redis 命中率。
        *   对于深度学习模型，监控推理延迟、吞吐量、GPU 使用率（如果适用）。
    *   **系统监控**: 监控服务器/容器的 CPU、内存、磁盘I/O、网络流量。
    *   **告警**: 基于关键指标设置告警，及时发现和响应问题。

6.  **安全性**:
    *   **HTTPS**: 必须为所有外部流量启用 HTTPS。可以使用 Let's Encrypt 获取免费证书，或使用云平台提供的证书管理服务。
    *   **Web 应用防火墙 (WAF)**: 在应用入口处（如 CDN 或负载均衡器层面）部署 WAF (如 AWS WAF, Azure Application Gateway with WAF, Cloudflare WAF) 来防御常见的 Web 攻击 (SQL注入, XSS 等)。
    *   **依赖项安全**: 定期使用工具 (如 `pnpm audit`, `pip-audit`, Snyk, GitHub Dependabot) 扫描前后端依赖库的安全漏洞，并及时更新。
    *   **输入验证**: 后端应严格验证所有来自客户端的输入。FastAPI 的 Pydantic 模型对此有很好的支持。
    *   **最小权限原则**: 数据库用户、API 密钥等应配置最小必要权限。
    *   **网络安全**: 使用虚拟私有云 (VPC) 和安全组/防火墙规则严格控制服务之间的网络访问。例如，数据库和 Redis 只允许从后端应用所在的网络访问。
    *   **CORS**: 在 FastAPI 后端正确配置跨源资源共享 (CORS)，只允许你的前端域名访问。

7.  **数据库管理**:
    *   **迁移**: 使用 `Alembic` 管理数据库 schema 的演进。生产环境的迁移应谨慎操作，最好在维护窗口进行，并确保有测试过的回滚方案。
    *   **备份与恢复**: 确保配置了自动化的每日备份，并定期测试恢复流程。云托管数据库通常内置此功能。
    *   **性能调优**: 定期分析慢查询，根据需要添加索引。监控数据库连接数。
    *   **连接池**: FastAPI 应用应使用数据库连接池 (如 SQLAlchemy 默认提供的)。

8.  **扩展性与可靠性**:
    *   **水平扩展**: 设计后端应用和模型服务为无状态的，以便可以通过运行多个副本来进行水平扩展。负载均衡器会将流量分发到这些副本。
    *   **健康检查**: 后端应用和模型服务应提供健康检查端点 (例如 `/health` 或 `/ping`)。容器编排系统会使用这些端点来确定实例是否健康，并在实例不健康时自动替换。
    *   **资源分配**: 根据预期的负载为容器分配合适的 CPU 和内存资源。对于模型推理，如果使用 GPU，确保 GPU 资源得到妥善管理和分配。

**推荐的初步行动计划：**

1.  **选择云平台和容器编排工具**: 这是基础决策，会影响后续许多选择。
2.  **编写 Dockerfiles**: 为前端、后端（可能还有独立模型服务）创建 Dockerfiles。
3.  **设置 CI/CD 基础**: 开始搭建自动化构建和测试流程。
4.  **配置开发/暂存环境**: 在本地或云上先搭建一个与生产类似的开发/暂存环境，用于测试部署流程。

这些建议比较全面，具体实施时可以根据你的项目规模、预算和团队熟悉度进行调整。

你希望我针对以上哪个方面进行更详细的说明，或者对某个特定的技术选型有疑问吗？例如，你对深度学习模型的部署方式（集成在 FastAPI vs. 独立服务）有什么初步的想法？

---

**User**

请检查本项目，并提出生产部署建议

---

**Cursor**

我来检查一下项目的具体结构和配置，然后提出针对性的生产部署建议。

基于对项目的详细检查，我现在可以为你提供针对性的生产部署建议。这是一个复杂的多模态情感分析系统，包含前端React应用、后端FastAPI服务以及多个深度学习模型。

## 🚀 生产部署建议

### **1. 容器化部署方案**

**当前状态分析：**
- ❌ 项目缺少Docker配置文件
- ❌ 没有容器编排配置
- ✅ 有基础的CI/CD配置（GitLab CI）
- ✅ 项目结构清晰，前后端分离

**建议创建的Docker配置：**

#### 1.1 后端Dockerfile
```dockerfile
# backend/Dockerfile
FROM python:3.10-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgcc-s1 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制依赖文件
COPY requirements.txt .
COPY requirements-core.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app
ENV MEDIAPIPE_GPU=0
ENV MEDIAPIPE_USE_CUSTOM_OPENCV=0
ENV MEDIAPIPE_DISABLE_GPU=1

# 创建必要目录
RUN mkdir -p logs uploads/original uploads/marked

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "emotionai.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 1.2 前端Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN pnpm add -g pnpm
RUN pnpm install --frozen-lockfile

COPY . .
RUN pnpm run build

# 生产环境
FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 1.3 Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: emotion_ai
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=emotion_ai
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - ./models:/app/models

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  postgres_data:
  redis_data:
```

### **2. 深度学习模型部署优化**

**当前状态分析：**
- ✅ 模型已集成在后端服务中
- ✅ 支持多模型融合（EmotionEnsemble）
- ✅ 有模型缓存机制
- ⚠️ 模型文件较大，需要优化加载

**建议优化方案：**

#### 2.1 模型服务分离（推荐）
```python
# 创建独立的模型推理服务
# backend/model_service.py
from fastapi import FastAPI
from emotionai.services.emotion_service import EmotionService

model_app = FastAPI(title="Emotion Model Service")
emotion_service = EmotionService()

@model_app.post("/predict")
async def predict_emotion(image_data: str):
    # 模型推理逻辑
    pass
```

#### 2.2 模型文件管理
```dockerfile
# 模型服务Dockerfile
FROM python:3.10-slim

# 安装模型依赖
RUN pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu

# 下载预训练模型
RUN python -c "
from transformers import AutoModel
AutoModel.from_pretrained('microsoft/DialoGPT-medium')
"

WORKDIR /app
COPY model_service.py .
CMD ["uvicorn", "model_service:model_app", "--host", "0.0.0.0", "--port", "8001"]
```

### **3. 生产环境配置管理**

#### 3.1 环境变量配置
```bash
# .env.production
ENVIRONMENT=production
DEBUG=false
SQL_DEBUG=false

# 数据库配置
POSTGRES_SERVER=your-postgres-host
POSTGRES_USER=your-db-user
POSTGRES_PASSWORD=your-secure-password
POSTGRES_DB=emotion_ai

# Redis配置
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# 安全配置
SECRET_KEY=your-super-secret-key
JWT_SECRET_KEY=your-jwt-secret

# CORS配置
BACKEND_CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# 文件上传配置
MAX_UPLOAD_SIZE=52428800  # 50MB
UPLOAD_DIR=/app/uploads

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=/app/logs
```

#### 3.2 Kubernetes部署配置
```yaml
# k8s/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: emotion-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: emotion-backend
  template:
    metadata:
      labels:
        app: emotion-backend
    spec:
      containers:
      - name: backend
        image: your-registry/emotion-backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### **4. CI/CD流程优化**

#### 4.1 增强的GitLab CI配置
```yaml
# .gitlab-ci.yml (增强版)
stages:
  - lint
  - test
  - build
  - deploy

variables:
  DOCKER_REGISTRY: your-registry.com
  PYTHON_VERSION: "3.10"

# 代码质量检查
lint:
  stage: lint
  image: python:${PYTHON_VERSION}
  script:
    - cd backend
    - pip install ruff black mypy
    - ruff check emotionai
    - black --check emotionai
    - mypy emotionai

# 单元测试
test:
  stage: test
  image: python:${PYTHON_VERSION}
  services:
    - postgres:15
    - redis:7
  variables:
    POSTGRES_DB: test_emotion_ai
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
  script:
    - cd backend
    - pip install -r requirements.txt
    - pytest tests/ --cov=emotionai

# 构建Docker镜像
build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t $DOCKER_REGISTRY/emotion-backend:$CI_COMMIT_SHA ./backend
    - docker build -t $DOCKER_REGISTRY/emotion-frontend:$CI_COMMIT_SHA ./frontend
    - docker push $DOCKER_REGISTRY/emotion-backend:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/emotion-frontend:$CI_COMMIT_SHA

# 部署到生产环境
deploy:
  stage: deploy
  image: kubectl:latest
  script:
    - kubectl set image deployment/emotion-backend backend=$DOCKER_REGISTRY/emotion-backend:$CI_COMMIT_SHA
    - kubectl set image deployment/emotion-frontend frontend=$DOCKER_REGISTRY/emotion-frontend:$CI_COMMIT_SHA
  only:
    - main
```

### **5. 监控和日志管理**

#### 5.1 Prometheus监控配置
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'emotion-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
```

#### 5.2 日志聚合配置
```yaml
# logging/fluentd.conf
<source>
  @type forward
  port 24224
</source>

<match emotion.**>
  @type elasticsearch
  host elasticsearch
  port 9200
  index_name emotion-logs
</match>
```

### **6. 安全性配置**

#### 6.1 Nginx安全配置
```nginx
# frontend/nginx.conf
server {
    listen 80;
    server_name your-domain.com;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # 限制请求大小
    client_max_body_size 50M;
    
    # 静态文件缓存
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### **7. 数据库优化**

#### 7.1 PostgreSQL生产配置
```sql
-- 性能优化配置
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- 创建必要索引
CREATE INDEX CONCURRENTLY idx_emotion_analysis_user_id ON emotion_analysis(user_id);
CREATE INDEX CONCURRENTLY idx_emotion_analysis_created_at ON emotion_analysis(created_at);
CREATE INDEX CONCURRENTLY idx_emotion_feedback_analysis_id ON emotion_feedback(analysis_id);
```

### **8. 部署检查清单**

#### 8.1 部署前检查
- [ ] 所有环境变量已正确配置
- [ ] 数据库迁移已执行
- [ ] SSL证书已配置
- [ ] 监控系统已部署
- [ ] 备份策略已实施
- [ ] 负载测试已完成

#### 8.2 部署后验证
- [ ] 健康检查端点正常响应
- [ ] 日志正常输出
- [ ] 监控指标正常
- [ ] 前端页面正常加载
- [ ] API接口正常工作
- [ ] 模型推理功能正常

### **9. 扩展性建议**

#### 9.1 水平扩展配置
```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: emotion-backend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: emotion-backend
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

这个部署方案考虑了你项目的具体特点：
1. **深度学习模型集成**：提供了模型服务分离的选项
2. **前后端分离架构**：分别优化了前后端的部署
3. **现有技术栈**：基于FastAPI、React、PostgreSQL、Redis
4. **安全性**：包含了完整的安全配置
5. **可扩展性**：支持水平扩展和负载均衡

你希望我详细展开哪个部分，或者对某个特定的部署环节有疑问吗？

```dockerfile
# backend/Dockerfile
FROM python:3.10-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgcc-s1 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制依赖文件
COPY requirements.txt .
COPY requirements-core.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app
ENV MEDIAPIPE_GPU=0
ENV MEDIAPIPE_USE_CUSTOM_OPENCV=0
ENV MEDIAPIPE_DISABLE_GPU=1

# 创建必要目录
RUN mkdir -p logs uploads/original uploads/marked

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "emotionai.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```dockerfile
# frontend/Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN pnpm add -g pnpm
RUN pnpm install --frozen-lockfile

COPY . .
RUN pnpm run build

# 生产环境
FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: emotion_ai
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=emotion_ai
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - ./models:/app/models

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  postgres_data:
  redis_data:
```

```python
# 创建独立的模型推理服务
# backend/model_service.py
from fastapi import FastAPI
from emotionai.services.emotion_service import EmotionService

model_app = FastAPI(title="Emotion Model Service")
emotion_service = EmotionService()

@model_app.post("/predict")
async def predict_emotion(image_data: str):
    # 模型推理逻辑
    pass
```

```dockerfile
# 模型服务Dockerfile
FROM python:3.10-slim

# 安装模型依赖
RUN pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu

# 下载预训练模型
RUN python -c "
from transformers import AutoModel
AutoModel.from_pretrained('microsoft/DialoGPT-medium')
"

WORKDIR /app
COPY model_service.py .
CMD ["uvicorn", "model_service:model_app", "--host", "0.0.0.0", "--port", "8001"]
```

```shellscript
# .env.production
ENVIRONMENT=production
DEBUG=false
SQL_DEBUG=false

# 数据库配置
POSTGRES_SERVER=your-postgres-host
POSTGRES_USER=your-db-user
POSTGRES_PASSWORD=your-secure-password
POSTGRES_DB=emotion_ai

# Redis配置
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# 安全配置
SECRET_KEY=your-super-secret-key
JWT_SECRET_KEY=your-jwt-secret

# CORS配置
BACKEND_CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# 文件上传配置
MAX_UPLOAD_SIZE=52428800  # 50MB
UPLOAD_DIR=/app/uploads

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=/app/logs
```

```yaml
# k8s/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: emotion-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: emotion-backend
  template:
    metadata:
      labels:
        app: emotion-backend
    spec:
      containers:
      - name: backend
        image: your-registry/emotion-backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

```yaml
# .gitlab-ci.yml (增强版)
stages:
  - lint
  - test
  - build
  - deploy

variables:
  DOCKER_REGISTRY: your-registry.com
  PYTHON_VERSION: "3.10"

# 代码质量检查
lint:
  stage: lint
  image: python:${PYTHON_VERSION}
  script:
    - cd backend
    - pip install ruff black mypy
    - ruff check emotionai
    - black --check emotionai
    - mypy emotionai

# 单元测试
test:
  stage: test
  image: python:${PYTHON_VERSION}
  services:
    - postgres:15
    - redis:7
  variables:
    POSTGRES_DB: test_emotion_ai
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
  script:
    - cd backend
    - pip install -r requirements.txt
    - pytest tests/ --cov=emotionai

# 构建Docker镜像
build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t $DOCKER_REGISTRY/emotion-backend:$CI_COMMIT_SHA ./backend
    - docker build -t $DOCKER_REGISTRY/emotion-frontend:$CI_COMMIT_SHA ./frontend
    - docker push $DOCKER_REGISTRY/emotion-backend:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/emotion-frontend:$CI_COMMIT_SHA

# 部署到生产环境
deploy:
  stage: deploy
  image: kubectl:latest
  script:
    - kubectl set image deployment/emotion-backend backend=$DOCKER_REGISTRY/emotion-backend:$CI_COMMIT_SHA
    - kubectl set image deployment/emotion-frontend frontend=$DOCKER_REGISTRY/emotion-frontend:$CI_COMMIT_SHA
  only:
    - main
```

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'emotion-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
```

```yaml
# logging/fluentd.conf
<source>
  @type forward
  port 24224
</source>

<match emotion.**>
  @type elasticsearch
  host elasticsearch
  port 9200
  index_name emotion-logs
</match>
```

```plaintext
# frontend/nginx.conf
server {
    listen 80;
    server_name your-domain.com;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # 限制请求大小
    client_max_body_size 50M;
    
    # 静态文件缓存
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

```sql
-- 性能优化配置
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- 创建必要索引
CREATE INDEX CONCURRENTLY idx_emotion_analysis_user_id ON emotion_analysis(user_id);
CREATE INDEX CONCURRENTLY idx_emotion_analysis_created_at ON emotion_analysis(created_at);
CREATE INDEX CONCURRENTLY idx_emotion_feedback_analysis_id ON emotion_feedback(analysis_id);
```

```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: emotion-backend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: emotion-backend
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

