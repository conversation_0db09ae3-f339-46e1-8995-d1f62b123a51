# ===========================================
# 情感分析AI系统 - 环境变量配置示例
# ===========================================

# 应用基础配置
ENVIRONMENT=development
DEBUG=true
PROJECT_NAME="Emotion AI System"
VERSION=1.0.0

# 服务端口配置
BACKEND_PORT=8000
FRONTEND_PORT=3000
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_USER=dom
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=emotion_ai

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=1440
REFRESH_TOKEN_EXPIRE_DAYS=30

# CORS配置
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,https://yourdomain.com

# 文件上传配置
MAX_UPLOAD_SIZE=52428800
UPLOAD_DIR=./uploads

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=./logs

# 深度学习模型配置
MEDIAPIPE_GPU=0
MEDIAPIPE_USE_CUSTOM_OPENCV=0
MEDIAPIPE_DISABLE_GPU=1
HF_DATASETS_OFFLINE=1
TRANSFORMERS_OFFLINE=1
NO_GIT=1

# 前端配置
VITE_API_URL=http://localhost:8000
VITE_PUBLIC_URL=
VITE_LOCAL_IP=*************
VITE_USE_HTTPS=false
NODE_ENV=development

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
SMTP_SSL=false
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME="Emotion AI System"

# 监控配置（可选）
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090

# Docker构建配置
BUILD_TARGET=development
DOCKER_REGISTRY=your-registry.com

# 生产环境特定配置（仅在生产环境使用）
# ENVIRONMENT=production
# DEBUG=false
# LOG_LEVEL=WARNING
# BUILD_TARGET=production 