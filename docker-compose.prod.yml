version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ema-backend-prod
    command: uvicorn emotionai.main:app --host 0.0.0.0 --port 8000 --workers 4 --timeout-keep-alive 300
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - ./models:/app/models
      - ./data:/app/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: ema-nginx-prod
    ports:
      - "80:80"
    volumes:
      - ./frontend/dist:/usr/share/nginx/html:ro
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/conf.d:/etc/nginx/conf.d:ro
    depends_on:
      - backend
    restart: unless-stopped