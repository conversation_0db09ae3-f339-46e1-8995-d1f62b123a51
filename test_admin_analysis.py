#!/usr/bin/env python3
"""
测试管理员分析页面的用户名显示
"""

import asyncio
import httpx
import json
from datetime import datetime

# API 基础配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

async def test_admin_analysis():
    """测试管理员分析API"""
    print("🧪 测试管理员分析API...")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        try:
            # 尝试访问管理员分析API
            response = await client.get(f"{API_BASE}/admin/analysis/")
            
            print(f"📥 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                analyses = result.get('items', [])
                total = result.get('total', 0)
                
                print(f"✅ 获取分析记录成功")
                print(f"   总记录数: {total}")
                print(f"   当前页记录数: {len(analyses)}")
                
                if analyses:
                    print(f"\n📋 分析记录详情:")
                    for i, analysis in enumerate(analyses[:3], 1):  # 只显示前3条
                        print(f"   记录 {i}:")
                        print(f"     ID: {analysis.get('id', 'N/A')}")
                        print(f"     用户名: {analysis.get('username', 'N/A')}")
                        print(f"     情绪: {analysis.get('emotion', 'N/A')}")
                        print(f"     置信度: {analysis.get('confidence', 'N/A')}")
                        print(f"     创建时间: {analysis.get('created_at', 'N/A')}")
                        print()
                        
                        # 检查用户名是否正确显示
                        username = analysis.get('username')
                        if username and username != "未知用户" and username is not None:
                            print(f"     ✅ 用户名显示正常: {username}")
                        else:
                            print(f"     ❌ 用户名显示异常: {username}")
                else:
                    print("   📝 没有找到分析记录")
                    
            elif response.status_code == 401:
                print(f"❌ 认证失败: 需要管理员权限")
                print(f"   提示: 这是正常的，因为API需要管理员认证")
            elif response.status_code == 403:
                print(f"❌ 权限不足: 需要超级管理员权限")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

async def test_database_relationship():
    """测试数据库关系"""
    print("\n🔗 测试数据库关系...")
    print("=" * 50)
    
    try:
        import sys
        sys.path.append('/Volumes/acasis/ema2_20250417/backend')
        
        from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
        from sqlalchemy.orm import sessionmaker
        from sqlalchemy import select
        from emotionai.core.config import settings
        from emotionai.models.ml.emotion import EmotionAnalysis
        
        # 创建异步数据库引擎
        engine = create_async_engine(settings.DATABASE_URL)
        async_session = sessionmaker(engine, class_=AsyncSession)
        
        async with async_session() as session:
            # 查询分析记录并加载用户关系
            statement = select(EmotionAnalysis).limit(3)
            result = await session.execute(statement)
            analyses = result.scalars().all()
            
            print(f"✅ 查询到 {len(analyses)} 条分析记录")
            
            for i, analysis in enumerate(analyses, 1):
                print(f"   记录 {i}:")
                print(f"     ID: {analysis.id}")
                print(f"     用户ID: {analysis.user_id}")
                
                # 检查用户关系
                try:
                    if hasattr(analysis, 'user') and analysis.user:
                        print(f"     用户对象: {analysis.user}")
                        print(f"     用户名: {analysis.user.username}")
                        print(f"     ✅ 用户关系正常")
                    else:
                        print(f"     ❌ 用户关系缺失")
                except Exception as e:
                    print(f"     ❌ 访问用户关系出错: {e}")
                print()
                
        await engine.dispose()
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")

async def main():
    """主函数"""
    print("🚀 开始测试管理员分析页面用户名显示")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    await test_admin_analysis()
    await test_database_relationship()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成！")
    print("\n📝 修复说明:")
    print("   - 重新添加了EmotionAnalysis模型中的user关系")
    print("   - 使用字符串引用避免循环导入问题")
    print("   - 重新启用了CRUD中的user关系加载")
    print("   - 恢复了API中的用户名获取逻辑")

if __name__ == "__main__":
    asyncio.run(main()) 