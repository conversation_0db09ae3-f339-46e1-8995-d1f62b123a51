#!/usr/bin/env python3
"""
年龄模型性能检查脚本

检查项目中使用的年龄模型类型、权重配置和预测性能
"""

import os
import sys
import logging
import numpy as np
import cv2
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
backend_path = project_root / "backend"
sys.path.insert(0, str(backend_path))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_age_models():
    """检查年龄模型配置"""
    print("=" * 60)
    print("年龄模型配置检查")
    print("=" * 60)
    
    try:
        # 1. 检查FaceAttributesEnsemble配置
        from emotionai.core.ml.face_attributes_ensemble import FaceAttributesEnsemble
        
        face_ensemble = FaceAttributesEnsemble()
        print("\n1. FaceAttributesEnsemble 配置:")
        print(f"   权重配置: {face_ensemble.weights}")
        
        # 检查年龄权重
        age_weights = face_ensemble.weights.get("age", {})
        print(f"   年龄模型权重:")
        for model, weight in age_weights.items():
            print(f"     - {model}: {weight}")
            
    except Exception as e:
        print(f"   FaceAttributesEnsemble 检查失败: {e}")
    
    try:
        # 2. 检查ImprovedAgeEnsemble配置
        from emotionai.core.ml.improved_age_ensemble import ImprovedAgeEnsemble
        
        age_ensemble = ImprovedAgeEnsemble()
        print("\n2. ImprovedAgeEnsemble 配置:")
        model_info = age_ensemble.get_model_info()
        print(f"   已加载模型: {model_info['loaded_models']}")
        print(f"   模型权重: {model_info['model_weights']}")
        print(f"   设备: {model_info['device']}")
        print(f"   模型目录: {model_info['model_dir']}")
        
    except Exception as e:
        print(f"   ImprovedAgeEnsemble 检查失败: {e}")

def check_deepface_model():
    """检查DeepFace模型"""
    print("\n3. DeepFace 模型检查:")
    
    try:
        from emotionai.core.ml.deepface_wrapper import DeepFaceWrapper
        
        deepface = DeepFaceWrapper()
        print(f"   模型目录: {deepface.models_dir}")
        print(f"   检测后端: {deepface.detector_backend}")
        
        # 检查模型文件
        model_files = [
            "age_model_weights.h5",
            "gender_model_weights.h5", 
            "race_model_single_batch.h5",
            "facial_expression_model_weights.h5"
        ]
        
        weights_dir = Path(deepface.models_dir) / ".deepface" / "weights"
        print(f"   权重目录: {weights_dir}")
        
        for model_file in model_files:
            model_path = weights_dir / model_file
            if model_path.exists():
                size_mb = model_path.stat().st_size / (1024 * 1024)
                print(f"     ✓ {model_file}: {size_mb:.1f} MB")
            else:
                print(f"     ✗ {model_file}: 不存在")
                
    except Exception as e:
        print(f"   DeepFace 检查失败: {e}")

def test_age_prediction():
    """测试年龄预测性能"""
    print("\n4. 年龄预测测试:")
    
    # 创建测试图像（随机噪声，仅用于测试）
    test_image = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
    
    try:
        # 测试FaceAttributesEnsemble
        from emotionai.core.ml.face_attributes_ensemble import FaceAttributesEnsemble
        
        face_ensemble = FaceAttributesEnsemble()
        print("\n   FaceAttributesEnsemble 测试:")
        
        # 测试年龄预测
        age_result = face_ensemble.predict_age(test_image)
        print(f"     年龄预测结果: {age_result}")
        
        # 测试完整分析
        full_result = face_ensemble.analyze(test_image)
        print(f"     完整分析结果: {full_result}")
        
    except Exception as e:
        print(f"     FaceAttributesEnsemble 测试失败: {e}")
    
    try:
        # 测试ImprovedAgeEnsemble
        from emotionai.core.ml.improved_age_ensemble import ImprovedAgeEnsemble
        
        age_ensemble = ImprovedAgeEnsemble()
        print("\n   ImprovedAgeEnsemble 测试:")
        
        age_result = age_ensemble.predict_age(test_image)
        print(f"     年龄预测结果: {age_result}")
        
    except Exception as e:
        print(f"     ImprovedAgeEnsemble 测试失败: {e}")

def analyze_age_correction():
    """分析年龄修正逻辑"""
    print("\n5. 年龄修正逻辑分析:")
    
    # 测试不同年龄的修正效果
    test_ages = [12, 18, 25, 35, 45, 55, 65, 75]
    
    try:
        from emotionai.core.ml.improved_age_ensemble import ImprovedAgeEnsemble
        
        age_ensemble = ImprovedAgeEnsemble()
        
        print("   DeepFace年龄修正测试:")
        print("   原始年龄 -> 修正年龄")
        for age in test_ages:
            corrected = age_ensemble._correct_deepface_age(age)
            print(f"     {age:2d} -> {corrected:5.1f} (调整系数: {corrected/age:.2f})")
            
    except Exception as e:
        print(f"   年龄修正测试失败: {e}")
    
    try:
        from emotionai.core.ml.pytorch_emotion_analyzer import correct_age_prediction
        
        print("\n   通用年龄修正测试:")
        print("   原始年龄 -> 修正年龄 (低置信度)")
        for age in test_ages:
            corrected = correct_age_prediction(age, confidence=0.2)
            print(f"     {age:2d} -> {corrected:5.1f}")
            
    except Exception as e:
        print(f"   通用年龄修正测试失败: {e}")

def check_model_weights_distribution():
    """检查模型权重分布"""
    print("\n6. 模型权重分布分析:")
    
    try:
        from emotionai.core.ml.face_attributes_ensemble import FaceAttributesEnsemble
        
        face_ensemble = FaceAttributesEnsemble()
        age_weights = face_ensemble.weights.get("age", {})
        
        print("   FaceAttributesEnsemble 年龄权重:")
        total_weight = sum(age_weights.values())
        for model, weight in age_weights.items():
            percentage = (weight / total_weight) * 100 if total_weight > 0 else 0
            print(f"     {model}: {weight:.2f} ({percentage:.1f}%)")
            
    except Exception as e:
        print(f"   FaceAttributesEnsemble 权重检查失败: {e}")
    
    try:
        from emotionai.core.ml.improved_age_ensemble import ImprovedAgeEnsemble
        
        age_ensemble = ImprovedAgeEnsemble()
        model_weights = age_ensemble.model_weights
        
        print("\n   ImprovedAgeEnsemble 模型权重:")
        total_weight = sum(model_weights.values())
        for model, weight in model_weights.items():
            percentage = (weight / total_weight) * 100 if total_weight > 0 else 0
            print(f"     {model}: {weight:.3f} ({percentage:.1f}%)")
            
    except Exception as e:
        print(f"   ImprovedAgeEnsemble 权重检查失败: {e}")

def main():
    """主函数"""
    print("年龄模型性能检查工具")
    print("检查项目中年龄预测模型的配置、权重和性能")
    
    # 检查各个组件
    check_age_models()
    check_deepface_model()
    test_age_prediction()
    analyze_age_correction()
    check_model_weights_distribution()
    
    print("\n" + "=" * 60)
    print("检查完成")
    print("=" * 60)
    
    # 提供优化建议
    print("\n年龄预测优化建议:")
    print("1. 当前使用的主要是DeepFace和InsightFace模型")
    print("2. DeepFace模型倾向于预测偏年轻的年龄，已应用修正系数")
    print("3. InsightFace在年龄预测中权重较高(0.9)，DeepFace权重较低(0.1)")
    print("4. 可以考虑:")
    print("   - 调整模型权重分配")
    print("   - 优化年龄修正算法")
    print("   - 添加更多训练数据")
    print("   - 使用更先进的年龄预测模型")

if __name__ == "__main__":
    main() 