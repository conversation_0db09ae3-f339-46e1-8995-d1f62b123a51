import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
sys.path.append(str(project_root))
sys.path.append(str(project_root / "backend"))

# 导入邮件发送函数
from emotionai.core.utils.email_utils import send_email_async

async def test_smtp():
    """测试SMTP服务"""
    print("开始测试SMTP服务...")
    import datetime
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    try:
        await send_email_async(
            email_to='<EMAIL>',  # 接收邮件的地址
            subject_template='EmotionAI系统测试邮件',
            html_template=f'<p>这是一封来自EmotionAI系统的测试邮件</p><p>如果您收到此邮件，说明SMTP服务配置正确。</p><p>发送时间：{current_time}</p>'
        )
        print("邮件发送成功！请检查**************的收件箱。")
    except Exception as e:
        print(f"邮件发送失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_smtp())
