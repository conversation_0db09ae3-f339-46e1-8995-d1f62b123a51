#!/usr/bin/env python3
"""
测试反馈功能的最终版本
验证新的反馈映射关系：good/average/bad
"""

import asyncio
import httpx
import json
from datetime import datetime

# API 基础配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

async def test_feedback_mapping():
    """测试新的反馈映射关系"""
    print("🧪 测试新的反馈映射关系...")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        # 1. 获取最新的分析记录
        print("📋 获取最新的分析记录...")
        try:
            # 使用正确的管理员API端点
            response = await client.get(f"{API_BASE}/admin/analysis/")
            if response.status_code == 200:
                result = response.json()
                analyses = result.get('items', [])
                if analyses:
                    latest_analysis = analyses[0]
                    analysis_id = latest_analysis['id']
                    print(f"✅ 找到分析记录 ID: {analysis_id}")
                else:
                    print("❌ 没有找到分析记录")
                    return
            else:
                print(f"❌ 获取分析记录失败: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return
        
        # 2. 测试新的反馈类型
        feedback_tests = [
            {
                "type": "good",
                "comment": "这个分析很准确，我确实感到很开心！",
                "expected_emotion": "good"
            },
            {
                "type": "average", 
                "comment": "分析结果还可以，但不是完全准确。",
                "expected_emotion": "average"
            },
            {
                "type": "bad",
                "comment": "分析结果不太对，我的情绪不是这样的。",
                "expected_emotion": "bad"
            }
        ]
        
        print("\n🔄 测试新的反馈映射...")
        for i, test in enumerate(feedback_tests, 1):
            print(f"\n--- 测试 {i}: {test['type']} ---")
            
            feedback_data = {
                "analysis_id": analysis_id,
                "emotion": test["expected_emotion"],  # 直接使用映射后的值
                "comment": test["comment"]
            }
            
            try:
                response = await client.post(
                    f"{API_BASE}/emotion/feedback",
                    json=feedback_data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 反馈提交成功")
                    print(f"   按钮类型: {test['type']}")
                    print(f"   存储情绪: {result.get('emotion', 'N/A')}")
                    print(f"   评论内容: {result.get('comment', 'N/A')}")
                    print(f"   反馈ID: {result.get('id', 'N/A')}")
                    
                    # 验证存储的情绪是否正确
                    if result.get('emotion') == test['expected_emotion']:
                        print(f"✅ 情绪映射正确: {test['type']} → {test['expected_emotion']}")
                    else:
                        print(f"❌ 情绪映射错误: 期望 {test['expected_emotion']}, 实际 {result.get('emotion')}")
                        
                else:
                    print(f"❌ 反馈提交失败: {response.status_code}")
                    print(f"   错误信息: {response.text}")
                    
            except Exception as e:
                print(f"❌ 请求异常: {e}")
        
        # 3. 验证反馈历史
        print(f"\n📜 验证反馈历史...")
        try:
            # 使用正确的反馈历史API端点
            response = await client.get(f"{API_BASE}/emotion/feedback/history/{analysis_id}")
            if response.status_code == 200:
                feedback_history = response.json()
                print(f"✅ 获取到 {len(feedback_history)} 条反馈记录")
                
                for feedback in feedback_history[-3:]:  # 显示最新的3条
                    print(f"   - 情绪: {feedback.get('emotion')}")
                    print(f"     评论: {feedback.get('comment', '无评论')[:50]}...")
                    print(f"     时间: {feedback.get('created_at', 'N/A')}")
                    print()
            else:
                print(f"❌ 获取反馈历史失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 获取反馈历史异常: {e}")

async def test_button_mapping():
    """测试按钮映射的完整流程"""
    print("\n🎯 测试按钮映射的完整流程...")
    print("=" * 50)
    
    # 模拟前端的映射逻辑
    button_mapping = {
        "good": "good",        # 好评 → good
        "average": "average",  # 一般 → average  
        "bad": "bad",          # 不好 → bad
    }
    
    # 显示映射关系
    print("📋 按钮到数据库的映射关系:")
    print("   好评按钮 (Good) → good")
    print("   一般按钮 (Average) → average") 
    print("   不好按钮 (Bad) → bad")
    
    # 验证映射逻辑
    print("\n✅ 映射关系验证:")
    for button_type, db_emotion in button_mapping.items():
        print(f"   {button_type} → {db_emotion} ✓")

async def main():
    """主函数"""
    print("🚀 开始测试反馈功能的新映射关系")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    await test_button_mapping()
    await test_feedback_mapping()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成！")
    print("\n📝 总结:")
    print("   - 按钮标签与数据库存储现在保持一致")
    print("   - 好评按钮 → good (而不是 happy)")
    print("   - 一般按钮 → average (而不是 neutral)")
    print("   - 不好按钮 → bad (而不是 sad)")
    print("   - 前端显示逻辑已更新以支持新的映射")

if __name__ == "__main__":
    asyncio.run(main()) 