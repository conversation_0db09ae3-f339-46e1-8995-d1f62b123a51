# ===================================
# 情感分析AI系统 - Redis配置
# ===================================

# 网络配置
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 60

# 通用配置
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""

# 数据库配置
databases 16
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# 复制配置
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5

# 安全配置
# requirepass your_password_here

# 内存管理
maxmemory-policy allkeys-lru
maxmemory-samples 5

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端配置
maxclients 10000

# 高级配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000

# 活跃重新哈希
activerehashing yes

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 频率
hz 10

# AOF重写
aof-rewrite-incremental-fsync yes 