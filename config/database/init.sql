-- ===================================
-- 情感分析AI系统 - 数据库初始化脚本
-- ===================================

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 创建数据库（如果不存在）
-- 注意：这个脚本在容器启动时运行，数据库已经存在

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建用户（如果需要）
-- 注意：在Docker环境中，用户通常通过环境变量创建

-- 授权
GRANT ALL PRIVILEGES ON DATABASE emotion_ai TO dom;

-- 创建基础表结构（如果需要）
-- 注意：实际的表结构由Alembic迁移管理

-- 插入初始数据（如果需要）
-- 例如：管理员用户、系统配置等

-- 创建索引（性能优化）
-- 这些索引将在Alembic迁移中创建，这里仅作为参考

-- 设置数据库参数
ALTER DATABASE emotion_ai SET log_statement = 'all';
ALTER DATABASE emotion_ai SET log_min_duration_statement = 1000;

-- 完成初始化
SELECT 'Database initialization completed' AS status; 