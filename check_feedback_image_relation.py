#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psycopg2
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接参数
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': os.getenv('DB_PORT', '5432'),
    'database': os.getenv('DB_NAME', 'emotion_ai'),
    'user': os.getenv('DB_USER', 'dom'),
    'password': os.getenv('DB_PASSWORD', '')
}

try:
    # 连接数据库
    conn = psycopg2.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('=== 检查反馈记录与图片的对应关系 ===')
    
    # 查询反馈记录及其对应的分析记录信息
    cursor.execute("""
        SELECT 
            ef.id as feedback_id,
            ef.emotion as feedback_emotion,
            ef.comment as feedback_comment,
            ef.created_at as feedback_time,
            ea.id as analysis_id,
            ea.input_type,
            ea.input_path,
            ea.emotion as original_emotion,
            ea.confidence,
            ea.created_at as analysis_time
        FROM emotion_feedback ef
        JOIN emotion_analysis ea ON ef.analysis_id = ea.id
        ORDER BY ef.created_at DESC;
    """)
    
    records = cursor.fetchall()
    
    if records:
        print(f'找到 {len(records)} 条反馈记录与图片的对应关系:')
        print()
        
        for i, record in enumerate(records):
            feedback_id, feedback_emotion, feedback_comment, feedback_time, analysis_id, input_type, input_path, original_emotion, confidence, analysis_time = record
            
            print(f'反馈记录 #{i+1}:')
            print(f'  反馈ID: {feedback_id}')
            print(f'  反馈情绪: {feedback_emotion}')
            print(f'  反馈评论: {feedback_comment}')
            print(f'  反馈时间: {feedback_time}')
            print(f'  ---')
            print(f'  对应的分析记录:')
            print(f'    分析ID: {analysis_id}')
            print(f'    输入类型: {input_type}')
            print(f'    图片路径: {input_path}')
            print(f'    原始分析情绪: {original_emotion}')
            print(f'    置信度: {confidence}')
            print(f'    分析时间: {analysis_time}')
            print(f'  ===')
            print()
    else:
        print('没有找到反馈记录与图片的对应关系')
    
    # 检查是否有图片文件存在
    print('=== 检查图片文件是否存在 ===')
    
    cursor.execute("""
        SELECT DISTINCT ea.input_path
        FROM emotion_feedback ef
        JOIN emotion_analysis ea ON ef.analysis_id = ea.id
        WHERE ea.input_path IS NOT NULL;
    """)
    
    image_paths = cursor.fetchall()
    
    if image_paths:
        print(f'检查 {len(image_paths)} 个图片文件:')
        
        for path_tuple in image_paths:
            image_path = path_tuple[0]
            full_path = os.path.join('/Volumes/acasis/ema2_20250417/backend', image_path)
            
            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                print(f'  ✅ {image_path} (大小: {file_size} bytes)')
            else:
                print(f'  ❌ {image_path} (文件不存在)')
    
    # 检查反馈与分析的情绪对比
    print('\n=== 反馈情绪 vs 原始分析情绪对比 ===')
    
    cursor.execute("""
        SELECT 
            ef.emotion as feedback_emotion,
            ea.emotion as original_emotion,
            COUNT(*) as count
        FROM emotion_feedback ef
        JOIN emotion_analysis ea ON ef.analysis_id = ea.id
        GROUP BY ef.emotion, ea.emotion
        ORDER BY count DESC;
    """)
    
    emotion_comparison = cursor.fetchall()
    
    if emotion_comparison:
        print('情绪对比统计:')
        for feedback_emotion, original_emotion, count in emotion_comparison:
            if feedback_emotion == original_emotion:
                status = "✅ 一致"
            else:
                status = "❌ 不一致"
            print(f'  反馈: {feedback_emotion} | 原始: {original_emotion} | 次数: {count} | {status}')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'数据库连接或查询错误: {e}') 