import smtplib
import ssl
import logging
import sys
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

# QQ邮箱SMTP设置
smtp_host = "smtp.qq.com"
smtp_port = 465  # SSL端口
smtp_user = "<EMAIL>"
smtp_password = "qweamijhnidebaji"  # 授权码
use_ssl = True

# 收件人邮箱
recipient = "<EMAIL>"

def test_smtp_connection():
    """测试SMTP连接和发送功能"""
    try:
        logger.info(f"尝试连接到 {smtp_host}:{smtp_port} {'使用SSL' if use_ssl else '不使用SSL'}")
        
        if use_ssl:
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(smtp_host, smtp_port, context=context, timeout=30)
        else:
            server = smtplib.SMTP(smtp_host, smtp_port, timeout=30)
            server.starttls()
        
        logger.info("SMTP连接成功")
        
        # 尝试登录
        logger.info(f"尝试使用 {smtp_user} 登录")
        server.login(smtp_user, smtp_password)
        logger.info("登录成功")
        
        # 构造正确编码的邮件
        msg = MIMEMultipart()
        msg['From'] = smtp_user
        msg['To'] = recipient
        msg['Subject'] = '测试邮件'
        
        # 添加正文
        body = MIMEText('这是一封测试邮件，用于验证SMTP连接。', 'plain', 'utf-8')
        msg.attach(body)
        
        # 尝试发送
        logger.info(f"尝试发送邮件到 {recipient}")
        server.sendmail(smtp_user, [recipient], msg.as_string())
        logger.info("邮件发送成功")
        
        # 关闭连接
        server.quit()
        logger.info("SMTP连接已关闭")
        return True
        
    except Exception as e:
        logger.error(f"SMTP测试失败: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    if test_smtp_connection():
        print("\n✅ SMTP测试成功！")
    else:
        print("\n❌ SMTP测试失败！")
