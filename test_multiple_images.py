#!/usr/bin/env python3
"""
测试多张图片的情绪预测效果

验证优化后的情绪预测系统在多种图片上的表现
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

import cv2
import numpy as np

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
    ]
)

logger = logging.getLogger(__name__)

async def test_multiple_images():
    """测试多张图片的情绪预测效果"""
    
    logger.info("="*60)
    logger.info("开始测试多张图片的情绪预测效果")
    logger.info("="*60)
    
    try:
        # 导入所需模块
        from emotionai.core.ml.emotion_ensemble import EmotionEnsemble
        
        # 初始化情绪分析器
        logger.info("初始化情绪分析器...")
        ensemble = EmotionEnsemble()
        
        # 显示当前权重配置
        current_weights = ensemble.get_weights()
        logger.info(f"当前权重配置: {current_weights}")
        
        # 测试图片列表
        test_images = [
            "test_images/angry_face.jpg",
            "test_images/happy_face.jpg", 
            "test_images/sad_face.jpg",
            "test_images/neutral_face.jpg",
            "example_marked.jpg",  # 项目根目录下的示例图片
            "improved_marked.jpg",  # 项目根目录下的示例图片
        ]
        
        # 如果测试图片不存在，创建一些测试图片
        test_images_dir = Path("test_images")
        if not test_images_dir.exists():
            test_images_dir.mkdir()
            logger.info("创建了test_images目录")
        
        # 使用现有的示例图片
        existing_images = []
        for img_name in ["example_marked.jpg", "improved_marked.jpg", "WechatIMG862.jpg"]:
            if Path(img_name).exists():
                existing_images.append(img_name)
        
        if not existing_images:
            logger.warning("没有找到测试图片，创建一个合成测试图片")
            # 创建一个简单的测试图片
            test_img = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
            cv2.imwrite("test_image.jpg", test_img)
            existing_images = ["test_image.jpg"]
        
        logger.info(f"找到 {len(existing_images)} 张测试图片")
        
        # 统计结果
        results_summary = {
            "total_images": 0,
            "successful_predictions": 0,
            "emotion_counts": {},
            "neutral_count": 0,
            "non_neutral_count": 0
        }
        
        # 测试每张图片
        for i, img_path in enumerate(existing_images):
            logger.info(f"\n--- 测试图片 {i+1}/{len(existing_images)}: {img_path} ---")
            
            try:
                # 读取图片
                image = cv2.imread(img_path)
                if image is None:
                    logger.warning(f"无法读取图片: {img_path}")
                    continue
                
                # 调整图片大小（如果太大）
                height, width = image.shape[:2]
                if width > 800 or height > 600:
                    scale = min(800/width, 600/height)
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    image = cv2.resize(image, (new_width, new_height))
                    logger.info(f"调整图片尺寸: {width}x{height} -> {new_width}x{new_height}")
                
                # 进行情绪预测
                result = await ensemble.analyze(image)
                
                results_summary["total_images"] += 1
                
                if result.get("success", False):
                    results_summary["successful_predictions"] += 1
                    
                    dominant_emotion = result.get("dominant_emotion", "unknown")
                    emotions = result.get("emotions", {})
                    model_results = result.get("model_results", {})
                    
                    # 统计情绪分布
                    if dominant_emotion in results_summary["emotion_counts"]:
                        results_summary["emotion_counts"][dominant_emotion] += 1
                    else:
                        results_summary["emotion_counts"][dominant_emotion] = 1
                    
                    # 统计neutral vs 非neutral
                    if dominant_emotion == "neutral":
                        results_summary["neutral_count"] += 1
                    else:
                        results_summary["non_neutral_count"] += 1
                    
                    logger.info(f"✅ 预测成功: {dominant_emotion}")
                    logger.info("情绪分布:")
                    
                    # 按置信度排序显示
                    sorted_emotions = sorted(emotions.items(), key=lambda x: x[1], reverse=True)
                    for emotion, confidence in sorted_emotions[:5]:  # 只显示前5个
                        logger.info(f"  {emotion}: {confidence:.4f} ({confidence*100:.1f}%)")
                    
                    # 显示各模型预测结果
                    logger.info("各模型预测:")
                    for model_name, model_result in model_results.items():
                        if model_result.get("success", False):
                            model_emotion = model_result.get("dominant_emotion", "unknown")
                            logger.info(f"  {model_name}: {model_emotion}")
                        else:
                            logger.info(f"  {model_name}: 失败")
                
                else:
                    logger.error(f"❌ 预测失败: {result.get('error', '未知错误')}")
                
            except Exception as e:
                logger.error(f"处理图片 {img_path} 时出错: {str(e)}")
        
        # 显示统计结果
        logger.info("\n" + "="*60)
        logger.info("测试结果统计")
        logger.info("="*60)
        logger.info(f"总图片数: {results_summary['total_images']}")
        logger.info(f"成功预测: {results_summary['successful_predictions']}")
        logger.info(f"成功率: {results_summary['successful_predictions']/max(results_summary['total_images'], 1)*100:.1f}%")
        
        logger.info(f"\n情绪分布统计:")
        for emotion, count in results_summary["emotion_counts"].items():
            percentage = count / max(results_summary["successful_predictions"], 1) * 100
            logger.info(f"  {emotion}: {count} 次 ({percentage:.1f}%)")
        
        logger.info(f"\nNeutral vs 非Neutral:")
        logger.info(f"  Neutral: {results_summary['neutral_count']} 次")
        logger.info(f"  非Neutral: {results_summary['non_neutral_count']} 次")
        
        if results_summary["non_neutral_count"] > results_summary["neutral_count"]:
            logger.info("✅ 优化成功！非neutral情绪比neutral情绪更多")
        else:
            logger.info("⚠️  可能仍有neutral偏向问题")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        logger.error(f"错误详情: ", exc_info=True)

if __name__ == "__main__":
    asyncio.run(test_multiple_images()) 