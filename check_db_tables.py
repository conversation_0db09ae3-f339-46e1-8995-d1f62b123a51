#!/usr/bin/env python3
import psycopg2
import sys

def check_database():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='emotion_ai', 
            user='dom',
            password='2345',
            port='5432'
        )
        cur = conn.cursor()
        
        # 检查所有表
        cur.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name")
        tables = cur.fetchall()
        print('数据库中的所有表:')
        for table in tables:
            print(f'  - {table[0]}')
        
        # 检查是否存在users表
        cur.execute("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users')")
        users_exists = cur.fetchone()[0]
        print(f'\nusers表是否存在: {users_exists}')
        
        # 检查emotion_feedback表的外键约束
        cur.execute("""
            SELECT 
                tc.constraint_name, 
                tc.table_name, 
                kcu.column_name, 
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name 
            FROM 
                information_schema.table_constraints AS tc 
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name='emotion_feedback'
        """)
        constraints = cur.fetchall()
        print('\nemotion_feedback表的外键约束:')
        for constraint in constraints:
            print(f'  约束名: {constraint[0]}')
            print(f'  本表列: {constraint[2]}')
            print(f'  外表: {constraint[3]}')
            print(f'  外表列: {constraint[4]}')
            print('  ---')
        
        # 检查emotion_feedback表结构
        cur.execute("SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name = 'emotion_feedback' ORDER BY ordinal_position")
        columns = cur.fetchall()
        print('\nemotion_feedback表结构:')
        for column in columns:
            print(f'  {column[0]}: {column[1]} (nullable: {column[2]})')
        
        conn.close()
        
    except Exception as e:
        print(f'数据库连接失败: {e}')
        sys.exit(1)

if __name__ == '__main__':
    check_database() 