# ===================================
# 生产环境专用依赖
# 基于当前运行环境的精确版本锁定
# ===================================

# 引入核心依赖
-r requirements-core.txt

# 生产环境专用的Web服务器
gunicorn==23.0.0

# 生产环境监控和日志
prometheus-client==0.21.1
prometheus-fastapi-instrumentator==7.1.0

# 生产环境安全
cryptography>=44.0.0,<45.0.0

# 生产环境性能优化
uvloop>=0.21.0,<1.0.0; sys_platform != "win32"
httptools>=0.6.4,<1.0.0

# 生产环境数据库连接池
asyncpg==0.30.0
psycopg2-binary==2.9.10

# 生产环境缓存
redis==6.0.0

# 生产环境文件处理
aiofiles==22.1.0

# 排除开发工具（确保不会意外安装）
# 注意：这些包不应该出现在生产环境中 