#!/usr/bin/env python
# -*- coding: utf-8 -*-

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import datetime

def test_smtp_direct():
    """直接使用smtplib测试SMTP连接"""
    print("开始直接测试SMTP连接...")
    
    # 配置信息
    smtp_host = "smtp.qq.com"
    smtp_port = 465  # SSL端口
    sender = "<EMAIL>"
    password = "qweamijhnidebaji"  # 授权码
    receiver = "<EMAIL>"
    
    # 创建邮件
    message = MIMEMultipart()
    message['From'] = sender
    message['To'] = receiver
    message['Subject'] = "EmotionAI系统SMTP测试邮件"
    
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    body = f"""
    <html>
    <body>
        <p>这是一封来自EmotionAI系统的测试邮件</p>
        <p>如果您收到此邮件，说明SMTP服务配置正确。</p>
        <p>发送时间：{current_time}</p>
    </body>
    </html>
    """
    message.attach(MIMEText(body, 'html'))
    
    try:
        # 使用SSL连接
        print(f"尝试使用SSL连接到{smtp_host}:{smtp_port}...")
        server = smtplib.SMTP_SSL(smtp_host, smtp_port)
        server.set_debuglevel(1)  # 启用详细日志
        print(f"连接成功，尝试登录...")
        server.login(sender, password)
        print(f"登录成功，尝试发送邮件到{receiver}...")
        server.sendmail(sender, receiver, message.as_string())
        server.quit()
        print("邮件发送成功！")
        return True
    except Exception as e:
        print(f"SSL连接失败: {e}")
        
        try:
            # 尝试使用STARTTLS连接
            print(f"\n尝试使用STARTTLS连接到{smtp_host}:587...")
            server = smtplib.SMTP(smtp_host, 587)
            server.set_debuglevel(1)  # 启用详细日志
            server.starttls()
            print(f"STARTTLS连接成功，尝试登录...")
            server.login(sender, password)
            print(f"登录成功，尝试发送邮件到{receiver}...")
            server.sendmail(sender, receiver, message.as_string())
            server.quit()
            print("邮件发送成功！")
            return True
        except Exception as e:
            print(f"STARTTLS连接失败: {e}")
            return False

if __name__ == "__main__":
    test_smtp_direct()
