# 🚀 前端启动方式快速总结

## 两种启动方式

### 📦 正常启动（日常开发）

```bash
pnpm start
```

- ✅ 启动快、内存少
- ✅ 纯净开发环境
- ❌ 无Stagewise工具

### 🎨 Stagewise启动（设计协作）

```bash
pnpm start:stagewise
```

- ✅ 包含Stagewise工具栏
- ✅ 设计协作功能
- ⚠️ 启动稍慢

## 何时使用

| 场景                     | 推荐方式               |
| ------------------------ | ---------------------- |
| 日常代码开发             | `pnpm start`           |
| 性能调试                 | `pnpm start`           |
| 生产环境测试             | `pnpm start`           |
| 设计协作                 | `pnpm start:stagewise` |
| 界面调试                 | `pnpm start:stagewise` |
| 使用Cursor Stagewise扩展 | `pnpm start:stagewise` |

## 技术实现

- **正常启动**: `VITE_ENABLE_STAGEWISE` 未设置或为false
- **Stagewise启动**: `VITE_ENABLE_STAGEWISE=true`
- **自动检测**: main.tsx中根据环境变量条件加载

---

💡 **推荐**: 日常开发优先使用 `pnpm start`，需要Stagewise时使用 `pnpm start:stagewise`
