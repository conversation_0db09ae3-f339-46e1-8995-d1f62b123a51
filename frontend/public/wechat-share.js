// 微信分享配置脚本
(function () {
  'use strict';

  // 检测是否在微信环境中
  function isWechat() {
    return /MicroMessenger/i.test(navigator.userAgent);
  }

  // 设置微信分享信息
  function setupWechatShare() {
    if (!isWechat()) {
      return;
    }

    // 分享配置
    const shareConfig = {
      title: '长小养照护智能 - AI情绪分析',
      desc: '基于深度学习的多模态情绪识别平台，支持图像和语音情绪分析',
      link: window.location.href,
      imgUrl: window.location.origin + '/logo.png',
    };

    // 如果微信JS-SDK可用，使用JS-SDK配置分享
    if (typeof wx !== 'undefined' && wx.config) {
      // 这里需要后端提供微信JS-SDK的配置信息
      // 暂时使用基础的meta标签配置
      console.log('微信JS-SDK可用，但需要后端配置签名');
    }

    // 设置页面meta标签（备用方案）
    function setMetaTag(property, content) {
      let meta = document.querySelector(`meta[property="${property}"]`);
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute('property', property);
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    }

    function setNameMetaTag(name, content) {
      let meta = document.querySelector(`meta[name="${name}"]`);
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute('name', name);
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    }

    // 设置Open Graph标签
    setMetaTag('og:title', shareConfig.title);
    setMetaTag('og:description', shareConfig.desc);
    setMetaTag('og:image', shareConfig.imgUrl);
    setMetaTag('og:url', shareConfig.link);
    setMetaTag('og:type', 'website');
    setMetaTag('og:site_name', '长小养照护智能');

    // 设置微信专用标签
    setNameMetaTag('wechat:title', shareConfig.title);
    setNameMetaTag('wechat:description', shareConfig.desc);
    setNameMetaTag('wechat:image', shareConfig.imgUrl);

    // 设置页面标题
    document.title = shareConfig.title;

    console.log('微信分享配置已设置:', shareConfig);
  }

  // 页面加载完成后设置分享信息
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupWechatShare);
  } else {
    setupWechatShare();
  }

  // 监听页面变化（SPA应用）
  let lastUrl = location.href;
  new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
      lastUrl = url;
      setTimeout(setupWechatShare, 100); // 延迟执行，确保页面更新完成
    }
  }).observe(document, { subtree: true, childList: true });
})();
