// 微信浏览器视口优化脚本
(function () {
  // 检测是否是微信浏览器
  function isWechat() {
    return /MicroMessenger/i.test(navigator.userAgent);
  }

  // 检测是否是移动设备
  function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent,
    );
  }

  // 设置适合微信浏览器的视口
  function setWechatViewport() {
    // 如果是微信浏览器且是移动设备
    if (isWechat() && isMobile()) {
      // 查找现有的viewport meta标签
      var viewport = document.querySelector('meta[name="viewport"]');

      // 如果没有找到，创建一个新的
      if (!viewport) {
        viewport = document.createElement('meta');
        viewport.name = 'viewport';
        document.head.appendChild(viewport);
      }

      // 设置viewport属性，确保内容不会溢出
      viewport.content =
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover';

      // 添加额外的meta标签，防止页面缩放
      var metaFormat = document.createElement('meta');
      metaFormat.name = 'format-detection';
      metaFormat.content = 'telephone=no';
      document.head.appendChild(metaFormat);

      // 添加微信专用的X5内核meta标签
      var metaX5 = document.createElement('meta');
      metaX5.name = 'x5-orientation';
      metaX5.content = 'portrait';
      document.head.appendChild(metaX5);

      // 添加微信专用的渲染模式meta标签
      var metaX5FullScreen = document.createElement('meta');
      metaX5FullScreen.name = 'x5-fullscreen';
      metaX5FullScreen.content = 'true';
      document.head.appendChild(metaX5FullScreen);

      // 添加微信专用的应用模式meta标签
      var metaX5Page = document.createElement('meta');
      metaX5Page.name = 'x5-page-mode';
      metaX5Page.content = 'app';
      document.head.appendChild(metaX5Page);

      // 添加额外的样式，确保页面不会溢出
      var style = document.createElement('style');
      style.textContent = `
        body {
          overflow-x: hidden !important;
          max-width: 100vw !important;
          position: relative !important;
        }
        * {
          max-width: 100vw !important;
          box-sizing: border-box !important;
        }
      `;
      document.head.appendChild(style);

      // 监听窗口大小变化，确保页面不会溢出
      window.addEventListener('resize', function () {
        document.body.style.width = window.innerWidth + 'px';
      });

      // 初始设置
      document.body.style.width = window.innerWidth + 'px';
    }
  }

  // 在DOM加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setWechatViewport);
  } else {
    setWechatViewport();
  }
})();
