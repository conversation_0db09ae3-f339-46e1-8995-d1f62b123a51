/**
 * 微信JSSDK配置脚本
 * 用于在微信浏览器中启用高级功能，如录音、上传等
 */

// 检测是否是微信浏览器
function isWechatBrowser() {
  return /MicroMessenger/i.test(navigator.userAgent);
}

// 加载微信JSSDK
function loadWechatJSSDK() {
  if (!isWechatBrowser()) return Promise.resolve(false);

  return new Promise((resolve, reject) => {
    // 检查是否已加载
    if (window.wx) {
      resolve(true);
      return;
    }

    // 创建script标签
    const script = document.createElement('script');
    script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js';
    script.async = true;

    script.onload = () => {
      console.log('微信JSSDK加载成功');
      resolve(true);
    };

    script.onerror = () => {
      console.error('微信JSSDK加载失败');
      reject(new Error('Failed to load WeChat JSSDK'));
    };

    document.head.appendChild(script);
  });
}

// 初始化微信JSSDK
async function initWechatJSSDK() {
  if (!isWechatBrowser()) return false;

  try {
    // 加载JSSDK
    await loadWechatJSSDK();

    // 如果没有wx对象，说明加载失败
    if (!window.wx) {
      console.error('微信JSSDK未加载');
      return false;
    }

    // 配置JSSDK
    // 注意：实际使用时需要从后端获取签名等信息
    // 这里仅为示例，实际项目中需要替换为真实的配置
    window.wx.config({
      debug: false,
      appId: '', // 由后端提供
      timestamp: '', // 由后端提供
      nonceStr: '', // 由后端提供
      signature: '', // 由后端提供
      jsApiList: [
        'startRecord',
        'stopRecord',
        'onVoiceRecordEnd',
        'playVoice',
        'pauseVoice',
        'stopVoice',
        'onVoicePlayEnd',
        'uploadVoice',
        'downloadVoice',
        'chooseImage',
        'previewImage',
        'uploadImage',
        'downloadImage',
      ],
    });

    // 注册ready事件
    window.wx.ready(function () {
      console.log('微信JSSDK配置成功');
      // 触发自定义事件，通知应用JSSDK已准备就绪
      const event = new CustomEvent('wx-jssdk-ready');
      window.dispatchEvent(event);
    });

    // 注册error事件
    window.wx.error(function (res) {
      console.error('微信JSSDK配置失败:', res);
      // 触发自定义事件，通知应用JSSDK配置失败
      const event = new CustomEvent('wx-jssdk-error', { detail: res });
      window.dispatchEvent(event);
    });

    return true;
  } catch (error) {
    console.error('初始化微信JSSDK失败:', error);
    return false;
  }
}

// 在页面加载完成后初始化
if (document.readyState === 'complete') {
  initWechatJSSDK();
} else {
  window.addEventListener('load', initWechatJSSDK);
}

// 导出工具函数
window.wechatJSSDK = {
  isWechatBrowser,
  loadWechatJSSDK,
  initWechatJSSDK,

  // 录音相关函数
  startRecord: function () {
    if (!window.wx) return Promise.reject(new Error('微信JSSDK未加载'));
    return new Promise((resolve) => {
      window.wx.startRecord({
        success: function () {
          console.log('开始录音');
          resolve(true);
        },
        fail: function (res) {
          console.error('开始录音失败:', res);
          resolve(false);
        },
      });
    });
  },

  stopRecord: function () {
    if (!window.wx) return Promise.reject(new Error('微信JSSDK未加载'));
    return new Promise((resolve) => {
      window.wx.stopRecord({
        success: function (res) {
          console.log('停止录音成功:', res);
          resolve(res);
        },
        fail: function (res) {
          console.error('停止录音失败:', res);
          resolve(null);
        },
      });
    });
  },

  // 监听录音自动停止
  onVoiceRecordEnd: function (callback) {
    if (!window.wx) return;
    window.wx.onVoiceRecordEnd({
      complete: function (res) {
        console.log('录音时间已超过一分钟，自动停止');
        callback && callback(res);
      },
    });
  },

  // 播放语音
  playVoice: function (localId) {
    if (!window.wx) return;
    window.wx.playVoice({
      localId: localId,
    });
  },

  // 暂停播放
  pauseVoice: function (localId) {
    if (!window.wx) return;
    window.wx.pauseVoice({
      localId: localId,
    });
  },

  // 停止播放
  stopVoice: function (localId) {
    if (!window.wx) return;
    window.wx.stopVoice({
      localId: localId,
    });
  },

  // 监听播放结束
  onVoicePlayEnd: function (callback) {
    if (!window.wx) return;
    window.wx.onVoicePlayEnd({
      success: function (res) {
        callback && callback(res);
      },
    });
  },

  // 上传语音
  uploadVoice: function (localId) {
    if (!window.wx) return Promise.reject(new Error('微信JSSDK未加载'));
    return new Promise((resolve, reject) => {
      window.wx.uploadVoice({
        localId: localId,
        isShowProgressTips: 1,
        success: function (res) {
          resolve(res);
        },
        fail: function (res) {
          reject(res);
        },
      });
    });
  },
};
