/**
 * 微信浏览器适配脚本
 * 根据微信官方文档：https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/iOS_WKWebview.html
 */
(function () {
  // 检测是否是微信浏览器
  function isWechatBrowser() {
    return /MicroMessenger/i.test(navigator.userAgent);
  }

  // 检测是否是iOS设备
  function isIOSDevice() {
    return /iPhone|iPad|iPod/i.test(navigator.userAgent);
  }

  // 检测是否是Android设备
  function isAndroidDevice() {
    return /Android/i.test(navigator.userAgent);
  }

  // 设置适合微信浏览器的视口
  function setWechatViewport() {
    var meta = document.querySelector('meta[name="viewport"]');
    if (!meta) {
      meta = document.createElement('meta');
      meta.name = 'viewport';
      document.head.appendChild(meta);
    }

    // 根据微信文档，设置适合的viewport
    meta.content =
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover';
  }

  // 添加微信专用的meta标签
  function addWechatMeta() {
    // 添加微信专用的渲染模式meta标签
    var metaFormat = document.createElement('meta');
    metaFormat.name = 'format-detection';
    metaFormat.content = 'telephone=no';
    document.head.appendChild(metaFormat);

    // 添加微信X5内核专用meta标签
    var metaX5 = document.createElement('meta');
    metaX5.name = 'x5-orientation';
    metaX5.content = 'portrait';
    document.head.appendChild(metaX5);

    var metaX5FullScreen = document.createElement('meta');
    metaX5FullScreen.name = 'x5-fullscreen';
    metaX5FullScreen.content = 'true';
    document.head.appendChild(metaX5FullScreen);

    var metaX5Page = document.createElement('meta');
    metaX5Page.name = 'x5-page-mode';
    metaX5Page.content = 'app';
    document.head.appendChild(metaX5Page);
  }

  // 添加微信浏览器专用样式
  function addWechatStyles() {
    var style = document.createElement('style');
    style.textContent = `
      /* 微信浏览器全局样式 */
      body {
        -webkit-overflow-scrolling: touch;
        overflow-x: hidden !important;
        max-width: 100vw !important;
        position: relative !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      /* 强制所有元素不超出屏幕宽度 */
      * {
        max-width: 100vw !important;
        box-sizing: border-box !important;
      }

      /* 修复微信浏览器中的文本溢出 */
      .englishDescription {
        white-space: normal !important;
        word-break: break-word !important;
        overflow-wrap: break-word !important;
        hyphens: auto !important;
      }

      /* 微信浏览器中的属性框样式 */
      @media screen and (max-width: 576px) {
        .faceInfo {
          display: flex !important;
          flex-direction: row !important;
          flex-wrap: nowrap !important;
          justify-content: space-between !important;
          width: 100% !important;
          gap: 2px !important;
          padding: 0 !important;
          margin: 10px 0 !important;
        }

        .faceInfo > div {
          flex: 1 1 25% !important;
          min-width: 0 !important;
          max-width: 25% !important;
          padding: 0 1px !important;
        }

        /* 环形图容器 */
        div[style*="height: '180px'"],
        div[style*="height: '160px'"] {
          height: 200px !important;
          display: flex !important;
          justify-content: space-between !important;
          align-items: center !important;
          margin: 15px 0 !important;
          position: relative !important;
          overflow: visible !important;
          width: 100% !important;
          box-sizing: border-box !important;
          padding: 0 !important;
        }

        /* 主要情绪环形图容器 */
        div[style*="height: '180px'"] > div:first-child,
        div[style*="height: '160px'"] > div:first-child {
          flex: 0 0 50% !important;
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
          position: relative !important;
          height: 100% !important;
          width: 50% !important;
          box-sizing: border-box !important;
          padding: 0 !important;
          margin: 0 !important;
        }

        /* 次要情绪环形图容器 */
        div[style*="height: '180px'"] > div:last-child,
        div[style*="height: '160px'"] > div:last-child {
          flex: 0 0 50% !important;
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
          position: relative !important;
          height: 100% !important;
          width: 50% !important;
          box-sizing: border-box !important;
          padding: 0 !important;
          margin: 0 !important;
        }

        /* 环形图 */
        .ant-progress-circle {
          width: 140px !important;
          height: 140px !important;
          transform: scale(1) !important;
          transform-origin: center center !important;
          position: relative !important;
          z-index: 1 !important;
        }

        /* 修复微信浏览器中的SVG渲染问题 */
        .ant-progress-circle svg {
          width: 100% !important;
          height: 100% !important;
          display: block !important;
          position: relative !important;
          z-index: 1 !important;
        }

        /* 修复微信浏览器中的环形图路径 */
        .ant-progress-circle path {
          stroke-width: 6 !important;
          transform-origin: center center !important;
        }

        /* 环形图中心文字容器 */
        div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"] {
          width: 100px !important;
          height: 100px !important;
          display: flex !important;
          flex-direction: column !important;
          justify-content: center !important;
          align-items: center !important;
          overflow: hidden !important;
          position: absolute !important;
          top: 50% !important;
          left: 50% !important;
          transform: translate(-50%, -50%) !important;
          text-align: center !important;
          z-index: 2 !important;
          background-color: transparent !important;
          pointer-events: none !important;
        }

        /* 环形图中心百分比 */
        div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"] > div:first-child {
          font-size: 28px !important;
          line-height: 1.2 !important;
          margin-bottom: 2px !important;
          font-weight: bold !important;
          width: auto !important;
          height: auto !important;
          display: block !important;
          text-shadow: 0 0 2px rgba(255, 255, 255, 0.5) !important;
        }

        /* 环形图中心情绪名称 */
        div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"] > div:nth-child(2) {
          font-size: 16px !important;
          line-height: 1.2 !important;
          white-space: nowrap !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          width: 90px !important;
          text-align: center !important;
          font-weight: 600 !important;
          display: block !important;
          text-shadow: 0 0 1px rgba(255, 255, 255, 0.5) !important;
        }

        /* 环形图中心情绪英文名 */
        div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"] > div:last-child {
          font-size: 13px !important;
          line-height: 1.2 !important;
          white-space: nowrap !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          width: 90px !important;
          text-align: center !important;
          opacity: 0.9 !important;
          font-weight: 500 !important;
          display: block !important;
          text-shadow: 0 0 1px rgba(255, 255, 255, 0.5) !important;
        }

        /* 修复微信浏览器中的SVG渲染问题 - 强制硬件加速 */
        .ant-progress-circle,
        .ant-progress-circle svg,
        .ant-progress-circle path,
        div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"] {
          -webkit-transform-style: preserve-3d !important;
          transform-style: preserve-3d !important;
          -webkit-backface-visibility: hidden !important;
          backface-visibility: hidden !important;
          -webkit-perspective: 1000 !important;
          perspective: 1000 !important;
        }
      }
    `;
    document.head.appendChild(style);
  }

  // 修复微信浏览器中的滚动问题
  function fixWechatScroll() {
    // 防止页面弹性滚动
    document.body.addEventListener(
      'touchmove',
      function (e) {
        if (e.touches.length > 1) {
          e.preventDefault();
        }
      },
      { passive: false },
    );

    // 修复iOS中的滚动问题
    if (isIOSDevice()) {
      document.documentElement.style.height = '100%';
      document.body.style.height = '100%';
      document.body.style.webkitOverflowScrolling = 'touch';
    }
  }

  // 修复微信浏览器中的点击延迟问题
  function fixWechatTapDelay() {
    // 添加fastclick库
    if (typeof FastClick !== 'undefined') {
      FastClick.attach(document.body);
    }
  }

  // 修复微信浏览器中的SVG渲染问题
  function fixWechatSVGRendering() {
    // 监听DOM变化，处理新添加的SVG元素
    var observer = new MutationObserver(function (mutations) {
      mutations.forEach(function (mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
          for (var i = 0; i < mutation.addedNodes.length; i++) {
            var node = mutation.addedNodes[i];
            if (node.nodeType === 1) {
              // 元素节点
              // 查找所有SVG元素
              var svgs = node.querySelectorAll ? node.querySelectorAll('svg') : [];
              if (node.nodeName.toLowerCase() === 'svg') {
                svgs = [node].concat(Array.prototype.slice.call(svgs));
              }

              // 处理SVG元素
              for (var j = 0; j < svgs.length; j++) {
                var svg = svgs[j];
                // 确保SVG元素有正确的宽高
                if (svg.getAttribute('width') === null || svg.getAttribute('height') === null) {
                  svg.setAttribute('width', '100%');
                  svg.setAttribute('height', '100%');
                }

                // 确保SVG元素有正确的viewBox
                if (svg.getAttribute('viewBox') === null) {
                  var width = svg.clientWidth || 100;
                  var height = svg.clientHeight || 100;
                  svg.setAttribute('viewBox', '0 0 ' + width + ' ' + height);
                }

                // 添加硬件加速
                svg.style.webkitTransform = 'translateZ(0)';
                svg.style.transform = 'translateZ(0)';
                svg.style.webkitBackfaceVisibility = 'hidden';
                svg.style.backfaceVisibility = 'hidden';

                // 处理SVG中的path元素
                var paths = svg.querySelectorAll('path');
                for (var k = 0; k < paths.length; k++) {
                  var path = paths[k];
                  path.style.webkitTransform = 'translateZ(0)';
                  path.style.transform = 'translateZ(0)';
                  path.style.webkitBackfaceVisibility = 'hidden';
                  path.style.backfaceVisibility = 'hidden';
                }
              }

              // 查找所有环形图中心文字容器
              var textContainers = node.querySelectorAll
                ? node.querySelectorAll(
                    'div[style*="position: \'absolute\'"][style*="transform: \'translate(-50%, -50%)\'"',
                  )
                : [];

              // 处理环形图中心文字容器
              for (var l = 0; l < textContainers.length; l++) {
                var container = textContainers[l];
                container.style.zIndex = '2';
                container.style.webkitTransform = 'translate(-50%, -50%) translateZ(0)';
                container.style.transform = 'translate(-50%, -50%) translateZ(0)';
                container.style.webkitBackfaceVisibility = 'hidden';
                container.style.backfaceVisibility = 'hidden';
              }
            }
          }
        }
      });
    });

    // 开始监听DOM变化
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  // 主函数
  function initWechatAdapter() {
    if (isWechatBrowser()) {
      console.log('检测到微信浏览器，应用微信适配');
      setWechatViewport();
      addWechatMeta();
      addWechatStyles();
      fixWechatScroll();
      fixWechatTapDelay();
      fixWechatSVGRendering();

      // 监听窗口大小变化，确保样式正确应用
      window.addEventListener('resize', function () {
        document.body.style.width = window.innerWidth + 'px';
      });

      // 初始设置
      document.body.style.width = window.innerWidth + 'px';

      // 添加微信浏览器标识类
      document.documentElement.classList.add('wechat-browser');
      if (isIOSDevice()) {
        document.documentElement.classList.add('wechat-ios');
      } else if (isAndroidDevice()) {
        document.documentElement.classList.add('wechat-android');
      }
    }
  }

  // 在DOM加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initWechatAdapter);
  } else {
    initWechatAdapter();
  }
})();
