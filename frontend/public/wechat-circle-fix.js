/**
 * 微信浏览器环形图修复脚本
 * 专门用于修复微信浏览器中环形图显示不正常的问题
 */
(function () {
  // 检测是否是微信浏览器
  function isWechatBrowser() {
    return /MicroMessenger/i.test(navigator.userAgent);
  }

  // 修复次要情绪环形图
  function fixSecondaryEmotionCircle() {
    // 如果不是微信浏览器，不执行修复
    if (!isWechatBrowser()) {
      return;
    }

    // 监听DOM变化，处理新添加的环形图
    var observer = new MutationObserver(function (mutations) {
      mutations.forEach(function (mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
          for (var i = 0; i < mutation.addedNodes.length; i++) {
            var node = mutation.addedNodes[i];
            if (node.nodeType === 1) {
              // 元素节点
              // 查找所有次要情绪环形图容器
              var containers = node.querySelectorAll
                ? node.querySelectorAll(
                    'div[style*="height: \'180px\'"] > div:last-child, div[style*="height: \'160px\'"] > div:last-child',
                  )
                : [];

              // 处理次要情绪环形图容器
              for (var j = 0; j < containers.length; j++) {
                var container = containers[j];

                // 查找环形图
                var circles = container.querySelectorAll('.ant-progress-circle');
                for (var k = 0; k < circles.length; k++) {
                  var circle = circles[k];

                  // 设置环形图样式
                  circle.style.width = '120px';
                  circle.style.height = '120px';
                  circle.style.overflow = 'visible';
                  circle.style.transform = 'scale(1)';
                  circle.style.transformOrigin = 'center center';
                  circle.style.position = 'relative';
                  circle.style.zIndex = '1';

                  // 查找SVG元素
                  var svg = circle.querySelector('svg');
                  if (svg) {
                    svg.style.width = '100%';
                    svg.style.height = '100%';
                    svg.style.display = 'block';
                    svg.style.position = 'relative';
                    svg.style.zIndex = '1';
                    svg.style.overflow = 'visible';

                    // 查找圆形路径
                    var paths = svg.querySelectorAll('path');
                    for (var l = 0; l < paths.length; l++) {
                      var path = paths[l];
                      path.style.strokeWidth = '6px';
                      path.style.transformOrigin = 'center center';
                      path.style.vectorEffect = 'non-scaling-stroke';
                    }

                    // 查找圆形
                    var circles = svg.querySelectorAll('circle');
                    for (var m = 0; m < circles.length; m++) {
                      var circleEl = circles[m];
                      circleEl.setAttribute('r', '47');
                      circleEl.style.strokeWidth = '6px';
                    }
                  }

                  // 查找文字容器
                  var textContainers = container.querySelectorAll(
                    'div[style*="position: \'absolute\'"][style*="transform: \'translate(-50%, -50%)\'"',
                  );
                  for (var n = 0; n < textContainers.length; n++) {
                    var textContainer = textContainers[n];

                    // 设置文字容器样式
                    textContainer.style.width = '90px';
                    textContainer.style.height = '90px';
                    textContainer.style.display = 'flex';
                    textContainer.style.flexDirection = 'column';
                    textContainer.style.justifyContent = 'center';
                    textContainer.style.alignItems = 'center';
                    textContainer.style.overflow = 'hidden';
                    textContainer.style.position = 'absolute';
                    textContainer.style.top = '50%';
                    textContainer.style.left = '50%';
                    textContainer.style.transform = 'translate(-50%, -50%)';
                    textContainer.style.textAlign = 'center';
                    textContainer.style.zIndex = '2';
                    textContainer.style.backgroundColor = 'transparent';
                    textContainer.style.pointerEvents = 'none';

                    // 查找百分比
                    var percentEl = textContainer.querySelector('div:first-child');
                    if (percentEl) {
                      percentEl.style.fontSize = '24px';
                      percentEl.style.lineHeight = '1.1';
                      percentEl.style.marginBottom = '1px';
                      percentEl.style.fontWeight = 'bold';
                      percentEl.style.width = 'auto';
                      percentEl.style.height = 'auto';
                      percentEl.style.display = 'block';
                      percentEl.style.textShadow = '0 0 2px rgba(255, 255, 255, 0.5)';
                    }

                    // 查找情绪名称
                    var nameEl = textContainer.querySelector('div:nth-child(2)');
                    if (nameEl) {
                      nameEl.style.fontSize = '14px';
                      nameEl.style.width = '80px';
                      nameEl.style.display = 'block';
                      nameEl.style.textShadow = '0 0 1px rgba(255, 255, 255, 0.5)';
                      nameEl.style.whiteSpace = 'nowrap';
                      nameEl.style.overflow = 'hidden';
                      nameEl.style.textOverflow = 'ellipsis';
                      nameEl.style.lineHeight = '1.1';
                      nameEl.style.fontWeight = '600';
                      nameEl.style.marginBottom = '1px';
                    }

                    // 查找情绪英文名
                    var engNameEl = textContainer.querySelector('div:last-child');
                    if (engNameEl) {
                      engNameEl.style.fontSize = '11px';
                      engNameEl.style.width = '80px';
                      engNameEl.style.display = 'block';
                      engNameEl.style.textShadow = '0 0 1px rgba(255, 255, 255, 0.5)';
                      engNameEl.style.whiteSpace = 'nowrap';
                      engNameEl.style.overflow = 'hidden';
                      engNameEl.style.textOverflow = 'ellipsis';
                      engNameEl.style.lineHeight = '1.1';
                      engNameEl.style.opacity = '0.9';
                      engNameEl.style.fontWeight = '500';
                    }
                  }
                }
              }
            }
          }
        }
      });
    });

    // 开始监听DOM变化
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // 立即处理已有的环形图
    setTimeout(function () {
      var containers = document.querySelectorAll(
        'div[style*="height: \'180px\'"] > div:last-child, div[style*="height: \'160px\'"] > div:last-child',
      );
      for (var j = 0; j < containers.length; j++) {
        var container = containers[j];
        var event = new CustomEvent('DOMNodeInserted');
        container.dispatchEvent(event);
      }
    }, 1000);
  }

  // 在DOM加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fixSecondaryEmotionCircle);
  } else {
    fixSecondaryEmotionCircle();
  }
})();
