/// <reference types="vitest" />
import { defineConfig, defaultClientConditions } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import os from 'os';
import http from 'http';
import { configDefaults } from 'vitest/config';

// 获取本机IP地址
function getLocalIP() {
  const networkInterfaces = os.networkInterfaces();
  for (const interfaceName in networkInterfaces) {
    const interfaces = networkInterfaces[interfaceName];
    if (!interfaces) continue;
    for (const iface of interfaces) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return 'localhost';
}

export default defineConfig(({ mode }) => {
  console.log('[vite.config.ts] Current Vite mode:', mode);
  const localIP = getLocalIP();
  console.log(`Using local IP: ${localIP}`);

  const useHttps = process.env.VITE_USE_HTTPS === 'true';

  const serverConfig = {
    host: '0.0.0.0',
    port: 3000,
    strictPort: false,
    fs: {
      strict: false,
      allow: ['../'],
    },
    cors: {
      origin: '*',
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      credentials: true,
    },
    hmr: {
      overlay: true,
      port: 3000,
      timeout: 10000,
    },
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    allowedHosts: ['localhost', '127.0.0.1', localIP, 'www.cxyai.net', 'cxyai.shenzhuo.vip'],
    proxy: {
      '/api/v1': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false,
        ws: true,
        timeout: 1800000,
        proxyTimeout: 1800000,
        agent: new http.Agent({ keepAlive: true }),
        configure: (proxy) => {
          proxy.on('error', (err) => {
            console.log('proxy error', err);
          });
        },
      },
      '/static': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false,
      },
    },
  };

  if (useHttps) {
    // 动态添加https配置，以满足类型检查
    // 注意：这需要@vitejs/plugin-basic-ssl插件，如果已卸载，此分支不会执行
    // serverConfig.https = true;
  }

  return {
    test: {
      globals: true,
      environment: 'happy-dom',
      setupFiles: ['./src/setupTests.ts'],
      include: ['**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
      exclude: [...configDefaults.exclude, 'node_modules', 'dist'],
    },
    optimizeDeps: {
      exclude: ['vite/dist/client/env.mjs'],
    },
    plugins: [react()],
    server: serverConfig,
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
      extensions: ['.tsx', '.ts', '.jsx', '.js'],
      conditions: [...defaultClientConditions],
    },
    build: {
      outDir: 'dist',
      sourcemap: true,
      cssMinify: 'esbuild',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: process.env.NODE_ENV === 'production',
          drop_debugger: true,
        },
      },
    },
    json: {
      stringify: true,
      namedExports: true,
    },
    define: {
      'process.env.NODE_ENV': JSON.stringify(mode),
      __API__: JSON.stringify(''),
      __LOCAL_IP__: JSON.stringify(localIP),
    },
  };
});
