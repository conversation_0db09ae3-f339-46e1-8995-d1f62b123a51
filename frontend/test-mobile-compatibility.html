<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端兼容性测试 - Mobile Compatibility Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            color: #1890ff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .device-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .device-item {
            background: #e6f7ff;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .device-item:hover {
            background: #bae7ff;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background-color: #52c41a; }
        .status-fail { background-color: #ff4d4f; }
        .status-pending { background-color: #faad14; }
        .test-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .result-pass { background-color: #f6ffed; border: 1px solid #b7eb8f; }
        .result-fail { background-color: #fff2f0; border: 1px solid #ffadd2; }
        .shortcuts {
            background: #fff7e6;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        @media (max-width: 768px) {
            body { padding: 10px; }
            .test-container { padding: 15px; }
            .device-list { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>移动端兼容性测试指南 📱</h1>
        <p>Mobile Compatibility Testing Guide</p>

        <!-- 快捷键说明 -->
        <div class="test-section">
            <div class="test-title">🚀 快速开始 Quick Start</div>
            <div class="shortcuts">
                <strong>开发者工具快捷键：</strong><br>
                • Chrome/Edge: <code>Ctrl+Shift+M</code> (Windows) / <code>Cmd+Shift+M</code> (Mac)<br>
                • Firefox: <code>Ctrl+Shift+M</code> (Windows) / <code>Cmd+Shift+M</code> (Mac)<br>
                • Safari: <code>Cmd+Option+R</code> (Mac)
            </div>
        </div>

        <!-- 设备列表 -->
        <div class="test-section">
            <div class="test-title">📱 推荐测试设备 Recommended Test Devices</div>
            <div class="device-list">
                <div class="device-item" onclick="testDevice('iPhone 12 Pro', 390, 844)">
                    <strong>iPhone 12 Pro</strong><br>
                    390×844, DPR: 3
                </div>
                <div class="device-item" onclick="testDevice('Galaxy S20', 360, 800)">
                    <strong>Galaxy S20</strong><br>
                    360×800, DPR: 3
                </div>
                <div class="device-item" onclick="testDevice('Pixel 5', 393, 851)">
                    <strong>Pixel 5</strong><br>
                    393×851, DPR: 2.75
                </div>
                <div class="device-item" onclick="testDevice('iPad', 768, 1024)">
                    <strong>iPad</strong><br>
                    768×1024, DPR: 2
                </div>
                <div class="device-item" onclick="testDevice('小屏手机', 320, 568)">
                    <strong>小屏手机</strong><br>
                    320×568, DPR: 2
                </div>
                <div class="device-item" onclick="testDevice('大屏手机', 414, 896)">
                    <strong>大屏手机</strong><br>
                    414×896, DPR: 3
                </div>
            </div>
        </div>

        <!-- 测试清单 -->
        <div class="test-section">
            <div class="test-title">✅ 测试清单 Test Checklist</div>
            
            <div class="test-steps">
                <h4>1. 环形图文字居中测试</h4>
                <div id="circle-test">
                    <span class="status-indicator status-pending"></span>
                    <span>检测 .ant-progress-circle .ant-progress-text 是否居中显示</span>
                </div>
                <button onclick="runCircleTest()">运行测试</button>
            </div>

            <div class="test-steps">
                <h4>2. 响应式布局测试</h4>
                <div id="layout-test">
                    <span class="status-indicator status-pending"></span>
                    <span>检测图片上传区域在不同屏幕尺寸下的布局</span>
                </div>
                <button onclick="runLayoutTest()">运行测试</button>
            </div>

            <div class="test-steps">
                <h4>3. 触摸交互测试</h4>
                <div id="touch-test">
                    <span class="status-indicator status-pending"></span>
                    <span>测试按钮点击和触摸反馈</span>
                </div>
                <button onclick="runTouchTest()">运行测试</button>
            </div>

            <div class="test-steps">
                <h4>4. 性能测试</h4>
                <div id="performance-test">
                    <span class="status-indicator status-pending"></span>
                    <span>检测页面加载速度和渲染性能</span>
                </div>
                <button onclick="runPerformanceTest()">运行测试</button>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="test-section">
            <div class="test-title">📊 测试结果 Test Results</div>
            <div id="test-results" class="test-result">
                等待测试结果... Waiting for test results...
            </div>
        </div>

        <!-- 调试工具 -->
        <div class="test-section">
            <div class="test-title">🔧 调试工具 Debug Tools</div>
            <button onclick="showDeviceInfo()">显示设备信息</button>
            <button onclick="checkUserAgent()">检查用户代理</button>
            <button onclick="testViewport()">测试视口</button>
            <button onclick="checkCSS()">检查CSS支持</button>
        </div>
    </div>

    <script>
        // 测试设备切换
        function testDevice(name, width, height) {
            const message = `请在开发者工具中设置设备为：\n${name}\n宽度：${width}px\n高度：${height}px`;
            alert(message);
            console.log(`Testing device: ${name} (${width}×${height})`);
        }

        // 环形图测试
        function runCircleTest() {
            const result = document.getElementById('circle-test');
            const indicator = result.querySelector('.status-indicator');
            
            // 检查是否存在Ant Design进度圆组件
            const circleElements = document.querySelectorAll('.ant-progress-circle .ant-progress-text');
            
            if (circleElements.length > 0) {
                indicator.className = 'status-indicator status-pass';
                updateTestResult('Circle Test', 'PASS', `找到 ${circleElements.length} 个环形图组件`);
                
                // 检查文字居中
                circleElements.forEach((el, index) => {
                    const styles = window.getComputedStyle(el);
                    const isFlexCentered = styles.display === 'flex' && 
                                         styles.alignItems === 'center' && 
                                         styles.justifyContent === 'center';
                    console.log(`环形图 ${index + 1} 居中状态:`, isFlexCentered, styles);
                });
            } else {
                indicator.className = 'status-indicator status-fail';
                updateTestResult('Circle Test', 'FAIL', '未找到环形图组件');
            }
        }

        // 响应式布局测试
        function runLayoutTest() {
            const result = document.getElementById('layout-test');
            const indicator = result.querySelector('.status-indicator');
            
            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight,
                devicePixelRatio: window.devicePixelRatio || 1
            };
            
            const isMobile = viewport.width <= 768;
            const isSmallScreen = viewport.width <= 480;
            
            indicator.className = 'status-indicator status-pass';
            updateTestResult('Layout Test', 'PASS', 
                `视口: ${viewport.width}×${viewport.height}, DPR: ${viewport.devicePixelRatio}, 移动端: ${isMobile}`);
        }

        // 触摸交互测试
        function runTouchTest() {
            const result = document.getElementById('touch-test');
            const indicator = result.querySelector('.status-indicator');
            
            const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            
            if (hasTouchSupport) {
                indicator.className = 'status-indicator status-pass';
                updateTestResult('Touch Test', 'PASS', '设备支持触摸交互');
            } else {
                indicator.className = 'status-indicator status-fail';
                updateTestResult('Touch Test', 'FAIL', '设备不支持触摸交互');
            }
        }

        // 性能测试
        function runPerformanceTest() {
            const result = document.getElementById('performance-test');
            const indicator = result.querySelector('.status-indicator');
            
            const startTime = performance.now();
            
            setTimeout(() => {
                const endTime = performance.now();
                const loadTime = endTime - startTime;
                
                indicator.className = loadTime < 100 ? 'status-indicator status-pass' : 'status-indicator status-fail';
                updateTestResult('Performance Test', loadTime < 100 ? 'PASS' : 'FAIL', 
                    `响应时间: ${loadTime.toFixed(2)}ms`);
            }, 50);
        }

        // 更新测试结果
        function updateTestResult(testName, status, details) {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = status === 'PASS' ? 'result-pass' : 'result-fail';
            
            resultsDiv.innerHTML += `
                <div class="${statusClass}">
                    [${timestamp}] ${testName}: ${status}
                    <br>详情: ${details}
                </div>
            `;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // 显示设备信息
        function showDeviceInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                screen: `${screen.width}×${screen.height}`,
                viewport: `${window.innerWidth}×${window.innerHeight}`,
                devicePixelRatio: window.devicePixelRatio || 1,
                touchSupport: 'ontouchstart' in window,
                orientation: screen.orientation ? screen.orientation.type : 'unknown'
            };
            
            console.log('设备信息:', info);
            updateTestResult('Device Info', 'INFO', JSON.stringify(info, null, 2));
        }

        // 检查用户代理
        function checkUserAgent() {
            const ua = navigator.userAgent;
            const isAndroid = /Android/i.test(ua);
            const isIOS = /iPhone|iPad|iPod/i.test(ua);
            const isWechat = /MicroMessenger/i.test(ua);
            const isMobile = /Mobile/i.test(ua);
            
            const analysis = {
                isAndroid,
                isIOS,
                isWechat,
                isMobile,
                browser: getBrowserName(ua)
            };
            
            console.log('用户代理分析:', analysis);
            updateTestResult('User Agent', 'INFO', JSON.stringify(analysis, null, 2));
        }

        // 获取浏览器名称
        function getBrowserName(ua) {
            if (ua.includes('Chrome')) return 'Chrome';
            if (ua.includes('Firefox')) return 'Firefox';
            if (ua.includes('Safari')) return 'Safari';
            if (ua.includes('Edge')) return 'Edge';
            return 'Unknown';
        }

        // 测试视口
        function testViewport() {
            const viewport = document.querySelector('meta[name="viewport"]');
            const content = viewport ? viewport.getAttribute('content') : 'Not found';
            
            updateTestResult('Viewport Test', 'INFO', `Viewport meta: ${content}`);
        }

        // 检查CSS支持
        function checkCSS() {
            const features = {
                flexbox: CSS.supports('display', 'flex'),
                grid: CSS.supports('display', 'grid'),
                transform: CSS.supports('transform', 'translateX(0)'),
                animation: CSS.supports('animation', 'test 1s'),
                mediaqueries: window.matchMedia ? true : false
            };
            
            console.log('CSS支持情况:', features);
            updateTestResult('CSS Support', 'INFO', JSON.stringify(features, null, 2));
        }

        // 自动检测当前环境
        document.addEventListener('DOMContentLoaded', function() {
            showDeviceInfo();
            checkUserAgent();
            testViewport();
            checkCSS();
        });
    </script>
</body>
</html> 