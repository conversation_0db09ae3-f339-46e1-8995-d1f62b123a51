#!/bin/bash
# 启动开发服务器

# 定义颜色
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 检查是否提供了后端URL作为参数
if [ -z "$1" ]; then
  echo -e "${YELLOW}警告: 未提供后端服务的URL。将使用默认值: http://127.0.0.1:8000${NC}"
  BACKEND_URL="http://127.0.0.1:8000"
else
  BACKEND_URL=$1
  echo -e "${GREEN}后端API将代理到: $BACKEND_URL${NC}"
fi

# 设置环境变量
export VITE_API_URL=$BACKEND_URL
export VITE_DISABLE_HMR=false
export VITE_ENABLE_HMR=true

# 检查是否提供了公共URL作为第二个参数
if [ -n "$2" ]; then
  export VITE_PUBLIC_URL=$2
  echo -e "${GREEN}公共访问URL设置为: $VITE_PUBLIC_URL${NC}"
fi

# 警告：这会禁用所有出站请求的TLS证书验证。
# 仅用于本地开发和调试，切勿在生产环境中使用。
if [[ "$BACKEND_URL" == https* ]]; then
  export NODE_TLS_REJECT_UNAUTHORIZED=0
  echo -e "${YELLOW}警告: 已为HTTPS后端禁用TLS证书验证 (NODE_TLS_REJECT_UNAUTHORIZED=0)。${NC}"
fi

# 切换到脚本所在目录的前端根目录
cd "$(dirname "$0")" || exit

# 启动Vite开发服务器
echo "正在启动Vite开发服务器..."
pnpm dev