/**
 * 多模态分析专用日志工具
 * 在生产环境中减少日志输出，只保留关键信息
 */

import logger from './logger';
import { LOGGING_CONFIG, shouldLog } from '../config/logging';

export class MultimodalLogger {
  private static isDevelopment = process.env.NODE_ENV === 'development';

  /**
   * 记录关键信息（在所有环境中都会记录）
   */
  static info(message: string, data?: any) {
    logger.info('MultimodalAnalysis', message, data);
  }

  /**
   * 记录警告信息（在所有环境中都会记录）
   */
  static warn(message: string, data?: any) {
    logger.warn('MultimodalAnalysis', message, data);
  }

  /**
   * 记录错误信息（在所有环境中都会记录）
   */
  static error(message: string, data?: any) {
    logger.error('MultimodalAnalysis', message, data);
  }

  /**
   * 记录调试信息（仅在开发环境中记录）
   */
  static debug(message: string, data?: any) {
    if (shouldLog.debug()) {
      logger.debug('MultimodalAnalysis', message, data);
    }
  }

  /**
   * 记录流式传输信息（仅在开发环境中记录）
   */
  static stream(message: string, data?: any) {
    // 使用配置控制流式传输日志
    if (shouldLog.stream()) {
      logger.stream('MultimodalAnalysis', message, data);
    }
  }

  /**
   * 记录设备检测信息（仅在开发环境中记录）
   */
  static device(message: string, data?: any) {
    if (shouldLog.device()) {
      console.log(`🔧 ${message}:`, data);
    }
  }

  /**
   * 记录性能信息（仅在开发环境中记录）
   */
  static performance(message: string, data?: any) {
    if (shouldLog.performance()) {
      console.log(`📊 ${message}:`, data);
    }
  }

  /**
   * 记录进度信息（减少频率）
   */
  static progress(currentChunk: number, totalLength: number, threshold?: number) {
    // 使用配置中的阈值
    const actualThreshold = threshold || LOGGING_CONFIG.progressLogThreshold;

    // 只在达到阈值时记录进度，并且只在开发环境中记录
    if (shouldLog.debug() && currentChunk % actualThreshold === 0) {
      this.debug(`进度更新: 已接收 ${currentChunk} 块，总长度: ${totalLength}`);
    }
  }

  /**
   * 设置是否为开发环境（用于测试）
   */
  static setDevelopmentMode(isDev: boolean) {
    this.isDevelopment = isDev;
  }
}
