# MultimodalLogger 使用示例

## 基本用法

```typescript
import { MultimodalLogger } from './multimodalLogger';

// 关键信息（所有环境都会记录）
MultimodalLogger.info('分析开始', { imageId: 'abc123' });
MultimodalLogger.warn('检测到潜在问题', { issue: 'timeout' });
MultimodalLogger.error('分析失败', new Error('网络错误'));

// 调试信息（仅开发环境记录）
MultimodalLogger.debug('详细状态信息', {
  state: 'processing',
  progress: 0.5,
});

// 设备信息（仅开发环境记录）
MultimodalLogger.device('设备检测结果', {
  isMobile: true,
  isWechat: false,
  isSafari: true,
});

// 性能信息（仅开发环境记录）
MultimodalLogger.performance('延迟策略', {
  firstDelay: 1000,
  secondDelay: 1500,
  totalDelay: 2500,
});

// 进度信息（自动控制频率）
for (let i = 1; i <= 5000; i++) {
  MultimodalLogger.progress(i, i * 100); // 只在特定阈值时记录
}

// 流式传输信息（完全禁用）
MultimodalLogger.stream('接收数据块'); // 不会输出任何日志
```

## 环境配置

### 开发环境 (NODE_ENV=development)

- ✅ info, warn, error 日志
- ✅ debug 日志
- ✅ device, performance 日志
- ❌ stream 日志（完全禁用）
- 📊 progress 日志（每1000次记录一次）

### 生产环境 (NODE_ENV=production)

- ✅ info, warn, error 日志
- ❌ debug 日志
- ❌ device, performance 日志
- ❌ stream 日志
- 📊 progress 日志（每5000次记录一次）

## 手动控制

```typescript
// 临时启用开发模式（用于调试）
MultimodalLogger.setDevelopmentMode(true);
MultimodalLogger.debug('临时调试信息'); // 会输出

// 恢复正常模式
MultimodalLogger.setDevelopmentMode(false);
MultimodalLogger.debug('正常调试信息'); // 根据环境决定是否输出
```

## 最佳实践

1. **使用合适的日志级别**

   - `info`: 关键业务信息
   - `warn`: 警告和潜在问题
   - `error`: 错误信息
   - `debug`: 详细调试信息

2. **避免过度日志记录**

   - 不要在循环中使用 `info` 级别
   - 使用 `progress` 方法记录进度
   - 避免记录敏感信息

3. **结构化日志数据**

   ```typescript
   // 好的做法
   MultimodalLogger.info('用户操作', {
     action: 'upload',
     fileSize: 1024,
     timestamp: Date.now(),
   });

   // 避免的做法
   MultimodalLogger.info(`用户上传了${fileSize}字节的文件`);
   ```

4. **错误处理**
   ```typescript
   try {
     await processData();
   } catch (error) {
     MultimodalLogger.error('数据处理失败', {
       error: error.message,
       stack: error.stack,
       context: { userId, dataId },
     });
     throw error;
   }
   ```
