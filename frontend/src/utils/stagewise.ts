import { initToolbar } from '@stagewise/toolbar';

// Stagewise工具栏配置
const stagewiseConfig = {
  plugins: [
    {
      name: 'emotion-analysis-plugin',
      description: '情感分析组件上下文信息',
      shortInfoForPrompt: () => {
        const selectedElement = document.querySelector('.ant-card, .emotion-card, .user-card');
        if (selectedElement) {
          return `当前选中的是情感分析相关组件，包含用户界面元素`;
        }
        return '选中的元素可能与情感分析功能相关';
      },
      mcp: null,
      actions: [
        {
          name: '查看组件信息',
          description: '显示当前组件的详细信息',
          execute: () => {
            const info = {
              页面: window.location.pathname,
              主题: document.documentElement.getAttribute('data-theme') || 'light',
              用户代理: navigator.userAgent.includes('Mobile') ? '移动设备' : '桌面设备',
              微信环境: navigator.userAgent.includes('MicroMessenger') ? '是' : '否',
            };

            console.log('当前组件信息:', info);
            window.alert(
              `组件信息:\n${Object.entries(info)
                .map(([k, v]) => `${k}: ${v}`)
                .join('\n')}`,
            );
          },
        },
        {
          name: '切换主题',
          description: '在亮色和暗色主题之间切换',
          execute: () => {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            window.alert(`已切换到${newTheme === 'dark' ? '暗色' : '亮色'}主题`);
          },
        },
        {
          name: '显示页面统计',
          description: '显示当前页面的组件统计信息',
          execute: () => {
            const stats = {
              按钮数量: document.querySelectorAll('button, .ant-btn').length,
              卡片数量: document.querySelectorAll('.ant-card').length,
              表单元素: document.querySelectorAll('input, textarea, select').length,
              图片数量: document.querySelectorAll('img').length,
              链接数量: document.querySelectorAll('a').length,
            };

            console.log('页面统计:', stats);
            window.alert(
              `页面统计:\n${Object.entries(stats)
                .map(([k, v]) => `${k}: ${v}`)
                .join('\n')}`,
            );
          },
        },
      ],
    },
    {
      name: 'responsive-design-plugin',
      description: '响应式设计助手',
      shortInfoForPrompt: () => {
        return `当前窗口尺寸: ${window.innerWidth}x${window.innerHeight}, 设备类型: ${window.innerWidth <= 768 ? '移动设备' : '桌面设备'}`;
      },
      mcp: null,
      actions: [
        {
          name: '模拟移动设备',
          description: '调整视窗到移动设备尺寸',
          execute: () => {
            // 由于安全限制，我们无法直接调整窗口大小，但可以提供信息
            window.alert('请在浏览器开发者工具中使用设备模拟器来测试移动端效果');
          },
        },
        {
          name: '检查响应式断点',
          description: '显示当前断点信息',
          execute: () => {
            const width = window.innerWidth;
            let breakpoint = '';

            if (width < 576) breakpoint = 'xs (< 576px)';
            else if (width < 768) breakpoint = 'sm (576px - 768px)';
            else if (width < 992) breakpoint = 'md (768px - 992px)';
            else if (width < 1200) breakpoint = 'lg (992px - 1200px)';
            else if (width < 1600) breakpoint = 'xl (1200px - 1600px)';
            else breakpoint = 'xxl (≥ 1600px)';

            window.alert(`当前断点: ${breakpoint}\n窗口尺寸: ${width}x${window.innerHeight}`);
          },
        },
      ],
    },
  ],
};

/**
 * 初始化Stagewise工具栏
 * 只在开发环境中启用
 */
export function setupStagewise() {
  // 只在开发环境中初始化，且只初始化一次
  if (process.env.NODE_ENV === 'development' && !window.__STAGEWISE_INITIALIZED__) {
    try {
      console.log('🎨 初始化 Stagewise 开发工具...');
      initToolbar(stagewiseConfig);
      window.__STAGEWISE_INITIALIZED__ = true;
      console.log('✅ Stagewise 工具栏已初始化');
    } catch (error) {
      console.error('❌ Stagewise 初始化失败:', error);
    }
  }
}

// 类型声明
declare global {
  interface Window {
    __STAGEWISE_INITIALIZED__?: boolean;
  }
}
