import React, { useState, useEffect, useRef } from 'react';
import { Button, Spin, Typography, Divider, message } from 'antd';
import { ThunderboltOutlined } from '@ant-design/icons';
import '../styles/multimodal-analysis.less';
import '../styles/multimodal-unlimited.css';
import '../styles/multimodal-layout-fix.css';
import { AnalysisResult } from '../types';
import logger from '../../../../utils/logger';
import { MultimodalLogger } from '../../../../utils/multimodalLogger';

const { Text } = Typography;

// 示例多模态分析结果
const EXAMPLE_MULTIMODAL_RESULT = `好的，作为情绪分析专家，我将对照片进行分析并给出建议。
1. 我的初步分析：
从这张照片来看，我认为照片中的人物是一位年轻女性，年龄大概在20-30岁之间。她正在微笑，面部表情非常积极阳光，看起来很开心。根据她的五官特征和肤色，我判断她可能是白人。整体感觉是充满活力、自信且快乐的。
2. 与系统分析结果对比：
系统的分析结果与我的初步判断高度一致：
*   性别: female (女性)
*   年龄: 25岁 （符合我的20-30岁预估）
*   种族: white/白人 （一致）
*   情绪: dominant_emotion: Happy (开心) - 系统分析显示"Happy"的情绪占比最高，为72.25%，这与我从照片中观察到的结果完全吻合。虽然其他负面情绪（愤怒、厌恶、恐惧、悲伤等）也有一定比例，但都非常低，表明整体情绪是积极向上的。
综合结论:  照片中的人物是一位大约25岁的白人女性，目前处于非常开心和快乐的状态。系统分析进一步证实了这一点，显示其主要情绪为"Happy"，并且负面情绪占比很小。
3. 针对性的建议：
虽然照片中的女士看起来很开心，但为了帮助她保持甚至提升这种积极的情绪状态，我提出以下建议:
*   继续做让你感到快乐的事情:  既然你已经找到了让自己开心的活动和爱好，请继续投入其中。这可以是与朋友相聚、阅读、运动、绘画等任何能带给你愉悦感的事情。
*   练习感恩: 每天花几分钟时间回顾一下生活中值得感恩的事，即使是很小的事情，例如一杯美味的咖啡、一个温暖的拥抱、晴朗的天气等等。这有助于培养积极的心态。
*   保持社交联系:  与家人和朋友保持良好的沟通和互动，分享你的快乐和烦恼。社会支持是应对压力的重要资源。
*   关注身心健康:  规律作息，均衡饮食，适量运动。身体的健康状况直接影响情绪状态。
*   正念冥想/呼吸练习: 如果感到压力或焦虑，可以尝试进行正念冥想或深呼吸练习，这有助于放松身心，平复情绪。
*   接纳负面情绪： 即使是开心的人也会有低落的时候。当出现负面情绪时，不要压抑或逃避，而是允许自己感受它，并寻找健康的应对方式。
总而言之，保持积极乐观的心态需要长期的努力和实践。希望这些建议能对照片中的女士有所帮助！`;

interface MultimodalAnalysisProps {
  imageId: string | null;
  isDarkMode: boolean;
  analysisResult?: AnalysisResult; // 添加情绪分析结果参数
}

const MultimodalAnalysis: React.FC<MultimodalAnalysisProps> = ({
  imageId,
  isDarkMode,
  analysisResult,
}) => {
  const [analyzing, setAnalyzing] = useState<boolean>(false);
  const [result, setResult] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const resultRef = useRef<HTMLDivElement>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  // 检查是否为示例模式，并在示例模式下显示示例分析结果
  useEffect(() => {
    // 检查是否为示例模式
    const isExampleMode = localStorage.getItem('using_example_data') === 'true';
    const isExampleId = imageId === 'cxy889';

    // 如果是示例模式且是示例ID，显示示例分析结果
    if (isExampleMode && isExampleId && !result) {
      MultimodalLogger.info('示例模式：显示示例多模态分析结果');
      setResult(EXAMPLE_MULTIMODAL_RESULT);
    }
  }, [imageId, result]);

  // 检测是否为移动设备
  const isMobileDevice = (): boolean => {
    return (
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
      window.innerWidth <= 768
    );
  };

  // 检测是否为微信浏览器
  const isWechatBrowser = (): boolean => {
    return /MicroMessenger/i.test(navigator.userAgent);
  };

  // 获取适合当前浏览器的副标题文字
  const getSubtitleText = (): string => {
    const isWechat = isWechatBrowser();
    const isMobile = isMobileDevice();

    if (isWechat || isMobile) {
      // 微信浏览器或移动端使用更简洁的文字
      return '（基于以上分析结果的AI独立分析与建议）';
    } else {
      // 桌面端使用完整文字
      return '（基于以上分析结果的AI独立分析与建议）';
    }
  };

  // 检测是否为Safari浏览器
  const isSafariBrowser = (): boolean => {
    return /Safari/i.test(navigator.userAgent) && !/Chrome/i.test(navigator.userAgent);
  };

  // 获取设备感知的延迟时间
  const getDeviceAwareDelays = () => {
    const isMobile = isMobileDevice();
    const isWechat = isWechatBrowser();
    const isSafari = isSafariBrowser();

    // 根据设备和浏览器类型设置更长的延迟时间
    let firstDelay = 200; // 桌面端默认
    let secondDelay = 300; // 桌面端默认

    if (isMobile) {
      if (isWechat) {
        // 微信浏览器需要最长延迟
        firstDelay = 1500;
        secondDelay = 2000;
      } else if (isSafari) {
        // Safari移动端需要较长延迟
        firstDelay = 1200;
        secondDelay = 1800;
      } else {
        // 其他移动端浏览器
        firstDelay = 1000;
        secondDelay = 1500;
      }
    }

    MultimodalLogger.device(
      '设备检测结果',
      `${isMobile ? '移动端' : '桌面端'}, ${isWechat ? '微信' : ''}${isSafari ? 'Safari' : ''}浏览器`,
    );
    MultimodalLogger.performance(
      '延迟策略',
      `${firstDelay}ms + ${secondDelay}ms = ${firstDelay + secondDelay}ms总延迟`,
    );

    return { firstDelay, secondDelay };
  };

  // 组件加载时检测浏览器类型
  useEffect(() => {
    const isWechat = isWechatBrowser();
    if (isWechat) {
      MultimodalLogger.info('检测到微信浏览器，将使用特殊优化');
    }
  }, []);

  
  

  // 清理函数，关闭EventSource连接
  const cleanupEventSource = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
      // 移除频繁的清理日志
    }
  };

  // 清理可能影响其他组件的全局状态
  const cleanupGlobalState = () => {
    try {
      // 清理可能的全局变量
      if ((window as any).lastMobileUpdate) {
        delete (window as any).lastMobileUpdate;
        MultimodalLogger.debug('已清理全局变量 lastMobileUpdate');
      }

      // iOS Safari 特殊清理
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

      if (isIOS && isSafari) {
        // 延迟执行，确保其他组件不受影响
        setTimeout(() => {
          MultimodalLogger.debug('iOS Safari 执行额外清理');
          // 这里可以添加额外的iOS Safari专用清理逻辑
        }, 100);
      }

      MultimodalLogger.debug('全局状态清理完成');
    } catch (error) {
      MultimodalLogger.error('清理全局状态时出错', error);
    }
  };

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      MultimodalLogger.debug('组件正在卸载，执行清理...');
      cleanupEventSource();
      cleanupGlobalState();
    };
  }, []);

  // 注意：我们现在直接在pre标签中处理文本，不再需要单独的格式化函数

  return (
    <div
      className="multimodal-analysis-container"
      style={{
        padding: '15px',
        borderRadius: '8px',
        marginBottom: '20px',
        backgroundColor: isDarkMode ? '#1f1f1f' : '#f5f5f5',
        border: `1px solid ${isDarkMode ? '#333' : '#e8e8e8'}`,
        width: '100%',
        boxSizing: 'border-box',
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '10px',
          flexWrap: 'wrap', // 添加换行支持
          gap: '10px', // 添加间距
        }}
      >
        <div
          style={{
            flex: '1',
            minWidth: '0', // 防止文本撑开容器
            marginRight: '10px', // 为按钮留出空间
          }}
        >
          <Text strong style={{ fontSize: '16px', color: isDarkMode ? '#fff' : '#000' }}>
            多模态分析 Multimodal analysis
          </Text>
          <br />
          <Text
            style={{
              fontSize: '12px', // 减小字体大小以适应微信浏览器
              color: isDarkMode ? '#aaa' : '#666',
              whiteSpace: 'nowrap', // 强制单行显示
              overflow: 'hidden', // 隐藏溢出内容
              textOverflow: 'ellipsis', // 显示省略号
              display: 'block', // 确保为块级元素
              maxWidth: '100%', // 限制最大宽度
            }}
          >
            {getSubtitleText()}
          </Text>
        </div>
        <Button
          type="primary"
          size="large"
          onClick={handleMultimodalAnalysis}
          loading={analyzing}
          disabled={!imageId}
          style={{
            height: '72px', // 进一步增加高度到72px，确保微信浏览器中文英文都能显示
            fontSize: '14px', // 稍微减小字体以适应更多内容
            borderRadius: '36px', // 相应调整圆角
            background: 'linear-gradient(45deg, #ff6b6b, #ee5a24)',
            border: 'none',
            boxShadow: '0 4px 15px rgba(255,107,107,0.4)',
            minWidth: '180px', // 增加最小宽度
            maxWidth: '220px', // 添加最大宽度限制
            color: '#ffffff',
            lineHeight: '1.3', // 调整行高
            padding: '8px 24px', // 增加内边距，特别是垂直方向
            flexShrink: 0,
            whiteSpace: 'normal', // 允许文字换行以适应微信浏览器
            wordBreak: 'break-word', // 允许单词断行
            // 微信浏览器特殊优化
            ...(isWechatBrowser()
              ? {
                  minHeight: '72px', // 微信浏览器确保最小高度
                  fontSize: '13px', // 微信浏览器使用更小字体
                  lineHeight: '1.4', // 微信浏览器使用更大行高
                  padding: '12px 20px', // 微信浏览器增加垂直内边距
                }
              : {}),
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              width: '100%',
            }}
          >
            <ThunderboltOutlined style={{ fontSize: '16px' }} />
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                lineHeight: '1.2',
              }}
            >
              {analyzing ? (
                <>
                  <span style={{ fontSize: '14px' }}>分析中...</span>
                  <span style={{ fontSize: '9px', opacity: 0.9 }}>Analyzing...</span>
                </>
              ) : (
                <>
                  <span style={{ fontSize: '14px' }}>执行分析</span>
                  <span style={{ fontSize: '9px', opacity: 0.9 }}>Execute Analysis</span>
                </>
              )}
            </div>
          </div>
        </Button>
      </div>

      <Divider style={{ margin: '10px 0', borderColor: isDarkMode ? '#333' : '#e8e8e8' }} />

      <div
        ref={resultRef}
        className="multimodal-result"
        style={{
          minHeight: '100px',
          overflowY: 'auto',
          padding: '10px',
          backgroundColor: isDarkMode ? '#141414' : '#fff',
          borderRadius: '4px',
          border: `1px solid ${isDarkMode ? '#333' : '#e8e8e8'}`,
          color: isDarkMode ? '#fff' : '#000',
          width: '100%',
          boxSizing: 'border-box',
        }}
      >
        {analyzing && !result && (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin />
            <div style={{ marginTop: '10px', color: isDarkMode ? '#aaa' : '#666' }}>
              正在分析中，请稍候...
            </div>
          </div>
        )}

        {error && <div style={{ color: 'red', padding: '10px' }}>错误: {error}</div>}

        {result && (
          <pre
            style={{
              margin: 0,
              color: isDarkMode ? '#fff' : '#000',
              lineHeight: '1.6',
              textAlign: 'left',
              fontFamily: 'inherit',
              fontSize: 'inherit',
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word',
              backgroundColor: 'transparent',
              border: 'none',
              padding: 0,
              width: '100%',
              boxSizing: 'border-box',
              wordBreak: 'break-word',
            }}
            className="multimodal-result-text"
            dangerouslySetInnerHTML={{
              __html: result
                .replace(/\*\*/g, '') // 去除**标记
                .replace(/\\n/g, '\n') // 将\n字符转换为实际的换行符
                .replace(/\n\s*\n+/g, '\n'), // 将多个空行替换为单个换行
            }}
          />
        )}

        {!analyzing && !result && !error && (
          <div
            style={{ textAlign: 'center', padding: '20px', color: isDarkMode ? '#aaa' : '#666' }}
          >
            点击"多模态分析"按钮，AI将独立分析图片并结合系统分析结果给出建议
          </div>
        )}
      </div>
    </div>
  );
};

export default MultimodalAnalysis;
