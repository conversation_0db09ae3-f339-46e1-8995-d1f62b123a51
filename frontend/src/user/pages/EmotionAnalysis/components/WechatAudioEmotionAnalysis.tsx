import React, { useState, useRef, useEffect } from 'react';
import { Button, Card, Progress, message, Alert, Typography } from 'antd';
import {
  AudioOutlined,
  StopOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  DeleteOutlined,
  ExperimentOutlined,
} from '@ant-design/icons';
import { checkWechatAudioSupport, WechatAudioRecorder } from '@/shared/utils/wechatCompat';

const { Text, Title } = Typography;

interface WechatAudioEmotionAnalysisProps {
  onAnalysisComplete?: (result: any) => void;
  isDarkMode?: boolean;
}

const WechatAudioEmotionAnalysis: React.FC<WechatAudioEmotionAnalysisProps> = ({
  onAnalysisComplete,
  isDarkMode = false,
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioSupport, setAudioSupport] = useState<any>(null);
  const [isStoppingRecording, setIsStoppingRecording] = useState(false);
  const [analysisId, setAnalysisId] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const recorderRef = useRef<WechatAudioRecorder | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    // 检测微信浏览器音频支持情况
    const support = checkWechatAudioSupport();
    setAudioSupport(support);

    if (support.supported) {
      recorderRef.current = new WechatAudioRecorder();
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (recorderRef.current) {
        recorderRef.current.cleanup();
      }
      // 清理音频资源
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = '';
        audioRef.current = null;
      }
    };
  }, []);

  // 状态监控useEffect，用于调试UI显示问题
  useEffect(() => {
    console.log('🎤 微信浏览器：状态变化监控:', {
      isRecording,
      isStoppingRecording,
      hasAudioBlob: !!audioBlob,
      audioBlobSize: audioBlob?.size,
      audioBlobType: audioBlob?.type,
      isAnalyzing,
      hasAnalysisResult: !!analysisResult,
      recordingTime,
      timestamp: new Date().toISOString(),
    });

    // 特别检查录音文件信息显示条件
    const shouldShowFileInfo = audioBlob && !isRecording;
    console.log('🎤 微信浏览器：录音文件信息显示条件:', {
      audioBlob: !!audioBlob,
      isRecording,
      shouldShowFileInfo,
      conditionCheck: `audioBlob(${!!audioBlob}) && !isRecording(${!isRecording}) = ${shouldShowFileInfo}`,
    });
  }, [isRecording, isStoppingRecording, audioBlob, isAnalyzing, analysisResult, recordingTime]);

  const startRecording = async () => {
    if (!recorderRef.current) {
      message.error('录音功能未初始化');
      return;
    }

    try {
      const result = await recorderRef.current.startRecording();

      if (result.success) {
        // 停止当前播放
        if (audioRef.current && isPlaying) {
          audioRef.current.pause();
          setIsPlaying(false);
        }

        setIsRecording(true);
        setRecordingTime(0);
        setAudioBlob(null);
        setAnalysisResult(null);
        setIsStoppingRecording(false); // 重置停止状态

        // 开始计时
        timerRef.current = setInterval(() => {
          setRecordingTime((prev) => {
            const newTime = prev + 1;
            // 30秒后自动停止录音
            if (newTime >= 30) {
              // 使用 setTimeout 确保在下一个事件循环中执行，避免状态冲突
              setTimeout(() => {
                // 检查录音器状态而不是 React 状态，避免闭包问题
                if (recorderRef.current && timerRef.current) {
                  console.log('🎤 微信浏览器：达到30秒，自动停止录音');
                  stopRecording();
                  message.info('已达到30秒最大录音时长，录音已自动停止');
                }
              }, 0);
            }
            return newTime;
          });
        }, 1000);

        message.success('录音已开始（最长30秒）');
      } else {
        message.error(result.error || '录音启动失败');

        // 如果是权限问题，提供用户指导
        if (result.error?.includes('权限')) {
          showPermissionGuide();
        }
      }
    } catch (error) {
      console.error('录音启动失败：', error);
      message.error('录音启动失败');
    }
  };

  const stopRecording = async () => {
    if (!recorderRef.current) {
      message.error('录音功能未初始化');
      return;
    }

    // 防止重复点击
    if (isStoppingRecording) {
      console.log('🎤 微信浏览器：正在停止录音中，忽略重复点击');
      return;
    }

    console.log('🎤 微信浏览器：开始停止录音');
    console.log('🎤 微信浏览器：停止前状态检查:', {
      isRecording,
      isStoppingRecording,
      hasRecorder: !!recorderRef.current,
      currentAudioBlob: !!audioBlob,
      recordingTime,
    });

    setIsStoppingRecording(true);

    try {
      // 立即设置录音状态为false，确保UI正确显示
      setIsRecording(false);

      // 停止计时
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      console.log('🎤 微信浏览器：调用recorder.stopRecording()...');
      const result = await recorderRef.current.stopRecording();

      console.log('🎤 微信浏览器：stopRecording结果:', {
        success: result.success,
        hasAudioBlob: !!result.audioBlob,
        audioBlobSize: result.audioBlob?.size,
        audioBlobType: result.audioBlob?.type,
        error: result.error,
      });

      if (result.success && result.audioBlob) {
        console.log('🎤 微信浏览器：设置audioBlob状态...');
        setAudioBlob(result.audioBlob);

        // 延迟一下确保状态更新完成
        setTimeout(() => {
          console.log('🎤 微信浏览器：状态更新后检查:', {
            isRecording,
            isStoppingRecording,
            hasAudioBlob: !!audioBlob,
            newAudioBlobSize: result.audioBlob?.size,
          });
        }, 100);

        message.success('录音已完成，点击“分析情绪”按钮开始分析');
        console.log('🎤 微信浏览器：录音停止成功，等待用户手动分析');
      } else {
        console.error('🎤 微信浏览器：录音停止失败:', result.error);
        message.error(result.error || '录音停止失败');
      }
    } catch (error) {
      console.error('🎤 微信浏览器：录音停止异常:', error);
      message.error('录音停止失败');

      // 确保录音状态被重置
      setIsRecording(false);
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    } finally {
      // 无论成功还是失败，都要重置停止状态
      setIsStoppingRecording(false);
      console.log('🎤 微信浏览器：停止录音流程完成');

      // 最终状态检查
      setTimeout(() => {
        console.log('🎤 微信浏览器：最终状态检查:', {
          isRecording,
          isStoppingRecording,
          hasAudioBlob: !!audioBlob,
          recordingTime,
        });
      }, 200);
    }
  };

  const analyzeAudio = async (blob: Blob) => {
    setIsAnalyzing(true);

    try {
      // 检查是否为微信 JS-SDK 录音
      if (blob.type === 'application/json') {
        const text = await blob.text();
        const data = JSON.parse(text);

        if (data.type === 'wechat' && data.localId) {
          message.info('微信录音需要上传到服务器进行分析');
          // 微信 JS-SDK 录音暂不支持直接分析
          setAnalysisResult({
            emotion: 'neutral',
            confidence: 0.8,
            message: '微信 JS-SDK 录音分析功能正在开发中，建议使用 WebRTC 录音',
          });
          setIsAnalyzing(false);
          return;
        }
      }

      // 获取token
      const token = localStorage.getItem('user_token') || '';
      if (!token) {
        message.error('未登录，请先登录');
        setIsAnalyzing(false);
        return;
      }

      // 普通 WebRTC 录音的分析逻辑
      const formData = new FormData();
      formData.append('file', blob, 'wechat-recording.webm');

      console.log('🎤 微信浏览器：开始音频分析', {
        blobSize: blob.size,
        blobType: blob.type,
      });

      // 调用音频分析 API
      const apiUrl = `/api/v1/emotion/audio-emotion-analysis`;
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 创建ReadableStream reader
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法读取响应流');
      }

      // 读取流数据
      let resultText = '';
      let decoder = new TextDecoder();
      let buffer = ''; // 缓冲区用于处理不完整的数据块

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            console.log('🎤 微信浏览器：流读取完成');
            break;
          }

          // 解码数据块
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // 按行分割处理
          const lines = buffer.split('\n');
          // 保留最后一个可能不完整的行
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim().startsWith('data: ')) {
              try {
                const dataStr = line.substring(6).trim();
                if (dataStr === '[DONE]') {
                  console.log('🎤 微信浏览器：接收到DONE标记');
                  continue;
                }

                const data = JSON.parse(dataStr);

                if (data.error) {
                  console.error('🎤 微信浏览器：服务器返回错误:', data.error);
                  throw new Error(data.error);
                } else if (data.content) {
                  resultText += data.content;
                  console.log('🎤 微信浏览器：接收到内容:', data.content.substring(0, 50) + '...');
                }
              } catch (parseError) {
                console.error('🎤 微信浏览器：解析数据失败:', parseError, '原始数据:', line);
              }
            }
          }
        }

        // 处理缓冲区中剩余的数据
        if (buffer.trim()) {
          const lines = buffer.split('\n');
          for (const line of lines) {
            if (line.trim().startsWith('data: ')) {
              try {
                const dataStr = line.substring(6).trim();
                if (dataStr !== '[DONE]') {
                  const data = JSON.parse(dataStr);
                  if (data.content) {
                    resultText += data.content;
                  }
                }
              } catch (parseError) {
                console.error('🎤 微信浏览器：解析缓冲区数据失败:', parseError);
              }
            }
          }
        }
      } finally {
        // 确保释放reader
        try {
          reader.releaseLock();
        } catch (e) {
          console.log('🎤 微信浏览器：释放reader锁失败:', e);
        }
      }

      console.log('🎤 微信浏览器：音频情绪分析完成，结果长度:', resultText.length);

      // 生成一个分析ID用于反馈
      const newAnalysisId = `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      setAnalysisId(newAnalysisId);

      // 设置分析结果
      setAnalysisResult({
        emotion: 'analyzed',
        confidence: 1.0,
        message: resultText || '分析完成，但未收到结果内容',
      });

      if (onAnalysisComplete) {
        onAnalysisComplete({
          emotion: 'analyzed',
          confidence: 1.0,
          message: resultText,
        });
      }

      setIsAnalyzing(false);
    } catch (error) {
      console.error('🎤 微信浏览器：音频分析失败:', error);
      message.error(`音频分析失败: ${error instanceof Error ? error.message : String(error)}`);
      setIsAnalyzing(false);
    }
  };

  const showPermissionGuide = () => {
    message.info({
      content: (
        <div>
          <div>请按以下步骤开启录音权限：</div>
          <div>1. 点击微信右上角 &quot;...&quot; 菜单</div>
          <div>2. 选择 &quot;设置&quot;</div>
          <div>3. 开启 &quot;使用麦克风&quot; 权限</div>
          <div>4. 刷新页面重试</div>
        </div>
      ),
      duration: 8,
    });
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const clearRecording = () => {
    // 停止播放
    if (audioRef.current && isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    }

    setAudioBlob(null);
    setAnalysisResult(null);
    setRecordingTime(0);
    setAnalysisId(null); // 清除分析ID
    setIsPlaying(false);
    message.info('录音已清除');
  };

  // 播放/暂停音频控制
  const togglePlayAudio = () => {
    if (!audioBlob) {
      message.error('没有可播放的音频文件');
      return;
    }

    try {
      if (isPlaying) {
        // 暂停播放
        if (audioRef.current) {
          audioRef.current.pause();
          setIsPlaying(false);
        }
      } else {
        // 开始播放
        if (!audioRef.current) {
          // 创建新的音频元素
          audioRef.current = new Audio(URL.createObjectURL(audioBlob));

          // 添加事件监听器
          audioRef.current.addEventListener('ended', () => {
            setIsPlaying(false);
          });

          audioRef.current.addEventListener('error', () => {
            setIsPlaying(false);
            message.error('音频播放失败');
          });
        }

        audioRef.current
          .play()
          .then(() => {
            setIsPlaying(true);
          })
          .catch((error) => {
            console.error('播放失败:', error);
            setIsPlaying(false);
            message.error('音频播放失败');
          });
      }
    } catch (error) {
      console.error('播放控制失败:', error);
      setIsPlaying(false);
      message.error('播放控制失败');
    }
  };

  // 如果不支持录音，显示提示信息
  if (!audioSupport?.supported) {
    return (
      <Card title="🎤 微信浏览器音频情绪分析">
        <Alert
          message="录音功能不可用"
          description={audioSupport?.reason || '当前环境不支持录音功能'}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        {audioSupport?.reason?.includes('HTTPS') && (
          <Alert
            message="需要 HTTPS 环境"
            description="微信浏览器中的录音功能需要在 HTTPS 环境下才能正常工作，请确保网站使用 HTTPS 协议访问。"
            type="info"
            showIcon
          />
        )}
      </Card>
    );
  }

  // 根据暗黑模式动态配色
  const getThemeColors = () => {
    if (isDarkMode) {
      return {
        // 暗黑模式配色 - 使用系统统一的背景色
        background: '#1f1f1f', // 使用系统卡片背景色，与其他组件保持一致
        cardBackground: 'rgba(255,255,255,0.08)',
        textColor: 'rgba(255, 255, 255, 0.85)', // 使用系统文字颜色
        secondaryTextColor: '#a0a0a0', // 使用系统次要文字颜色
        borderColor: '#303030', // 使用系统边框颜色
        recordingGradient: 'linear-gradient(45deg, #ff6b6b, #ee5a24)',
        analyzeGradient: 'linear-gradient(45deg, #11998e, #38ef7d)',
        fileIconGradient: 'linear-gradient(45deg, #4facfe, #00f2fe)',
        shadowColor: 'rgba(0,0,0,0.2)', // 使用系统阴影
        glassBackground: 'rgba(255,255,255,0.05)',
        resultBackground: '#1f1f1f', // 暗黑模式下结果区域也使用深色背景
        resultTextColor: 'rgba(255, 255, 255, 0.85)',
      };
    } else {
      return {
        // 亮色模式配色
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        cardBackground: 'rgba(255,255,255,0.15)',
        textColor: '#ffffff',
        secondaryTextColor: 'rgba(255,255,255,0.8)',
        borderColor: 'rgba(255,255,255,0.2)',
        recordingGradient: 'linear-gradient(45deg, #ff6b6b, #ee5a24)',
        analyzeGradient: 'linear-gradient(45deg, #11998e, #38ef7d)',
        fileIconGradient: 'linear-gradient(45deg, #4facfe, #00f2fe)',
        shadowColor: 'rgba(0,0,0,0.1)',
        glassBackground: 'rgba(255,255,255,0.15)',
        resultBackground: 'rgba(255,255,255,0.95)',
        resultTextColor: '#333',
      };
    }
  };

  const themeColors = getThemeColors();

  return (
    <div
      style={{
        background: themeColors.background,
        borderRadius: '16px',
        padding: '24px',
        color: themeColors.textColor,
        boxShadow: `0 8px 32px ${themeColors.shadowColor}`,
        border: isDarkMode ? `1px solid ${themeColors.borderColor}` : 'none',
      }}
    >
      {/* 标题区域 */}
      <div style={{ textAlign: 'center', marginBottom: '24px' }}>
        <div
          style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px',
          }}
        >
          AI音频情绪分析
          <br />
          Audio Emotion Analysis
        </div>
        <div
          style={{
            fontSize: '14px',
            color: themeColors.secondaryTextColor,
            background: themeColors.cardBackground,
            padding: '6px 12px',
            borderRadius: '20px',
            display: 'inline-block',
            border: `1px solid ${themeColors.borderColor}`,
          }}
        ></div>
      </div>

      {/* 录音控制区域 */}
      <div style={{ textAlign: 'center', marginBottom: '32px' }}>
        {!isRecording ? (
          <div>
            <Button
              type="primary"
              size="large"
              onClick={startRecording}
              disabled={isAnalyzing}
              style={{
                height: '72px', // 与执行分析按钮一致
                fontSize: '14px',
                borderRadius: '36px', // 与执行分析按钮一致
                background: themeColors.recordingGradient,
                border: 'none',
                boxShadow: '0 4px 15px rgba(255,107,107,0.4)',
                minWidth: '180px', // 与执行分析按钮一致
                maxWidth: '220px', // 与执行分析按钮一致
                color: '#ffffff',
                lineHeight: '1.3',
                padding: '8px 24px',
                flexShrink: 0,
                whiteSpace: 'normal',
                wordBreak: 'break-word',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px',
                  width: '100%',
                }}
              >
                <AudioOutlined style={{ fontSize: '16px' }} />
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    lineHeight: '1.2',
                  }}
                >
                  {audioBlob ? (
                    <>
                      <span style={{ fontSize: '14px' }}>重新录音</span>
                      <span style={{ fontSize: '9px', opacity: 0.9 }}>Re-record</span>
                    </>
                  ) : (
                    <>
                      <span style={{ fontSize: '14px' }}>开始录音</span>
                      <span style={{ fontSize: '9px', opacity: 0.9 }}>Start Recording</span>
                    </>
                  )}
                </div>
              </div>
            </Button>
            {audioBlob && (
              <div
                style={{
                  marginTop: '12px',
                  fontSize: '14px',
                  opacity: 0.8,
                }}
              >
                点击重新录音或使用下方录音文件 Click to re-record or use the audio file below
              </div>
            )}
          </div>
        ) : (
          <div>
            <div
              style={{
                width: '120px',
                height: '120px',
                borderRadius: '50%',
                background:
                  recordingTime >= 25
                    ? 'linear-gradient(135deg, #ff4d4f, #ff7875)'
                    : recordingTime >= 20
                      ? 'linear-gradient(135deg, #faad14, #ffc53d)'
                      : themeColors.recordingGradient,
                margin: '0 auto 20px',
                animation: recordingTime >= 25 ? 'pulse 0.8s infinite' : 'pulse 1.5s infinite',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow:
                  recordingTime >= 25
                    ? '0 8px 32px rgba(255,77,79,0.5)'
                    : recordingTime >= 20
                      ? '0 8px 32px rgba(250,173,20,0.4)'
                      : '0 8px 32px rgba(255,107,107,0.3)',
              }}
            >
              <AudioOutlined style={{ color: 'white', fontSize: '48px' }} />
            </div>

            <div
              style={{
                fontSize: '32px',
                fontWeight: 'bold',
                marginBottom: '8px',
                fontFamily: 'monospace',
                color:
                  recordingTime >= 25 ? '#ff4d4f' : recordingTime >= 20 ? '#faad14' : 'inherit',
              }}
            >
              {formatTime(recordingTime)}
            </div>

            <div
              style={{
                fontSize: '16px',
                marginBottom: '16px',
                opacity: 0.9,
                color:
                  recordingTime >= 25 ? '#ff4d4f' : recordingTime >= 20 ? '#faad14' : 'inherit',
              }}
            >
              {recordingTime >= 25
                ? '即将达到最大时长...'
                : recordingTime >= 20
                  ? '录音即将结束...'
                  : '正在录音中... Recording...'}
            </div>

            {/* 录音进度条 */}
            <div
              style={{
                width: '200px',
                margin: '0 auto 20px',
                padding: '0 10px',
              }}
            >
              <Progress
                percent={(recordingTime / 30) * 100}
                strokeColor={
                  recordingTime >= 25 ? '#ff4d4f' : recordingTime >= 20 ? '#faad14' : '#52c41a'
                }
                trailColor="rgba(255,255,255,0.2)"
                showInfo={false}
                size="small"
                style={{
                  marginBottom: '8px',
                }}
              />
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  fontSize: '12px',
                  opacity: 0.8,
                  color:
                    recordingTime >= 25 ? '#ff4d4f' : recordingTime >= 20 ? '#faad14' : 'inherit',
                }}
              >
                <span>0:00</span>
                <span>{30 - recordingTime}秒剩余</span>
                <span>0:30</span>
              </div>
            </div>

            <Button
              danger
              size="large"
              onClick={stopRecording}
              disabled={isStoppingRecording}
              loading={isStoppingRecording}
              style={{
                height: '72px', // 与其他按钮一致
                fontSize: '14px',
                borderRadius: '36px', // 与其他按钮一致
                background: themeColors.cardBackground,
                border: `2px solid ${themeColors.borderColor}`,
                color: themeColors.textColor,
                minWidth: '180px', // 与其他按钮一致
                maxWidth: '220px', // 与其他按钮一致
                lineHeight: '1.3',
                padding: '8px 24px',
                flexShrink: 0,
                whiteSpace: 'normal',
                wordBreak: 'break-word',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px',
                  width: '100%',
                }}
              >
                <StopOutlined style={{ fontSize: '16px' }} />
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    lineHeight: '1.2',
                  }}
                >
                  {isStoppingRecording ? (
                    <>
                      <span style={{ fontSize: '14px' }}>停止中...</span>
                      <span style={{ fontSize: '9px', opacity: 0.9 }}>Stopping...</span>
                    </>
                  ) : (
                    <>
                      <span style={{ fontSize: '14px' }}>停止录音</span>
                      <span style={{ fontSize: '9px', opacity: 0.9 }}>Stop Recording</span>
                    </>
                  )}
                </div>
              </div>
            </Button>
          </div>
        )}
      </div>

      {/* 录音文件信息卡片 */}
      {audioBlob && !isRecording && (
        <div
          style={{
            background: themeColors.glassBackground,
            backdropFilter: 'blur(10px)',
            borderRadius: '16px',
            padding: '20px',
            marginBottom: '24px',
            border: `1px solid ${themeColors.borderColor}`,
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '16px',
              gap: '12px',
            }}
          >
            <div
              style={{
                width: '48px',
                height: '48px',
                borderRadius: '12px',
                background: themeColors.fileIconGradient,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '20px',
              }}
            >
              📁
            </div>
            <div style={{ flex: 1 }}>
              <div
                style={{
                  fontSize: '16px',
                  fontWeight: 'bold',
                  marginBottom: '4px',
                }}
              >
                录音文件 Audio File
              </div>
              <div
                style={{
                  fontSize: '13px',
                  color: themeColors.secondaryTextColor,
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '8px',
                }}
              >
                <span>📊 {(audioBlob.size / 1024).toFixed(1)} KB</span>
                <span>🎵 {audioBlob.type?.split('/')[1] || 'webm'}</span>
                <span>⏱️ {formatTime(recordingTime)}</span>
              </div>
            </div>
          </div>

          <div
            style={{
              display: 'flex',
              gap: '8px',
              flexWrap: 'wrap',
              justifyContent: 'center',
            }}
          >
            {audioBlob.type !== 'application/json' && (
              <Button
                onClick={togglePlayAudio}
                style={{
                  borderRadius: '20px',
                  background: isPlaying ? 'rgba(240,86,84,0.1)' : themeColors.cardBackground,
                  border: `1px solid ${isPlaying ? '#f05654' : themeColors.borderColor}`,
                  color: isPlaying ? '#f05654' : themeColors.textColor,
                  height: '36px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                }}
              >
                {isPlaying ? (
                  <PauseCircleOutlined style={{ fontSize: '14px' }} />
                ) : (
                  <PlayCircleOutlined style={{ fontSize: '14px' }} />
                )}
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    lineHeight: '1.1',
                  }}
                >
                  <span style={{ fontSize: '12px' }}>{isPlaying ? '暂停' : '播放'}</span>
                  <span style={{ fontSize: '8px', opacity: 0.8 }}>
                    {isPlaying ? 'Pause' : 'Play'}
                  </span>
                </div>
              </Button>
            )}
            <Button
              type="primary"
              onClick={() => analyzeAudio(audioBlob)}
              disabled={isAnalyzing}
              loading={isAnalyzing}
              style={{
                borderRadius: '20px',
                background: '#f05654', // 使用系统主题色（粉红色），与多模态分析按钮一致
                borderColor: '#f05654',
                height: '36px',
                boxShadow: '0 2px 8px rgba(240,86,84,0.3)',
                color: '#ffffff',
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
              }}
            >
              <ExperimentOutlined style={{ fontSize: '14px' }} />
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  lineHeight: '1.1',
                }}
              >
                {isAnalyzing ? (
                  <>
                    <span style={{ fontSize: '12px' }}>分析中...</span>
                    <span style={{ fontSize: '8px', opacity: 0.9 }}>Analyzing...</span>
                  </>
                ) : (
                  <>
                    <span style={{ fontSize: '12px' }}>分析情绪</span>
                    <span style={{ fontSize: '8px', opacity: 0.9 }}>Analyze Emotion</span>
                  </>
                )}
              </div>
            </Button>
            <Button
              onClick={clearRecording}
              disabled={isAnalyzing}
              style={{
                borderRadius: '20px',
                background: 'rgba(255,107,107,0.2)',
                border: '1px solid rgba(255,107,107,0.3)',
                color: themeColors.textColor,
                height: '36px',
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
              }}
            >
              <DeleteOutlined style={{ fontSize: '14px' }} />
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  lineHeight: '1.1',
                }}
              >
                <span style={{ fontSize: '12px' }}>清除</span>
                <span style={{ fontSize: '8px', opacity: 0.8 }}>Clear</span>
              </div>
            </Button>
          </div>
        </div>
      )}

      {/* 分析进度 */}
      {isAnalyzing && (
        <div
          style={{
            background: themeColors.cardBackground,
            borderRadius: '12px',
            padding: '20px',
            marginBottom: '24px',
            textAlign: 'center',
            border: `1px solid ${themeColors.borderColor}`,
          }}
        >
          <div
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              marginBottom: '16px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
            }}
          >
            🧠 AI正在分析音频情绪... AI is analyzing audio emotion...
          </div>
          <Progress
            percent={50}
            status="active"
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            style={{ marginBottom: '8px' }}
          />
          <div style={{ fontSize: '14px', color: themeColors.secondaryTextColor }}>
            请稍候，分析通常需要几秒钟 Please wait, analysis usually takes a few seconds
          </div>
        </div>
      )}

      {/* 分析结果 */}
      {analysisResult && (
        <div
          style={{
            background: isDarkMode ? themeColors.cardBackground : themeColors.resultBackground,
            borderRadius: '16px',
            padding: '24px',
            color: isDarkMode ? themeColors.textColor : themeColors.resultTextColor,
            boxShadow: `0 4px 20px ${themeColors.shadowColor}`,
            border: `1px solid ${themeColors.borderColor}`,
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '16px',
              gap: '12px',
            }}
          >
            <div
              style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                background: themeColors.background,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '18px',
              }}
            >
              🎯
            </div>
            <Title
              level={4}
              style={{
                margin: 0,
                color: isDarkMode ? themeColors.textColor : themeColors.resultTextColor,
              }}
            >
              分析结果 Analysis Result
            </Title>
          </div>

          {analysisResult.emotion === 'analyzed' ? (
            <div
              style={{
                background: isDarkMode ? 'rgba(255,255,255,0.05)' : '#f8f9fa',
                borderRadius: '12px',
                padding: '8px 4px',
                border: `1px solid ${isDarkMode ? themeColors.borderColor : '#e9ecef'}`,
              }}
            >
              <div
                style={{
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                  lineHeight: '1.4',
                  fontSize: '15px',
                  color: isDarkMode ? themeColors.textColor : '#495057',
                  width: '100%',
                  maxWidth: '100%',
                  boxSizing: 'border-box',
                }}
              >
                {analysisResult.message
                  .split('\n')
                  .filter((line: string) => line.trim() !== '') // 过滤空行
                  .map((line: string, index: number) => (
                    <React.Fragment key={index}>
                      {line.trim()}
                      <br />
                    </React.Fragment>
                  ))}
              </div>
            </div>
          ) : (
            <div style={{ textAlign: 'center' }}>
              <Title
                level={3}
                style={{ color: isDarkMode ? themeColors.textColor : themeColors.resultTextColor }}
              >
                情绪: {analysisResult.emotion}
              </Title>
              <Text
                style={{
                  fontSize: '16px',
                  color: isDarkMode ? themeColors.secondaryTextColor : '#666',
                }}
              >
                置信度: {(analysisResult.confidence * 100).toFixed(1)}%
              </Text>
              <div
                style={{
                  marginTop: '16px',
                  padding: '12px',
                  background: '#f8f9fa',
                  borderRadius: '8px',
                }}
              >
                <Text style={{ color: '#666' }}>{analysisResult.message}</Text>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 反馈组件已移至主页面统一管理，避免重复显示 */}

      <style>{`
        @keyframes pulse {
          0% {
            transform: scale(1);
            box-shadow: 0 8px 32px rgba(255,107,107,0.3);
          }
          50% {
            transform: scale(1.05);
            box-shadow: 0 12px 40px rgba(255,107,107,0.5);
          }
          100% {
            transform: scale(1);
            box-shadow: 0 8px 32px rgba(255,107,107,0.3);
          }
        }
        
        .ant-btn:hover {
          transform: translateY(-2px);
          transition: all 0.3s ease;
        }
        
        .ant-progress-bg {
          background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
        }
      `}</style>
    </div>
  );
};

export default WechatAudioEmotionAnalysis;
