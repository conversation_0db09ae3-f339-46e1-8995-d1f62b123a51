/**
 * 简单属性布局测试组件
 * 用于验证最基础的移动端响应式设计
 */

import React from 'react';
import { Card } from 'antd';
import { getEmotionColor } from '@/utils/emotionUtils';

const SimpleAttributesTest: React.FC = () => {
  // 示例数据
  const primaryEmotion = { emotion: 'sad', value: 0.41 };
  const gender = 'female';
  const age = 28;
  const raceZh = '亚裔';
  const raceEn = 'asian';

  // 基础样式变量
  const borderColor = '#e8e8e8';
  const secondaryTextColor = '#666';

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Card title="简单属性布局测试" style={{ marginBottom: '20px' }}>
        <p>这是最基础的移动端响应式设计测试，不使用任何复杂的修复脚本。</p>
        <p>四个属性应该在一行显示：主要情绪、性别、年龄、种族</p>
      </Card>

      <Card title="属性显示区域">
        {/* 基础属性容器 */}
        <div
          className="faceInfo"
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            marginBottom: '15px',
            borderBottom: `1px solid ${borderColor}`,
            paddingBottom: '15px',
            marginTop: '10px',
            flexWrap: 'nowrap',
            gap: '5px',
            width: '100%',
          }}
        >
          {/* 主要情绪 */}
          <div
            style={{
              textAlign: 'center',
              flex: '1',
            }}
          >
            <div style={{ color: secondaryTextColor, fontSize: '13px', fontWeight: 500 }}>
              主要情绪
            </div>
            <div
              style={{ color: secondaryTextColor, fontSize: '11px', fontWeight: 400, marginTop: 2 }}
            >
              Emotion
            </div>
            <div
              style={{
                color: getEmotionColor(primaryEmotion.emotion),
                fontSize: '14px',
                fontWeight: 'bold',
                marginTop: '4px',
                backgroundColor: `${getEmotionColor(primaryEmotion.emotion)}20`,
                padding: '4px 6px',
                borderRadius: '4px',
                width: '100%',
                textAlign: 'center',
                display: 'block',
                lineHeight: 1.3,
                boxSizing: 'border-box',
                borderLeft: `3px solid ${getEmotionColor(primaryEmotion.emotion)}`,
              }}
            >
              悲伤
              <span
                style={{
                  display: 'block',
                  fontSize: '12px',
                  opacity: 0.9,
                  fontWeight: 'normal',
                  color: getEmotionColor(primaryEmotion.emotion),
                }}
              >
                Sad
              </span>
            </div>
          </div>

          {/* 性别 */}
          <div
            style={{
              textAlign: 'center',
              flex: '1',
            }}
          >
            <div style={{ color: secondaryTextColor, fontSize: '13px', fontWeight: 500 }}>性别</div>
            <div
              style={{ color: secondaryTextColor, fontSize: '11px', fontWeight: 400, marginTop: 2 }}
            >
              Gender
            </div>
            <div
              style={{
                color: gender === 'female' ? '#eb2f96' : '#1890ff',
                fontSize: '14px',
                fontWeight: 'bold',
                marginTop: '4px',
                backgroundColor: gender === 'female' ? '#eb2f9620' : '#1890ff20',
                padding: '4px 6px',
                borderRadius: '4px',
                width: '100%',
                textAlign: 'center',
                display: 'block',
                lineHeight: 1.3,
                boxSizing: 'border-box',
              }}
            >
              <div>女性</div>
              <div
                style={{
                  fontSize: '12px',
                  opacity: 0.9,
                  fontWeight: 'normal',
                  marginTop: '2px',
                }}
              >
                Female
              </div>
            </div>
          </div>

          {/* 年龄 */}
          <div
            style={{
              textAlign: 'center',
              flex: '1',
            }}
          >
            <div style={{ color: secondaryTextColor, fontSize: '13px', fontWeight: 500 }}>年龄</div>
            <div
              style={{ color: secondaryTextColor, fontSize: '11px', fontWeight: 400, marginTop: 2 }}
            >
              Age
            </div>
            <div
              style={{
                color: '#13c2c2',
                fontSize: '14px',
                fontWeight: 'bold',
                marginTop: '4px',
                backgroundColor: '#13c2c220',
                padding: '4px 6px',
                borderRadius: '4px',
                width: '100%',
                textAlign: 'center',
                display: 'block',
                lineHeight: 1.3,
                boxSizing: 'border-box',
              }}
            >
              <div>{age}</div>
              <div
                style={{
                  fontSize: '12px',
                  opacity: 0.9,
                  fontWeight: 'normal',
                  marginTop: '2px',
                }}
              >
                Years
              </div>
            </div>
          </div>

          {/* 种族 */}
          <div
            style={{
              textAlign: 'center',
              flex: '1',
            }}
          >
            <div style={{ color: secondaryTextColor, fontSize: '13px', fontWeight: 500 }}>种族</div>
            <div
              style={{ color: secondaryTextColor, fontSize: '11px', fontWeight: 400, marginTop: 2 }}
            >
              Race
            </div>
            <div
              style={{
                color: '#009688',
                fontSize: '14px',
                fontWeight: 'bold',
                marginTop: '4px',
                backgroundColor: '#00968820',
                padding: '4px 6px',
                borderRadius: '4px',
                width: '100%',
                textAlign: 'center',
                display: 'block',
                lineHeight: 1.3,
                boxSizing: 'border-box',
              }}
            >
              <div>{raceZh}</div>
              <div
                style={{
                  fontSize: '12px',
                  opacity: 0.9,
                  fontWeight: 'normal',
                  marginTop: '2px',
                }}
              >
                {raceEn && raceEn.charAt(0).toUpperCase() + raceEn.slice(1)}
              </div>
            </div>
          </div>
        </div>

        <div
          style={{
            fontSize: '12px',
            color: '#666',
            textAlign: 'center',
            marginTop: '10px',
            padding: '10px',
            backgroundColor: '#f5f5f5',
            borderRadius: '4px',
          }}
        >
          <strong>测试说明：</strong>
          <br />
          1. 四个属性应该在一行显示，不换行
          <br />
          2. 每个属性占用相等的宽度（25%）
          <br />
          3. 在移动端也应该保持一行显示
          <br />
          4. 如果显示正常，说明基础响应式设计工作正常
        </div>
      </Card>
    </div>
  );
};

export default SimpleAttributesTest;
