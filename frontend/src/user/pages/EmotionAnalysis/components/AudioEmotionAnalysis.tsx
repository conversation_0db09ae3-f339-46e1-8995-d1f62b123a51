import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button, Card, Spin, Typography, message, Upload, Divider } from 'antd';
import {
  AudioOutlined,
  UploadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  DownOutlined,
  UpOutlined,
} from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import '../styles/audio-emotion-analysis.less';
import logger from '../../../../utils/logger';

const { Text, Title, Paragraph } = Typography;

interface AudioEmotionAnalysisProps {
  isDarkMode: boolean;
  setIosSafariInitializing?: (value: boolean) => void;
}

// iOS Safari 检测
const isIOSSafari = (): boolean => {
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  return isIOS && isSafari;
};

// iOS Safari 音频处理等待时间配置
const IOS_SAFARI_AUDIO_CONFIG = {
  MIN_WAIT_TIME: 3000, // 最少等待3秒
  MAX_WAIT_TIME: 8000, // 最多等待8秒
  EXTRA_WAIT_TIME: 2000, // 额外等待2秒（当检测到文件可能未完全处理时）
  MIN_VALID_SIZE: 44, // 最小有效音频文件大小（WAV头部大小）
} as const;

// 安全的状态更新函数
const safeStateUpdate = (callback: () => void) => {
  try {
    // 使用 React 的 unstable_batchedUpdates 来批量更新状态（如果可用）
    if (typeof (React as any).unstable_batchedUpdates === 'function') {
      (React as any).unstable_batchedUpdates(callback);
    } else {
      callback();
    }
  } catch (error) {
    console.error('状态更新失败:', error);
    // 延迟重试
    setTimeout(() => {
      try {
        callback();
      } catch (retryError) {
        console.error('状态更新重试失败:', retryError);
      }
    }, 100);
  }
};

// iOS Safari 专用的超级安全DOM操作
const iosSafariSuperSafeDOMOperation = (
  operation: () => void,
  errorMessage: string,
  maxRetries: number = 3,
) => {
  let retryCount = 0;

  const executeWithRetry = () => {
    try {
      // 检查文档状态
      if (document.readyState !== 'complete' && document.readyState !== 'interactive') {
        console.warn(`🍎 文档未就绪，延迟执行: ${errorMessage}`);
        setTimeout(executeWithRetry, 100);
        return;
      }

      // 检查DOM是否稳定
      if (document.body && document.documentElement) {
        operation();
        console.log(`✅ iOS Safari DOM操作成功: ${errorMessage}`);
      } else {
        throw new Error('DOM未就绪');
      }
    } catch (error) {
      retryCount++;
      console.error(
        `❌ iOS Safari DOM操作失败 (尝试 ${retryCount}/${maxRetries}): ${errorMessage}`,
        error,
      );

      if (retryCount < maxRetries) {
        // 指数退避重试
        const delay = Math.min(100 * Math.pow(2, retryCount - 1), 1000);
        setTimeout(executeWithRetry, delay);
      } else {
        console.error(`🚨 iOS Safari DOM操作最终失败: ${errorMessage}`);
        // 触发组件错误边界
        throw new Error(`iOS Safari DOM操作失败: ${errorMessage}`);
      }
    }
  };

  if (isIOSSafari()) {
    // iOS Safari使用多重保护
    setTimeout(() => {
      requestAnimationFrame(() => {
        setTimeout(executeWithRetry, 50);
      });
    }, 100);
  } else {
    // 其他浏览器使用标准操作
    try {
      requestAnimationFrame(() => {
        try {
          operation();
        } catch (error) {
          console.error(`DOM操作失败: ${errorMessage}`, error);
        }
      });
    } catch (error) {
      console.error(`安全DOM操作失败: ${errorMessage}`, error);
    }
  }
};

// 替换原来的 iosSafariSafeDOMOperation
const iosSafariSafeDOMOperation = iosSafariSuperSafeDOMOperation;

// 组件唯一标识符生成器
const generateComponentId = () => {
  return `audio-emotion-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
};

const AudioEmotionAnalysis: React.FC<AudioEmotionAnalysisProps> = ({
  isDarkMode,
  setIosSafariInitializing,
}) => {
  // 组件唯一标识符，用于在iOS Safari中避免状态污染
  const componentIdRef = useRef<string>(generateComponentId());
  const initializationRef = useRef<boolean>(false);
  const mountedRef = useRef<boolean>(false); // 新增：组件挂载状态跟踪
  const domReadyRef = useRef<boolean>(false); // 新增：DOM就绪状态跟踪

  // iOS Safari状态更新队列
  const stateUpdateQueueRef = useRef<Array<() => void>>([]);
  const isProcessingStateUpdateRef = useRef<boolean>(false);

  const [analyzing, setAnalyzing] = useState<boolean>(false);
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [isStoppingRecording, setIsStoppingRecording] = useState<boolean>(false);
  const [recordingTime, setRecordingTime] = useState<number>(0);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [exampleExpanded, setExampleExpanded] = useState<boolean>(false);
  const [iosSafariReady, setIosSafariReady] = useState<boolean>(!isIOSSafari()); // iOS Safari需要额外初始化
  const [internalIosSafariInitializing, setInternalIosSafariInitializing] =
    useState<boolean>(isIOSSafari()); // iOS Safari初始化状态

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const audioFormatRef = useRef<{ mimeType: string; extension: string } | null>(null);

  // iOS Safari专用的DOM节点检查器
  const iosSafariDOMChecker = useCallback(() => {
    if (!isIOSSafari()) return true;

    try {
      // 检查基本DOM结构
      if (!document || !document.body || !document.documentElement) {
        console.warn(`🍎 [${componentIdRef.current}] DOM基本结构不完整`);
        return false;
      }

      // 检查文档状态
      if (document.readyState === 'loading') {
        console.warn(`🍎 [${componentIdRef.current}] 文档仍在加载中`);
        return false;
      }

      // 检查组件是否仍然挂载
      if (!mountedRef.current) {
        console.warn(`🍎 [${componentIdRef.current}] 组件未挂载`);
        return false;
      }

      // 检查React根节点是否存在
      const reactRoot = document.getElementById('root');
      if (!reactRoot) {
        console.warn(`🍎 [${componentIdRef.current}] React根节点不存在`);
        return false;
      }

      console.log(`✅ [${componentIdRef.current}] iOS Safari DOM检查通过`);
      return true;
    } catch (error) {
      console.error(`❌ [${componentIdRef.current}] iOS Safari DOM检查失败:`, error);
      return false;
    }
  }, []);

  // iOS Safari专用的安全状态更新（增强版）
  const iosSafariSafeStateUpdate = useCallback(
    (updateFunction: () => void) => {
      if (!isIOSSafari()) {
        safeStateUpdate(updateFunction);
        return;
      }

      // iOS Safari专用检查
      if (!iosSafariDOMChecker()) {
        console.warn(`🍎 [${componentIdRef.current}] DOM检查失败，跳过状态更新`);
        return;
      }

      // 将状态更新加入队列
      stateUpdateQueueRef.current.push(() => {
        try {
          // 再次检查DOM状态
          if (iosSafariDOMChecker()) {
            updateFunction();
          } else {
            console.warn(`🍎 [${componentIdRef.current}] 执行前DOM检查失败，跳过更新`);
          }
        } catch (error) {
          console.error(`❌ [${componentIdRef.current}] iOS Safari状态更新执行失败:`, error);
        }
      });

      console.log(
        `📝 [${componentIdRef.current}] iOS Safari: 状态更新加入队列，当前队列长度: ${stateUpdateQueueRef.current.length}`,
      );

      // 尝试处理队列
      processStateUpdateQueue();
    },
    [iosSafariDOMChecker],
  );

  // 组件挂载和卸载跟踪
  useEffect(() => {
    mountedRef.current = true;
    console.log(`🔗 [${componentIdRef.current}] 组件已挂载`);

    // 检查DOM就绪状态
    const checkDOMReady = () => {
      if (document.readyState === 'complete') {
        domReadyRef.current = true;
        console.log(`📄 [${componentIdRef.current}] DOM已就绪`);
      } else {
        setTimeout(checkDOMReady, 50);
      }
    };
    checkDOMReady();

    return () => {
      mountedRef.current = false;
      domReadyRef.current = false;
      console.log(`🔌 [${componentIdRef.current}] 组件已卸载`);
    };
  }, []);

  // iOS Safari专用的状态更新队列处理器
  const processStateUpdateQueue = useCallback(() => {
    if (isProcessingStateUpdateRef.current || stateUpdateQueueRef.current.length === 0) {
      return;
    }

    // iOS Safari专用检查
    if (isIOSSafari() && !iosSafariDOMChecker()) {
      console.warn(`🍎 [${componentIdRef.current}] DOM检查失败，暂停队列处理`);
      // 延迟重试
      setTimeout(() => {
        if (stateUpdateQueueRef.current.length > 0) {
          processStateUpdateQueue();
        }
      }, 200);
      return;
    }

    isProcessingStateUpdateRef.current = true;
    console.log(
      `🔄 [${componentIdRef.current}] iOS Safari: 处理状态更新队列，队列长度: ${stateUpdateQueueRef.current.length}`,
    );

    const processNext = () => {
      if (stateUpdateQueueRef.current.length === 0) {
        isProcessingStateUpdateRef.current = false;
        console.log(`✅ [${componentIdRef.current}] iOS Safari: 状态更新队列处理完成`);
        return;
      }

      // 再次检查DOM状态（iOS Safari专用）
      if (isIOSSafari() && !iosSafariDOMChecker()) {
        console.warn(`🍎 [${componentIdRef.current}] 处理过程中DOM检查失败，停止队列处理`);
        isProcessingStateUpdateRef.current = false;
        return;
      }

      const update = stateUpdateQueueRef.current.shift();
      if (update) {
        try {
          update();
          // iOS Safari需要更长的间隔来确保状态更新安全
          setTimeout(processNext, isIOSSafari() ? 150 : 50); // 增加iOS Safari的间隔
        } catch (error) {
          console.error(`❌ [${componentIdRef.current}] iOS Safari: 状态更新失败:`, error);

          // 错误恢复：清空队列并重置状态
          if (isIOSSafari()) {
            console.warn(`🍎 [${componentIdRef.current}] 错误恢复：清空状态更新队列`);
            stateUpdateQueueRef.current = [];
            isProcessingStateUpdateRef.current = false;

            // 触发组件重置
            setTimeout(() => {
              try {
                forceComponentReset(false);
              } catch (resetError) {
                console.error(`❌ [${componentIdRef.current}] 错误恢复失败:`, resetError);
              }
            }, 100);
            return;
          }

          setTimeout(processNext, 100);
        }
      }
    };

    if (isIOSSafari()) {
      // iOS Safari使用延迟处理
      setTimeout(processNext, 100); // 增加延迟
    } else {
      processNext();
    }
  }, [iosSafariDOMChecker]);

  // iOS Safari内存管理：定期清理不需要的引用
  useEffect(() => {
    if (!isIOSSafari()) return;

    const cleanup = () => {
      console.log(`🧹 [${componentIdRef.current}] iOS Safari: 执行内存清理`);

      // 清理可能的内存泄漏
      if (window.gc && typeof window.gc === 'function') {
        try {
          window.gc();
        } catch (e) {
          // 忽略错误
        }
      }
    };

    const intervalId = setInterval(cleanup, 30000); // 每30秒清理一次

    return () => {
      clearInterval(intervalId);
    };
  }, []);

  // iOS Safari专用的组件挂载检测
  useEffect(() => {
    if (isIOSSafari()) {
      console.log(`🍎 [${componentIdRef.current}] iOS Safari组件已挂载，启用专用兼容模式`);
      console.log(`📱 设备信息:`, {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        memory: (navigator as any).deviceMemory,
        connection: (navigator as any).connection?.effectiveType,
      });

      // iOS Safari专用的全局错误监听器
      const handleIOSSafariError = (event: ErrorEvent) => {
        const error = event.error || new Error(event.message);
        console.error(`🚨 [${componentIdRef.current}] iOS Safari全局错误:`, {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: error,
          stack: error.stack,
        });

        // 检查是否是DOM相关错误
        if (
          event.message &&
          (event.message.includes('insertBefore') ||
            event.message.includes('The object can not be found here') ||
            event.message.includes('NotFoundError') ||
            event.message.includes('DOM'))
        ) {
          console.error(`🔴 [${componentIdRef.current}] 检测到iOS Safari DOM错误，启动恢复机制`);

          // 延迟执行恢复，避免在错误处理过程中再次触发错误
          setTimeout(() => {
            try {
              // 清空状态更新队列
              stateUpdateQueueRef.current = [];
              isProcessingStateUpdateRef.current = false;

              // 强制重置组件状态
              forceComponentReset(false);

              console.log(`🔧 [${componentIdRef.current}] iOS Safari DOM错误恢复完成`);
            } catch (recoveryError) {
              console.error(
                `❌ [${componentIdRef.current}] iOS Safari错误恢复失败:`,
                recoveryError,
              );
            }
          }, 500);
        }
      };

      // 添加错误监听器
      window.addEventListener('error', handleIOSSafariError);

      // 添加未处理的Promise拒绝监听器
      const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
        console.error(
          `🚨 [${componentIdRef.current}] iOS Safari未处理的Promise拒绝:`,
          event.reason,
        );

        if (event.reason && typeof event.reason === 'object' && event.reason.message) {
          const message = event.reason.message;
          if (
            message.includes('insertBefore') ||
            message.includes('DOM') ||
            message.includes('NotFoundError')
          ) {
            console.error(`🔴 [${componentIdRef.current}] Promise中检测到DOM错误，启动恢复机制`);

            setTimeout(() => {
              try {
                forceComponentReset(false);
              } catch (recoveryError) {
                console.error(`❌ [${componentIdRef.current}] Promise错误恢复失败:`, recoveryError);
              }
            }, 300);
          }
        }
      };

      window.addEventListener('unhandledrejection', handleUnhandledRejection);

      // 清理函数
      return () => {
        window.removeEventListener('error', handleIOSSafariError);
        window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      };
    }
  }, []);

  // 强制组件重置函数 - 专门用于解决iOS Safari状态污染问题
  const forceComponentReset = (fullReset: boolean = false) => {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

    console.log(
      `🔄 [${componentIdRef.current}] 强制组件重置 - iOS: ${isIOS}, Safari: ${isSafari}, 完全重置: ${fullReset}`,
    );

    try {
      // 1. 立即停止所有进行中的操作
      if (mediaRecorderRef.current) {
        try {
          const recorder = mediaRecorderRef.current;
          if (recorder.state === 'recording') {
            recorder.stop();
          }

          // 强制清理事件处理器
          recorder.onstop = null;
          recorder.ondataavailable = null;
          recorder.onerror = null;

          // 停止媒体流
          if (recorder.stream) {
            recorder.stream.getTracks().forEach((track) => {
              try {
                track.stop();
                console.log(`🎵 [${componentIdRef.current}] 停止媒体轨道:`, track.kind);
              } catch (e) {
                console.error(`❌ [${componentIdRef.current}] 停止媒体轨道失败:`, e);
              }
            });
          }

          mediaRecorderRef.current = null;
        } catch (e) {
          console.error(`❌ [${componentIdRef.current}] 清理MediaRecorder失败:`, e);
        }
      }

      // 2. 清理音频元素
      if (audioRef.current) {
        try {
          audioRef.current.pause();
          audioRef.current.src = '';
          audioRef.current.load();
          console.log(`🎵 [${componentIdRef.current}] 音频元素已重置`);
        } catch (e) {
          console.error(`❌ [${componentIdRef.current}] 清理音频元素失败:`, e);
        }
      }

      // 3. 只在完全重置时清理URL资源
      if (fullReset && audioUrl) {
        try {
          URL.revokeObjectURL(audioUrl);
          console.log(`🔗 [${componentIdRef.current}] 完全重置 - 释放URL:`, audioUrl);
        } catch (e) {
          console.error(`❌ [${componentIdRef.current}] 释放URL失败:`, e);
        }
      }

      // 4. 重置状态（根据fullReset参数决定是否重置audioUrl）
      iosSafariSafeStateUpdate(() => {
        setIsRecording(false);
        setIsStoppingRecording(false);
        setAnalyzing(false);
        setIsPlaying(false);
        setRecordingTime(0);
        setError(null);

        // 只在完全重置时清理这些状态
        if (fullReset) {
          setResult(null);
          setAudioUrl(null);
          setFileList([]);
          console.log(`🧹 [${componentIdRef.current}] 完全重置 - 清理所有状态`);
        }
      });

      // 5. 清理音频块缓存
      audioChunksRef.current = [];

      // 6. iOS Safari 需要额外的清理周期
      if (isIOS && isSafari) {
        setTimeout(() => {
          console.log(`🍎 [${componentIdRef.current}] iOS Safari 额外清理周期`);

          // 强制再次确认状态重置
          iosSafariSafeStateUpdate(() => {
            setIsRecording(false);
            setIsStoppingRecording(false);
            setError(null);
          });

          // 尝试清理可能残留的媒体流权限
          if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            try {
              // 通过一个虚假的请求来触发权限清理
              navigator.mediaDevices
                .getUserMedia({ audio: false })
                .then((stream) => {
                  stream.getTracks().forEach((track) => track.stop());
                })
                .catch(() => {
                  // 忽略错误，这只是为了触发清理
                });
            } catch (e) {
              // 忽略错误
            }
          }
        }, 300);
      }

      console.log(`✅ [${componentIdRef.current}] 组件重置完成`);
    } catch (error) {
      console.error(`❌ [${componentIdRef.current}] 强制重置失败:`, error);

      // 最后的兜底重置
      setTimeout(() => {
        iosSafariSafeStateUpdate(() => {
          setIsRecording(false);
          setIsStoppingRecording(false);
          setAnalyzing(false);
          setIsPlaying(false);
          setRecordingTime(0);
          setError('系统已重置，请重试');
        });
      }, 100);
    }
  };

  // 组件初始化时强制重置 - 确保干净的状态
  useEffect(() => {
    if (!initializationRef.current) {
      initializationRef.current = true;
      console.log(`🚀 [${componentIdRef.current}] 组件初始化，执行基础重置`);

      // 检测iOS Safari并提供额外信息
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

      if (isIOS && isSafari) {
        console.log(`🍎 [${componentIdRef.current}] 检测到iOS Safari，启用增强兼容模式`);
        console.log(`🔧 [${componentIdRef.current}] 用户代理:`, navigator.userAgent);
        console.log(`📱 [${componentIdRef.current}] 屏幕信息:`, {
          width: window.screen.width,
          height: window.screen.height,
          devicePixelRatio: window.devicePixelRatio,
        });

        // iOS Safari专用的音频状态清理
        const cleanupAudioState = async () => {
          console.log(`🧹 [${componentIdRef.current}] iOS Safari音频状态清理开始`);

          try {
            // 检查是否有残留的音频权限状态
            if (navigator.permissions && navigator.permissions.query) {
              const permissionStatus = await navigator.permissions.query({
                name: 'microphone' as PermissionName,
              });
              logger.debug(
                'AudioEmotionAnalysis',
                `[${componentIdRef.current}] 麦克风权限状态: ${permissionStatus.state}`,
              );

              // 如果权限已授予但可能有状态污染，进行清理
              if (permissionStatus.state === 'granted') {
                console.log(`🧹 [${componentIdRef.current}] 权限已授予，检查是否需要清理音频状态`);

                try {
                  // 尝试获取一个短暂的音频流来检测状态
                  const testStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                  const testTrack = testStream.getAudioTracks()[0];

                  console.log(`🔍 [${componentIdRef.current}] 初始化时音频状态:`, {
                    muted: testTrack.muted,
                    enabled: testTrack.enabled,
                    readyState: testTrack.readyState,
                    label: testTrack.label,
                  });

                  // 如果检测到音频被静音，记录警告
                  if (testTrack.muted) {
                    console.warn(
                      `⚠️ [${componentIdRef.current}] 初始化时检测到音频被静音，可能存在状态污染`,
                    );
                  }

                  // 立即停止测试流，避免占用资源
                  testTrack.stop();

                  console.log(`✅ [${componentIdRef.current}] 音频状态检查完成`);
                } catch (testError) {
                  console.warn(`⚠️ [${componentIdRef.current}] 音频状态检查失败:`, testError);
                  // 不抛出错误，因为这只是预检查
                }
              }
            }
          } catch (cleanupError) {
            console.warn(`⚠️ [${componentIdRef.current}] 音频状态清理失败:`, cleanupError);
            // 不抛出错误，因为这不应该阻止组件初始化
          }
        };

        // iOS Safari专用的预热初始化
        setTimeout(() => {
          console.log(`🔥 [${componentIdRef.current}] iOS Safari预热初始化开始`);

          // 先执行音频状态清理
          cleanupAudioState().then(() => {
            // 1. 预检查麦克风权限状态
            if (navigator.permissions && navigator.permissions.query) {
              navigator.permissions
                .query({ name: 'microphone' as PermissionName })
                .then((permissionStatus) => {
                  logger.debug(
                    'AudioEmotionAnalysis',
                    `[${componentIdRef.current}] 麦克风权限状态: ${permissionStatus.state}`,
                  );

                  // 如果权限已授予，进行预热
                  if (permissionStatus.state === 'granted') {
                    console.log(`✅ [${componentIdRef.current}] 麦克风权限已授予，进行预热`);

                    // 预热MediaRecorder
                    navigator.mediaDevices
                      .getUserMedia({ audio: true })
                      .then((stream) => {
                        console.log(`🎵 [${componentIdRef.current}] 预热获取媒体流成功`);

                        // 创建一个临时的MediaRecorder进行预热
                        const audioFormat = getSupportedAudioFormat();
                        try {
                          const tempRecorder = new MediaRecorder(stream, {
                            mimeType: audioFormat.mimeType,
                          });
                          console.log(`📹 [${componentIdRef.current}] 预热MediaRecorder创建成功`);

                          // 立即停止并清理
                          setTimeout(() => {
                            stream.getTracks().forEach((track) => track.stop());
                            console.log(`🧹 [${componentIdRef.current}] 预热清理完成`);
                          }, 100);
                        } catch (e) {
                          console.warn(
                            `⚠️ [${componentIdRef.current}] 预热MediaRecorder创建失败:`,
                            e,
                          );
                          // 清理流
                          stream.getTracks().forEach((track) => track.stop());
                        }
                      })
                      .catch((error) => {
                        console.warn(`⚠️ [${componentIdRef.current}] 预热获取媒体流失败:`, error);
                      });
                  } else {
                    console.log(
                      `🔒 [${componentIdRef.current}] 麦克风权限未授予，状态: ${permissionStatus.state}`,
                    );
                  }
                })
                .catch((error) => {
                  console.warn(`⚠️ [${componentIdRef.current}] 查询麦克风权限失败:`, error);
                });
            } else {
              console.log(`📱 [${componentIdRef.current}] 浏览器不支持权限查询API`);
            }

            // 2. 预热DOM操作
            setTimeout(() => {
              console.log(`🔥 [${componentIdRef.current}] DOM预热操作`);

              // 确保DOM已就绪
              domReadyRef.current = true;

              // 预热状态更新机制 - 使用更安全的方式
              try {
                iosSafariSafeStateUpdate(() => {
                  // 这是一个空的状态更新，用于预热状态更新机制
                  setError(null);
                });

                // 额外的稳定性检查
                setTimeout(() => {
                  // 确保组件仍然挂载
                  if (mountedRef.current) {
                    // 设置iOS Safari就绪状态
                    setTimeout(() => {
                      setIosSafariReady(true);
                      setInternalIosSafariInitializing(false); // 清除初始化状态
                      setIosSafariInitializing?.(false); // 同步外部状态
                      console.log(`🍎 [${componentIdRef.current}] iOS Safari已就绪，可以开始录音`);
                    }, 100);
                  }
                }, 100);

                console.log(`✅ [${componentIdRef.current}] iOS Safari预热初始化完成`);
              } catch (preheatingError) {
                console.error(`❌ [${componentIdRef.current}] 预热过程中出错:`, preheatingError);
                // 即使预热失败，也要设置就绪状态，避免用户无法使用
                setTimeout(() => {
                  if (mountedRef.current) {
                    setIosSafariReady(true);
                    setInternalIosSafariInitializing(false); // 清除初始化状态
                    setIosSafariInitializing?.(false); // 同步外部状态
                    console.log(`🍎 [${componentIdRef.current}] iOS Safari预热失败但仍设置为就绪`);
                  }
                }, 500);
              }
            }, 300); // 增加延迟，确保DOM完全稳定
          });
        }, 200); // 增加初始延迟
      }

      // 延迟执行，只进行基础重置，不清理audioUrl
      setTimeout(() => {
        forceComponentReset(false); // 基础重置，保留已有的音频
      }, 50);
    }
  }, []);

  // 检测浏览器支持的音频格式
  const getSupportedAudioFormat = (): { mimeType: string; extension: string } => {
    const formats = [
      { mimeType: 'audio/mp4', extension: 'mp4' },
      { mimeType: 'audio/wav', extension: 'wav' },
      { mimeType: 'audio/ogg;codecs=opus', extension: 'ogg' },
      { mimeType: 'audio/ogg', extension: 'ogg' },
      { mimeType: 'audio/webm;codecs=opus', extension: 'webm' },
      { mimeType: 'audio/webm', extension: 'webm' },
    ];

    for (const format of formats) {
      if (MediaRecorder.isTypeSupported(format.mimeType)) {
        console.log(`🎵 [${componentIdRef.current}] 选择音频格式:`, format);
        return format;
      }
    }

    // 如果都不支持，使用默认格式
    console.warn(`⚠️ [${componentIdRef.current}] 没有找到支持的音频格式，使用默认mp4格式`);
    return { mimeType: 'audio/mp4', extension: 'mp4' };
  };

  // 添加错误恢复功能
  const resetAllStates = (clearAudio: boolean = false) => {
    console.log(`🔄 [${componentIdRef.current}] 重置所有状态... clearAudio: ${clearAudio}`);
    try {
      // 检测设备类型
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

      console.log(`🔍 [${componentIdRef.current}] 重置状态 - 设备检测:`, { isIOS, isSafari });

      // 使用批量状态更新，减少React渲染次数
      iosSafariSafeStateUpdate(() => {
        setIsRecording(false);
        setIsStoppingRecording(false);
        setAnalyzing(false);
        setIsPlaying(false);
        setRecordingTime(0);
        setError(null);

        // 只在明确要求时清理音频相关状态
        if (clearAudio) {
          setResult(null);
          setAudioUrl(null);
          setFileList([]);
          console.log(`🧹 [${componentIdRef.current}] 清理音频相关状态`);
        }
      });

      // 清理媒体资源
      if (mediaRecorderRef.current) {
        try {
          const recorder = mediaRecorderRef.current;

          // 清理事件处理器
          recorder.onstop = null;
          recorder.ondataavailable = null;
          recorder.onerror = null;

          if (recorder.state === 'recording') {
            recorder.stop();
          }

          // 停止媒体流
          if (recorder.stream) {
            recorder.stream.getTracks().forEach((track) => {
              try {
                track.stop();
              } catch (e) {
                console.error(`❌ [${componentIdRef.current}] 停止媒体轨道时出错:`, e);
              }
            });
          }

          mediaRecorderRef.current = null;
        } catch (e) {
          console.error(`❌ [${componentIdRef.current}] 重置时停止录音失败:`, e);
        }
      }

      if (audioRef.current) {
        try {
          audioRef.current.pause();
          audioRef.current.src = '';
          audioRef.current.load();
        } catch (e) {
          console.error(`❌ [${componentIdRef.current}] 重置时暂停音频失败:`, e);
        }
      }

      // 只在明确要求时清理音频URL
      if (clearAudio && audioUrl) {
        try {
          URL.revokeObjectURL(audioUrl);
          console.log(`🔗 [${componentIdRef.current}] 清理模式 - 释放audioUrl:`, audioUrl);
        } catch (e) {
          console.error(`❌ [${componentIdRef.current}] 释放URL时出错:`, e);
        }
      }

      // iOS Safari需要额外的延迟来确保状态完全重置
      if (isIOS && isSafari) {
        setTimeout(() => {
          console.log(`🍎 [${componentIdRef.current}] iOS Safari: 执行额外的状态确认重置`);
          iosSafariSafeStateUpdate(() => {
            setIsRecording(false);
            setIsStoppingRecording(false);
            setError(null);
          });
        }, 200);
      }
    } catch (error) {
      console.error(`❌ [${componentIdRef.current}] 重置状态时出错:`, error);
      // 强制最终状态重置
      setTimeout(() => {
        iosSafariSafeStateUpdate(() => {
          setIsRecording(false);
          setIsStoppingRecording(false);
          setAnalyzing(false);
          setIsPlaying(false);
          setRecordingTime(0);
          setError('系统已重置，请重试');
        });
      }, 100);
    }
  };

  // 监听全局错误
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error(`❌ [${componentIdRef.current}] 全局错误捕获:`, event.error);
      // 全局错误时使用温和的重置策略，不清理音频
      resetAllStates(false);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error(`❌ [${componentIdRef.current}] 未处理的Promise拒绝:`, event.reason);
      // Promise拒绝时也使用温和的重置策略
      resetAllStates(false);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  // 组件卸载时的清理逻辑
  useEffect(() => {
    return () => {
      console.log(`🧹 [${componentIdRef.current}] AudioEmotionAnalysis组件正在卸载，清理资源...`);

      try {
        // 清理录音相关的资源
        if (mediaRecorderRef.current) {
          const recorder = mediaRecorderRef.current;

          // 如果正在录音，先停止
          if (recorder.state === 'recording') {
            try {
              recorder.stop();
              console.log(`⏸️ [${componentIdRef.current}] 卸载时停止录音`);
            } catch (error) {
              console.error(`❌ [${componentIdRef.current}] 组件卸载时停止录音失败:`, error);
            }
          }

          // 安全地移除事件处理器
          try {
            recorder.onstop = null;
            recorder.ondataavailable = null;
            recorder.onerror = null;
            console.log(`🔗 [${componentIdRef.current}] 已移除MediaRecorder事件处理器`);
          } catch (error) {
            console.error(`❌ [${componentIdRef.current}] 移除事件处理器时出错:`, error);
          }

          // 停止媒体流
          try {
            if (recorder.stream) {
              recorder.stream.getTracks().forEach((track) => {
                try {
                  track.stop();
                  console.log(`🎵 [${componentIdRef.current}] 停止媒体轨道:`, track.kind);
                } catch (error) {
                  console.error(`❌ [${componentIdRef.current}] 停止媒体轨道时出错:`, error);
                }
              });
            }
          } catch (error) {
            console.error(`❌ [${componentIdRef.current}] 停止媒体流时出错:`, error);
          }

          mediaRecorderRef.current = null; // 清除引用
          console.log(`📝 [${componentIdRef.current}] 已清除MediaRecorder引用`);
        }

        // 清理audio元素
        if (audioRef.current) {
          try {
            audioRef.current.pause();
            audioRef.current.src = '';
            audioRef.current.load();
            console.log(`🎵 [${componentIdRef.current}] 已清理audio元素`);
          } catch (error) {
            console.error(`❌ [${componentIdRef.current}] 清理audio元素时出错:`, error);
          }
        }

        console.log(`✅ [${componentIdRef.current}] 资源清理完成`);
      } catch (error) {
        console.error(`❌ [${componentIdRef.current}] 组件卸载时清理资源出错:`, error);
      }
    };
  }, []); // 移除audioUrl依赖，只在组件真正卸载时执行

  // 单独管理audioUrl的清理逻辑
  useEffect(() => {
    // 保存当前的audioUrl引用
    const currentAudioUrl = audioUrl;

    console.log(`🔗 [${componentIdRef.current}] audioUrl状态变化:`, {
      from: 'previous',
      to: currentAudioUrl,
      hasAudio: !!currentAudioUrl,
    });

    return () => {
      // 只在audioUrl真正改变或组件卸载时清理旧的URL
      if (currentAudioUrl && currentAudioUrl !== audioUrl) {
        try {
          URL.revokeObjectURL(currentAudioUrl);
          console.log(`🔗 [${componentIdRef.current}] 已释放旧的audioUrl:`, currentAudioUrl);
        } catch (error) {
          console.error(`❌ [${componentIdRef.current}] 释放旧URL时出错:`, error);
        }
      }
    };
  }, [audioUrl]); // 这里可以安全地使用audioUrl作为依赖

  // 监控播放按钮状态变化的调试代码
  useEffect(() => {
    console.log(`🎵 [${componentIdRef.current}] 播放按钮状态监控:`, {
      audioUrl: !!audioUrl,
      audioUrlLength: audioUrl?.length || 0,
      fileListLength: fileList.length,
      isPlaying,
      isRecording,
      analyzing,
      hasAudioRef: !!audioRef.current,
    });

    // 如果audioUrl突然变成null，记录详细信息
    if (!audioUrl && fileList.length > 0) {
      console.warn(`⚠️ [${componentIdRef.current}] 播放按钮消失：audioUrl为空但fileList有内容`, {
        fileList: fileList.map((f) => ({ name: f.name, status: f.status, uid: f.uid })),
        timestamp: new Date().toISOString(),
      });
    }
  }, [audioUrl, fileList, isPlaying, isRecording, analyzing]);

  // 完整示例文本
  const fullExampleText = `在苍茫的大海上，狂风卷集着乌云。在乌云和大海之间，海燕像黑色的闪电，在高傲地飞翔。
一会儿翅膀碰着波浪，一会儿箭一般地直冲向乌云，它叫喊着，──就在这鸟儿勇敢的叫喊声里，乌云听出了欢乐。
在这叫喊声里──充满着对暴风雨的渴望！在这叫喊声里，乌云听出了愤怒的力量、热情的火焰和胜利的信心。
海鸥在暴风雨来临之前呻吟着，──呻吟着，在大海上面飞窜，想把自己对暴风雨的恐惧，掩藏到大海深处。
海鸭也在呻吟着，──它们这些海鸭啊，享受不了生活的战斗的欢乐：轰隆隆的雷声就把它们吓坏了。
蠢笨的企鹅，胆怯地把肥胖的身体躲藏到悬崖底下……只有那高傲的海燕，勇敢地，自由自在地，在泛起白沫的大海上飞翔！
乌云越来越暗，越来越低，向海面直压下来，而波浪一边歌唱，一边冲向高空，去迎接那雷声。
雷声轰响。波浪在愤怒的飞沫中呼叫，跟狂风争鸣。看吧，狂风紧紧抱起一层层巨浪，恶狠狠地把它们甩到悬崖上，把这些大块的翡翠摔成尘雾和碎沫。`;

  // 切换示例文本展开/收起状态
  const toggleExampleText = () => {
    setExampleExpanded(!exampleExpanded);
  };

  // 处理文件上传
  const handleFileChange = (info: any) => {
    let newFileList = [...info.fileList];

    // 只保留最后一个文件
    newFileList = newFileList.slice(-1);

    // 更新文件列表
    setFileList(newFileList);

    // 如果上传成功，设置音频URL
    if (info.file.status === 'done') {
      message.success(`${info.file.name} 上传成功`);

      // 创建音频URL
      if (newFileList.length > 0 && newFileList[0].originFileObj) {
        const url = URL.createObjectURL(newFileList[0].originFileObj);
        setAudioUrl(url);
        setResult(null); // 清除之前的分析结果
      }
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`);
    }
  };

  // 开始录音
  const startRecording = async () => {
    try {
      logger.info('AudioEmotionAnalysis', `[${componentIdRef.current}] 开始录音被调用`);

      // iOS Safari专用的预检查
      if (isIOSSafari()) {
        console.log(`🍎 [${componentIdRef.current}] iOS Safari专用预检查开始`);

        // 1. 检查iOS Safari是否已就绪
        if (!iosSafariReady) {
          console.log(`⏳ [${componentIdRef.current}] iOS Safari未就绪，等待初始化完成`);

          // 等待iOS Safari就绪
          await new Promise((resolve) => {
            const checkReady = () => {
              if (iosSafariReady) {
                resolve(void 0);
              } else {
                setTimeout(checkReady, 100);
              }
            };
            checkReady();
          });

          console.log(`✅ [${componentIdRef.current}] iOS Safari就绪检查完成`);
        }

        // 2. 检查组件是否已完全初始化
        if (!domReadyRef.current) {
          console.log(`⏳ [${componentIdRef.current}] DOM未就绪，等待初始化完成`);

          // 等待DOM就绪
          await new Promise((resolve) => {
            const checkReady = () => {
              if (domReadyRef.current) {
                resolve(void 0);
              } else {
                setTimeout(checkReady, 50);
              }
            };
            checkReady();
          });

          console.log(`✅ [${componentIdRef.current}] DOM就绪检查完成`);
        }

        // 3. 确保状态更新机制正常
        await new Promise((resolve) => {
          iosSafariSafeStateUpdate(() => {
            setError(null); // 清除可能的错误状态
            setTimeout(resolve, 50);
          });
        });

        console.log(`✅ [${componentIdRef.current}] iOS Safari预检查完成`);
      }

      // iOS Safari专用：音频状态检测和自动恢复
      if (isIOSSafari()) {
        console.log(`🍎 [${componentIdRef.current}] iOS Safari音频状态检测开始`);

        // 尝试获取音频流进行状态检测
        try {
          const testStream = await navigator.mediaDevices.getUserMedia({ audio: true });
          const testTrack = testStream.getAudioTracks()[0];

          console.log(`🔍 [${componentIdRef.current}] 音频轨道状态检测:`, {
            muted: testTrack.muted,
            enabled: testTrack.enabled,
            readyState: testTrack.readyState,
            label: testTrack.label,
          });

          // 如果音频轨道被静音，尝试恢复
          if (testTrack.muted) {
            console.warn(`⚠️ [${componentIdRef.current}] 检测到音频轨道被静音，尝试恢复`);

            // 停止测试流
            testTrack.stop();

            // 等待一段时间让系统清理状态
            await new Promise((resolve) => setTimeout(resolve, 200));

            // 再次尝试获取音频流
            const recoveryStream = await navigator.mediaDevices.getUserMedia({ audio: true });
            const recoveryTrack = recoveryStream.getAudioTracks()[0];

            console.log(`🔄 [${componentIdRef.current}] 恢复后音频轨道状态:`, {
              muted: recoveryTrack.muted,
              enabled: recoveryTrack.enabled,
              readyState: recoveryTrack.readyState,
              label: recoveryTrack.label,
            });

            // 停止恢复流，准备正式录音
            recoveryTrack.stop();

            if (recoveryTrack.muted) {
              console.error(
                `❌ [${componentIdRef.current}] 音频状态恢复失败，可能需要用户重新授权`,
              );
              throw new Error('音频设备状态异常，请刷新页面重试');
            }

            console.log(`✅ [${componentIdRef.current}] 音频状态恢复成功`);
          } else {
            // 音频状态正常，停止测试流
            testTrack.stop();
            console.log(`✅ [${componentIdRef.current}] 音频状态正常`);
          }
        } catch (testError) {
          console.error(`❌ [${componentIdRef.current}] 音频状态检测失败:`, testError);
          throw new Error('无法访问音频设备，请检查权限设置');
        }
      }

      // 清除之前的录音
      audioChunksRef.current = [];
      setAudioUrl(null);
      setResult(null);

      // 检测浏览器支持的音频格式
      const audioFormat = getSupportedAudioFormat();
      audioFormatRef.current = audioFormat;
      console.log('使用音频格式:', audioFormat);

      // 获取麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // iOS Safari专用：验证最终音频流状态
      if (isIOSSafari()) {
        const finalTrack = stream.getAudioTracks()[0];
        console.log(`🔍 [${componentIdRef.current}] 最终音频流状态:`, {
          muted: finalTrack.muted,
          enabled: finalTrack.enabled,
          readyState: finalTrack.readyState,
          label: finalTrack.label,
        });

        if (finalTrack.muted) {
          console.error(`❌ [${componentIdRef.current}] 最终音频流仍被静音`);
          finalTrack.stop();
          throw new Error('音频设备被系统静音，请重试');
        }
      }

      // 创建MediaRecorder，指定音频格式
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: audioFormat.mimeType,
      });
      mediaRecorderRef.current = mediaRecorder;

      // 监听数据可用事件
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      // 监听录音停止事件
      mediaRecorder.onstop = () => {
        console.log('录音已停止，准备处理音频数据...');

        // 释放麦克风资源
        if (mediaRecorder.stream) {
          logger.debug('AudioEmotionAnalysis', '释放麦克风资源');
          mediaRecorder.stream.getTracks().forEach((track) => {
            track.stop();
            logger.debug('AudioEmotionAnalysis', `麦克风轨道已停止: ${track.kind} ${track.id}`);
          });
        }

        if (audioChunksRef.current.length === 0) {
          console.warn('录音停止了，但没有收集到音频数据。');
          setIsRecording(false);
          setRecordingTime(0);
          // 可能需要通知用户录音失败或没有录到内容
          message.warning('未能录制到有效音频，请重试。');
          return;
        }

        const currentFormat = audioFormatRef.current || {
          mimeType: 'audio/webm',
          extension: 'webm',
        };
        const audioBlob = new Blob(audioChunksRef.current, { type: currentFormat.mimeType });
        console.log('音频 Blob 类型:', audioBlob.type, '大小:', audioBlob.size);

        if (audioUrl) {
          URL.revokeObjectURL(audioUrl);
          console.log('已释放旧的 audioUrl:', audioUrl);
        }

        const newUrl = URL.createObjectURL(audioBlob);
        console.log('创建了新的 audioUrl:', newUrl);
        setAudioUrl(newUrl); // 取消注释，恢复此行

        const timestamp = new Date().getTime();
        const uniqueId = `rc-upload-${timestamp}`;
        const fileName = `recording-${timestamp}.${currentFormat.extension}`;

        // 1. 创建原始 File 对象
        const originalFile = new File([audioBlob], fileName, {
          type: currentFormat.mimeType,
          lastModified: timestamp,
        });

        // 2. 为原始 File 对象添加 uid，使其符合 RcFile 的部分要求
        //    (我们将 File 强制转换为 any 来添加属性，这在处理库类型时常见)
        (originalFile as any).uid = uniqueId;

        // 3. 创建 UploadFile 对象
        const uploadableFile: UploadFile = {
          uid: uniqueId,
          name: fileName,
          status: 'done', // 初始状态可以设为 'done' 因为文件已在客户端准备好
          originFileObj: originalFile as any, // 现在 originalFile 有了 uid
          url: newUrl, // 可选，用于预览
          size: audioBlob.size,
          type: audioBlob.type,
        };

        console.log('创建的文件对象 (UploadFile):', uploadableFile);
        setFileList([uploadableFile]); // 使用新创建的 UploadFile 对象

        setIsRecording(false);
        setRecordingTime(0); // 重置录音时间
        console.log('录音状态已设置为 false，录音时间已重置');
      };

      // 监听录音错误事件
      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event.error);
        message.error(`录音过程中发生错误: ${event.error ? event.error.name : 'Unknown error'}`);
        // 确保在发生错误时也停止录音状态和媒体流
        if (mediaRecorderRef.current && mediaRecorderRef.current.stream) {
          mediaRecorderRef.current.stream.getTracks().forEach((track) => track.stop());
        }
        setIsRecording(false);
      };

      // 开始录音
      mediaRecorder.start();
      setIsRecording(true);

      // 30秒后自动停止录音
      setTimeout(() => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
          stopRecording();
          message.info('已达到30秒最大录音时长');
        }
      }, 30000);

      message.success('开始录音（最长30秒）');
    } catch (error) {
      console.error('录音失败:', error);

      // iOS Safari专用错误处理
      if (isIOSSafari() && error instanceof Error) {
        if (
          error.message.includes('音频设备状态异常') ||
          error.message.includes('音频设备被系统静音')
        ) {
          message.error('音频设备状态异常，建议刷新页面后重试');
        } else if (error.message.includes('无法访问音频设备')) {
          message.error('无法访问麦克风，请检查浏览器权限设置');
        } else {
          message.error('录音启动失败，请重试');
        }
      } else {
        message.error('无法访问麦克风，请检查浏览器权限设置');
      }
    }
  };

  // iOS Safari专用的简化停止录音流程
  const iosSafariStopRecording = useCallback(() => {
    console.group(`🍎 [${componentIdRef.current}] iOS Safari: 简化停止录音流程`);

    if (!mediaRecorderRef.current || mediaRecorderRef.current.state !== 'recording') {
      console.log(`⏭️ 录音器状态无效，跳过操作`);
      console.groupEnd();
      return;
    }

    try {
      // 第一步：立即设置状态
      setIsStoppingRecording(true);
      setIsRecording(false);

      const currentRecorder = mediaRecorderRef.current;
      console.log(`📹 当前录音器状态: ${currentRecorder.state}`);

      // 第二步：设置简化的停止处理器
      currentRecorder.onstop = () => {
        console.log(`📊 iOS Safari: 录音停止事件触发`);

        // 延迟处理，确保DOM稳定
        setTimeout(() => {
          try {
            if (audioChunksRef.current.length === 0) {
              console.warn(`⚠️ 没有音频数据`);
              setRecordingTime(0);
              setIsStoppingRecording(false);
              message.warning('未能录制到有效音频，请重试。');
              console.groupEnd();
              return;
            }

            const currentFormat = audioFormatRef.current || {
              mimeType: 'audio/webm',
              extension: 'webm',
            };
            const audioBlob = new Blob(audioChunksRef.current, { type: currentFormat.mimeType });
            console.log(`📁 音频Blob创建完成，大小: ${audioBlob.size}`);

            // 释放旧URL
            if (audioUrl) {
              try {
                URL.revokeObjectURL(audioUrl);
              } catch (e) {
                console.error('释放旧URL失败:', e);
              }
            }

            // 创建新URL
            const newUrl = URL.createObjectURL(audioBlob);
            const timestamp = Date.now();
            const fileName = `recording-${timestamp}.${currentFormat.extension}`;

            // 创建文件对象
            const originalFile = new File([audioBlob], fileName, {
              type: currentFormat.mimeType,
              lastModified: timestamp,
            });
            (originalFile as any).uid = `rc-upload-${timestamp}`;

            const uploadableFile: UploadFile = {
              uid: `rc-upload-${timestamp}`,
              name: fileName,
              status: 'done',
              originFileObj: originalFile as any,
              url: newUrl,
              size: audioBlob.size,
              type: audioBlob.type,
            };

            console.log(`📄 文件对象创建完成: ${fileName}`);

            // iOS Safari: 使用同步状态更新
            setTimeout(() => {
              console.log(`📝 第一次状态更新`);
              setAudioUrl(newUrl);
              setFileList([uploadableFile]);

              setTimeout(() => {
                console.log(`📝 第二次状态更新`);
                setRecordingTime(0);
                setIsStoppingRecording(false);
                console.log(`✅ iOS Safari停止录音完成`);
                console.groupEnd();
              }, 200);
            }, 500); // iOS Safari需要更长延迟
          } catch (error) {
            console.error(`❌ 处理录音数据失败:`, error);
            setTimeout(() => {
              setIsRecording(false);
              setRecordingTime(0);
              setIsStoppingRecording(false);
              setError('录音处理失败，请重试');
            }, 100);
            console.groupEnd();
          }
        }, 300); // iOS Safari专用延迟
      };

      // 第三步：停止录音
      currentRecorder.stop();
      console.log(`⏸️ MediaRecorder.stop() 已调用`);

      // 第四步：释放麦克风资源
      if (currentRecorder.stream) {
        console.log(`🎤 [${componentIdRef.current}] 释放麦克风资源`);
        currentRecorder.stream.getTracks().forEach((track) => {
          track.stop();
          console.log(`🎤 [${componentIdRef.current}] 麦克风轨道已停止:`, track.kind, track.id);
        });
      }

      message.success('录音已保存');
    } catch (error) {
      console.error(`❌ iOS Safari停止录音失败:`, error);
      setTimeout(() => {
        setIsRecording(false);
        setRecordingTime(0);
        setIsStoppingRecording(false);
        setError('停止录音失败，请重试');
      }, 100);
      message.error('停止录音失败，请重试');
      console.groupEnd();
    }
  }, [audioUrl]);

  // 停止录音 - 根据浏览器选择不同策略
  const stopRecording = useCallback(() => {
    if (isIOSSafari()) {
      console.log(`🍎 [${componentIdRef.current}] 使用iOS Safari专用停止录音流程`);
      iosSafariStopRecording();
    } else {
      console.group(`🛑 [${componentIdRef.current}] 停止录音被调用`);

      try {
        // 详细的状态检查和日志
        console.log('🔍 当前状态检查:', {
          isRecording,
          isStoppingRecording,
          hasMediaRecorder: !!mediaRecorderRef.current,
          mediaRecorderState: mediaRecorderRef.current?.state,
          audioChunksLength: audioChunksRef.current.length,
          hasAudioUrl: !!audioUrl,
          fileListLength: fileList.length,
          hasAudioRef: !!audioRef.current,
        });

        // 防止重复调用 - 使用状态锁
        if (!mediaRecorderRef.current || !isRecording || isStoppingRecording) {
          console.log(`⏭️ [${componentIdRef.current}] 录音未在进行或正在停止中，跳过操作`);
          console.groupEnd();
          return;
        }

        // 检查 MediaRecorder 状态
        if (mediaRecorderRef.current.state !== 'recording') {
          console.log(
            `⚠️ [${componentIdRef.current}] MediaRecorder状态不是recording，当前状态:`,
            mediaRecorderRef.current.state,
          );
          // 使用安全的状态更新
          iosSafariSafeDOMOperation(() => {
            iosSafariSafeStateUpdate(() => {
              setIsRecording(false);
              setRecordingTime(0);
              setIsStoppingRecording(false);
            });
          }, '重置录音状态');
          console.groupEnd();
          return;
        }

        console.log(`⏳ [${componentIdRef.current}] 正在停止录音...`);

        // 立即设置状态锁，防止重复调用 - 使用安全的状态更新
        iosSafariSafeDOMOperation(() => {
          iosSafariSafeStateUpdate(() => {
            setIsStoppingRecording(true);
            setIsRecording(false);
          });
        }, '设置停止录音状态');

        // 保存当前的 MediaRecorder 引用
        const currentRecorder = mediaRecorderRef.current;
        console.log(`📹 [${componentIdRef.current}] 当前录音器状态:`, {
          state: currentRecorder.state,
          mimeType: currentRecorder.mimeType,
          hasStream: !!currentRecorder.stream,
        });

        // 创建一个安全的停止处理器
        const handleRecorderStop = () => {
          // 确保释放麦克风资源
          if (currentRecorder.stream) {
            logger.debug('AudioEmotionAnalysis', `[${componentIdRef.current}] 释放麦克风资源`);
            currentRecorder.stream.getTracks().forEach((track) => {
              track.stop();
              logger.debug(
                'AudioEmotionAnalysis',
                `[${componentIdRef.current}] 麦克风轨道已停止: ${track.kind} ${track.id}`,
              );
            });
          }
          console.log(`📊 [${componentIdRef.current}] 录音停止事件处理器被调用`);

          // 检测是否为iOS设备，使用更保守的延迟策略
          const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
          const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
          const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            navigator.userAgent,
          );

          const processDelay = isIOS && isSafari ? 300 : isMobile ? 150 : 100; // 增加延迟时间

          console.log(`🔍 [${componentIdRef.current}] 设备检测结果:`, {
            isIOS,
            isSafari,
            isMobile,
            processDelay,
          });

          // 使用更安全的异步处理方式
          const processRecordingData = () => {
            console.log(`🎯 [${componentIdRef.current}] 开始处理录音数据`);

            try {
              console.log(`📊 [${componentIdRef.current}] 录音停止事件触发`);

              if (audioChunksRef.current.length === 0) {
                console.warn(`⚠️ [${componentIdRef.current}] 录音停止了，但没有收集到音频数据`);
                iosSafariSafeDOMOperation(() => {
                  iosSafariSafeStateUpdate(() => {
                    setRecordingTime(0);
                    setIsStoppingRecording(false);
                  });
                }, '重置录音时间状态');
                message.warning('未能录制到有效音频，请重试。');
                console.groupEnd();
                return;
              }

              const currentFormat = audioFormatRef.current || {
                mimeType: 'audio/webm',
                extension: 'webm',
              };
              const audioBlob = new Blob(audioChunksRef.current, { type: currentFormat.mimeType });
              console.log(
                `📁 [${componentIdRef.current}] 音频 Blob 类型:`,
                audioBlob.type,
                '大小:',
                audioBlob.size,
              );

              // 释放旧的 URL
              if (audioUrl) {
                try {
                  URL.revokeObjectURL(audioUrl);
                  console.log(`🔗 [${componentIdRef.current}] 已释放旧的 audioUrl:`, audioUrl);
                } catch (urlError) {
                  console.error(`❌ [${componentIdRef.current}] 释放旧URL失败:`, urlError);
                }
              }

              const newUrl = URL.createObjectURL(audioBlob);
              console.log(`🔗 [${componentIdRef.current}] 创建了新的 audioUrl:`, newUrl);

              const timestamp = new Date().getTime();
              const uniqueId = `rc-upload-${timestamp}`;
              const fileName = `recording-${timestamp}.${currentFormat.extension}`;

              // 创建原始 File 对象
              const originalFile = new File([audioBlob], fileName, {
                type: currentFormat.mimeType,
                lastModified: timestamp,
              });

              // 为原始 File 对象添加 uid
              (originalFile as any).uid = uniqueId;

              // 创建 UploadFile 对象
              const uploadableFile: UploadFile = {
                uid: uniqueId,
                name: fileName,
                status: 'done',
                originFileObj: originalFile as any,
                url: newUrl,
                size: audioBlob.size,
                type: audioBlob.type,
              };

              console.log(`📄 [${componentIdRef.current}] 创建的文件对象:`, {
                uid: uploadableFile.uid,
                name: uploadableFile.name,
                status: uploadableFile.status,
                size: uploadableFile.size,
                type: uploadableFile.type,
              });

              // 使用更安全的状态更新策略 - 分批更新，避免同时更新过多状态
              const updateStates = () => {
                console.log(`🔄 [${componentIdRef.current}] 开始更新状态`);

                iosSafariSafeDOMOperation(() => {
                  iosSafariSafeStateUpdate(() => {
                    console.log(`📝 [${componentIdRef.current}] 更新audioUrl和fileList`);
                    setAudioUrl(newUrl);
                    setFileList([uploadableFile]);
                  });
                }, '更新音频URL和文件列表');

                // 稍后更新其他状态
                setTimeout(() => {
                  iosSafariSafeDOMOperation(() => {
                    iosSafariSafeStateUpdate(() => {
                      console.log(`📝 [${componentIdRef.current}] 重置录音时间和停止状态`);
                      setRecordingTime(0);
                      setIsStoppingRecording(false);
                    });
                  }, '重置录音时间和停止状态');

                  console.log(`✅ [${componentIdRef.current}] 状态更新完成`);
                  console.groupEnd();
                }, 50);
              };

              // iOS Safari需要更长的延迟来确保状态更新安全
              const finalDelay = isIOS && isSafari ? 400 : 100;
              console.log(`⏰ [${componentIdRef.current}] 将在${finalDelay}ms后更新状态`);
              setTimeout(updateStates, finalDelay);
            } catch (error) {
              console.error(`❌ [${componentIdRef.current}] 处理录音停止事件时出错:`, error);
              console.error(`❌ 错误堆栈:`, error instanceof Error ? error.stack : 'No stack');

              // 确保UI状态正确，使用安全的状态更新
              const resetErrorState = () => {
                iosSafariSafeDOMOperation(() => {
                  iosSafariSafeStateUpdate(() => {
                    setIsRecording(false);
                    setRecordingTime(0);
                    setIsStoppingRecording(false);
                    setError('录音处理失败，请重试');
                  });
                }, '重置错误状态');
              };
              setTimeout(resetErrorState, 100);
              console.groupEnd();
            }
          };

          // 使用适合设备的异步处理方式
          if (isIOS && isSafari) {
            // iOS Safari需要特殊处理
            console.log(`🍎 [${componentIdRef.current}] iOS Safari: 使用延迟处理`);
            setTimeout(processRecordingData, processDelay);
          } else {
            // 其他浏览器使用requestAnimationFrame
            console.log(`💻 [${componentIdRef.current}] 非iOS Safari: 使用requestAnimationFrame`);
            requestAnimationFrame(() => {
              setTimeout(processRecordingData, processDelay);
            });
          }
        };

        // 设置一次性事件处理器，避免重复绑定
        if (currentRecorder.onstop !== handleRecorderStop) {
          currentRecorder.onstop = handleRecorderStop;
          console.log(`🔗 [${componentIdRef.current}] 设置录音停止事件处理器`);
        }

        // 清理错误处理器，避免重复绑定
        currentRecorder.onerror = (event) => {
          console.error(`❌ [${componentIdRef.current}] MediaRecorder 错误:`, event);
          const resetErrorState = () => {
            iosSafariSafeDOMOperation(() => {
              iosSafariSafeStateUpdate(() => {
                setIsRecording(false);
                setRecordingTime(0);
                setIsStoppingRecording(false);
                setError('录音过程中发生错误，请重试');
              });
            }, '重置MediaRecorder错误状态');
          };
          setTimeout(resetErrorState, 100);
          console.groupEnd();
        };

        // 停止录音
        console.log(`⏸️ [${componentIdRef.current}] 即将调用 MediaRecorder.stop()`);
        currentRecorder.stop();
        console.log(`⏸️ [${componentIdRef.current}] 已调用 MediaRecorder.stop()`);

        // 显示成功消息
        message.success('录音已保存');
      } catch (error) {
        console.error(`❌ [${componentIdRef.current}] 停止录音失败:`, error);
        console.error(`❌ 错误详情:`, {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : 'No stack',
          name: error instanceof Error ? error.name : 'Unknown',
        });

        // 使用安全的状态更新
        const resetErrorState = () => {
          iosSafariSafeDOMOperation(() => {
            iosSafariSafeStateUpdate(() => {
              setIsRecording(false);
              setRecordingTime(0);
              setIsStoppingRecording(false);
              setError('停止录音失败，请重试');
            });
          }, '重置停止录音错误状态');
        };
        setTimeout(resetErrorState, 100);

        message.error('停止录音失败，请重试');
        console.groupEnd();
      }
    }
  }, [isRecording, isStoppingRecording, audioUrl, fileList, iosSafariStopRecording]); // 添加必要的依赖

  // 监听audioUrl变化，确保音频元素正确初始化
  useEffect(() => {
    if (audioUrl && audioRef.current) {
      console.log('audioUrl变化，初始化音频元素:', audioUrl);

      try {
        // 重置播放状态
        setIsPlaying(false);

        // 设置音频源
        audioRef.current.src = audioUrl;

        // 添加事件监听器
        const audioElement = audioRef.current;

        const handleCanPlay = () => {
          console.log('音频可以播放');
        };

        const handleError = (e: Event) => {
          console.error('音频加载错误:', e);
          setIsPlaying(false);
        };

        const handleEnded = () => {
          console.log('音频播放结束');
          setIsPlaying(false);
        };

        const handlePlay = () => {
          console.log('音频开始播放');
          setIsPlaying(true);
        };

        const handlePause = () => {
          console.log('音频暂停');
          setIsPlaying(false);
        };

        // 添加事件监听
        audioElement.addEventListener('canplay', handleCanPlay);
        audioElement.addEventListener('error', handleError);
        audioElement.addEventListener('ended', handleEnded);
        audioElement.addEventListener('play', handlePlay);
        audioElement.addEventListener('pause', handlePause);

        // 预加载音频
        audioElement.load();

        // 清理函数
        return () => {
          audioElement.removeEventListener('canplay', handleCanPlay);
          audioElement.removeEventListener('error', handleError);
          audioElement.removeEventListener('ended', handleEnded);
          audioElement.removeEventListener('play', handlePlay);
          audioElement.removeEventListener('pause', handlePause);
        };
      } catch (error) {
        console.error('初始化音频元素时出错:', error);
      }
    }
  }, [audioUrl]);

  // 播放/暂停音频 - 使用useCallback优化
  const togglePlayAudio = useCallback(async () => {
    console.log('togglePlayAudio被调用，当前播放状态:', isPlaying);

    if (!audioRef.current) {
      console.log('audioRef.current不存在');
      message.error('音频元素未就绪，请重试');
      return;
    }

    if (!audioUrl) {
      console.log('audioUrl不存在');
      message.error('音频文件不存在，请重新录制');
      return;
    }

    try {
      if (isPlaying) {
        console.log('暂停音频播放');
        audioRef.current.pause();
        iosSafariSafeDOMOperation(() => {
          iosSafariSafeStateUpdate(() => {
            setIsPlaying(false);
          });
        }, '设置播放状态为暂停');
      } else {
        console.log('开始音频播放');

        // 确保音频源已设置
        if (audioRef.current.src !== audioUrl) {
          console.log('设置音频源:', audioUrl);
          audioRef.current.src = audioUrl;

          // 等待音频元数据加载
          await new Promise<void>((resolve, reject) => {
            const timeoutId = setTimeout(() => {
              reject(new Error('音频加载超时'));
            }, 5000);

            audioRef.current!.oncanplay = () => {
              clearTimeout(timeoutId);
              console.log('音频可以播放了');
              resolve();
            };

            audioRef.current!.onerror = () => {
              clearTimeout(timeoutId);
              reject(new Error('音频加载失败'));
            };

            // 触发加载
            audioRef.current!.load();
          });
        }

        // 在iOS Safari中，play()返回Promise
        const playPromise = audioRef.current.play();

        if (playPromise !== undefined) {
          await playPromise;
          console.log('音频播放成功');
          iosSafariSafeDOMOperation(() => {
            iosSafariSafeStateUpdate(() => {
              setIsPlaying(true);
            });
          }, '设置播放状态为播放中');
        } else {
          // 旧版浏览器可能不返回Promise
          iosSafariSafeDOMOperation(() => {
            iosSafariSafeStateUpdate(() => {
              setIsPlaying(true);
            });
          }, '设置播放状态为播放中（兼容模式）');
        }
      }
    } catch (error) {
      console.error('音频播放操作失败:', error);
      iosSafariSafeDOMOperation(() => {
        iosSafariSafeStateUpdate(() => {
          setIsPlaying(false);
        });
      }, '重置播放状态');

      // 根据错误类型提供不同的提示
      if (error instanceof Error) {
        if (error.message.includes('超时')) {
          message.error('音频加载超时，请检查网络连接');
        } else if (error.message.includes('加载失败')) {
          message.error('音频文件损坏，请重新录制');
        } else if (error.name === 'NotAllowedError') {
          message.error('浏览器阻止了音频播放，请手动点击播放按钮');
        } else {
          message.error(`音频播放失败: ${error.message}`);
        }
      } else {
        message.error('音频播放失败，请重试');
      }
    }
  }, [isPlaying, audioUrl]); // 添加必要的依赖

  // 分析音频情绪
  const analyzeAudioEmotion = async () => {
    if (fileList.length === 0) {
      message.error('请先上传或录制音频');
      return;
    }

    // 检查文件是否有效
    if (fileList.length === 0 || !fileList[0].originFileObj) {
      message.error('音频文件无效，请重新录制');
      return;
    }

    try {
      setAnalyzing(true);
      setResult(null);
      setError(null);

      // 获取token
      const token = localStorage.getItem('user_token') || '';
      if (!token) {
        setError('未登录，请先登录');
        setAnalyzing(false);
        return;
      }

      // iOS Safari专用：音频处理等待机制
      if (isIOSSafari()) {
        console.log(`🍎 [${componentIdRef.current}] iOS Safari音频处理等待机制启动`);

        const file = fileList[0].originFileObj;
        const fileAge = Date.now() - file.lastModified;
        const minWaitTime = IOS_SAFARI_AUDIO_CONFIG.MIN_WAIT_TIME;
        const maxWaitTime = IOS_SAFARI_AUDIO_CONFIG.MAX_WAIT_TIME;

        // 记录详细的文件信息和设备信息
        const deviceInfo = {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          memory: (navigator as any).deviceMemory,
          connection: (navigator as any).connection?.effectiveType,
          screenSize: `${window.screen.width}x${window.screen.height}`,
          devicePixelRatio: window.devicePixelRatio,
        };

        console.log(`📊 [${componentIdRef.current}] 文件信息:`, {
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
          fileAge: fileAge,
          name: file.name,
        });

        console.log(`📱 [${componentIdRef.current}] 设备信息:`, deviceInfo);

        // 记录等待时间的变量
        let actualWaitTime = 0;

        // 如果文件刚刚创建（录音完成），需要等待iOS Safari完成音频处理
        if (fileAge < minWaitTime) {
          const waitTime = Math.min(minWaitTime - fileAge, maxWaitTime);
          const startTime = Date.now();

          console.log(
            `⏳ [${componentIdRef.current}] 文件较新，等待 ${waitTime}ms 让iOS Safari完成音频处理`,
          );
          console.log(`📊 [${componentIdRef.current}] 等待策略:`, {
            fileAge,
            minWaitTime,
            maxWaitTime,
            calculatedWaitTime: waitTime,
            startTime,
          });

          // 显示等待提示
          const hideWaitingMessage = message.loading({
            content: (
              <div>
                <div>🍎 iOS Safari 正在处理音频文件...</div>
                <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                  预计等待 {Math.ceil(waitTime / 1000)} 秒
                </div>
              </div>
            ),
            duration: 0, // 不自动关闭
            key: 'ios-audio-processing',
          });

          // 等待音频处理完成
          await new Promise((resolve) => setTimeout(resolve, waitTime));

          // 关闭等待提示
          hideWaitingMessage();

          actualWaitTime = Date.now() - startTime;
          console.log(`✅ [${componentIdRef.current}] iOS Safari音频处理等待完成`, {
            plannedWaitTime: waitTime,
            actualWaitTime,
            difference: actualWaitTime - waitTime,
          });
        } else {
          console.log(`✅ [${componentIdRef.current}] 文件已足够成熟，无需等待`, {
            fileAge,
            minWaitTime,
            skipReason: 'file_age_sufficient',
          });
        }

        // 验证音频文件是否已正确处理
        try {
          const validationStartTime = Date.now();
          const arrayBuffer = await file.arrayBuffer();
          const validationTime = Date.now() - validationStartTime;

          console.log(`🔍 [${componentIdRef.current}] 音频文件验证:`, {
            arrayBufferSize: arrayBuffer.byteLength,
            fileSize: file.size,
            isValid: arrayBuffer.byteLength > IOS_SAFARI_AUDIO_CONFIG.MIN_VALID_SIZE,
            validationTime,
            sizeRatio: arrayBuffer.byteLength / file.size,
          });

          if (arrayBuffer.byteLength <= IOS_SAFARI_AUDIO_CONFIG.MIN_VALID_SIZE) {
            console.warn(
              `⚠️ [${componentIdRef.current}] 音频文件可能未完全处理，大小: ${arrayBuffer.byteLength}`,
            );

            // 再等待一段时间
            const extraWaitTime = IOS_SAFARI_AUDIO_CONFIG.EXTRA_WAIT_TIME;
            const extraStartTime = Date.now();

            console.log(`⏳ [${componentIdRef.current}] 额外等待 ${extraWaitTime}ms`, {
              reason: 'file_size_too_small',
              currentSize: arrayBuffer.byteLength,
              minValidSize: IOS_SAFARI_AUDIO_CONFIG.MIN_VALID_SIZE,
            });

            const hideExtraWaitMessage = message.loading({
              content: '🔄 音频文件处理中，请稍候...',
              duration: 0,
              key: 'ios-audio-extra-wait',
            });

            await new Promise((resolve) => setTimeout(resolve, extraWaitTime));
            hideExtraWaitMessage();

            const actualExtraWaitTime = Date.now() - extraStartTime;

            // 重新验证
            const revalidationStartTime = Date.now();
            const newArrayBuffer = await file.arrayBuffer();
            const revalidationTime = Date.now() - revalidationStartTime;

            console.log(`🔍 [${componentIdRef.current}] 重新验证音频文件:`, {
              arrayBufferSize: newArrayBuffer.byteLength,
              fileSize: file.size,
              previousSize: arrayBuffer.byteLength,
              sizeIncrease: newArrayBuffer.byteLength - arrayBuffer.byteLength,
              actualExtraWaitTime,
              revalidationTime,
            });

            if (newArrayBuffer.byteLength <= IOS_SAFARI_AUDIO_CONFIG.MIN_VALID_SIZE) {
              console.error(`❌ [${componentIdRef.current}] 音频文件仍然无效`, {
                finalSize: newArrayBuffer.byteLength,
                minValidSize: IOS_SAFARI_AUDIO_CONFIG.MIN_VALID_SIZE,
                totalWaitTime: actualWaitTime + actualExtraWaitTime,
                deviceInfo,
              });
              throw new Error('音频文件处理失败，请重新录制');
            }

            console.log(`✅ [${componentIdRef.current}] 额外等待后音频文件验证通过`);
          }
        } catch (validationError) {
          console.error(`❌ [${componentIdRef.current}] 音频文件验证失败:`, validationError);
          if (
            validationError instanceof Error &&
            validationError.message.includes('音频文件处理失败')
          ) {
            throw validationError;
          }
          // 其他验证错误不阻止上传，可能是权限问题
          console.warn(
            `⚠️ [${componentIdRef.current}] 音频验证遇到问题，但继续上传:`,
            validationError,
          );
        }

        console.log(`🎯 [${componentIdRef.current}] iOS Safari音频处理等待机制完成，开始上传`);
      }

      // 创建FormData
      const formData = new FormData();

      // 普通录音直接上传文件
      formData.append('file', fileList[0].originFileObj);

      // 使用EventSource进行流式接收
      const apiUrl = `/api/v1/emotion/audio-emotion-analysis`;

      // 检测设备类型和浏览器
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent,
      );
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

      console.log('设备检测:', { isMobile, isIOS, isSafari });

      // 发送请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 创建ReadableStream reader
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法读取响应流');
      }

      // 读取流数据
      let resultText = '';
      let decoder = new TextDecoder();
      let buffer = ''; // 缓冲区用于处理不完整的数据块

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            console.log('流读取完成');
            break;
          }

          // 解码数据块
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // 按行分割处理
          const lines = buffer.split('\n');
          // 保留最后一个可能不完整的行
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim().startsWith('data: ')) {
              try {
                const dataStr = line.substring(6).trim();
                if (dataStr === '[DONE]') {
                  console.log('接收到DONE标记');
                  continue;
                }

                const data = JSON.parse(dataStr);

                if (data.error) {
                  console.error('服务器返回错误:', data.error);
                  setError(data.error);
                } else if (data.content) {
                  resultText += data.content;
                  console.log('接收到内容:', data.content, '累计长度:', resultText.length);

                  // 使用 requestAnimationFrame 更新UI，避免阻塞
                  requestAnimationFrame(() => {
                    setResult(resultText);
                  });

                  // 移动端添加额外延迟，确保数据传输稳定
                  if (isMobile) {
                    await new Promise((resolve) => setTimeout(resolve, 20));
                  }
                }
              } catch (parseError) {
                console.error('解析数据失败:', parseError, '原始数据:', line);
              }
            }
          }

          // 移动端浏览器添加额外的处理延迟
          if (isMobile) {
            await new Promise((resolve) => setTimeout(resolve, 10));
          }
        }

        // 处理缓冲区中剩余的数据
        if (buffer.trim()) {
          const lines = buffer.split('\n');
          for (const line of lines) {
            if (line.trim().startsWith('data: ')) {
              try {
                const dataStr = line.substring(6).trim();
                if (dataStr !== '[DONE]') {
                  const data = JSON.parse(dataStr);
                  if (data.content) {
                    resultText += data.content;
                    console.log('处理缓冲区剩余内容:', data.content);
                    setResult(resultText);
                  }
                }
              } catch (parseError) {
                console.error('解析缓冲区数据失败:', parseError);
              }
            }
          }
        }
      } finally {
        // 确保释放reader
        try {
          reader.releaseLock();
        } catch (e) {
          console.log('释放reader锁失败:', e);
        }
      }

      console.log('音频情绪分析完成，最终结果长度:', resultText.length);
      setAnalyzing(false);

      // 移动端添加额外延迟，确保最终状态更新完成
      if (isMobile) {
        setTimeout(() => {
          console.log('移动端延迟后的最终结果:', resultText);
          setResult(resultText);
        }, 100);
      }
    } catch (error) {
      console.error('分析音频情绪失败:', error);

      // iOS Safari专用错误处理
      if (isIOSSafari() && error instanceof Error) {
        if (error.message.includes('音频文件处理失败')) {
          setError('音频文件处理失败，请重新录制音频');
        } else {
          setError(`分析失败: ${error.message}`);
        }
      } else {
        setError(`分析失败: ${error instanceof Error ? error.message : String(error)}`);
      }
      setAnalyzing(false);
    }
  };

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <AudioOutlined style={{ fontSize: '18px', color: isDarkMode ? '#1890ff' : '#1890ff' }} />
          <Text
            style={{ marginLeft: '8px', fontSize: '16px', color: isDarkMode ? '#fff' : '#000' }}
          >
            AI音频情绪分析（AI audio sentiment analysis）
          </Text>
        </div>
      }
      className={`audio-emotion-analysis-card ${isDarkMode ? 'dark' : ''}`}
      variant="outlined"
    >
      <div className="audio-controls">
        <div className="audio-upload">
          <Upload
            accept="audio/*"
            maxCount={1}
            fileList={fileList}
            onChange={handleFileChange}
            beforeUpload={() => {
              // 只在前端保存文件，不自动上传
              return false;
            }}
          >
            <Button icon={<UploadOutlined />} type="default">
              上传音频
            </Button>
          </Upload>
        </div>

        <div className="audio-record">
          {!isRecording ? (
            <Button
              type="primary"
              onClick={startRecording}
              icon={<AudioOutlined />}
              disabled={isIOSSafari() && !iosSafariReady}
              title={isIOSSafari() && !iosSafariReady ? 'iOS Safari正在初始化，请稍候...' : ''}
            >
              {isIOSSafari() && !iosSafariReady ? '初始化中...' : '开始录音'}
            </Button>
          ) : (
            <Button
              danger
              disabled={isStoppingRecording}
              onClick={(e) => {
                // iOS Safari专用的点击处理
                if (isIOSSafari()) {
                  console.log(`🍎 [${componentIdRef.current}] iOS Safari: 停止录音按钮点击`);

                  // 阻止事件传播
                  e.preventDefault();
                  e.stopPropagation();

                  // 防止重复点击
                  if (isStoppingRecording || !isRecording) {
                    console.log('iOS Safari: 忽略重复点击或无效状态');
                    return;
                  }

                  // 禁用按钮
                  const button = e.currentTarget as HTMLButtonElement;
                  button.disabled = true;

                  // 延迟执行，确保DOM稳定
                  setTimeout(() => {
                    try {
                      stopRecording();
                    } catch (error) {
                      console.error('iOS Safari: 停止录音失败:', error);
                      button.disabled = false;
                      setIsStoppingRecording(false);
                    }
                  }, 100);

                  // 安全恢复按钮
                  setTimeout(() => {
                    if (button.disabled) {
                      button.disabled = false;
                    }
                  }, 5000);
                } else {
                  // 其他浏览器使用原有逻辑
                  // 阻止事件传播和默认行为
                  e.preventDefault();
                  e.stopPropagation();

                  console.log(`🎯 [${componentIdRef.current}] 停止录音按钮被点击`);

                  // 检查是否正在停止录音
                  if (isStoppingRecording) {
                    console.log('正在停止录音中，忽略重复点击');
                    return;
                  }

                  // 获取按钮引用并临时禁用
                  const button = e.currentTarget as HTMLButtonElement;
                  if (button.disabled) {
                    console.log('按钮已被禁用，忽略点击');
                    return;
                  }

                  // 立即禁用按钮，防止重复点击
                  button.disabled = true;

                  // 检测设备类型
                  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
                  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
                  const isMobile =
                    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
                      navigator.userAgent,
                    );

                  console.log('停止录音点击事件 - 设备检测:', { isIOS, isSafari, isMobile });

                  // 使用防抖机制和设备感知的执行策略
                  const executeStopRecording = () => {
                    try {
                      // 再次检查状态，确保安全
                      if (isStoppingRecording || !isRecording) {
                        console.log('状态已变化，取消停止录音操作');
                        button.disabled = false;
                        return;
                      }

                      // 使用安全的DOM操作执行停止录音
                      iosSafariSafeDOMOperation(() => {
                        stopRecording();
                      }, '执行停止录音操作');
                    } catch (error) {
                      console.error('执行停止录音时出错:', error);
                      // 重置状态
                      iosSafariSafeDOMOperation(() => {
                        iosSafariSafeStateUpdate(() => {
                          setIsStoppingRecording(false);
                        });
                      }, '重置停止录音状态');
                      button.disabled = false;
                      message.error('停止录音失败，请重试');
                    }
                  };

                  // 根据设备类型选择执行策略 - 增加延迟时间，确保DOM稳定
                  if (isIOS && isSafari) {
                    // iOS Safari使用更保守的策略
                    console.log('iOS Safari: 使用保守执行策略');
                    setTimeout(executeStopRecording, 100); // 增加延迟
                  } else if (isMobile) {
                    // 移动端使用中等延迟
                    console.log('移动端: 使用中等延迟策略');
                    if (window.requestIdleCallback) {
                      window.requestIdleCallback(executeStopRecording, { timeout: 200 });
                    } else {
                      setTimeout(executeStopRecording, 60); // 增加延迟
                    }
                  } else {
                    // 桌面端使用较短延迟
                    console.log('桌面端: 使用快速执行策略');
                    if (window.requestIdleCallback) {
                      window.requestIdleCallback(executeStopRecording, { timeout: 100 });
                    } else {
                      setTimeout(executeStopRecording, 30); // 增加延迟
                    }
                  }

                  // 安全恢复按钮状态的超时机制 - 增加超时时间
                  const recoveryTimeout = isIOS && isSafari ? 3000 : 2000; // 增加超时时间
                  setTimeout(() => {
                    if (button && button.disabled) {
                      console.log('超时恢复按钮状态');
                      button.disabled = false;
                      // 如果状态异常，也进行恢复
                      if (isStoppingRecording) {
                        iosSafariSafeDOMOperation(() => {
                          iosSafariSafeStateUpdate(() => {
                            setIsStoppingRecording(false);
                          });
                        }, '超时恢复停止录音状态');
                      }
                    }
                  }, recoveryTimeout);
                }
              }}
              icon={<AudioOutlined />}
            >
              {isStoppingRecording ? '停止中...' : '停止录音'}
            </Button>
          )}
        </div>

        <Button
          type="primary"
          onClick={analyzeAudioEmotion}
          loading={analyzing}
          disabled={fileList.length === 0 || isRecording}
        >
          分析情绪
        </Button>
      </div>

      {audioUrl && (
        <div className="audio-player">
          <audio
            key={audioUrl} // 添加 key 属性
            ref={audioRef}
            src={audioUrl}
            style={{ display: 'none' }}
          />
          <Button
            type="link"
            icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={togglePlayAudio}
          >
            {isPlaying ? '暂停' : '播放'}
          </Button>
          <Text type="secondary">
            {fileList[0]?.name || '录音.mp4'} {/* 修改默认文件名 */}
          </Text>
        </div>
      )}

      <Divider style={{ margin: '12px 0' }} />

      {analyzing && (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin>
            <div style={{ padding: '30px', textAlign: 'center' }}>
              <p style={{ marginTop: '10px' }}>正在分析音频情绪...</p>
            </div>
          </Spin>
        </div>
      )}

      {error && !analyzing && <div style={{ color: 'red', padding: '10px' }}>{error}</div>}

      {result && !analyzing && (
        <div className="analysis-result" style={{ marginTop: '8px' }}>
          <Paragraph
            style={{
              margin: 0,
              padding: 0,
              width: '100%',
              maxWidth: '100%',
              boxSizing: 'border-box',
              lineHeight: '1.4', // 减少行间距，使显示更紧凑
            }}
          >
            {result
              .split('\n')
              .filter((line) => line.trim() !== '') // 过滤空行
              .map((line, index) => (
                <React.Fragment key={index}>
                  {line.trim()}
                  <br />
                </React.Fragment>
              ))}
          </Paragraph>
        </div>
      )}

      {!analyzing && !result && !error && (
        <div style={{ padding: '20px', color: isDarkMode ? '#aaa' : '#666' }}>
          <p style={{ marginBottom: '10px', textAlign: 'center' }}>
            上传或录制音频（最长30秒），然后点击&ldquo;分析情绪&rdquo;按钮，AI将分析音频中的情绪
          </p>

          {/* iOS Safari专用提示 */}
          {isIOSSafari() && (
            <div
              style={{
                marginBottom: '15px',
                padding: '12px',
                backgroundColor: isDarkMode ? 'rgba(255, 193, 7, 0.1)' : 'rgba(255, 193, 7, 0.1)',
                borderLeft: `4px solid ${isDarkMode ? '#ffc107' : '#ff9800'}`,
                borderRadius: '4px',
                fontSize: '13px',
                lineHeight: '1.5',
              }}
            >
              <div
                style={{
                  fontWeight: 'bold',
                  color: isDarkMode ? '#ffc107' : '#ff9800',
                  marginBottom: '5px',
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                🍎 iOS Safari 用户提示
              </div>
              <div style={{ color: isDarkMode ? '#e0e0e0' : '#666' }}>
                • 如果首次录音失败，请再次点击&ldquo;开始录音&rdquo;按钮
                <br />
                • 录音完成后，系统会自动等待几秒让音频文件完成处理
                <br />
                • 建议在使用前关闭其他音频应用（如音乐播放器）
                <br />• 如遇问题，可尝试刷新页面重新开始
              </div>
            </div>
          )}

          <div
            style={{
              marginTop: '15px',
              padding: '15px',
              backgroundColor: isDarkMode ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)',
              borderRadius: '8px',
              fontSize: '14px',
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '10px',
              }}
            >
              <p
                style={{
                  fontWeight: 'bold',
                  margin: 0,
                  color: isDarkMode ? '#3eeb34' : '#52c41a',
                  textShadow: isDarkMode ? '0 0 1px rgba(62, 235, 52, 0.5)' : 'none',
                }}
                className="prompt-text"
              >
                不知道说什么？试试朗读下面这段文字：
              </p>
              <Button
                type="link"
                size="small"
                onClick={toggleExampleText}
                icon={exampleExpanded ? <UpOutlined /> : <DownOutlined />}
                style={{ padding: '0 8px' }}
              >
                {exampleExpanded ? '收起' : '展开全文'}
              </Button>
            </div>
            <div
              style={{
                lineHeight: '1.4', // 减少行间距，与音频分析结果一致
                textAlign: 'left',
                maxHeight: exampleExpanded ? '1000px' : '120px',
                overflow: 'hidden',
                transition: 'max-height 0.3s ease-in-out',
              }}
            >
              {fullExampleText
                .split('\n')
                .filter((paragraph) => paragraph.trim() !== '') // 过滤空行
                .map((paragraph, index) => (
                  <React.Fragment key={index}>
                    {paragraph.trim()}
                    <br />
                  </React.Fragment>
                ))}
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

// React错误边界组件，防止DOM操作错误导致应用崩溃
class AudioAnalysisErrorBoundary extends React.Component<
  { children: React.ReactNode; isDarkMode: boolean; iosSafariInitializing?: boolean },
  { hasError: boolean; error: Error | null; errorInfo: any; autoRecoveryAttempts: number }
> {
  private recoveryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: {
    children: React.ReactNode;
    isDarkMode: boolean;
    iosSafariInitializing?: boolean;
  }) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      autoRecoveryAttempts: 0,
    };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('🚨 AudioAnalysis错误边界捕获错误:', error);
    console.error('🚨 错误堆栈:', error.stack);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('🚨 AudioAnalysis错误详情:', error, errorInfo);
    console.error('🚨 组件堆栈:', errorInfo.componentStack);
    this.setState({ errorInfo });

    // 特别关注iOS Safari和微信浏览器的错误信息
    const isIOSSafari =
      /iPad|iPhone|iPod/.test(navigator.userAgent) &&
      /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isWechatBrowser = /MicroMessenger/i.test(navigator.userAgent);

    // 发送错误信息到控制台，便于调试
    console.group('🚨 AudioAnalysis 完整错误信息');
    console.error('🔍 浏览器环境:', {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      isIOSSafari: isIOSSafari,
      memory: (navigator as any).deviceMemory,
      connection: (navigator as any).connection?.effectiveType,
      screenSize: `${window.screen.width}x${window.screen.height}`,
      devicePixelRatio: window.devicePixelRatio,
      windowSize: `${window.innerWidth}x${window.innerHeight}`,
    });
    console.error('❌ 错误对象:', error);
    console.error('📝 错误信息:', error.message);
    console.error('📋 错误名称:', error.name);
    console.error('📊 错误堆栈:', error.stack);
    console.error('🧩 组件堆栈:', errorInfo.componentStack);
    console.error('⏰ 时间戳:', new Date().toISOString());

    // iOS Safari和微信浏览器特有的错误分析
    if (isIOSSafari || isWechatBrowser) {
      const browserType = isWechatBrowser ? '微信浏览器' : 'iOS Safari';
      console.error(`🍎 ${browserType}特有信息:`, {
        isStandalone: (window.navigator as any).standalone,
        webkitFullscreenEnabled: (document as any).webkitFullscreenEnabled,
        memoryInfo: (performance as any).memory,
        webkitGetUserMedia: !!(navigator as any).webkitGetUserMedia,
        mediaDevices: !!navigator.mediaDevices,
        mediaRecorderSupported: typeof MediaRecorder !== 'undefined',
      });

      // 更严格的错误模式检查 - 只有真正的DOM错误才触发自动恢复
      const isDOMError =
        error.message.includes('insertBefore') ||
        error.message.includes('The object can not be found here') ||
        error.message.includes('NotFoundError');

      const isReactError =
        error.message.includes('React') ||
        error.message.includes('render') ||
        (errorInfo.componentStack && errorInfo.componentStack.includes('AudioEmotionAnalysis'));

      // 检查是否是初始化相关的错误（这些不应该触发自动恢复）
      const isInitializationError =
        error.message.includes('getUserMedia') ||
        error.message.includes('MediaRecorder') ||
        error.message.includes('permission') ||
        error.message.includes('NotAllowedError');

      console.error('🔍 错误分类:', {
        isDOMError,
        isReactError,
        isInitializationError,
        shouldAutoRecover: isDOMError && !isInitializationError,
      });

      if (isDOMError && !isInitializationError) {
        console.error('🔴 iOS Safari DOM操作错误，可能原因:');
        console.error('  - React并发渲染冲突');
        console.error('  - iOS内存压力导致DOM节点被回收');
        console.error('  - Safari WebKit引擎的特殊行为');
        console.error('  - 组件状态更新与DOM操作时序冲突');

        // 检查是否在初始化期间
        const isInitializingNow = this.props.iosSafariInitializing;
        console.error('🔍 当前初始化状态:', { isInitializingNow });

        // 只有在真正的DOM错误且尝试次数较少且不在初始化期间时才自动恢复
        if (this.state.autoRecoveryAttempts < 1 && !isInitializingNow) {
          console.log(`🔧 尝试自动恢复 (第${this.state.autoRecoveryAttempts + 1}次)`);
          this.attemptAutoRecovery();
        } else if (isInitializingNow) {
          console.log(`🚫 iOS Safari正在初始化，跳过自动恢复`);
        } else {
          console.log(`🚫 已达到最大自动恢复次数，停止自动恢复`);
        }
      } else if (isInitializationError) {
        console.error('🔴 iOS Safari初始化错误，不进行自动恢复:');
        console.error('  - 用户权限问题');
        console.error('  - 设备资源不足');
        console.error('  - MediaRecorder不支持');
      } else {
        console.error('🔴 iOS Safari其他错误，不进行自动恢复');
      }

      if (error.message.includes('MediaRecorder') || error.message.includes('getUserMedia')) {
        console.error('🔴 iOS Safari媒体API错误，可能原因:');
        console.error('  - iOS Safari的MediaRecorder兼容性问题');
        console.error('  - 用户权限被撤销');
        console.error('  - 设备资源不足');
      }
    }

    console.groupEnd();
  }

  // iOS Safari专用的自动恢复机制
  attemptAutoRecovery = () => {
    const isIOSSafari =
      /iPad|iPhone|iPod/.test(navigator.userAgent) &&
      /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

    if (!isIOSSafari) return;

    console.log('🔧 启动iOS Safari自动恢复机制...');

    // 清理之前的恢复定时器
    if (this.recoveryTimeoutId) {
      clearTimeout(this.recoveryTimeoutId);
    }

    // 检查是否是DOM错误，如果是则快速恢复
    const isDOMError =
      this.state.error &&
      (this.state.error.message.includes('insertBefore') ||
        this.state.error.message.includes('The object can not be found here') ||
        this.state.error.message.includes('NotFoundError') ||
        this.state.error.message.includes('DOM'));

    // DOM错误使用更短的恢复时间，其他错误使用较长时间
    const recoveryDelay = isDOMError ? 200 : 1000;

    console.log(`🔧 使用${recoveryDelay}ms恢复延迟 (DOM错误: ${isDOMError})`);

    // 延迟恢复，给DOM时间稳定
    this.recoveryTimeoutId = setTimeout(() => {
      try {
        console.log('🔄 执行自动恢复...');

        // 增加恢复尝试次数
        this.setState((prevState) => ({
          hasError: false,
          error: null,
          errorInfo: null,
          autoRecoveryAttempts: prevState.autoRecoveryAttempts + 1,
        }));

        console.log('✅ iOS Safari自动恢复完成');
      } catch (recoveryError) {
        console.error('❌ 自动恢复失败:', recoveryError);
      }
    }, recoveryDelay);
  };

  handleReset = () => {
    console.log('🔄 用户手动重置音频组件');

    // 清理恢复定时器
    if (this.recoveryTimeoutId) {
      clearTimeout(this.recoveryTimeoutId);
      this.recoveryTimeoutId = null;
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      autoRecoveryAttempts: 0,
    });
  };

  componentWillUnmount() {
    // 清理恢复定时器
    if (this.recoveryTimeoutId) {
      clearTimeout(this.recoveryTimeoutId);
    }
  }

  render() {
    if (this.state.hasError) {
      const isDev = process.env.NODE_ENV === 'development';
      const isIOSSafari =
        /iPad|iPhone|iPod/.test(navigator.userAgent) &&
        /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

      // 检查是否是iOS Safari DOM错误
      const isDOMError =
        this.state.error &&
        (this.state.error.message.includes('insertBefore') ||
          this.state.error.message.includes('The object can not be found here') ||
          this.state.error.message.includes('NotFoundError') ||
          this.state.error.message.includes('DOM'));

      return (
        <Card
          title="音频情绪分析"
          className={`audio-emotion-analysis-card ${this.props.isDarkMode ? 'dark' : ''}`}
          bordered
        >
          <div
            style={{
              textAlign: 'center',
              padding: '20px',
              color: this.props.isDarkMode ? '#ff7875' : '#ff4d4f',
            }}
          >
            {/* iOS Safari DOM错误的特殊处理 */}
            {isIOSSafari && isDOMError ? (
              <>
                <h3>🍎 iOS Safari 兼容性问题</h3>
                <p>检测到 iOS Safari 浏览器的兼容性问题，这是由于 Safari 的特殊渲染机制导致的。</p>

                {this.state.autoRecoveryAttempts > 0 && (
                  <div
                    style={{
                      marginBottom: '15px',
                      padding: '10px',
                      backgroundColor: this.props.isDarkMode ? '#1a1a1a' : '#f0f8ff',
                      borderRadius: '6px',
                      fontSize: '14px',
                    }}
                  >
                    <p>🔧 已尝试自动恢复 {this.state.autoRecoveryAttempts} 次</p>
                  </div>
                )}

                <div style={{ marginTop: '20px', marginBottom: '20px' }}>
                  <Button type="primary" onClick={this.handleReset} style={{ marginRight: '10px' }}>
                    重新加载组件
                  </Button>
                  <Button onClick={() => window.location.reload()}>刷新页面</Button>
                </div>

                <div
                  style={{
                    marginTop: '15px',
                    fontSize: '12px',
                    color: this.props.isDarkMode ? '#aaa' : '#666',
                    textAlign: 'left',
                  }}
                >
                  <p>
                    <strong>建议解决方案：</strong>
                  </p>
                  <ul style={{ paddingLeft: '20px' }}>
                    <li>尝试关闭其他标签页以释放内存</li>
                    <li>重启 Safari 浏览器</li>
                    <li>如果问题持续，建议使用 Chrome 浏览器</li>
                  </ul>
                </div>
              </>
            ) : (
              <>
                <h3>组件暂时不可用</h3>
                <p>音频分析组件遇到了问题，请尝试以下解决方案：</p>

                <div style={{ marginTop: '20px', marginBottom: '20px' }}>
                  <Button type="primary" onClick={this.handleReset} style={{ marginRight: '10px' }}>
                    重置组件
                  </Button>
                  <Button onClick={() => window.location.reload()}>刷新页面</Button>
                </div>
              </>
            )}

            {/* 开发环境显示详细错误信息 */}
            {isDev && this.state.error && (
              <div
                style={{
                  textAlign: 'left',
                  marginTop: '20px',
                  padding: '15px',
                  backgroundColor: this.props.isDarkMode ? '#1a1a1a' : '#f5f5f5',
                  borderRadius: '8px',
                  border: `1px solid ${this.props.isDarkMode ? '#333' : '#e8e8e8'}`,
                  fontSize: '12px',
                  fontFamily: 'monospace',
                }}
              >
                <details>
                  <summary style={{ cursor: 'pointer', marginBottom: '10px', fontWeight: 'bold' }}>
                    🐛 调试信息 (开发环境)
                  </summary>
                  <div style={{ marginBottom: '10px' }}>
                    <strong>错误信息:</strong> {this.state.error.message}
                  </div>
                  <div style={{ marginBottom: '10px' }}>
                    <strong>错误类型:</strong> {this.state.error.name}
                  </div>
                  <div style={{ marginBottom: '10px' }}>
                    <strong>浏览器:</strong>{' '}
                    {isIOSSafari
                      ? 'iOS Safari'
                      : /MicroMessenger/i.test(navigator.userAgent)
                        ? '微信内置浏览器'
                        : navigator.userAgent}
                  </div>
                  <div style={{ marginBottom: '10px' }}>
                    <strong>恢复尝试:</strong> {this.state.autoRecoveryAttempts} 次
                  </div>
                  {this.state.error.stack && (
                    <div style={{ marginBottom: '10px' }}>
                      <strong>错误堆栈:</strong>
                      <pre
                        style={{
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-all',
                          maxHeight: '200px',
                          overflow: 'auto',
                          marginTop: '5px',
                          padding: '5px',
                          backgroundColor: this.props.isDarkMode ? '#000' : '#fff',
                          border: '1px solid #ccc',
                        }}
                      >
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}
                  {this.state.errorInfo?.componentStack && (
                    <div>
                      <strong>组件堆栈:</strong>
                      <pre
                        style={{
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-all',
                          maxHeight: '200px',
                          overflow: 'auto',
                          marginTop: '5px',
                          padding: '5px',
                          backgroundColor: this.props.isDarkMode ? '#000' : '#fff',
                          border: '1px solid #ccc',
                        }}
                      >
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </details>
              </div>
            )}

            {!isDev && (
              <div
                style={{
                  marginTop: '15px',
                  fontSize: '12px',
                  color: this.props.isDarkMode ? '#aaa' : '#666',
                }}
              >
                如果问题持续存在，请联系技术支持
              </div>
            )}
          </div>
        </Card>
      );
    }

    return this.props.children;
  }
}

// 导出包装后的组件
const WrappedAudioEmotionAnalysis: React.FC<AudioEmotionAnalysisProps> = (props) => {
  const [iosSafariInitializing, setIosSafariInitializing] = useState<boolean>(isIOSSafari());

  return (
    <AudioAnalysisErrorBoundary
      isDarkMode={props.isDarkMode}
      iosSafariInitializing={iosSafariInitializing}
    >
      <AudioEmotionAnalysis {...props} setIosSafariInitializing={setIosSafariInitializing} />
    </AudioAnalysisErrorBoundary>
  );
};

export default WrappedAudioEmotionAnalysis;
