/**
 * 超简单环形图测试组件
 * 使用最基础的Ant Design Progress组件，不添加任何复杂样式
 */

import React from 'react';
import { Card, Progress } from 'antd';
import { getEmotionColor, getEmotionLabel } from '@/utils/emotionUtils';

const UltraSimpleCircleTest: React.FC = () => {
  // 示例数据
  const primaryEmotion = { emotion: 'happy', value: 0.85 };
  const secondaryEmotion = { emotion: 'neutral', value: 0.1 };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Card title="超简单环形图测试 - 最基础版本" style={{ marginBottom: '20px' }}>
        <p>这是最基础的环形图测试，只显示百分比，不添加任何复杂的自定义样式。</p>
        <p>如果这个版本在iOS Safari中显示正常，说明问题出在自定义样式上。</p>
      </Card>

      <Card title="基础环形图显示">
        {/* 环形图容器 */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            margin: '15px 0',
            minHeight: '220px',
            width: '100%',
          }}
        >
          {/* 主要情绪环形图 */}
          <div
            style={{
              flex: '1',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-start',
              alignItems: 'center',
            }}
          >
            <Progress
              type="circle"
              percent={Math.round(primaryEmotion.value * 100)}
              format={() => `${Math.round(primaryEmotion.value * 100)}%`}
              strokeColor={getEmotionColor(primaryEmotion.emotion)}
              size={150}
              trailColor="rgba(0,0,0,0.06)"
            />
            {/* 主要情绪标签 */}
            <div
              style={{
                textAlign: 'center',
                marginTop: '8px',
              }}
            >
              <div
                style={{
                  color: '#333333',
                  fontSize: '14px',
                  fontWeight: 600,
                  marginBottom: '2px',
                }}
              >
                {getEmotionLabel(primaryEmotion.emotion)}
              </div>
              <div
                style={{
                  color: getEmotionColor(primaryEmotion.emotion),
                  fontSize: '11px',
                  fontWeight: 500,
                  opacity: 0.9,
                }}
              >
                {primaryEmotion.emotion.charAt(0).toUpperCase() + primaryEmotion.emotion.slice(1)}
              </div>
            </div>
          </div>

          {/* 次要情绪环形图 */}
          <div
            style={{
              flex: '1',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-start',
              alignItems: 'center',
            }}
          >
            <Progress
              type="circle"
              percent={Math.round(secondaryEmotion.value * 100)}
              format={() => `${Math.round(secondaryEmotion.value * 100)}%`}
              strokeColor={getEmotionColor(secondaryEmotion.emotion)}
              size={120}
              trailColor="rgba(0,0,0,0.06)"
            />
            {/* 次要情绪标签 */}
            <div
              style={{
                textAlign: 'center',
                marginTop: '8px',
              }}
            >
              <div
                style={{
                  color: '#333333',
                  fontSize: '12px',
                  fontWeight: 600,
                  marginBottom: '2px',
                }}
              >
                {getEmotionLabel(secondaryEmotion.emotion)}
              </div>
              <div
                style={{
                  color: getEmotionColor(secondaryEmotion.emotion),
                  fontSize: '10px',
                  fontWeight: 500,
                  opacity: 0.9,
                }}
              >
                {secondaryEmotion.emotion.charAt(0).toUpperCase() +
                  secondaryEmotion.emotion.slice(1)}
              </div>
            </div>
          </div>
        </div>

        <div
          style={{
            fontSize: '12px',
            color: '#666',
            textAlign: 'center',
            marginTop: '10px',
            padding: '10px',
            backgroundColor: '#f5f5f5',
            borderRadius: '4px',
          }}
        >
          <strong>测试说明：</strong>
          <br />
          1. 这是最基础的Ant Design Progress组件
          <br />
          2. 只显示百分比，没有复杂的自定义格式
          <br />
          3. 情绪标签显示在环形图下方
          <br />
          4. 如果这个版本正常显示，说明问题出在复杂的自定义样式上
          <br />
          5. 左侧：开心85%，右侧：平静10%
        </div>

        {/* 单独测试不同尺寸 */}
        <div style={{ marginTop: '30px' }}>
          <h4>不同尺寸基础环形图测试</h4>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-around',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: '20px',
              margin: '20px 0',
            }}
          >
            {/* 大环形图 */}
            <div style={{ textAlign: 'center' }}>
              <h5>大环形图 (180px)</h5>
              <Progress
                type="circle"
                percent={75}
                format={(percent) => `${percent}%`}
                size={180}
                strokeColor="#1890ff"
              />
              <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>测试大尺寸</div>
            </div>

            {/* 中环形图 */}
            <div style={{ textAlign: 'center' }}>
              <h5>中环形图 (150px)</h5>
              <Progress
                type="circle"
                percent={60}
                format={(percent) => `${percent}%`}
                size={150}
                strokeColor="#52c41a"
              />
              <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>测试中尺寸</div>
            </div>

            {/* 小环形图 */}
            <div style={{ textAlign: 'center' }}>
              <h5>小环形图 (120px)</h5>
              <Progress
                type="circle"
                percent={45}
                format={(percent) => `${percent}%`}
                size={120}
                strokeColor="#f5222d"
              />
              <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>测试小尺寸</div>
            </div>
          </div>
        </div>

        {/* 默认环形图测试 */}
        <div style={{ marginTop: '30px' }}>
          <h4>完全默认的环形图（不指定format）</h4>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              margin: '20px 0',
            }}
          >
            <Progress type="circle" percent={85} size={150} strokeColor="#52c41a" />
          </div>
          <div
            style={{
              fontSize: '12px',
              color: '#666',
              textAlign: 'center',
              marginTop: '10px',
            }}
          >
            这是完全默认的环形图，使用Ant Design的默认format函数
          </div>
        </div>
      </Card>
    </div>
  );
};

export default UltraSimpleCircleTest;
