/**
 * 简单环形图测试组件
 * 用于验证最基础的环形图显示
 */

import React from 'react';
import { Card, Progress } from 'antd';
import { getEmotionColor, getEmotionLabel } from '@/utils/emotionUtils';

const SimpleCircleTest: React.FC = () => {
  // 示例数据
  const primaryEmotion = { emotion: 'sad', value: 0.41 };
  const secondaryEmotion = { emotion: 'happy', value: 0.31 };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Card title="简单环形图测试" style={{ marginBottom: '20px' }}>
        <p>这是最基础的环形图显示测试，不使用任何复杂的修复脚本。</p>
        <p>应该显示两个环形图：主要情绪（悲伤）和次要情绪（高兴）</p>
      </Card>

      <Card title="环形图显示区域">
        {/* 基础环形图容器 */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            margin: '15px 0',
            height: '180px',
            width: '100%',
          }}
        >
          {/* 主要情绪环形图 */}
          <div
            style={{
              flex: '1',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Progress
              type="circle"
              percent={Math.round(primaryEmotion.value * 100)}
              format={() => (
                <div
                  style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: '100%',
                    textAlign: 'center',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <div
                    style={{
                      color: getEmotionColor(primaryEmotion.emotion),
                      fontSize: '24px',
                      fontWeight: 'bold',
                      lineHeight: 1,
                      marginBottom: '2px',
                    }}
                  >
                    {Math.round(primaryEmotion.value * 100)}%
                  </div>
                  <div
                    style={{
                      color: '#333333',
                      fontSize: '14px',
                      fontWeight: 600,
                      lineHeight: 1,
                      marginBottom: '1px',
                    }}
                  >
                    {getEmotionLabel(primaryEmotion.emotion)}
                  </div>
                  <div
                    style={{
                      color: getEmotionColor(primaryEmotion.emotion),
                      fontSize: '11px',
                      fontWeight: 500,
                      lineHeight: 1,
                      opacity: 0.9,
                    }}
                  >
                    {primaryEmotion.emotion.charAt(0).toUpperCase() +
                      primaryEmotion.emotion.slice(1)}
                  </div>
                </div>
              )}
              strokeColor={getEmotionColor(primaryEmotion.emotion)}
              size={150}
              trailColor="rgba(0,0,0,0.06)"
            />
          </div>

          {/* 次要情绪环形图 */}
          <div
            style={{
              flex: '1',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Progress
              type="circle"
              percent={Math.round(secondaryEmotion.value * 100)}
              format={() => (
                <div
                  style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: '100%',
                    textAlign: 'center',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <div
                    style={{
                      color: getEmotionColor(secondaryEmotion.emotion),
                      fontSize: '20px',
                      fontWeight: 'bold',
                      lineHeight: 1,
                      marginBottom: '2px',
                    }}
                  >
                    {Math.round(secondaryEmotion.value * 100)}%
                  </div>
                  <div
                    style={{
                      color: '#333333',
                      fontSize: '12px',
                      fontWeight: 600,
                      lineHeight: 1,
                      marginBottom: '1px',
                    }}
                  >
                    {getEmotionLabel(secondaryEmotion.emotion)}
                  </div>
                  <div
                    style={{
                      color: getEmotionColor(secondaryEmotion.emotion),
                      fontSize: '10px',
                      fontWeight: 500,
                      lineHeight: 1,
                      opacity: 0.9,
                    }}
                  >
                    {secondaryEmotion.emotion.charAt(0).toUpperCase() +
                      secondaryEmotion.emotion.slice(1)}
                  </div>
                </div>
              )}
              strokeColor={getEmotionColor(secondaryEmotion.emotion)}
              size={120}
              trailColor="rgba(0,0,0,0.06)"
            />
          </div>
        </div>

        <div
          style={{
            fontSize: '12px',
            color: '#666',
            textAlign: 'center',
            marginTop: '10px',
            padding: '10px',
            backgroundColor: '#f5f5f5',
            borderRadius: '4px',
          }}
        >
          <strong>测试说明：</strong>
          <br />
          1. 左侧应该显示主要情绪环形图（悲伤，41%）
          <br />
          2. 右侧应该显示次要情绪环形图（高兴，31%）
          <br />
          3. 环形图应该正确显示百分比、中文名称和英文名称
          <br />
          4. 在移动端也应该正常显示
          <br />
          5. 如果显示正常，说明基础环形图功能工作正常
        </div>

        {/* 单独的环形图测试 */}
        <div style={{ marginTop: '30px', textAlign: 'center' }}>
          <h4>单独环形图测试</h4>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-around',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: '20px',
              margin: '20px 0',
            }}
          >
            {/* 大环形图 */}
            <div>
              <h5>大环形图 (150px)</h5>
              <Progress
                type="circle"
                percent={75}
                format={(percent) => (
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff' }}>
                      {percent}%
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>测试</div>
                  </div>
                )}
                size={150}
                strokeColor="#1890ff"
              />
            </div>

            {/* 中环形图 */}
            <div>
              <h5>中环形图 (120px)</h5>
              <Progress
                type="circle"
                percent={60}
                format={(percent) => (
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#52c41a' }}>
                      {percent}%
                    </div>
                    <div style={{ fontSize: '11px', color: '#666' }}>测试</div>
                  </div>
                )}
                size={120}
                strokeColor="#52c41a"
              />
            </div>

            {/* 小环形图 */}
            <div>
              <h5>小环形图 (100px)</h5>
              <Progress
                type="circle"
                percent={45}
                format={(percent) => (
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#f5222d' }}>
                      {percent}%
                    </div>
                    <div style={{ fontSize: '10px', color: '#666' }}>测试</div>
                  </div>
                )}
                size={100}
                strokeColor="#f5222d"
              />
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SimpleCircleTest;
