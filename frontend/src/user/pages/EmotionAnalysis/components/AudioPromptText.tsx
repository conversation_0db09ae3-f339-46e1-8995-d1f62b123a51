import React, { useState } from 'react';
import { Card, Typography, Button } from 'antd';
import { SoundOutlined, DownOutlined, UpOutlined } from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface AudioPromptTextProps {
  isDarkMode?: boolean;
}

const AudioPromptText: React.FC<AudioPromptTextProps> = ({ isDarkMode = false }) => {
  const [expanded, setExpanded] = useState(false);

  const promptText = `在苍茫的大海上，狂风卷集着乌云。在乌云和大海之间，海燕像黑色的闪电，在高傲地飞翔。

一会儿翅膀碰着波浪，一会儿箭一般地直冲向乌云，它叫喊着，──就在这鸟儿勇敢的叫喊声里，乌云听出了欢乐。

在这叫喊声里──充满着对暴风雨的渴望！在这叫喊声里，乌云听出了愤怒的力量、热情的火焰和胜利的信心。

海鸥在暴风雨来临之前呻吟着，──呻吟着，在大海上面飞窜，想把自己对暴风雨的恐惧，掩藏到大海深处。

海鸭也在呻吟着，──它们这些海鸭啊，享受不了生活的战斗的欢乐：轰隆隆的雷声就把它们吓坏了。

蠢笨的企鹅，胆怯地把肥胖的身体躲藏到悬崖底下……只有那高傲的海燕，勇敢地，自由自在地，在泛起白沫的大海上飞翔！

乌云越来越暗，越来越低，向海面直压下来，而波浪一边歌唱，一边冲向高空，去迎接那雷声。

雷声轰响。波浪在愤怒的飞沫中呼叫，跟狂风争鸣。看吧，狂风紧紧抱起一层层巨浪，恶狠狠地把它们甩到悬崖上，把这些大块的翡翠摔成尘雾和碎沫。`;

  const getThemeColors = () => {
    if (isDarkMode) {
      return {
        cardBackground: 'rgba(255, 255, 255, 0.08)',
        textColor: '#ffffff',
        secondaryTextColor: 'rgba(255, 255, 255, 0.7)',
        borderColor: 'rgba(255, 255, 255, 0.15)',
        buttonBackground: 'rgba(24, 144, 255, 0.2)',
        buttonBorder: 'rgba(24, 144, 255, 0.4)',
        promptBackground: 'rgba(255, 255, 255, 0.05)',
        shadowColor: 'rgba(0, 0, 0, 0.3)',
      };
    } else {
      return {
        cardBackground: '#ffffff',
        textColor: '#333333',
        secondaryTextColor: '#666666',
        borderColor: '#e8e8e8',
        buttonBackground: 'rgba(24, 144, 255, 0.1)',
        buttonBorder: 'rgba(24, 144, 255, 0.3)',
        promptBackground: '#f8f9fa',
        shadowColor: 'rgba(0, 0, 0, 0.1)',
      };
    }
  };

  const themeColors = getThemeColors();

  return (
    <Card
      style={{
        background: themeColors.cardBackground,
        border: `1px solid ${themeColors.borderColor}`,
        borderRadius: '16px',
        boxShadow: `0 4px 20px ${themeColors.shadowColor}`,
        marginBottom: '20px',
      }}
      bodyStyle={{ padding: '20px' }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '16px',
          gap: '12px',
        }}
      >
        <div
          style={{
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '18px',
          }}
        >
          📖
        </div>
        <div style={{ flex: 1 }}>
          <div
            className="audio-prompt-green-text"
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              color: '#52c41a', // 绿色
              display: 'block',
              WebkitTextFillColor: '#52c41a', // 兼容webkit浏览器
              textShadow: 'none', // 确保没有文字阴影影响颜色
            }}
          >
            不知道说什么？试试朗读下面这段文字
          </div>
          <Text
            style={{
              fontSize: '13px',
              color: themeColors.secondaryTextColor,
            }}
          >
            Don&apos;t know what to say? Try reading this text aloud
          </Text>
        </div>
        <Button
          type="text"
          icon={expanded ? <UpOutlined /> : <DownOutlined />}
          onClick={() => setExpanded(!expanded)}
          style={{
            color: themeColors.textColor,
            border: 'none',
            background: 'transparent',
          }}
        />
      </div>

      {expanded && (
        <div
          style={{
            background: themeColors.promptBackground,
            borderRadius: '12px',
            padding: '20px',
            border: `1px solid ${themeColors.borderColor}`,
            marginTop: '16px',
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '12px',
              gap: '8px',
            }}
          >
            <SoundOutlined
              style={{
                fontSize: '16px',
                color: '#1890ff',
              }}
            />
            <Text
              style={{
                fontSize: '14px',
                fontWeight: '500',
                color: themeColors.textColor,
              }}
            >
              《海燕》节选 - 高尔基
            </Text>
          </div>

          <Paragraph
            style={{
              color: themeColors.textColor,
              lineHeight: '1.4',
              fontSize: '15px',
              margin: 0,
            }}
          >
            {promptText
              .split('\n')
              .filter((line) => line.trim() !== '')
              .map((line, index) => (
                <React.Fragment key={index}>
                  {line.trim()}
                  <br />
                </React.Fragment>
              ))}
          </Paragraph>

          <div
            style={{
              marginTop: '16px',
              padding: '12px',
              background: isDarkMode ? 'rgba(24, 144, 255, 0.1)' : 'rgba(24, 144, 255, 0.05)',
              borderRadius: '8px',
              border: `1px solid ${isDarkMode ? 'rgba(24, 144, 255, 0.2)' : 'rgba(24, 144, 255, 0.1)'}`,
            }}
          >
            <Text
              style={{
                fontSize: '13px',
                color: themeColors.secondaryTextColor,
                fontStyle: 'italic',
              }}
            >
              💡 提示：朗读时请保持自然的语调和情感，这样能够更好地进行情绪分析
            </Text>
          </div>
        </div>
      )}

      {!expanded && (
        <div
          style={{
            textAlign: 'center',
            padding: '12px',
            background: themeColors.promptBackground,
            borderRadius: '8px',
            border: `1px solid ${themeColors.borderColor}`,
          }}
        >
          <Text
            style={{
              fontSize: '14px',
              color: themeColors.secondaryTextColor,
            }}
          >
            点击展开查看朗读文本 Click to expand reading text
          </Text>
        </div>
      )}

      <style>{`
        .ant-card-body {
          padding: 20px !important;
        }
        
        .ant-typography {
          margin-bottom: 0 !important;
        }
        
        .ant-btn:hover {
          transform: translateY(-1px);
          transition: all 0.3s ease;
        }
        
        .audio-prompt-green-text {
          color: #52c41a !important;
        }
        
        .audio-prompt-green-text.ant-typography {
          color: #52c41a !important;
        }
        
        span.audio-prompt-green-text,
        div.audio-prompt-green-text {
          color: #52c41a !important;
          -webkit-text-fill-color: #52c41a !important;
        }
      `}</style>
    </Card>
  );
};

export default AudioPromptText;
