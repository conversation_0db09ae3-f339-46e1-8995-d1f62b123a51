/**
 * 纯净环形图测试组件
 * 完全不导入任何项目CSS，只使用Ant Design默认样式
 */

import React from 'react';
import { Progress } from 'antd';

const PureCircleTest: React.FC = () => {
  return (
    <div
      style={{
        padding: '20px',
        maxWidth: '800px',
        margin: '0 auto',
        fontFamily: 'Arial, sans-serif',
      }}
    >
      <div
        style={{
          backgroundColor: '#fff',
          padding: '20px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          marginBottom: '20px',
        }}
      >
        <h2 style={{ margin: '0 0 10px 0', color: '#333' }}>纯净环形图测试 - 零CSS污染</h2>
        <p style={{ margin: '0 0 20px 0', color: '#666' }}>
          这个测试页面完全不导入任何项目CSS，只使用Ant Design的默认样式。
          如果这里的环形图显示正常，说明问题出在项目的全局CSS上。
        </p>
      </div>

      <div
        style={{
          backgroundColor: '#fff',
          padding: '20px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        }}
      >
        <h3 style={{ margin: '0 0 20px 0', color: '#333' }}>环形图显示测试</h3>

        {/* 完全默认的环形图 */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            margin: '20px 0',
            padding: '20px',
            backgroundColor: '#f5f5f5',
            borderRadius: '8px',
          }}
        >
          <div style={{ textAlign: 'center' }}>
            <h4 style={{ margin: '0 0 10px 0', color: '#333' }}>85% 开心</h4>
            <Progress type="circle" percent={85} size={150} strokeColor="#52c41a" />
            <div style={{ marginTop: '10px', color: '#666', fontSize: '14px' }}>
              完全默认的环形图
            </div>
          </div>

          <div style={{ textAlign: 'center' }}>
            <h4 style={{ margin: '0 0 10px 0', color: '#333' }}>10% 平静</h4>
            <Progress type="circle" percent={10} size={120} strokeColor="#1890ff" />
            <div style={{ marginTop: '10px', color: '#666', fontSize: '14px' }}>
              完全默认的环形图
            </div>
          </div>
        </div>

        {/* 不同尺寸测试 */}
        <div style={{ marginTop: '30px' }}>
          <h4 style={{ margin: '0 0 20px 0', color: '#333' }}>不同尺寸测试</h4>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-around',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: '20px',
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ marginBottom: '10px', color: '#333', fontSize: '14px' }}>
                大 (200px)
              </div>
              <Progress type="circle" percent={75} size={200} strokeColor="#f5222d" />
            </div>

            <div style={{ textAlign: 'center' }}>
              <div style={{ marginBottom: '10px', color: '#333', fontSize: '14px' }}>
                中 (150px)
              </div>
              <Progress type="circle" percent={60} size={150} strokeColor="#fa8c16" />
            </div>

            <div style={{ textAlign: 'center' }}>
              <div style={{ marginBottom: '10px', color: '#333', fontSize: '14px' }}>
                小 (100px)
              </div>
              <Progress type="circle" percent={45} size={100} strokeColor="#722ed1" />
            </div>

            <div style={{ textAlign: 'center' }}>
              <div style={{ marginBottom: '10px', color: '#333', fontSize: '14px' }}>
                迷你 (80px)
              </div>
              <Progress type="circle" percent={30} size={80} strokeColor="#eb2f96" />
            </div>
          </div>
        </div>

        {/* 不同百分比测试 */}
        <div style={{ marginTop: '30px' }}>
          <h4 style={{ margin: '0 0 20px 0', color: '#333' }}>不同百分比测试</h4>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-around',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: '20px',
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <Progress type="circle" percent={0} size={120} strokeColor="#52c41a" />
              <div style={{ marginTop: '5px', color: '#666', fontSize: '12px' }}>0%</div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <Progress type="circle" percent={25} size={120} strokeColor="#1890ff" />
              <div style={{ marginTop: '5px', color: '#666', fontSize: '12px' }}>25%</div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <Progress type="circle" percent={50} size={120} strokeColor="#fa8c16" />
              <div style={{ marginTop: '5px', color: '#666', fontSize: '12px' }}>50%</div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <Progress type="circle" percent={75} size={120} strokeColor="#f5222d" />
              <div style={{ marginTop: '5px', color: '#666', fontSize: '12px' }}>75%</div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <Progress type="circle" percent={100} size={120} strokeColor="#722ed1" />
              <div style={{ marginTop: '5px', color: '#666', fontSize: '12px' }}>100%</div>
            </div>
          </div>
        </div>

        <div
          style={{
            marginTop: '30px',
            padding: '15px',
            backgroundColor: '#e6f7ff',
            borderRadius: '6px',
            border: '1px solid #91d5ff',
          }}
        >
          <h4 style={{ margin: '0 0 10px 0', color: '#0050b3' }}>测试说明</h4>
          <ul style={{ margin: 0, paddingLeft: '20px', color: '#0050b3' }}>
            <li>这个页面完全不导入任何项目CSS文件</li>
            <li>只使用Ant Design的默认Progress组件</li>
            <li>如果这里的环形图显示正常，说明问题出在项目的全局CSS上</li>
            <li>如果这里的环形图也有问题，说明是Ant Design版本或浏览器兼容性问题</li>
            <li>请在iOS Safari中测试这个页面</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PureCircleTest;
