/**
 * 简化版环形图组件
 * 使用最基础的Ant Design样式，确保跨浏览器兼容性
 */

import React from 'react';
import { Progress } from 'antd';
import { getEmotionColor, getEmotionLabel } from '@/utils/emotionUtils';

interface SimpleCircleProgressProps {
  emotion: string;
  value: number;
  size?: number;
  isDarkMode?: boolean;
  showLabel?: boolean;
}

const SimpleCircleProgress: React.FC<SimpleCircleProgressProps> = ({
  emotion,
  value,
  size = 150,
  isDarkMode = false,
  showLabel = true,
}) => {
  const percent = Math.round(value * 100);
  const emotionColor = getEmotionColor(emotion);
  const emotionLabel = getEmotionLabel(emotion);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {/* 使用最简单的环形图，不自定义format */}
      <Progress
        type="circle"
        percent={percent}
        strokeColor={emotionColor}
        size={size}
        trailColor={isDarkMode ? 'rgba(255,255,255,0.15)' : 'rgba(0,0,0,0.06)'}
        format={(percent) => (
          <div
            style={{
              textAlign: 'center',
              color: emotionColor,
            }}
          >
            <div
              style={{
                fontSize: size > 120 ? '24px' : '20px',
                fontWeight: 'bold',
                lineHeight: 1,
              }}
            >
              {percent}%
            </div>
          </div>
        )}
      />

      {/* 标签显示在环形图下方 */}
      {showLabel && (
        <div
          style={{
            textAlign: 'center',
            marginTop: '8px',
            maxWidth: size,
          }}
        >
          <div
            style={{
              color: isDarkMode ? '#ffffff' : '#333333',
              fontSize: '14px',
              fontWeight: 600,
              marginBottom: '2px',
            }}
          >
            {emotionLabel}
          </div>
          <div
            style={{
              color: emotionColor,
              fontSize: '11px',
              fontWeight: 500,
              opacity: 0.9,
            }}
          >
            {emotion.charAt(0).toUpperCase() + emotion.slice(1)}
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleCircleProgress;
