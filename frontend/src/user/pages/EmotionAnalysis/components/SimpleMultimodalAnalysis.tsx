/**
 * 简化版多模态分析组件
 * 移除复杂的CSS属性，使用最基础的样式
 */

import React, { useState } from 'react';
import { Button, Typography, Divider, Card } from 'antd';

const { Text } = Typography;

interface SimpleMultimodalAnalysisProps {
  imageId: string | null;
  isDarkMode?: boolean;
}

const SimpleMultimodalAnalysis: React.FC<SimpleMultimodalAnalysisProps> = ({
  imageId,
  isDarkMode = false,
}) => {
  const [result, setResult] = useState<string>('');
  const [analyzing, setAnalyzing] = useState(false);

  const handleAnalysis = async () => {
    if (!imageId) {
      setResult('请先上传图片');
      return;
    }

    setAnalyzing(true);
    setResult('');

    // 模拟分析过程
    setTimeout(() => {
      setResult(`基于图片 ${imageId} 的多模态分析结果：

这是一个测试分析结果。如果您能看到这段文字，说明多模态组件在当前设备上显示正常。

分析要点：
• 情绪状态：积极乐观
• 建议：保持良好心态
• 注意事项：适当休息

这个简化版本移除了所有复杂的CSS属性，使用最基础的样式来确保跨浏览器兼容性。`);
      setAnalyzing(false);
    }, 2000);
  };

  return (
    <Card
      title={
        <div>
          <Text strong style={{ fontSize: '16px', color: isDarkMode ? '#fff' : '#000' }}>
            多模态分析 Multimodal Analysis
          </Text>
          <Text
            style={{ marginLeft: '8px', fontSize: '14px', color: isDarkMode ? '#aaa' : '#666' }}
          >
            （简化版本）
          </Text>
        </div>
      }
      extra={
        <Button type="primary" onClick={handleAnalysis} loading={analyzing} disabled={!imageId}>
          执行分析
        </Button>
      }
      style={{
        marginBottom: '20px',
        backgroundColor: isDarkMode ? '#1f1f1f' : '#fff',
        borderColor: isDarkMode ? '#333' : '#e8e8e8',
      }}
      bodyStyle={{
        backgroundColor: isDarkMode ? '#1f1f1f' : '#fff',
      }}
    >
      <Divider style={{ margin: '10px 0', borderColor: isDarkMode ? '#333' : '#e8e8e8' }} />

      <div
        style={{
          minHeight: '100px',
          maxHeight: '300px',
          overflowY: 'auto',
          padding: '15px',
          backgroundColor: isDarkMode ? '#141414' : '#f9f9f9',
          borderRadius: '6px',
          border: `1px solid ${isDarkMode ? '#333' : '#e8e8e8'}`,
          color: isDarkMode ? '#fff' : '#000',
        }}
      >
        {analyzing && (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <div style={{ color: isDarkMode ? '#aaa' : '#666' }}>正在分析中，请稍候...</div>
          </div>
        )}

        {!analyzing && !result && (
          <div
            style={{
              textAlign: 'center',
              padding: '20px',
              color: isDarkMode ? '#aaa' : '#666',
            }}
          >
            点击"执行分析"开始多模态分析
          </div>
        )}

        {result && (
          <div
            style={{
              color: isDarkMode ? '#fff' : '#000',
              lineHeight: '1.6',
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word',
            }}
          >
            {result}
          </div>
        )}
      </div>
    </Card>
  );
};

export default SimpleMultimodalAnalysis;
