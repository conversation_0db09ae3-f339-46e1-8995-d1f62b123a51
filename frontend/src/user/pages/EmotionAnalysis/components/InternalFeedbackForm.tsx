import React, { useState } from 'react';
import { Input, Select, Button, Form, message } from 'antd';
import { emotionColors, emotionLabels } from '@/constants/emotion';
// 假设已有API方法 submitFeedback，实际请根据你的API路径调整
// import { submitFeedback } from '@/api/feedback';

const { Option } = Select;

interface InternalFeedbackFormProps {
  analysisId: string;
  imageUrl: string;
  emotion?: string;
}

const InternalFeedbackForm: React.FC<InternalFeedbackFormProps> = ({
  analysisId,
  imageUrl,
  emotion,
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (values: any) => {
    setSubmitting(true);
    try {
      const token = localStorage.getItem('user_token');
      const response = await fetch('/api/v1/emotion/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        body: JSON.stringify({
          ...values,
          analysis_id: analysisId,
          emotion: values.emotion,
          rating: '', // rating 字段直接传空字符串
        }),
      });
      if (response.ok) {
        message.success('标注提交成功！');
        form.resetFields();
      } else {
        const data = await response.json().catch(() => ({}));
        message.error(`提交失败: ${data.detail || response.statusText}`);
      }
    } catch (e) {
      message.error('提交失败，请重试');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      <style>
        {`
          html[data-theme='dark'] .internal-feedback-form input,
          html[data-theme='dark'] .internal-feedback-form .ant-input,
          html[data-theme='dark'] .internal-feedback-form .ant-select-selector,
          html[data-theme='dark'] .internal-feedback-form .ant-select-selection-item,
          html[data-theme='dark'] .internal-feedback-form .ant-select-selection-placeholder,
          html[data-theme='dark'] .internal-feedback-form label,
          html[data-theme='dark'] .internal-feedback-form .ant-form-item-label > label {
            color: #fff !important;
            background: #222 !important;
            border-color: #444 !important;
          }
          html[data-theme='dark'] .internal-feedback-form .ant-btn-primary {
            background: #ffb6c1 !important;
            color: #222 !important;
            border: none;
          }
          html[data-theme='dark'] .internal-feedback-form input::placeholder,
          html[data-theme='dark'] .internal-feedback-form .ant-input::placeholder,
          html[data-theme='dark'] .internal-feedback-form .ant-select-selection-placeholder {
            color: #bbbbbb !important;
            opacity: 1 !important;
          }
          /* 全局覆盖下拉菜单样式，确保性别选项为深色背景亮色字体 */
          html[data-theme='dark'] .ant-select-dropdown {
            background: #222 !important;
          }
          html[data-theme='dark'] .ant-select-dropdown .ant-select-item {
            background: #222 !important;
            color: #fff !important;
          }
          html[data-theme='dark'] .ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled),
          html[data-theme='dark'] .ant-select-dropdown .ant-select-item-option-active {
            background: #333 !important;
            color: #ffb6c1 !important;
          }
          html[data-theme='dark'] .ant-select-dropdown .ant-select-item-option-disabled {
            color: #888 !important;
            background: #222 !important;
            opacity: 1 !important;
          }
        `}
      </style>
      <Form
        className="internal-feedback-form"
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        style={{ maxWidth: 400, margin: '0 auto' }}
      >
        <Form.Item name="name" label="姓名（可选） / Name (Optional)">
          <Input placeholder="请输入姓名 / Please enter name" style={{ height: 55 }} />
        </Form.Item>
        <Form.Item
          name="gender"
          label="性别 / Gender"
          rules={[{ required: true, message: '请选择性别 / Please select gender' }]}
        >
          <Select placeholder="请选择性别 / Please select gender" style={{ height: 55 }}>
            <Option value="male">男 / Male</Option>
            <Option value="female">女 / Female</Option>
          </Select>
        </Form.Item>
        <Form.Item
          name="age"
          label="年龄 / Age"
          rules={[
            { required: true, message: '请输入年龄 / Please enter age' },
            { type: 'number', min: 0, max: 120, message: '年龄范围0-120 / Age range 0-120' },
          ]}
          getValueFromEvent={(e) => Number(e.target.value)}
        >
          <Input
            type="number"
            min={0}
            max={120}
            placeholder="请输入年龄 / Please enter age"
            style={{ height: 55 }}
          />
        </Form.Item>
        <Form.Item
          name="emotion"
          label="情绪标签 / Emotion Label"
          rules={[{ required: true, message: '请选择情绪 / Please select emotion' }]}
        >
          <Select
            placeholder="请选择情绪 / Please select emotion"
            optionLabelProp="label"
            style={{ height: 55 }}
          >
            {Object.keys(emotionLabels).map((key) => (
              <Option
                key={key}
                value={key}
                label={
                  <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                    <span
                      style={{
                        display: 'inline-block',
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        background: emotionColors[key as keyof typeof emotionColors],
                        marginRight: 8,
                        verticalAlign: 'middle',
                      }}
                    />
                    {emotionLabels[key as keyof typeof emotionLabels]} /{' '}
                    {key.charAt(0).toUpperCase() + key.slice(1)}
                  </span>
                }
              >
                <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                  <span
                    style={{
                      display: 'inline-block',
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      background: emotionColors[key as keyof typeof emotionColors],
                      marginRight: 8,
                      verticalAlign: 'middle',
                    }}
                  />
                  {emotionLabels[key as keyof typeof emotionLabels]} /{' '}
                  {key.charAt(0).toUpperCase() + key.slice(1)}
                </span>
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item style={{ textAlign: 'center' }}>
          <Button type="primary" htmlType="submit" loading={submitting} style={{ height: 58 }}>
            提交标注 / Submit Annotation
          </Button>
        </Form.Item>
      </Form>
    </>
  );
};

export default InternalFeedbackForm;
