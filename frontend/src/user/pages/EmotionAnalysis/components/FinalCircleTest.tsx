/**
 * 最终环形图测试组件
 * 测试修复全局CSS后的环形图显示效果
 */

import React from 'react';
import { Card, Progress } from 'antd';
import { getEmotionColor, getEmotionLabel } from '@/utils/emotionUtils';

const FinalCircleTest: React.FC = () => {
  // 示例数据
  const primaryEmotion = { emotion: 'happy', value: 0.85 };
  const secondaryEmotion = { emotion: 'neutral', value: 0.1 };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Card title="最终环形图测试 - 修复全局CSS后" style={{ marginBottom: '20px' }}>
        <p>这个测试页面验证修复全局CSS后的环形图显示效果。</p>
        <p>修复内容：</p>
        <ul>
          <li>修复了 index.less 中强制设置环形图文字为黑色的问题</li>
          <li>修复了 dark-theme.less 中的Progress文字颜色覆盖问题</li>
          <li>环形图现在使用完全默认的Ant Design样式</li>
        </ul>
      </Card>

      <Card title="修复后的环形图显示">
        {/* 环形图容器 - 与实际组件相同的布局 */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            margin: '15px 0',
            minHeight: '220px',
            width: '100%',
          }}
        >
          {/* 主要情绪环形图 */}
          <div
            style={{
              flex: '1',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-start',
              alignItems: 'center',
            }}
          >
            <Progress
              type="circle"
              percent={Math.round(primaryEmotion.value * 100)}
              strokeColor={getEmotionColor(primaryEmotion.emotion)}
              size={150}
              trailColor="rgba(0,0,0,0.06)"
            />
            {/* 主要情绪标签 */}
            <div
              style={{
                textAlign: 'center',
                marginTop: '8px',
              }}
            >
              <div
                style={{
                  color: '#333333',
                  fontSize: '14px',
                  fontWeight: 600,
                  marginBottom: '2px',
                }}
              >
                {getEmotionLabel(primaryEmotion.emotion)}
              </div>
              <div
                style={{
                  color: getEmotionColor(primaryEmotion.emotion),
                  fontSize: '11px',
                  fontWeight: 500,
                  opacity: 0.9,
                }}
              >
                {primaryEmotion.emotion.charAt(0).toUpperCase() + primaryEmotion.emotion.slice(1)}
              </div>
            </div>
          </div>

          {/* 次要情绪环形图 */}
          <div
            style={{
              flex: '1',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-start',
              alignItems: 'center',
            }}
          >
            <Progress
              type="circle"
              percent={Math.round(secondaryEmotion.value * 100)}
              strokeColor={getEmotionColor(secondaryEmotion.emotion)}
              size={120}
              trailColor="rgba(0,0,0,0.06)"
            />
            {/* 次要情绪标签 */}
            <div
              style={{
                textAlign: 'center',
                marginTop: '8px',
              }}
            >
              <div
                style={{
                  color: '#333333',
                  fontSize: '12px',
                  fontWeight: 600,
                  marginBottom: '2px',
                }}
              >
                {getEmotionLabel(secondaryEmotion.emotion)}
              </div>
              <div
                style={{
                  color: getEmotionColor(secondaryEmotion.emotion),
                  fontSize: '10px',
                  fontWeight: 500,
                  opacity: 0.9,
                }}
              >
                {secondaryEmotion.emotion.charAt(0).toUpperCase() +
                  secondaryEmotion.emotion.slice(1)}
              </div>
            </div>
          </div>
        </div>

        <div
          style={{
            fontSize: '12px',
            color: '#666',
            textAlign: 'center',
            marginTop: '10px',
            padding: '10px',
            backgroundColor: '#f0f9ff',
            borderRadius: '4px',
            border: '1px solid #bae6fd',
          }}
        >
          <strong>修复说明：</strong>
          <br />
          1. 修复了全局CSS中强制设置Progress文字颜色的问题
          <br />
          2. 环形图现在使用Ant Design的默认文字居中机制
          <br />
          3. 左侧：开心85%，右侧：平静10%
          <br />
          4. 如果这个版本在iOS Safari中显示正常，说明修复成功
        </div>

        {/* 对比测试 */}
        <div style={{ marginTop: '30px' }}>
          <h4>对比测试 - 不同情绪和百分比</h4>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
              gap: '20px',
              margin: '20px 0',
            }}
          >
            {/* 不同情绪测试 */}
            {[
              { emotion: 'happy', value: 0.85, label: '开心' },
              { emotion: 'sad', value: 0.65, label: '悲伤' },
              { emotion: 'angry', value: 0.45, label: '愤怒' },
              { emotion: 'surprise', value: 0.25, label: '惊讶' },
              { emotion: 'fear', value: 0.15, label: '恐惧' },
              { emotion: 'neutral', value: 0.05, label: '平静' },
            ].map((item, index) => (
              <div key={index} style={{ textAlign: 'center' }}>
                <Progress
                  type="circle"
                  percent={Math.round(item.value * 100)}
                  strokeColor={getEmotionColor(item.emotion)}
                  size={100}
                  trailColor="rgba(0,0,0,0.06)"
                />
                <div
                  style={{
                    marginTop: '8px',
                    fontSize: '12px',
                    color: '#333',
                    fontWeight: 600,
                  }}
                >
                  {item.label}
                </div>
                <div
                  style={{
                    fontSize: '11px',
                    color: getEmotionColor(item.emotion),
                    opacity: 0.8,
                  }}
                >
                  {item.emotion}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 边界值测试 */}
        <div style={{ marginTop: '30px' }}>
          <h4>边界值测试</h4>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-around',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: '20px',
              margin: '20px 0',
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <Progress type="circle" percent={0} strokeColor="#52c41a" size={120} />
              <div style={{ marginTop: '8px', fontSize: '12px', color: '#333' }}>0% - 最小值</div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <Progress type="circle" percent={1} strokeColor="#1890ff" size={120} />
              <div style={{ marginTop: '8px', fontSize: '12px', color: '#333' }}>1% - 极小值</div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <Progress type="circle" percent={99} strokeColor="#fa8c16" size={120} />
              <div style={{ marginTop: '8px', fontSize: '12px', color: '#333' }}>99% - 极大值</div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <Progress type="circle" percent={100} strokeColor="#f5222d" size={120} />
              <div style={{ marginTop: '8px', fontSize: '12px', color: '#333' }}>100% - 最大值</div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default FinalCircleTest;
