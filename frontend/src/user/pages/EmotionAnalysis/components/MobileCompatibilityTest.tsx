/**
 * 移动端兼容性测试组件
 * 用于测试环形图和多模态组件在移动端的显示情况
 */

import React, { useState, useEffect } from 'react';
import { Progress, Button, Card, Typography, Divider } from 'antd';
import { getEmotionColor, getEmotionLabel } from '@/utils/emotionUtils';
import { isWechatBrowser } from '@/shared/utils/browserDetect';

const { Text } = Typography;

const MobileCompatibilityTest: React.FC = () => {
  const [showMultimodal, setShowMultimodal] = useState(false);

  // 测试数据
  const primaryEmotion = { emotion: 'happy', value: 0.85 };
  const secondaryEmotion = { emotion: 'neutral', value: 0.1 };

  // 获取设备信息的函数，在微信浏览器中保护隐私
  const getDisplayDeviceInfo = () => {
    if (isWechatBrowser()) {
      // 微信浏览器中只显示基本的、非敏感信息
      return {
        userAgent: '微信内置浏览器',
        screenSize: `${window.innerWidth} x ${window.innerHeight}`,
        pixelRatio: window.devicePixelRatio,
        touchSupport: 'ontouchstart' in window ? '是' : '否',
        browserType: '微信浏览器',
        privacyMode: true,
      };
    }

    // 非微信浏览器显示完整信息
    return {
      userAgent: navigator.userAgent,
      screenSize: `${window.innerWidth} x ${window.innerHeight}`,
      pixelRatio: window.devicePixelRatio,
      touchSupport: 'ontouchstart' in window ? '是' : '否',
      browserType: '标准浏览器',
      privacyMode: false,
    };
  };

  const deviceInfo = getDisplayDeviceInfo();

  return (
    <div style={{ padding: '20px', maxWidth: '850px', margin: '0 auto' }}>
      <Card title="移动端兼容性测试" style={{ marginBottom: '20px' }}>
        <Text>
          这个页面用于测试环形图和多模态组件在移动端（特别是iOS Safari）的显示情况。
          请在不同设备上测试以下组件的显示效果。
        </Text>
      </Card>

      {/* 1. 基础环形图测试 */}
      <Card title="1. 基础环形图测试" style={{ marginBottom: '20px' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: '20px',
          }}
        >
          {/* 完全默认的环形图 */}
          <div style={{ textAlign: 'center' }}>
            <h4>默认环形图</h4>
            <Progress type="circle" percent={85} size={150} strokeColor="#52c41a" />
            <div style={{ marginTop: '10px' }}>85% 高兴</div>
          </div>

          {/* 自定义format的环形图 */}
          <div style={{ textAlign: 'center' }}>
            <h4>自定义format</h4>
            <Progress
              type="circle"
              percent={85}
              size={150}
              strokeColor="#52c41a"
              format={(percent) => `${percent}%`}
            />
            <div style={{ marginTop: '10px' }}>85% 高兴</div>
          </div>
        </div>
      </Card>

      {/* 2. 项目中使用的环形图样式 */}
      <Card title="2. 项目环形图样式测试" style={{ marginBottom: '20px' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            margin: '15px 0',
            minHeight: '180px',
            width: '100%',
          }}
        >
          {/* 主要情绪环形图 */}
          <div
            style={{
              flex: '1',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-start',
              alignItems: 'center',
              position: 'relative',
            }}
          >
            <Progress
              type="circle"
              percent={Math.round(primaryEmotion.value * 100)}
              strokeColor={getEmotionColor(primaryEmotion.emotion)}
              size={150}
              trailColor="rgba(0,0,0,0.06)"
              format={() => (
                <div
                  style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: '100%',
                    textAlign: 'center',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    pointerEvents: 'none',
                  }}
                >
                  <div
                    style={{
                      color: getEmotionColor(primaryEmotion.emotion),
                      fontSize: '24px',
                      fontWeight: 'bold',
                      lineHeight: 1,
                      marginBottom: '2px',
                    }}
                  >
                    {Math.round(primaryEmotion.value * 100)}%
                  </div>
                  <div
                    style={{
                      color: '#333333',
                      fontSize: '14px',
                      fontWeight: 600,
                      marginBottom: '2px',
                    }}
                  >
                    {getEmotionLabel(primaryEmotion.emotion)}
                  </div>
                  <div
                    style={{
                      color: getEmotionColor(primaryEmotion.emotion),
                      fontSize: '11px',
                      fontWeight: 500,
                      opacity: 0.9,
                    }}
                  >
                    {primaryEmotion.emotion.charAt(0).toUpperCase() +
                      primaryEmotion.emotion.slice(1)}
                  </div>
                </div>
              )}
            />
          </div>

          {/* 次要情绪环形图 */}
          <div
            style={{
              flex: '1',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-start',
              alignItems: 'center',
              position: 'relative',
            }}
          >
            <Progress
              type="circle"
              percent={Math.round(secondaryEmotion.value * 100)}
              strokeColor={getEmotionColor(secondaryEmotion.emotion)}
              size={120}
              trailColor="rgba(0,0,0,0.06)"
              format={() => (
                <div
                  style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: '100%',
                    textAlign: 'center',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    pointerEvents: 'none',
                  }}
                >
                  <div
                    style={{
                      color: getEmotionColor(secondaryEmotion.emotion),
                      fontSize: '20px',
                      fontWeight: 'bold',
                      lineHeight: 1,
                      marginBottom: '2px',
                    }}
                  >
                    {Math.round(secondaryEmotion.value * 100)}%
                  </div>
                  <div
                    style={{
                      color: '#333333',
                      fontSize: '12px',
                      fontWeight: 600,
                      marginBottom: '2px',
                    }}
                  >
                    {getEmotionLabel(secondaryEmotion.emotion)}
                  </div>
                  <div
                    style={{
                      color: getEmotionColor(secondaryEmotion.emotion),
                      fontSize: '10px',
                      fontWeight: 500,
                      opacity: 0.9,
                    }}
                  >
                    {secondaryEmotion.emotion.charAt(0).toUpperCase() +
                      secondaryEmotion.emotion.slice(1)}
                  </div>
                </div>
              )}
            />
          </div>
        </div>
      </Card>

      {/* 3. 多模态组件测试 */}
      <Card title="3. 多模态组件测试" style={{ marginBottom: '20px' }}>
        <Button
          type="primary"
          onClick={() => setShowMultimodal(!showMultimodal)}
          style={{ marginBottom: '15px' }}
        >
          {showMultimodal ? '隐藏' : '显示'}多模态组件
        </Button>

        {showMultimodal && (
          <div
            style={{
              padding: '15px',
              borderRadius: '8px',
              marginBottom: '20px',
              backgroundColor: '#f5f5f5',
              border: '1px solid #e8e8e8',
              width: '100%',
              boxSizing: 'border-box',
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '10px',
              }}
            >
              <div>
                <Text strong style={{ fontSize: '16px' }}>
                  多模态分析 Multimodal analysis
                </Text>
                <Text style={{ marginLeft: '8px', fontSize: '14px', color: '#666' }}>
                  （基于以上分析结果的AI独立分析与建议）
                </Text>
              </div>
              <Button type="primary">执行</Button>
            </div>

            <Divider style={{ margin: '10px 0' }} />

            <div
              style={{
                minHeight: '100px',
                maxHeight: '300px',
                overflowY: 'auto',
                padding: '10px',
                backgroundColor: '#fff',
                borderRadius: '4px',
                border: '1px solid #e8e8e8',
                width: '100%',
                boxSizing: 'border-box',
              }}
            >
              <div style={{ color: '#666', lineHeight: '1.6' }}>
                这是多模态分析组件的测试内容。如果您能看到这段文字，说明多模态组件在当前设备上显示正常。
                <br />
                <br />
                请检查：
                <br />• 组件是否完整显示
                <br />• 文字是否清晰可读
                <br />• 滚动是否正常工作
                <br />• 布局是否正确
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* 4. 设备信息 - 在微信浏览器中完全隐藏 */}
      {!isWechatBrowser() && (
        <Card title="4. 设备信息">
          <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
            <div>
              <strong>浏览器类型:</strong> {deviceInfo.browserType}
            </div>
            <div>
              <strong>User Agent:</strong> {deviceInfo.userAgent}
            </div>
            <div>
              <strong>屏幕尺寸:</strong> {deviceInfo.screenSize}
            </div>
            <div>
              <strong>设备像素比:</strong> {deviceInfo.pixelRatio}
            </div>
            <div>
              <strong>是否触摸设备:</strong> {deviceInfo.touchSupport}
            </div>

            {deviceInfo.privacyMode && (
              <div
                style={{
                  marginTop: '10px',
                  padding: '8px',
                  backgroundColor: '#f0f8ff',
                  borderRadius: '4px',
                  fontSize: '12px',
                  color: '#666',
                }}
              >
                🔒 隐私保护模式：在微信浏览器中，部分设备信息已被隐藏以保护您的隐私
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  );
};

export default MobileCompatibilityTest;
