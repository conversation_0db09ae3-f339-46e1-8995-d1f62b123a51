import React, { useState, useRef, useEffect } from 'react';
import { Button, Input } from 'antd';

interface FeedbackInputBoxProps {
  visible: boolean;
  feedbackType: 'average' | 'bad' | null;
  onSubmit: (comment: string) => void;
  onCancel: () => void;
  isDarkMode: boolean;
}

const FeedbackInputBox: React.FC<FeedbackInputBoxProps> = ({
  visible,
  feedbackType,
  onSubmit,
  onCancel,
  isDarkMode,
}) => {
  const [comment, setComment] = useState('');
  const inputRef = useRef<any>(null);

  // 当输入框显示时自动聚焦并强制设置样式
  useEffect(() => {
    if (visible && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();

        // 直接操作 DOM 强制设置暗黑模式样式
        if (isDarkMode && inputRef.current) {
          const textareaElement = inputRef.current.resizableTextArea?.textArea || inputRef.current;
          if (textareaElement) {
            textareaElement.style.setProperty('color', '#ffffff', 'important');
            textareaElement.style.setProperty('background-color', '#1a1a1a', 'important');
            textareaElement.style.setProperty(
              'border-color',
              feedbackType === 'average' ? '#faad14' : '#f05654',
              'important',
            );
          }
        }
      }, 100);
    }
  }, [visible, isDarkMode, feedbackType]);

  // 重置输入内容
  useEffect(() => {
    if (!visible) {
      setComment('');
    }
  }, [visible]);

  // 添加调试日志，确认组件被正确调用
  useEffect(() => {
    console.log('FeedbackInputBox props 变化:', {
      visible,
      feedbackType,
      isDarkMode: isDarkMode ? '暗黑模式' : '亮色模式',
    });

    if (visible) {
      console.log('✅ FeedbackInputBox 应该显示');
    } else {
      console.log('❌ FeedbackInputBox 应该隐藏');
    }
  }, [visible, isDarkMode, feedbackType]);

  // 强制应用暗黑模式样式（包括占位符文字）
  useEffect(() => {
    if (isDarkMode && inputRef.current) {
      const applyDarkStyles = () => {
        const textareaElement = inputRef.current?.resizableTextArea?.textArea || inputRef.current;
        if (textareaElement) {
          textareaElement.style.setProperty('color', '#ffffff', 'important');
          textareaElement.style.setProperty('background-color', '#1a1a1a', 'important');
          textareaElement.style.setProperty(
            'border-color',
            feedbackType === 'average' ? '#faad14' : '#f05654',
            'important',
          );

          // 设置占位符文字颜色
          const style = document.createElement('style');
          style.innerHTML = `
            #feedback-input-box textarea::placeholder {
              color: #cccccc !important;
              opacity: 1 !important;
            }
            #feedback-input-box .ant-input::placeholder {
              color: #cccccc !important;
              opacity: 1 !important;
            }
          `;
          document.head.appendChild(style);

          // 清理函数
          return () => {
            if (style.parentNode) {
              style.parentNode.removeChild(style);
            }
          };
        }
      };

      // 立即应用
      const cleanup = applyDarkStyles();

      // 延迟应用，确保 Ant Design 的样式加载完成后覆盖
      const timer = setTimeout(() => {
        applyDarkStyles();
      }, 50);

      return () => {
        clearTimeout(timer);
        if (cleanup) cleanup();
      };
    }
  }, [isDarkMode, feedbackType, comment]);

  const handleSubmit = () => {
    console.log('📤 FeedbackInputBox 提交:', { comment, feedbackType });
    onSubmit(comment);
    setComment('');
  };

  const handleCancel = () => {
    console.log('🚫 FeedbackInputBox 取消');
    onCancel();
    setComment('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  // 条件返回移到所有 hooks 之后 - 使用 CSS 隐藏而不是完全不渲染
  // if (!visible) return null;

  const promptMessage =
    feedbackType === 'average'
      ? '您觉得分析结果一般，请告诉我们如何改进：'
      : '您觉得分析结果不好，请告诉我们哪里需要改进：';

  return (
    <div
      id="feedback-input-box"
      style={{
        position: 'absolute',
        bottom: '80px', // 位置在按钮正上方
        left: '50%',
        transform: 'translateX(-50%)',
        width: '90%',
        maxWidth: '400px',
        backgroundColor: isDarkMode ? '#2a2a2a' : '#ffffff',
        border: `2px solid ${feedbackType === 'average' ? '#faad14' : '#f05654'}`,
        borderRadius: '12px',
        padding: '16px',
        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.3)',
        zIndex: 1000,
        // 使用 CSS 控制显示/隐藏，避免组件频繁挂载/卸载
        display: visible ? 'block' : 'none',
        opacity: visible ? 1 : 0,
        transition: 'opacity 0.3s ease-out',
        // 移除可能导致问题的 animation 属性
        // animation: 'fadeInUp 0.3s ease-out',
      }}
    >
      {/* 简化的CSS样式 - 移除复杂动画避免 rc-motion 冲突 */}

      {/* 提示文字 */}
      <div
        className="feedback-prompt-text"
        style={{
          fontSize: '14px',
          fontWeight: 600,
          color: isDarkMode ? '#cccccc' : '#333333',
          marginBottom: '12px',
          textAlign: 'center',
        }}
      >
        {promptMessage}
      </div>

      {/* 专门为提示文字添加强制样式 - 使用最高优先级 */}
      <style>
        {`
          /* 暗黑模式提示文字样式 - 使用最强选择器 */
          ${
            isDarkMode
              ? `
            html body div#feedback-input-box div.feedback-prompt-text,
            html body #feedback-input-box .feedback-prompt-text,
            #feedback-input-box .feedback-prompt-text,
            .feedback-prompt-text {
              color: #cccccc !important;
            }
            
                         /* 覆盖可能的全局暗黑模式样式 */
             [data-theme="dark"] .feedback-prompt-text,
             .dark .feedback-prompt-text,
             html[data-theme="dark"] .feedback-prompt-text,
             body.dark .feedback-prompt-text,
             html body div#feedback-input-box div.feedback-prompt-text {
               color: #cccccc !important;
             }
             
             /* 字符计数颜色 */
             html body div#feedback-input-box div.feedback-char-count,
             html body #feedback-input-box .feedback-char-count,
             #feedback-input-box .feedback-char-count,
             .feedback-char-count {
               color: #cccccc !important;
             }
             
             /* 覆盖可能的全局暗黑模式样式 - 字符计数 */
             [data-theme="dark"] .feedback-char-count,
             .dark .feedback-char-count,
             html[data-theme="dark"] .feedback-char-count,
             body.dark .feedback-char-count,
             html body div#feedback-input-box div.feedback-char-count {
               color: #cccccc !important;
             }
          `
              : `
            .feedback-prompt-text {
              color: #333333 !important;
            }
          `
          }
        `}
      </style>

      {/* 输入框 */}
      <div style={{ position: 'relative' }}>
        <Input.TextArea
          ref={inputRef}
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          onKeyDown={handleKeyPress}
          placeholder="请输入您的建议..."
          rows={3}
          maxLength={200}
          style={{
            marginBottom: '12px',
            borderRadius: '8px',
            backgroundColor: isDarkMode ? '#1a1a1a' : '#ffffff',
            borderColor: feedbackType === 'average' ? '#faad14' : '#f05654',
            color: isDarkMode ? '#ffffff' : '#333333',
          }}
        />
      </div>

      {/* 全局强制样式覆盖 - 使用最高优先级 */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
          /* 使用最强选择器覆盖所有可能的样式 */
          ${
            isDarkMode
              ? `
            /* 强制设置提示文字颜色 */
            #feedback-input-box .feedback-prompt-text,
            html body #feedback-input-box .feedback-prompt-text,
            html body div#feedback-input-box .feedback-prompt-text {
              color: #cccccc !important;
            }
            
            #feedback-input-box textarea,
            #feedback-input-box .ant-input,
            html body #feedback-input-box textarea,
            html body #feedback-input-box .ant-input,
            html body div#feedback-input-box textarea,
            html body div#feedback-input-box .ant-input {
              color: #ffffff !important;
              background-color: #1a1a1a !important;
              border-color: ${feedbackType === 'average' ? '#faad14' : '#f05654'} !important;
            }
            
            #feedback-input-box textarea:focus,
            #feedback-input-box .ant-input:focus,
            html body #feedback-input-box textarea:focus,
            html body #feedback-input-box .ant-input:focus {
              color: #ffffff !important;
              background-color: #1a1a1a !important;
              border-color: ${feedbackType === 'average' ? '#faad14' : '#f05654'} !important;
              box-shadow: 0 0 0 2px ${feedbackType === 'average' ? 'rgba(250, 173, 20, 0.2)' : 'rgba(240, 86, 84, 0.2)'} !important;
            }
            
                         #feedback-input-box textarea::placeholder,
             #feedback-input-box .ant-input::placeholder,
             html body #feedback-input-box textarea::placeholder,
             html body #feedback-input-box .ant-input::placeholder {
               color: #cccccc !important;
               opacity: 1 !important;
             }
            
                         /* 使用属性选择器作为备用方案 */
             textarea[placeholder="请输入您的建议..."] {
               color: #ffffff !important;
               background-color: #1a1a1a !important;
             }
             
             /* 占位符文字颜色 */
             textarea[placeholder="请输入您的建议..."]::placeholder {
               color: #cccccc !important;
               opacity: 1 !important;
             }
          `
              : ''
          }
        `,
        }}
      />

      {/* 字符计数 */}
      <div
        className="feedback-char-count"
        style={{
          fontSize: '12px',
          color: isDarkMode ? '#cccccc' : '#999999',
          textAlign: 'right',
          marginBottom: '12px',
        }}
      >
        {comment.length}/200
      </div>

      {/* 专门为字符计数添加强制样式 - 使用最高优先级 */}
      <style>
        {`
          /* 字符计数样式 - 使用最强选择器 */
          ${
            isDarkMode
              ? `
            html body div#feedback-input-box div.feedback-char-count,
            html body #feedback-input-box .feedback-char-count,
            #feedback-input-box .feedback-char-count,
            .feedback-char-count {
              color: #cccccc !important;
            }
            
            /* 覆盖可能的全局暗黑模式样式 - 字符计数 */
            [data-theme="dark"] .feedback-char-count,
            .dark .feedback-char-count,
            html[data-theme="dark"] .feedback-char-count,
            body.dark .feedback-char-count,
            html body div#feedback-input-box div.feedback-char-count {
              color: #cccccc !important;
            }
          `
              : `
            .feedback-char-count {
              color: #999999 !important;
            }
          `
          }
        `}
      </style>

      {/* 按钮组 */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          gap: '8px',
        }}
      >
        <Button
          onClick={handleCancel}
          style={{
            flex: 1,
            borderRadius: '6px',
            backgroundColor: isDarkMode ? '#404040' : '#f5f5f5',
            borderColor: isDarkMode ? '#606060' : '#d9d9d9',
            color: isDarkMode ? '#ffffff' : '#333333',
          }}
        >
          取消
        </Button>
        <Button
          type="primary"
          onClick={handleSubmit}
          style={{
            flex: 1,
            borderRadius: '6px',
            backgroundColor: feedbackType === 'average' ? '#faad14' : '#f05654',
            borderColor: feedbackType === 'average' ? '#faad14' : '#f05654',
            color: '#ffffff', // 主按钮始终使用白色文字
          }}
        >
          提交反馈
        </Button>
      </div>

      {/* 最终的全局样式覆盖 - 确保在所有场景下都能正确显示 */}
      <style>
        {`
          /* 最强优先级的样式覆盖 - 针对用户上传图片后的场景 */
          ${
            isDarkMode
              ? `
            /* 覆盖所有可能的全局暗黑模式样式 */
            html[data-theme="dark"] body #emotionAnalysisPage #feedback-input-box .feedback-prompt-text,
            html[data-theme="dark"] body .feedback-wrapper #feedback-input-box .feedback-prompt-text,
            html[data-theme="dark"] body .feedbackContainer #feedback-input-box .feedback-prompt-text,
            html[data-theme="dark"] body .feedbackButtons #feedback-input-box .feedback-prompt-text,
            body[data-theme="dark"] #feedback-input-box .feedback-prompt-text,
            .dark #feedback-input-box .feedback-prompt-text,
            html.dark #feedback-input-box .feedback-prompt-text {
              color: #cccccc !important;
            }
            
            /* 输入框文字颜色 */
            html[data-theme="dark"] body #emotionAnalysisPage #feedback-input-box textarea,
            html[data-theme="dark"] body .feedback-wrapper #feedback-input-box textarea,
            html[data-theme="dark"] body .feedbackContainer #feedback-input-box textarea,
            html[data-theme="dark"] body .feedbackButtons #feedback-input-box textarea,
            body[data-theme="dark"] #feedback-input-box textarea,
            .dark #feedback-input-box textarea,
            html.dark #feedback-input-box textarea {
              color: #ffffff !important;
              background-color: #1a1a1a !important;
            }
            
            /* 占位符文字颜色 */
            html[data-theme="dark"] body #emotionAnalysisPage #feedback-input-box textarea::placeholder,
            html[data-theme="dark"] body .feedback-wrapper #feedback-input-box textarea::placeholder,
            html[data-theme="dark"] body .feedbackContainer #feedback-input-box textarea::placeholder,
            html[data-theme="dark"] body .feedbackButtons #feedback-input-box textarea::placeholder,
            body[data-theme="dark"] #feedback-input-box textarea::placeholder,
            .dark #feedback-input-box textarea::placeholder,
            html.dark #feedback-input-box textarea::placeholder {
              color: #cccccc !important;
              opacity: 0.7 !important;
            }
            
            /* 字符计数颜色 */
            html[data-theme="dark"] body #emotionAnalysisPage #feedback-input-box .feedback-char-count,
            html[data-theme="dark"] body .feedback-wrapper #feedback-input-box .feedback-char-count,
            html[data-theme="dark"] body .feedbackContainer #feedback-input-box .feedback-char-count,
            html[data-theme="dark"] body .feedbackButtons #feedback-input-box .feedback-char-count,
            body[data-theme="dark"] #feedback-input-box .feedback-char-count,
            .dark #feedback-input-box .feedback-char-count,
            html.dark #feedback-input-box .feedback-char-count {
              color: #cccccc !important;
            }
          `
              : ''
          }
        `}
      </style>
    </div>
  );
};

export default FeedbackInputBox;
