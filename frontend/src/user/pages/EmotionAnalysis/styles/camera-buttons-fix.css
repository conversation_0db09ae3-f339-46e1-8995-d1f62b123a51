/* 摄像头按钮样式修复 */

/* 确保按钮可点击 */
.camera-button {
  pointer-events: auto !important;
  cursor: pointer !important;
  z-index: 1000 !important;
  position: relative !important;
  touch-action: manipulation !important; /* 优化触摸操作 */
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important; /* 移除iOS点击高亮 */
  user-select: none !important; /* 防止文本选择 */
}

/* 确保按钮点击状态正确 */
.camera-button:active {
  opacity: 0.8 !important;
  transform: scale(0.98) !important;
  transition: all 0.1s !important;
}

/* 移动端样式优化 */
@media screen and (max-width: 768px) {
  .camera-button {
    width: 130px !important;
    height: 55px !important;
    padding: 0 10px !important;
  }
}

/* 小屏幕设备样式优化 */
@media screen and (max-width: 576px) {
  .camera-button {
    width: 120px !important;
    height: 50px !important;
    padding: 0 8px !important;
  }
}

/* 确保按钮文字在暗黑模式下可见 */
html[data-theme='dark'] .camera-button {
  color: white !important;
}

/* 确保按钮在所有浏览器中都能正确显示 */
.camera-button * {
  pointer-events: none; /* 防止子元素捕获点击事件 */
}

/* 修复Safari浏览器中的按钮问题 */
@supports (-webkit-touch-callout: none) {
  .camera-button {
    -webkit-appearance: none;
    -webkit-tap-highlight-color: transparent;
  }
}
