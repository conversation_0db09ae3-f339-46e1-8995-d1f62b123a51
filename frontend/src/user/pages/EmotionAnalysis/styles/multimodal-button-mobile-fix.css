/* 多模态组件执行分析按钮移动端显示修复 */

/* 移动端多模态分析按钮优化 */
@media screen and (max-width: 576px) {
  /* 多模态分析容器标题区域优化 */
  .multimodal-analysis-container > div:first-child {
    display: flex !important;
    flex-direction: column !important; /* 移动端改为垂直布局 */
    align-items: stretch !important; /* 拉伸对齐 */
    gap: 12px !important;
    margin-bottom: 15px !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* 标题文本区域 */
  .multimodal-analysis-container > div:first-child > div:first-child {
    flex: none !important;
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 8px !important;
    text-align: center !important; /* 移动端居中显示 */
  }

  /* 执行分析按钮容器优化 */
  .multimodal-analysis-container .ant-btn {
    width: 100% !important; /* 移动端按钮占满宽度 */
    max-width: 100% !important;
    min-width: 0 !important;
    height: 48px !important; /* 减小高度，更适合移动端 */
    border-radius: 24px !important; /* 相应调整圆角 */
    font-size: 16px !important; /* 增大字体以适应新高度 */
    line-height: 1.2 !important;
    padding: 8px 16px !important;
    box-sizing: border-box !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24) !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4) !important;
  }

  /* 按钮内容区域 */
  .multimodal-analysis-container .ant-btn > div {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    width: 100% !important;
    flex-wrap: nowrap !important;
  }

  /* 图标优化 */
  .multimodal-analysis-container .ant-btn .anticon {
    font-size: 18px !important;
    flex-shrink: 0 !important;
  }

  /* 按钮文字区域 */
  .multimodal-analysis-container .ant-btn > div > div:last-child {
    display: flex !important;
    flex-direction: row !important; /* 移动端改为水平布局 */
    align-items: center !important;
    gap: 6px !important;
    line-height: 1.2 !important;
  }

  /* 中文文字 */
  .multimodal-analysis-container .ant-btn > div > div:last-child > span:first-child {
    font-size: 16px !important;
    font-weight: 600 !important;
    white-space: nowrap !important;
  }

  /* 英文文字 */
  .multimodal-analysis-container .ant-btn > div > div:last-child > span:last-child {
    font-size: 12px !important;
    opacity: 0.9 !important;
    white-space: nowrap !important;
    margin-left: 4px !important;
  }

  /* 加载状态下的按钮 */
  .multimodal-analysis-container .ant-btn.ant-btn-loading {
    background: linear-gradient(45deg, #ff9999, #ff8855) !important;
  }

  /* 禁用状态下的按钮 */
  .multimodal-analysis-container .ant-btn[disabled] {
    background: #d9d9d9 !important;
    color: #00000040 !important;
    box-shadow: none !important;
  }

  /* 暗色模式下的按钮优化 */
  html[data-theme='dark'] .multimodal-analysis-container .ant-btn[disabled] {
    background: #434343 !important;
    color: #ffffff40 !important;
  }
}

/* 超小屏幕优化 (320px及以下) */
@media screen and (max-width: 320px) {
  .multimodal-analysis-container .ant-btn {
    height: 44px !important;
    border-radius: 22px !important;
    font-size: 15px !important;
    padding: 6px 12px !important;
  }

  .multimodal-analysis-container .ant-btn .anticon {
    font-size: 16px !important;
  }

  .multimodal-analysis-container .ant-btn > div > div:last-child > span:first-child {
    font-size: 15px !important;
  }

  .multimodal-analysis-container .ant-btn > div > div:last-child > span:last-child {
    font-size: 11px !important;
  }
}

/* 中等移动端屏幕优化 (321px - 375px) */
@media screen and (min-width: 321px) and (max-width: 375px) {
  .multimodal-analysis-container .ant-btn {
    height: 46px !important;
    border-radius: 23px !important;
    font-size: 15px !important;
  }

  .multimodal-analysis-container .ant-btn .anticon {
    font-size: 17px !important;
  }

  .multimodal-analysis-container .ant-btn > div > div:last-child > span:first-child {
    font-size: 15px !important;
  }

  .multimodal-analysis-container .ant-btn > div > div:last-child > span:last-child {
    font-size: 11px !important;
  }
}

/* 微信浏览器特殊适配 */
.wx-browser .multimodal-analysis-container .ant-btn,
html.wechat-browser .multimodal-analysis-container .ant-btn {
  height: 52px !important; /* 微信浏览器中使用稍大的高度 */
  font-size: 16px !important;
  line-height: 1.3 !important;
}

.wx-browser .multimodal-analysis-container .ant-btn > div > div:last-child,
html.wechat-browser .multimodal-analysis-container .ant-btn > div > div:last-child {
  flex-direction: column !important; /* 微信浏览器中恢复垂直布局 */
  gap: 2px !important;
}

.wx-browser .multimodal-analysis-container .ant-btn > div > div:last-child > span:last-child,
html.wechat-browser
  .multimodal-analysis-container
  .ant-btn
  > div
  > div:last-child
  > span:last-child {
  margin-left: 0 !important;
}

/* Safari 移动端特殊适配 */
@supports (-webkit-touch-callout: none) {
  @media screen and (max-width: 576px) {
    .multimodal-analysis-container .ant-btn {
      -webkit-appearance: none !important;
      -webkit-tap-highlight-color: transparent !important;
      transform: translateZ(0) !important; /* 启用硬件加速 */
    }

    .multimodal-analysis-container .ant-btn > div > div:last-child > span {
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
    }
  }
}

/* 横屏模式下的优化 */
@media screen and (max-height: 500px) and (orientation: landscape) and (max-width: 900px) {
  .multimodal-analysis-container > div:first-child {
    flex-direction: row !important; /* 横屏时恢复水平布局 */
    align-items: center !important;
  }

  .multimodal-analysis-container > div:first-child > div:first-child {
    flex: 1 !important;
    margin-right: 10px !important;
    margin-bottom: 0 !important;
    text-align: left !important;
  }

  .multimodal-analysis-container .ant-btn {
    width: auto !important;
    min-width: 160px !important;
    max-width: 200px !important;
  }
}

/* 确保按钮文字不被截断 */
@media screen and (max-width: 576px) {
  .multimodal-analysis-container .ant-btn * {
    box-sizing: border-box !important;
    overflow: visible !important;
  }

  /* 防止按钮内容溢出 */
  .multimodal-analysis-container .ant-btn > div {
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  /* 确保图标和文字都能显示 */
  .multimodal-analysis-container .ant-btn .anticon,
  .multimodal-analysis-container .ant-btn > div > div:last-child > span {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}
