/* 原生按钮样式 - 替代Antd Button组件 */
.native-button {
  /* 重置默认样式 */
  border: none;
  outline: none;
  margin: 0;
  padding: 0;
  background: none;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;

  /* Antd Button样式 */
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  position: relative;

  /* 防止双击选中文本 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 触摸优化 */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.native-button:hover {
  opacity: 0.8;
}

.native-button:active {
  transform: translateY(1px);
}

.native-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 禁用状态 */
.native-button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.native-button:disabled:hover {
  opacity: 0.6;
  transform: none;
}

/* 确保按钮内容不被选中 */
.native-button * {
  pointer-events: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 微信浏览器特殊优化 */
@media screen and (max-width: 768px) {
  .native-button {
    /* 增加触摸区域 */
    min-height: 44px;
    min-width: 44px;

    /* 优化触摸响应 */
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    -webkit-touch-callout: none;
  }
}
