// 检测是否是微信浏览器并添加类名
(function () {
  function isWechatBrowser() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.indexOf('micromessenger') !== -1;
  }

  // 如果是微信浏览器，给 body 添加 wx-browser 类名
  if (isWechatBrowser()) {
    document.body.classList.add('wx-browser');
    console.log('微信浏览器检测：已添加 wx-browser 类名');
  }

  // 确保在 DOM 加载完成后也执行一次检查
  document.addEventListener('DOMContentLoaded', function () {
    if (isWechatBrowser() && !document.body.classList.contains('wx-browser')) {
      document.body.classList.add('wx-browser');
      console.log('DOM加载后微信浏览器检测：已添加 wx-browser 类名');
    }
  });
})();

// 导出检测函数，以便其他地方使用
export function isWechatBrowser() {
  const ua = navigator.userAgent.toLowerCase();
  return ua.indexOf('micromessenger') !== -1;
}
