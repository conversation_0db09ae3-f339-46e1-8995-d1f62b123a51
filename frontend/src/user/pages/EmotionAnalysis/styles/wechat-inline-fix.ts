// 微信浏览器内联样式修复
// 这个文件包含专门用于微信浏览器的内联样式

import React from 'react';

// 检测是否是微信浏览器
export const isWechatBrowser = (): boolean => {
  if (typeof navigator !== 'undefined') {
    return /MicroMessenger/i.test(navigator.userAgent);
  }
  return false;
};

// 检测是否是移动设备
export const isMobileDevice = (): boolean => {
  if (typeof navigator !== 'undefined') {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent,
    );
  }
  return false;
};

// 属性框容器样式 - 简化版本
export const getFaceInfoContainerStyle = (): React.CSSProperties | null => {
  const isWechat = isWechatBrowser();
  const isMobile = isMobileDevice();

  if (isWechat && isMobile) {
    return {
      display: 'flex',
      flexWrap: 'nowrap',
      gap: '2px',
    };
  }

  return null; // 返回null表示使用默认样式
};

// 属性框样式 - 简化版本
export const getFaceInfoItemStyle = (): React.CSSProperties | null => {
  const isWechat = isWechatBrowser();
  const isMobile = isMobileDevice();

  if (isWechat && isMobile) {
    return {
      textAlign: 'center',
      flex: '1',
    };
  }

  return null; // 返回null表示使用默认样式
};

// 属性值容器样式
export const getAttributeValueStyle = (color: string): React.CSSProperties | null => {
  const isWechat = isWechatBrowser();
  const isMobile = isMobileDevice();

  if (isWechat && isMobile) {
    return {
      color: color,
      fontSize: '12px',
      fontWeight: 'bold',
      marginTop: '2px',
      backgroundColor: `${color}20`,
      padding: '2px 4px',
      borderRadius: '4px',
      width: '100%',
      textAlign: 'center',
      display: 'block',
      lineHeight: 1.2,
      boxSizing: 'border-box',
      borderLeft: `3px solid ${color}`,
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    };
  }

  return null; // 返回null表示使用默认样式
};

// 环形图容器样式
export const getCircleChartContainerStyle = (): React.CSSProperties | null => {
  const isWechat = isWechatBrowser();
  const isMobile = isMobileDevice();

  if (isWechat && isMobile) {
    return {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      margin: '15px 0',
      position: 'relative',
      height: '180px',
      overflow: 'hidden',
    };
  }

  return null; // 返回null表示使用默认样式
};

// 环形图文字容器样式
export const getCircleTextContainerStyle = (isPrimary = true): React.CSSProperties | null => {
  const isWechat = isWechatBrowser();
  const isMobile = isMobileDevice();

  if (isWechat && isMobile) {
    return {
      position: 'absolute',
      top: '0',
      left: '0',
      right: '0',
      bottom: '0',
      textAlign: 'center',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      overflow: 'hidden',
      backgroundColor: 'transparent',
      pointerEvents: 'none',
      padding: 0,
      margin: 0,
    };
  }

  return null; // 返回null表示使用默认样式
};

// 环形图百分比样式
export const getCirclePercentStyle = (
  color: string,
  isDarkMode: boolean,
): React.CSSProperties | null => {
  const isWechat = isWechatBrowser();
  const isMobile = isMobileDevice();

  if (isWechat && isMobile) {
    return {
      fontSize: '28px',
      fontWeight: 'bold',
      color: color,
      textShadow: isDarkMode ? '0 1px 3px rgba(0,0,0,0.5)' : 'none',
      width: '100%',
      textAlign: 'center',
      marginBottom: '4px',
      lineHeight: '1.2',
    };
  }

  return null; // 返回null表示使用默认样式
};

// 环形图情绪名称样式
export const getCircleEmotionNameStyle = (
  isDarkMode: boolean,
  isPrimary = true,
): React.CSSProperties | null => {
  const isWechat = isWechatBrowser();
  const isMobile = isMobileDevice();

  if (isWechat && isMobile) {
    return {
      fontSize: isPrimary ? '16px' : '15px',
      color: isDarkMode ? '#ffffff' : '#333333',
      fontWeight: '600',
      textShadow: isDarkMode ? '0 1px 2px rgba(0,0,0,0.5)' : 'none',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      width: '100%',
      textAlign: 'center',
      marginBottom: '2px',
      lineHeight: '1.2',
    };
  }

  return null; // 返回null表示使用默认样式
};

// 环形图情绪英文名样式
export const getCircleEmotionEnglishStyle = (
  color: string,
  isDarkMode: boolean,
  isPrimary = true,
): React.CSSProperties | null => {
  const isWechat = isWechatBrowser();
  const isMobile = isMobileDevice();

  if (isWechat && isMobile) {
    return {
      fontSize: isPrimary ? '13px' : '12px',
      color: color,
      opacity: 0.9,
      fontWeight: '500',
      textShadow: isDarkMode ? '0 1px 1px rgba(0,0,0,0.3)' : 'none',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      width: '100%',
      textAlign: 'center',
      lineHeight: '1.2',
    };
  }

  return null; // 返回null表示使用默认样式
};
