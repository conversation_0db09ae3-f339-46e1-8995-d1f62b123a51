/* 移动端多模态组件显示修复 */

/* 确保多模态组件在移动端正确显示 */
@media screen and (max-width: 576px) {
  /* 多模态分析包装器 */
  .multimodal-analysis-wrapper {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    width: 100% !important;
    max-width: 100% !important;
    min-width: 0 !important;
    box-sizing: border-box !important;
    overflow: visible !important;
    margin: 20px 0 !important;
    padding: 0 !important;
    z-index: 1 !important;
  }

  /* 多模态分析容器 */
  .multimodal-analysis-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    width: 100% !important;
    max-width: 100% !important;
    min-width: 0 !important;
    box-sizing: border-box !important;
    overflow: visible !important;
    margin: 0 !important;
    padding: 15px !important;
    border-radius: 8px !important;
    background-color: #f5f5f5 !important;
    border: 1px solid #e8e8e8 !important;
  }

  /* 暗色模式下的多模态分析容器 */
  html[data-theme='dark'] .multimodal-analysis-container {
    background-color: #1f1f1f !important;
    border-color: #333 !important;
  }

  /* 多模态分析标题区域 */
  .multimodal-analysis-container > div:first-child {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 10px !important;
    flex-wrap: wrap !important;
    gap: 10px !important;
  }

  /* 多模态分析按钮 */
  .multimodal-analysis-container .ant-btn {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    flex-shrink: 0 !important;
    min-width: 120px !important;
    max-width: 200px !important;
  }

  /* 多模态分析结果区域 */
  .multimodal-analysis-container .multimodal-result {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    width: 100% !important;
    max-width: 100% !important;
    min-width: 0 !important;
    box-sizing: border-box !important;
    overflow-y: auto !important;
    max-height: none !important;
    min-height: 100px !important;
    padding: 10px !important;
    background-color: #fff !important;
    border-radius: 4px !important;
    border: 1px solid #e8e8e8 !important;
  }

  /* 暗色模式下的结果区域 */
  html[data-theme='dark'] .multimodal-analysis-container .multimodal-result {
    background-color: #141414 !important;
    border-color: #333 !important;
    color: #fff !important;
  }

  /* 多模态分析结果文本 */
  .multimodal-analysis-container .multimodal-result-text {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    max-width: 100% !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    white-space: pre-wrap !important;
    line-height: 1.6 !important;
    font-size: 14px !important;
    color: inherit !important;
  }

  /* 确保分隔线正确显示 */
  .multimodal-analysis-container .ant-divider {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin: 10px 0 !important;
  }

  /* 加载状态 */
  .multimodal-analysis-container .ant-spin {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* 超小屏幕优化 */
@media screen and (max-width: 375px) {
  .multimodal-analysis-container {
    padding: 12px !important;
  }

  .multimodal-analysis-container .multimodal-result {
    padding: 8px !important;
    font-size: 13px !important;
  }

  .multimodal-analysis-container .multimodal-result-text {
    font-size: 13px !important;
  }
}

/* 微信浏览器特殊优化 */
@media screen and (max-width: 576px) {
  /* 微信浏览器中确保组件可见 */
  html.wechat-browser .multimodal-analysis-wrapper,
  body.wx-browser .multimodal-analysis-wrapper {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  html.wechat-browser .multimodal-analysis-container,
  body.wx-browser .multimodal-analysis-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* 强制覆盖任何可能隐藏组件的样式 */
@media screen and (max-width: 576px) {
  [class*='multimodal'] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  [class*='multimodal-analysis'] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}
