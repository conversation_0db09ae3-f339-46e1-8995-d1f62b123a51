/* 微信浏览器版权信息专项修复 - 确保英文在一行显示 */

/* 微信浏览器检测 */
@media screen and (max-width: 768px) {
  /* 版权信息容器 */
  .copyright-info {
    padding: 10px 5px !important; /* 减少内边距 */
    margin-top: 20px !important;
  }

  /* 中文版权信息 */
  .copyright-info > div:first-child {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    font-size: 12px !important; /* 稍微减小中文字体 */
    padding: 0 8px !important;
    line-height: 1.4 !important;
  }

  /* 英文版权信息 - 大幅缩小字体确保一行显示 */
  .copyright-info > div:last-child {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    font-size: 9px !important; /* 从12px减小到9px */
    padding: 0 8px !important;
    margin-top: 3px !important; /* 减少上边距 */
    line-height: 1.3 !important;
    letter-spacing: -0.2px !important; /* 减少字母间距 */
  }
}

/* 更小屏幕的额外优化 */
@media screen and (max-width: 576px) {
  .copyright-info > div:first-child {
    font-size: 11px !important;
    padding: 0 5px !important;
  }

  .copyright-info > div:last-child {
    font-size: 8px !important; /* 进一步缩小到8px */
    padding: 0 5px !important;
    letter-spacing: -0.3px !important;
  }
}

/* 微信浏览器特定优化 */
@media screen and (max-width: 414px) {
  /* iPhone 6/7/8 Plus 及类似尺寸 */
  .copyright-info > div:last-child {
    font-size: 7px !important; /* 最小屏幕使用7px */
    letter-spacing: -0.4px !important;
  }
}

/* 确保在暗黑模式下也正确显示 */
html[data-theme='dark'] .copyright-info > div:first-child,
html[data-theme='dark'] .copyright-info > div:last-child {
  color: var(--text-color-secondary) !important;
}

/* 高优先级选择器确保样式生效 */
body .copyright-info > div:last-child {
  font-size: 9px !important;
}

@media screen and (max-width: 576px) {
  body .copyright-info > div:last-child {
    font-size: 8px !important;
  }
}

@media screen and (max-width: 414px) {
  body .copyright-info > div:last-child {
    font-size: 7px !important;
  }
}
