/* 图片显示修复样式 */

/* 通用图片容器样式 */
.image-frame-container {
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.image-frame-container > div:last-child {
  flex: 1 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  overflow: hidden !important;
  background-color: #f5f5f5 !important;
}

/* Ant Design Image 组件样式修复 */
.image-frame-container .ant-image {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.image-frame-container .ant-image-img {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  object-position: center center !important;
  background-color: transparent !important;
}

/* 桌面端样式 */
@media screen and (min-width: 769px) {
  .image-frame-container {
    height: 600px !important; /* 确保桌面端有足够高度 */
  }

  /* 确保图片在桌面端完整显示 */
  .image-frame-container .ant-image-img {
    max-width: 100% !important;
    max-height: 100% !important;
  }
}

/* 平板端样式 */
@media screen and (min-width: 577px) and (max-width: 768px) {
  .image-frame-container {
    height: 500px !important;
  }
}

/* 移动端样式 */
@media screen and (max-width: 576px) {
  .image-frame-container {
    height: auto !important;
    min-height: 0 !important;
  }

  /* 移动端图片样式 */
  .image-frame-container .ant-image-img {
    max-width: 100% !important;
    max-height: 100% !important;
  }

  /* 移动端专属：原始图片和特征点图片满框显示 */
  .emotion-image {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    margin: 0 !important;
    border-radius: 0;
    box-shadow: none;
    background: #fff;
    display: block !important;
    object-fit: contain !important;
  }
}

/* 微信浏览器特殊处理 */
html.wechat-browser .image-frame-container,
body.wx-browser .image-frame-container {
  background-color: #f5f5f5 !important;
}

html.wechat-browser .image-frame-container .ant-image-img,
body.wx-browser .image-frame-container .ant-image-img {
  object-fit: contain !important;
  background-color: transparent !important;
}

/* 加载状态样式 */
.image-frame-container .ant-spin-container {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 预览模式样式 */
.ant-image-preview-img {
  max-width: 90% !important;
  max-height: 90% !important;
  object-fit: contain !important;
}
