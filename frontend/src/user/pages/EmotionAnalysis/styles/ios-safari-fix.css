/* iOS Safari 特定的修复样式 */

/* 为 iOS Safari 添加特殊样式 */
.ios-safari .emotion-item {
  /* 确保元素不会被压缩 */
  min-height: 60px !important;
  /* 防止布局抖动 */
  transform: translateZ(0);
  /* 确保元素可见 */
  opacity: 1 !important;
  /* 使用更稳定的布局方式 */
  display: block !important;
  position: relative !important;
  margin-bottom: 10px !important;
  /* 确保边框可见 */
  border-width: 0 0 0 3px !important;
  border-style: solid !important;
}

/* 确保进度条在 iOS Safari 中可见 */
.ios-safari .emotion-item .progress-bar-container {
  display: block !important;
  height: 5px !important;
  position: relative !important;
  margin-top: 5px !important;
  margin-bottom: 5px !important;
  overflow: visible !important;
  background-color: #e8e8e8;
  border-radius: 3px;
}

.ios-safari .emotion-item .progress-bar-fill {
  position: absolute !important;
  height: 100% !important;
  left: 0 !important;
  top: 0 !important;
  border-radius: 3px;
}

/* 确保文本不会溢出或被截断 */
.ios-safari .emotion-item .emotion-label,
.ios-safari .emotion-item .emotion-percentage {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 确保文本容器有足够的空间 */
.ios-safari .emotion-item .emotion-text-container {
  display: flex !important;
  justify-content: space-between !important;
  width: 100% !important;
  min-height: 24px !important;
}

/* 暗黑模式适配 */
.ios-safari .dark-mode .emotion-item .progress-bar-container {
  background-color: #555555;
}
