/* 微信浏览器反馈按钮专项修复 - 解决高度不够和文字溢出问题 */

/* 微信浏览器检测 */
@media screen and (max-width: 768px) {
  /* 微信浏览器中的反馈按钮容器 */
  .feedbackButtons {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    justify-content: space-between !important;
    width: 100% !important;
    gap: 8px !important;
    margin: 20px 0 !important;
  }

  /* 微信浏览器中的反馈按钮 - 增加高度解决文字溢出 */
  .feedbackButtons .feedback-button {
    width: 32% !important;
    min-width: 0 !important;
    max-width: 32% !important;
    height: 70px !important; /* 从50px增加到70px */
    padding: 8px 4px !important; /* 增加垂直内边距 */
    flex: 1 1 auto !important;
    margin: 0 !important;
    border-radius: 25px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* 确保按钮内容容器有足够空间 */
  .feedbackButtons .feedback-button > div {
    width: 100% !important;
    height: 100% !important;
    padding: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 2px !important; /* 增加中英文之间的间距 */
  }

  /* 中文文字样式优化 */
  .feedbackButtons .feedback-button span:first-child {
    font-size: 15px !important; /* 稍微减小字体避免溢出 */
    line-height: 1.2 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: block !important;
    margin-bottom: 2px !important;
    font-weight: 600 !important;
  }

  /* 英文文字样式优化 */
  .feedbackButtons .feedback-button span:last-child {
    font-size: 11px !important; /* 稍微减小英文字体 */
    line-height: 1.1 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: block !important;
    opacity: 0.9 !important;
    font-weight: 500 !important;
  }

  /* 确保所有按钮文字颜色正确 */
  .feedbackButtons .feedback-button,
  .feedbackButtons .feedback-button * {
    color: #333333 !important;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5) !important;
  }

  /* 好评按钮特定样式 */
  .feedbackButtons .goodButton {
    background-color: #52c41a !important;
    border-color: #52c41a !important;
  }

  /* 一般按钮特定样式 */
  .feedbackButtons .averageButton {
    background-color: #faad14 !important;
    border-color: #faad14 !important;
  }

  /* 不好按钮特定样式 */
  .feedbackButtons .badButton {
    background-color: #f05654 !important;
    border-color: #f05654 !important;
  }
}

/* 更小屏幕的额外优化 */
@media screen and (max-width: 576px) {
  .feedbackButtons .feedback-button {
    height: 65px !important; /* 小屏幕稍微减少高度 */
    padding: 6px 2px !important;
  }

  .feedbackButtons .feedback-button span:first-child {
    font-size: 14px !important;
  }

  .feedbackButtons .feedback-button span:last-child {
    font-size: 10px !important;
  }
}

/* 确保在暗黑模式下也正确显示 */
html[data-theme='dark'] .feedbackButtons .feedback-button,
html[data-theme='dark'] .feedbackButtons .feedback-button * {
  color: #333333 !important;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5) !important;
}

/* 高优先级选择器确保样式生效 */
body .feedbackButtons .feedback-button {
  height: 70px !important;
}

@media screen and (max-width: 576px) {
  body .feedbackButtons .feedback-button {
    height: 65px !important;
  }
}

/* 微信浏览器专用样式 - 防止页面死掉 */
.wechat-feedback-button {
  /* 禁用所有可能导致问题的CSS属性 */
  transition: none !important;
  transform: none !important;
  box-shadow: none !important;
  animation: none !important;

  /* 确保按钮可点击 */
  pointer-events: auto !important;
  cursor: pointer !important;

  /* 防止事件冲突 */
  user-select: none !important;
  -webkit-user-select: none !important;
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1) !important;

  /* 确保按钮在微信浏览器中有足够的点击区域 */
  min-height: 60px !important;
  min-width: 80px !important;

  /* 优化触摸响应 */
  touch-action: manipulation !important;
}

/* 微信浏览器按钮的所有状态都使用相同样式，防止状态切换导致问题 */
.wechat-feedback-button:hover,
.wechat-feedback-button:active,
.wechat-feedback-button:focus,
.wechat-feedback-button:visited {
  transition: none !important;
  transform: none !important;
  box-shadow: none !important;
  animation: none !important;
  background-color: inherit !important;
  border-color: inherit !important;
  outline: none !important;
}

/* 微信浏览器中禁用所有伪元素，防止渲染问题 */
.wechat-feedback-button::before,
.wechat-feedback-button::after {
  display: none !important;
  content: none !important;
}

/* 微信浏览器中的按钮文字保护 - 移除pointer-events阻止，允许点击事件传递 */
.wechat-feedback-button span {
  user-select: none !important;
  -webkit-user-select: none !important;
}

/* 强制微信浏览器使用硬件加速，提高性能 */
.wechat-feedback-button {
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;
  will-change: auto !important;
}

/* 微信浏览器专用的容器样式 */
@media screen and (max-width: 768px) {
  .feedbackButtons .wechat-feedback-button {
    /* 确保在微信浏览器中按钮有足够的点击区域 */
    min-height: 70px !important;
    touch-action: manipulation !important;

    /* 防止双击缩放 */
    -ms-touch-action: manipulation !important;

    /* 优化触摸响应 */
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1) !important;
  }
}

/* 微信浏览器中的按钮内容保护 - 移除pointer-events: none，允许点击事件 */
.wechat-feedback-button > div,
.wechat-feedback-button > div > span {
  user-select: none !important;
  -webkit-user-select: none !important;
  -webkit-touch-callout: none !important;
}

/* 确保微信浏览器中按钮的z-index正确 */
.wechat-feedback-button {
  position: relative !important;
  z-index: 1 !important;
}

/* 微信浏览器性能优化 */
.feedbackButtons {
  /* 启用硬件加速 */
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;

  /* 优化渲染性能 */
  contain: layout style paint !important;

  /* 防止重排 */
  will-change: auto !important;
}
