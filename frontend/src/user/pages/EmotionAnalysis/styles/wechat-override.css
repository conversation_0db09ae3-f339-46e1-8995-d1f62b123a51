/**
 * 微信浏览器样式覆盖
 * 这个文件包含专门用于微信浏览器的样式，使用!important确保覆盖默认样式
 */

/* 微信浏览器标识类由wechat-adapter.js添加 */
html.wechat-browser {
  -webkit-text-size-adjust: 100% !important;
}

/* 全局样式 */
html.wechat-browser body {
  -webkit-overflow-scrolling: touch !important;
  overflow-x: hidden !important;
  max-width: 100vw !important;
  position: relative !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 微信浏览器中的情绪分析容器 */
html.wechat-browser .emotionAnalysisContainer,
body.wx-browser .emotionAnalysisContainer {
  border-radius: 8px !important; /* 恢复四周圆角 */
  margin-top: 10px !important; /* 添加顶部间距 */
  border-top: 1px solid var(--border-color) !important; /* 恢复顶部边框 */
}

/* 图片框容器样式修复 */
html.wechat-browser .image-frame-container,
body.wx-browser .image-frame-container {
  height: auto !important;
  min-height: 350px !important;
  max-height: none !important;
  aspect-ratio: 1/1 !important; /* 保持1:1的宽高比 */
  box-sizing: border-box !important;
}

/* 图片样式修复 */
html.wechat-browser .wechat-image-fix,
body.wx-browser .wechat-image-fix {
  max-width: 100% !important;
  max-height: 100% !important; /* 使用100%最大高度 */
  width: auto !important; /* 自动宽度 */
  height: auto !important; /* 自动高度 */
  object-fit: contain !important; /* 使用contain确保图片完整显示 */
  margin: 0 auto !important;
  display: block !important;
  aspect-ratio: auto !important; /* 保持原始宽高比 */
}

/* 图片容器修复 */
html.wechat-browser .ant-image,
body.wx-browser .ant-image {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  overflow: hidden !important;
  background-color: transparent !important; /* 确保背景透明 */
  min-height: 300px !important; /* 确保容器有足够的高度 */
}

/* 移除图片框黑边 */
html.wechat-browser .image-frame-container,
body.wx-browser .image-frame-container,
html.wechat-browser .ant-image-img,
body.wx-browser .ant-image-img {
  background-color: transparent !important;
}

/* 桌面端样式 - 确保图片完整显示 */
@media screen and (min-width: 769px) {
  .image-frame-container {
    min-height: 600px !important; /* 确保桌面端有足够高度 */
  }

  .image-frame-container .ant-image,
  .image-frame-container .ant-image-img {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    object-position: center center !important;
  }
}

/* 移动端样式 */
@media screen and (max-width: 576px) {
  /* 微信浏览器中的图片框架修复 */
  html.wechat-browser .image-frame-container,
  body.wx-browser .image-frame-container {
    min-height: 500px !important; /* 增加最小高度到500px */
    height: auto !important;
    margin-bottom: 10px !important;
  }

  /* 微信浏览器中的图片修复 */
  html.wechat-browser .wechat-image-fix,
  body.wx-browser .wechat-image-fix {
    max-height: 100% !important; /* 使用100%最大高度 */
    height: auto !important; /* 自动高度 */
    object-fit: contain !important; /* 使用contain确保图片完整显示 */
  }

  /* 属性框容器 */
  html.wechat-browser .faceInfo {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    width: 100% !important;
    gap: 2px !important;
    padding: 0 !important;
    margin: 10px 0 !important;
    overflow: visible !important;
  }

  /* 属性框 */
  html.wechat-browser .faceInfo > div {
    flex: 1 1 25% !important;
    min-width: 0 !important;
    max-width: 25% !important;
    padding: 0 1px !important;
    box-sizing: border-box !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
  }

  /* 属性标题 */
  html.wechat-browser .faceInfo > div > div:first-child {
    font-size: 12px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    width: 100% !important;
    text-align: center !important;
  }

  /* 属性英文标题 */
  html.wechat-browser .faceInfo > div > div:nth-child(2) {
    font-size: 10px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    width: 100% !important;
    text-align: center !important;
    margin-top: 0 !important;
  }

  /* 属性值容器 */
  html.wechat-browser .faceInfo > div > div:nth-child(3) {
    width: 100% !important;
    max-width: 100% !important;
    padding: 2px 3px !important;
    font-size: 12px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    text-align: center !important;
    box-sizing: border-box !important;
  }

  /* 环形图容器 */
  html.wechat-browser div[style*="height: '180px'"],
  html.wechat-browser div[style*="height: '160px'"] {
    height: 200px !important; /* 增加高度 */
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin: 15px 0 !important;
    position: relative !important;
    overflow: visible !important;
    width: 100% !important;
    box-sizing: border-box !important;
    padding: 0 !important;
  }

  /* 主要情绪环形图容器 */
  html.wechat-browser div[style*="height: '180px'"] > div:first-child,
  html.wechat-browser div[style*="height: '160px'"] > div:first-child {
    flex: 0 0 50% !important; /* 调整为平均分配 */
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    position: relative !important;
    height: 100% !important;
    width: 50% !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* 次要情绪环形图容器 */
  html.wechat-browser div[style*="height: '180px'"] > div:last-child,
  html.wechat-browser div[style*="height: '160px'"] > div:last-child {
    flex: 0 0 50% !important; /* 调整为平均分配 */
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    position: relative !important;
    height: 100% !important;
    width: 50% !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* 环形图 */
  html.wechat-browser .ant-progress-circle {
    width: 140px !important;
    height: 140px !important;
    transform: scale(1) !important; /* 确保不被缩放 */
    transform-origin: center center !important;
    position: relative !important;
    z-index: 1 !important;
    overflow: visible !important;
  }

  /* 次要情绪环形图 */
  html.wechat-browser div[style*="height: '180px'"] > div:last-child .ant-progress-circle,
  html.wechat-browser div[style*="height: '160px'"] > div:last-child .ant-progress-circle {
    width: 120px !important;
    height: 120px !important;
    transform: scale(1) !important; /* 确保不被缩放 */
    transform-origin: center center !important;
    overflow: visible !important;
  }

  /* 修复次要情绪环形图的圆圈 */
  html.wechat-browser div[style*="height: '180px'"] > div:last-child .ant-progress-circle circle,
  html.wechat-browser div[style*="height: '160px'"] > div:last-child .ant-progress-circle circle {
    stroke-width: 6 !important;
    r: 47 !important; /* 减小半径 */
  }

  /* 修复微信浏览器中的SVG渲染问题 */
  html.wechat-browser .ant-progress-circle svg {
    width: 100% !important;
    height: 100% !important;
    display: block !important;
    position: relative !important;
    z-index: 1 !important;
    overflow: visible !important;
  }

  /* 修复微信浏览器中的环形图路径 */
  html.wechat-browser .ant-progress-circle path {
    stroke-width: 6 !important; /* 确保路径宽度一致 */
    transform-origin: center center !important;
    vector-effect: non-scaling-stroke !important; /* 防止缩放影响线条宽度 */
  }

  /* 修复次要情绪环形图的路径 */
  html.wechat-browser div[style*="height: '180px'"] > div:last-child .ant-progress-circle path,
  html.wechat-browser div[style*="height: '160px'"] > div:last-child .ant-progress-circle path {
    stroke-width: 6 !important;
    d: path('M 60,60 m 0,-47 a 47,47 0 1 1 0,94 a 47,47 0 1 1 0,-94') !important; /* 使用绝对路径 */
  }

  /* 环形图中心文字容器 */
  html.wechat-browser
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"] {
    width: 100px !important;
    height: 100px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    overflow: hidden !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    text-align: center !important;
    z-index: 2 !important; /* 确保文字在SVG上方 */
    background-color: transparent !important;
    pointer-events: none !important; /* 防止文字阻挡点击事件 */
  }

  /* 次要情绪环形图中心文字容器 */
  html.wechat-browser
    div[style*="height: '180px'"]
    > div:last-child
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"],
  html.wechat-browser
    div[style*="height: '160px'"]
    > div:last-child
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"] {
    width: 90px !important;
    height: 90px !important;
    z-index: 2 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    overflow: hidden !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    text-align: center !important;
    background-color: transparent !important;
    pointer-events: none !important;
  }

  /* 修复微信浏览器中的绝对定位问题 */
  html.wechat-browser div[style*="position: 'relative'"] {
    position: relative !important;
    transform: none !important;
  }

  /* 环形图中心百分比 */
  html.wechat-browser
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"]
    > div:first-child {
    font-size: 28px !important;
    line-height: 1.2 !important;
    margin-bottom: 2px !important;
    font-weight: bold !important;
    width: auto !important;
    height: auto !important;
    display: block !important;
    text-shadow: 0 0 2px rgba(255, 255, 255, 0.5) !important; /* 添加文字阴影增加可读性 */
  }

  /* 环形图中心情绪名称 */
  html.wechat-browser
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"]
    > div:nth-child(2) {
    font-size: 16px !important;
    line-height: 1.2 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    width: 90px !important; /* 增加宽度 */
    text-align: center !important;
    font-weight: 600 !important;
    display: block !important;
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.5) !important; /* 添加文字阴影增加可读性 */
  }

  /* 环形图中心情绪英文名 */
  html.wechat-browser
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"]
    > div:last-child {
    font-size: 13px !important;
    line-height: 1.2 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    width: 90px !important; /* 增加宽度 */
    text-align: center !important;
    opacity: 0.9 !important;
    font-weight: 500 !important;
    display: block !important;
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.5) !important; /* 添加文字阴影增加可读性 */
  }

  /* 次要情绪环形图中心百分比 */
  html.wechat-browser
    div[style*="height: '180px'"]
    > div:last-child
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"]
    > div:first-child,
  html.wechat-browser
    div[style*="height: '160px'"]
    > div:last-child
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"]
    > div:first-child {
    font-size: 24px !important;
    width: auto !important;
    height: auto !important;
    display: block !important;
    text-shadow: 0 0 2px rgba(255, 255, 255, 0.5) !important;
    margin-bottom: 1px !important;
    line-height: 1.1 !important;
    font-weight: bold !important;
  }

  /* 次要情绪环形图中心情绪名称 */
  html.wechat-browser
    div[style*="height: '180px'"]
    > div:last-child
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"]
    > div:nth-child(2),
  html.wechat-browser
    div[style*="height: '160px'"]
    > div:last-child
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"]
    > div:nth-child(2) {
    font-size: 14px !important;
    width: 80px !important;
    display: block !important;
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.5) !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    line-height: 1.1 !important;
    font-weight: 600 !important;
    margin-bottom: 1px !important;
  }

  /* 次要情绪环形图中心情绪英文名 */
  html.wechat-browser
    div[style*="height: '180px'"]
    > div:last-child
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"]
    > div:last-child,
  html.wechat-browser
    div[style*="height: '160px'"]
    > div:last-child
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"]
    > div:last-child {
    font-size: 11px !important;
    width: 80px !important;
    display: block !important;
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.5) !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    line-height: 1.1 !important;
    opacity: 0.9 !important;
    font-weight: 500 !important;
  }

  /* 修复微信浏览器中的SVG渲染问题 - 强制硬件加速 */
  html.wechat-browser .ant-progress-circle,
  html.wechat-browser .ant-progress-circle svg,
  html.wechat-browser .ant-progress-circle path,
  html.wechat-browser
    div[style*="position: 'absolute'"][style*="transform: 'translate(-50%, -50%)'"] {
    -webkit-transform-style: preserve-3d !important;
    transform-style: preserve-3d !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    -webkit-perspective: 1000 !important;
    perspective: 1000 !important;
  }

  /* 版权信息样式已移至 wechat-copyright-fix.css 专门处理 */
}
