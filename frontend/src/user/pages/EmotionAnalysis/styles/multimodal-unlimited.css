/* 多模态分析组件无限制样式 - 彻底移除所有高度限制 */

/* 确保多模态组件可见 - 调试样式 */
.multimodal-analysis-wrapper {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 1 !important;
}

.emotion-analysis-page .multimodal-analysis-container {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 移除主要组件的高度限制 */
.emotion-analysis-page .multimodal-analysis-container .multimodal-result {
  max-height: none !important;
  height: auto !important;
  overflow-y: auto !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 移除移动端的高度限制 - 强力覆盖 */
@media (max-width: 576px) {
  .emotion-analysis-page .multimodal-analysis-container .multimodal-result {
    max-height: none !important;
    height: auto !important;
    overflow-y: auto !important;
    min-height: 100px !important;
  }

  /* 额外的移动端强力覆盖 */
  .multimodal-analysis-container .multimodal-result {
    max-height: none !important;
    height: auto !important;
  }

  /* 针对可能的布局冲突 */
  [class*='multimodal'] [class*='result'] {
    max-height: none !important;
    height: auto !important;
  }

  /* 确保按钮容器在移动端正确显示 */
  .multimodal-analysis-container > div:first-child {
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
    align-items: stretch !important;
  }

  /* 确保按钮在移动端可见 */
  .multimodal-analysis-container .ant-btn {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    max-width: 120px !important;
    margin: 0 auto !important;
    flex-shrink: 0 !important;
  }

  /* 移动端文本标题布局优化 */
  .multimodal-analysis-container .ant-typography {
    display: block !important;
    width: 100% !important;
    margin-bottom: 5px !important;
  }
}

/* 移除微信浏览器的高度限制 */
.emotion-analysis-page.wechat-container .multimodal-analysis-container .multimodal-result {
  max-height: none !important;
  height: auto !important;
  overflow-y: auto !important;
}

/* 确保文本内容可以完整显示 */
.multimodal-result-text {
  max-height: none !important;
  height: auto !important;
  overflow: visible !important;
}

/* 确保结果区域可以自适应内容高度 - 彻底移除所有高度限制 */
.multimodal-result {
  max-height: none !important;
  height: auto !important;
  min-height: 100px !important;
  overflow-y: auto !important;
}

/* 超级强力的全局覆盖 - 确保任何情况下都不会被截断 */
.emotion-analysis-page *[class*='multimodal'] *[class*='result'] {
  max-height: none !important;
  height: auto !important;
}

/* 针对可能的容器高度限制 */
.multimodal-analysis-wrapper,
.multimodal-analysis-container {
  max-height: none !important;
  height: auto !important;
}

/* 
彻底移除所有视口高度限制 - 允许LM Studio完整输出
不设置任何max-height限制，让内容自然展开
用户可以通过滚动查看完整内容
*/

/* 超小屏幕设备（如竖屏手机）*/
@media (max-width: 480px) {
  .multimodal-analysis-container > div:first-child {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
  }

  .multimodal-analysis-container .ant-btn {
    width: 80px !important;
    height: 36px !important;
    font-size: 14px !important;
    margin-top: 10px !important;
  }
}

/* 微信浏览器移动端特殊处理 */
@media (max-width: 576px) {
  .wechat-container .multimodal-analysis-container > div:first-child {
    display: flex !important;
    flex-direction: column !important;
    gap: 15px !important;
  }

  .wechat-container .multimodal-analysis-container .ant-btn {
    display: block !important;
    width: 100px !important;
    margin: 0 auto !important;
  }
}
