/* 反馈输入框暗黑模式颜色修复 - 专门针对用户上传图片后的场景 */

/* 最高优先级的样式覆盖 - 确保在所有场景下都能正确显示 */

/* 提示文字颜色修复 */
html[data-theme='dark'] body #emotionAnalysisPage #feedback-input-box .feedback-prompt-text,
html[data-theme='dark'] body .feedback-wrapper #feedback-input-box .feedback-prompt-text,
html[data-theme='dark'] body .feedbackContainer #feedback-input-box .feedback-prompt-text,
html[data-theme='dark'] body .feedbackButtons #feedback-input-box .feedback-prompt-text,
html[data-theme='dark'] #feedback-input-box .feedback-prompt-text,
body[data-theme='dark'] #feedback-input-box .feedback-prompt-text,
.dark #feedback-input-box .feedback-prompt-text,
html.dark #feedback-input-box .feedback-prompt-text {
  color: #cccccc !important;
}

/* 输入框文字颜色修复 */
html[data-theme='dark'] body #emotionAnalysisPage #feedback-input-box textarea,
html[data-theme='dark'] body .feedback-wrapper #feedback-input-box textarea,
html[data-theme='dark'] body .feedbackContainer #feedback-input-box textarea,
html[data-theme='dark'] body .feedbackButtons #feedback-input-box textarea,
html[data-theme='dark'] #feedback-input-box textarea,
body[data-theme='dark'] #feedback-input-box textarea,
.dark #feedback-input-box textarea,
html.dark #feedback-input-box textarea {
  color: #ffffff !important;
  background-color: #1a1a1a !important;
}

/* 输入框焦点状态修复 */
html[data-theme='dark'] body #emotionAnalysisPage #feedback-input-box textarea:focus,
html[data-theme='dark'] body .feedback-wrapper #feedback-input-box textarea:focus,
html[data-theme='dark'] body .feedbackContainer #feedback-input-box textarea:focus,
html[data-theme='dark'] body .feedbackButtons #feedback-input-box textarea:focus,
html[data-theme='dark'] #feedback-input-box textarea:focus,
body[data-theme='dark'] #feedback-input-box textarea:focus,
.dark #feedback-input-box textarea:focus,
html.dark #feedback-input-box textarea:focus {
  color: #ffffff !important;
  background-color: #1a1a1a !important;
}

/* 占位符文字颜色修复 */
html[data-theme='dark'] body #emotionAnalysisPage #feedback-input-box textarea::placeholder,
html[data-theme='dark'] body .feedback-wrapper #feedback-input-box textarea::placeholder,
html[data-theme='dark'] body .feedbackContainer #feedback-input-box textarea::placeholder,
html[data-theme='dark'] body .feedbackButtons #feedback-input-box textarea::placeholder,
html[data-theme='dark'] #feedback-input-box textarea::placeholder,
body[data-theme='dark'] #feedback-input-box textarea::placeholder,
.dark #feedback-input-box textarea::placeholder,
html.dark #feedback-input-box textarea::placeholder {
  color: #cccccc !important;
  opacity: 0.7 !important;
}

/* 字符计数颜色修复 */
html[data-theme='dark'] body #emotionAnalysisPage #feedback-input-box .feedback-char-count,
html[data-theme='dark'] body .feedback-wrapper #feedback-input-box .feedback-char-count,
html[data-theme='dark'] body .feedbackContainer #feedback-input-box .feedback-char-count,
html[data-theme='dark'] body .feedbackButtons #feedback-input-box .feedback-char-count,
html[data-theme='dark'] #feedback-input-box .feedback-char-count,
body[data-theme='dark'] #feedback-input-box .feedback-char-count,
.dark #feedback-input-box .feedback-char-count,
html.dark #feedback-input-box .feedback-char-count {
  color: #cccccc !important;
}

/* Ant Design 输入框组件特定修复 */
html[data-theme='dark'] body #feedback-input-box .ant-input,
html[data-theme='dark'] body #feedback-input-box .ant-input:focus,
html[data-theme='dark'] body #feedback-input-box .ant-input:hover,
body[data-theme='dark'] #feedback-input-box .ant-input,
.dark #feedback-input-box .ant-input {
  color: #ffffff !important;
  background-color: #1a1a1a !important;
}

/* Ant Design 输入框占位符修复 */
html[data-theme='dark'] body #feedback-input-box .ant-input::placeholder,
body[data-theme='dark'] #feedback-input-box .ant-input::placeholder,
.dark #feedback-input-box .ant-input::placeholder {
  color: #cccccc !important;
  opacity: 0.7 !important;
}

/* 覆盖可能的全局样式 */
html[data-theme='dark'] #feedback-input-box *,
body[data-theme='dark'] #feedback-input-box *,
.dark #feedback-input-box * {
  /* 确保所有子元素都继承正确的颜色 */
}

/* 特殊情况：如果有其他全局样式覆盖，使用更高的优先级 */
html[data-theme='dark']
  body
  div#emotionAnalysisPage
  div.feedback-wrapper
  div#feedback-input-box
  div.feedback-prompt-text {
  color: #cccccc !important;
}

html[data-theme='dark']
  body
  div#emotionAnalysisPage
  div.feedback-wrapper
  div#feedback-input-box
  textarea {
  color: #ffffff !important;
  background-color: #1a1a1a !important;
}

html[data-theme='dark']
  body
  div#emotionAnalysisPage
  div.feedback-wrapper
  div#feedback-input-box
  div.feedback-char-count {
  color: #cccccc !important;
}
