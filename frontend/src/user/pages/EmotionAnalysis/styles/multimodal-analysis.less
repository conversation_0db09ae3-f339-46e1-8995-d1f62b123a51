/* 多模态分析组件样式 - 确保不影响页面布局 */
.emotion-analysis-page .multimodal-analysis-container {
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: block;
  position: relative;
  word-break: break-word;
  overflow-wrap: break-word;

  &.dark {
    background-color: #1f1f1f;
    color: #fff;

    .multimodal-result {
      background-color: #141414;
      border-color: #333;
      color: #fff;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #333;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #555;
        border-radius: 3px;
      }

      // 段落样式
      .ant-typography {
        color: #fff;

        // 关键信息高亮
        strong,
        b {
          color: #1890ff;
        }
      }
    }
  }

  &.light {
    background-color: #f5f5f5;
    color: #000;

    .multimodal-result {
      background-color: #fff;
      border-color: #e8e8e8;
      color: #000;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f0f0f0;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #ccc;
        border-radius: 3px;
      }

      // 段落样式
      .ant-typography {
        color: #000;

        // 关键信息高亮
        strong,
        b {
          color: #1890ff;
        }
      }
    }
  }

  /* 多模态分析结果区域样式 */
  .multimodal-result {
    transition: all 0.3s ease;
    line-height: 1.6;
    font-size: 14px;
    padding: 12px;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: auto;
    display: block;
    position: relative;
    word-break: break-word;
    overflow-wrap: break-word;

    // 结构化内容样式
    .ant-typography {
      white-space: pre-line;
      line-height: 1.8;
    }

    /* 使用更具体的选择器，确保样式不会影响其他组件 */
    .multimodal-result-text {
      line-height: 1.8;
      white-space: pre-wrap;
      word-wrap: break-word;
      font-family: inherit;
      width: 100% !important;
      max-width: 100% !important;
      box-sizing: border-box !important;
      overflow: hidden !important;
      display: block !important;
      position: relative !important;
      word-break: break-word !important;
      overflow-wrap: break-word !important;
      max-inline-size: 100% !important;
      table-layout: fixed !important;
    }

    /* 移动端适配 */
    @media (max-width: 576px) {
      font-size: 13px;
      padding: 10px;
    }
  }
}

/* 微信浏览器适配 - 使用更具体的选择器，确保样式不会影响其他组件 */
.emotion-analysis-page.wechat-container {
  .multimodal-analysis-container {
    margin-bottom: 15px;
  }
}

/* 多模态分析组件包装器 - 确保组件不影响页面布局 */
.multimodal-analysis-wrapper {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: block;
  position: relative;
  margin: 0;

  /* 确保内部的多模态分析组件不会超出包装器 */
  .multimodal-analysis-container {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    margin: 0;
  }
}
