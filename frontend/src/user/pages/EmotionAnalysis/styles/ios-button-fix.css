/**
 * iOS Safari按钮大小修复
 * 解决iOS Safari中上传和拍照按钮大小不一致的问题
 */

/* 修复iOS Safari中按钮尺寸不一致的问题 */
@supports (-webkit-touch-callout: none) {
  /* 使用grid布局让按钮容器保持一致 */
  div:has(> .emotion-button-fix) {
    min-width: 110px !important;
    max-width: 110px !important;
    width: 110px !important;
    box-sizing: border-box !important;
    margin: 0 !important;
  }

  /* 按钮容器的父元素改为grid布局 */
  div:has(> div:has(> .emotion-button-fix)) {
    display: grid !important;
    grid-template-columns: repeat(2, 110px) !important;
    grid-gap: 20px !important;
    justify-content: center !important;
  }

  /* iOS Safari特定样式 */
  .emotion-button-fix {
    width: 110px !important;
    height: 40px !important;
    min-width: 110px !important;
    max-width: 110px !important;
    padding: 0 !important;
    line-height: 1.2 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    border-radius: 6px !important;
    box-sizing: border-box !important;
    flex: 0 0 110px !important;
  }

  /* 确保所有iOS Safari按钮具有相同的尺寸和样式 */
  .emotion-button-fix > span,
  .emotion-button-fix > div {
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    line-height: 1.2 !important;
    text-align: center !important;
  }

  /* 确保iOS Safari中的上传和拍照按钮具有相同的宽度 */
  .upload-button,
  .camera-button {
    width: 110px !important;
    min-width: 110px !important;
    max-width: 110px !important;
    flex: 0 0 110px !important;
    height: 40px !important;
    border-radius: 6px !important;
  }

  /* 修复iOS Safari中的按钮文字居中问题 */
  .button-content {
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    overflow: hidden !important;
  }

  /* 修复iOS Safari中的按钮主文字样式 */
  .main-text {
    font-size: 16px !important;
    font-weight: 500 !important;
    margin-bottom: 0 !important;
    line-height: 1.2 !important;
    text-align: center !important;
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }

  /* 修复iOS Safari中的按钮副文字样式 */
  .sub-text {
    font-size: 12px !important;
    opacity: 0.8 !important;
    line-height: 1.2 !important;
    text-align: center !important;
    width: 100% !important;
    display: block !important;
  }

  /* 强制使用绝对单位 */
  .emotion-button-fix,
  .upload-button,
  .camera-button {
    font-size: 16px !important; /* 使用绝对字体大小 */
  }

  /* 强制固定宽度 - 特别针对iOS Safari */
  @media screen and (-webkit-min-device-pixel-ratio: 0) {
    .upload-button,
    .camera-button {
      width: 110px !important;
      min-width: 110px !important;
      max-width: 110px !important;
      flex-basis: 110px !important;
    }
  }
}
