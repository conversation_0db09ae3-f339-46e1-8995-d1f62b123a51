/* 移动端情绪标签显示修复 - 优化版 */

/* 移动端情绪标签列表优化 */
@media screen and (max-width: 576px) {
  /* 情绪标签容器 - 使用类名选择器 */
  .emotion-tags-container {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 4px !important;
    margin: 10px 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: visible !important;
    padding: 0 2px !important;
  }

  /* 情绪标签项 - 使用类名选择器 */
  .emotion-tag-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 4px 6px !important;
    border-radius: 6px !important;
    width: 100% !important;
    max-width: 100% !important;
    min-width: 0 !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    position: relative !important;
  }

  /* 情绪信息容器 */
  .emotion-info-container {
    display: flex !important;
    align-items: center !important;
    flex: 1 !important;
    min-width: 0 !important;
    overflow: hidden !important;
    margin-right: 4px !important;
  }

  /* 颜色指示器 */
  .emotion-color-indicator {
    width: 10px !important;
    height: 10px !important;
    border-radius: 2px !important;
    margin-right: 6px !important;
    flex-shrink: 0 !important;
  }

  /* 中文情绪名称 */
  .emotion-label-chinese {
    font-size: 14px !important;
    font-weight: bold !important;
    margin-right: 3px !important;
    flex-shrink: 0 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 40px !important;
  }

  /* 英文情绪名称 */
  .emotion-label-english {
    font-size: 11px !important;
    opacity: 0.8 !important;
    font-weight: normal !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    flex: 1 !important;
    min-width: 0 !important;
    max-width: 60px !important;
  }

  /* 百分比 */
  .emotion-percentage {
    font-size: 12px !important;
    font-weight: bold !important;
    flex-shrink: 0 !important;
    margin-left: 4px !important;
    white-space: nowrap !important;
    min-width: 28px !important;
    text-align: right !important;
  }

  /* 群体分析专用样式优化 */
  .groupAnalysisContainer .emotion-tags-container {
    gap: 4px !important;
    margin: 10px 0 !important;
    padding: 0 1px !important;
  }

  .groupAnalysisContainer .emotion-tag-item {
    padding: 5px 7px !important;
    min-height: 40px !important;
    max-height: 50px !important;
  }

  .groupAnalysisContainer .emotion-label-chinese {
    max-width: 45px !important;
    font-size: 15px !important;
  }

  .groupAnalysisContainer .emotion-label-english {
    max-width: 65px !important;
    font-size: 12px !important;
  }

  .groupAnalysisContainer .emotion-percentage {
    font-size: 13px !important;
    min-width: 30px !important;
  }

  .groupAnalysisContainer .emotion-color-indicator {
    width: 12px !important;
    height: 12px !important;
    margin-right: 8px !important;
  }
}

/* 超小屏幕优化 (320px及以下) */
@media screen and (max-width: 320px) {
  .emotion-tags-container {
    gap: 3px !important;
    padding: 0 1px !important;
  }

  .emotion-tag-item {
    padding: 3px 5px !important;
  }

  .emotion-label-chinese {
    font-size: 13px !important;
    max-width: 35px !important;
  }

  .emotion-label-english {
    font-size: 10px !important;
    max-width: 45px !important;
  }

  .emotion-percentage {
    font-size: 11px !important;
    min-width: 25px !important;
  }

  .emotion-color-indicator {
    width: 8px !important;
    height: 8px !important;
    margin-right: 4px !important;
  }

  /* 群体分析在超小屏幕下的特殊优化 */
  .groupAnalysisContainer .emotion-label-chinese {
    max-width: 38px !important;
    font-size: 13px !important;
  }

  .groupAnalysisContainer .emotion-label-english {
    max-width: 50px !important;
    font-size: 11px !important;
  }

  .groupAnalysisContainer .emotion-percentage {
    font-size: 11px !important;
    min-width: 26px !important;
  }

  /* 微信浏览器在超小屏幕下的字体增强 */
  .wx-browser .emotion-label-chinese,
  html.wechat-browser .emotion-label-chinese {
    font-size: 15px !important; /* 超小屏幕下微信浏览器仍保持较大字体 */
  }

  .wx-browser .emotion-label-english,
  html.wechat-browser .emotion-label-english {
    font-size: 12px !important; /* 超小屏幕下微信浏览器英文字体 */
  }

  .wx-browser .groupAnalysisContainer .emotion-label-chinese,
  html.wechat-browser .groupAnalysisContainer .emotion-label-chinese {
    font-size: 16px !important; /* 超小屏幕下群体分析中文字体 */
  }

  .wx-browser .groupAnalysisContainer .emotion-label-english,
  html.wechat-browser .groupAnalysisContainer .emotion-label-english {
    font-size: 13px !important; /* 超小屏幕下群体分析英文字体 */
  }
}

/* 中等移动端屏幕优化 (321px - 375px) */
@media screen and (min-width: 321px) and (max-width: 375px) {
  .emotion-label-chinese {
    max-width: 42px !important;
  }

  .emotion-label-english {
    max-width: 55px !important;
  }

  .emotion-percentage {
    min-width: 30px !important;
  }

  /* 群体分析中等屏幕优化 */
  .groupAnalysisContainer .emotion-label-chinese {
    max-width: 48px !important;
  }

  .groupAnalysisContainer .emotion-label-english {
    max-width: 68px !important;
  }
}

/* 大一点的移动端屏幕优化 (376px - 576px) */
@media screen and (min-width: 376px) and (max-width: 576px) {
  .emotion-label-chinese {
    font-size: 15px !important;
    max-width: 45px !important;
  }

  .emotion-label-english {
    font-size: 12px !important;
    max-width: 65px !important;
  }

  .emotion-percentage {
    font-size: 13px !important;
    min-width: 32px !important;
  }

  /* 群体分析大屏移动端优化 */
  .groupAnalysisContainer .emotion-label-chinese {
    max-width: 50px !important;
    font-size: 16px !important;
  }

  .groupAnalysisContainer .emotion-label-english {
    max-width: 70px !important;
    font-size: 13px !important;
  }

  .groupAnalysisContainer .emotion-percentage {
    font-size: 14px !important;
    min-width: 32px !important;
  }
}

/* 微信浏览器特殊适配 */
.wx-browser .emotion-tags-container,
html.wechat-browser .emotion-tags-container {
  padding: 0 3px !important;
  gap: 3px !important;
}

.wx-browser .emotion-tag-item,
html.wechat-browser .emotion-tag-item {
  padding: 3px 5px !important;
}

/* 微信浏览器字体增强 */
.wx-browser .emotion-label-chinese,
html.wechat-browser .emotion-label-chinese {
  font-size: 16px !important; /* 微信浏览器中增大到16px */
  font-weight: bold !important;
}

.wx-browser .emotion-label-english,
html.wechat-browser .emotion-label-english {
  font-size: 13px !important; /* 微信浏览器中增大到13px */
  font-weight: 500 !important; /* 增加字重 */
}

.wx-browser .emotion-percentage,
html.wechat-browser .emotion-percentage {
  font-size: 15px !important; /* 增大百分比字体 */
  font-weight: bold !important;
}

/* 微信浏览器群体分析字体增强 */
.wx-browser .groupAnalysisContainer .emotion-label-chinese,
html.wechat-browser .groupAnalysisContainer .emotion-label-chinese {
  font-size: 17px !important; /* 群体分析中文更大 */
}

.wx-browser .groupAnalysisContainer .emotion-label-english,
html.wechat-browser .groupAnalysisContainer .emotion-label-english {
  font-size: 14px !important; /* 群体分析英文更大 */
}

/* Safari 移动端特殊适配 */
@supports (-webkit-touch-callout: none) {
  @media screen and (max-width: 576px) {
    .emotion-tags-container {
      -webkit-text-size-adjust: 100% !important;
      text-size-adjust: 100% !important;
    }

    .emotion-label-chinese,
    .emotion-label-english,
    .emotion-percentage {
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
    }
  }
}

/* 横屏模式下的优化 */
@media screen and (max-height: 500px) and (orientation: landscape) and (max-width: 900px) {
  .emotion-tags-container {
    gap: 3px !important;
    margin: 8px 0 !important;
  }

  .emotion-tag-item {
    padding: 3px 5px !important;
  }

  .emotion-label-chinese {
    font-size: 13px !important;
  }

  .emotion-label-english {
    font-size: 11px !important;
  }

  .emotion-percentage {
    font-size: 11px !important;
  }
}

/* 确保在不同主题下都能正常显示 */
html[data-theme='dark'] .emotion-tag-item,
.dark-mode .emotion-tag-item {
  background-color: #333333 !important;
}

html[data-theme='light'] .emotion-tag-item,
.light-mode .emotion-tag-item {
  background-color: #ffffff !important;
}

/* 针对特定浏览器的额外修复 */
/* Chrome 移动端 */
@media screen and (max-width: 576px) {
  @supports (display: -webkit-flex) {
    .emotion-info-container {
      display: -webkit-flex !important;
      -webkit-align-items: center !important;
      -webkit-flex: 1 !important;
    }
  }
}

/* Edge 移动端 */
@supports (-ms-flex: 1) {
  @media screen and (max-width: 576px) {
    .emotion-info-container {
      display: -ms-flexbox !important;
      -ms-flex: 1 !important;
      -ms-flex-align: center !important;
    }
  }
}

/* 确保所有文本都不会被意外选中导致布局问题 */
@media screen and (max-width: 576px) {
  .emotion-tag-item,
  .emotion-info-container,
  .emotion-label-chinese,
  .emotion-label-english,
  .emotion-percentage {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
  }
}

/* 特殊情况：处理特别长的情绪名称 */
@media screen and (max-width: 576px) {
  /* 针对"蔑视 Contempt"等较长的标签进行特殊处理 */
  .emotion-tags-container > .emotion-tag-item:nth-child(even) {
    /* 右列的标签给予更多空间 */
    min-width: 0 !important;
  }

  .emotion-tags-container > .emotion-tag-item:nth-child(odd) {
    /* 左列的标签也确保不溢出 */
    min-width: 0 !important;
  }
}
