/* 多模态分析组件完全独立布局修复 */

/* 确保多模态分析组件完全独立，不影响任何其他组件 */
.multimodal-analysis-wrapper {
  /* 完全独立的布局容器 */
  width: 100%;
  max-width: 100%;
  min-width: 0; /* 防止内容撑开容器 */
  box-sizing: border-box;
  overflow: hidden;
  display: block;
  position: relative;
  margin: 0;
  padding: 0;

  /* 强制布局和样式隔离 - 移除可能有问题的属性 */
  /* contain: layout style size; */
  /* isolation: isolate; */ /* 创建新的层叠上下文 */

  /* 防止影响兄弟元素 */
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
}

/* 多模态分析容器样式 - 完全独立 */
.emotion-analysis-page .multimodal-analysis-container {
  /* 完全独立的尺寸控制 */
  width: 100%;
  max-width: 100%;
  min-width: 0; /* 防止内容撑开容器 */
  box-sizing: border-box;

  /* 独立的布局控制 */
  display: block;
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;

  /* 文本处理 */
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto; /* 自动断词 */

  /* 强制完全隔离 - 移除可能有问题的属性 */
  /* contain: layout style size; */
  /* isolation: isolate; */

  /* 防止影响父容器和兄弟元素 */
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;

  /* 防止内容溢出影响其他组件 */
  will-change: auto; /* 避免创建不必要的合成层 */
}

/* 多模态分析结果区域 - 完全独立 */
.emotion-analysis-page .multimodal-analysis-container .multimodal-result {
  /* 完全独立的尺寸控制 */
  width: 100%;
  max-width: 100%;
  min-width: 0; /* 防止内容撑开容器 */
  box-sizing: border-box;

  /* 独立的布局控制 */
  display: block;
  position: relative;
  overflow: auto;
  margin: 0;
  padding: 0;

  /* 文本处理 */
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;

  /* 滚动条样式 */
  scrollbar-width: thin;

  /* 完全隔离 */
  /* contain: layout style; */
  /* isolation: isolate; */

  /* 防止影响其他组件 */
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
}

/* 多模态分析结果文本 */
.multimodal-result-text {
  /* 基础尺寸 */
  width: 100%;
  max-width: 100%;
  min-width: 0;
  box-sizing: border-box;

  /* 文本处理 */
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;

  /* 布局控制 */
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 移动端特殊处理 */
@media screen and (max-width: 576px) {
  .multimodal-analysis-wrapper {
    /* 移动端确保不超出屏幕 */
    max-width: 100vw;
    overflow-x: hidden;
  }

  .emotion-analysis-page .multimodal-analysis-container {
    /* 移动端布局优化 */
    max-width: 100vw;
    overflow-x: hidden;
  }

  .emotion-analysis-page .multimodal-analysis-container .multimodal-result {
    /* 移动端结果区域优化 */
    max-width: 100vw;
    overflow-x: hidden;
    overflow-y: auto;
  }
}

/* 微信浏览器特殊处理 */
.wechat-container .multimodal-analysis-wrapper {
  /* 微信浏览器中确保布局稳定 */
  max-width: 100%;
  overflow: hidden;
}

.wechat-container .emotion-analysis-page .multimodal-analysis-container {
  /* 微信浏览器中的容器优化 */
  max-width: 100%;
  overflow: hidden;
}

/* 防止多模态组件影响其他组件的完全样式隔离 */
.multimodal-analysis-wrapper * {
  /* 确保内部所有元素完全独立 */
  max-width: 100%;
  min-width: 0;
  box-sizing: border-box;

  /* 防止内部元素影响外部 */
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
}

/* 特别针对可能影响布局的元素 */
.multimodal-analysis-wrapper pre,
.multimodal-analysis-wrapper div,
.multimodal-analysis-wrapper span,
.multimodal-analysis-wrapper p {
  /* 强制文本元素不影响容器宽度 */
  max-width: 100%;
  min-width: 0;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;

  /* 防止文本撑开容器 */
  white-space: pre-wrap;
  overflow: hidden;
}

/* 确保多模态组件与其他组件的间距一致 */
.multimodal-analysis-wrapper + .audio-emotion-analysis-wrapper,
.multimodal-analysis-wrapper + .feedback-wrapper {
  margin-top: 20px;
}

/* 暗色模式下的布局保持一致 */
html[data-theme='dark'] .multimodal-analysis-wrapper,
html[data-theme='dark'] .emotion-analysis-page .multimodal-analysis-container {
  /* 暗色模式下保持相同的布局规则 */
  width: 100%;
  max-width: 100%;
  min-width: 0;
  box-sizing: border-box;
  overflow: hidden;
  /* 移除可能有问题的属性 */
  /* contain: layout style size; */
  /* isolation: isolate; */
}

/* 强制隔离规则 - 确保多模态组件绝对不影响其他组件 */
.multimodal-analysis-wrapper {
  /* 创建新的格式化上下文 */
  display: flow-root !important;

  /* 强制边界 */
  border: 0 solid transparent !important;

  /* 防止margin塌陷 */
  padding-top: 0.1px !important;
  margin-top: -0.1px !important;

  /* 完全隔离 - 只影响自己 - 移除可能有问题的属性 */
  /* contain: layout style size !important; */
  /* isolation: isolate !important; */

  /* 确保不会影响兄弟元素 */
  position: relative !important;
  z-index: auto !important;
}

/* 确保多模态组件不会影响后续元素的布局 */
.multimodal-analysis-wrapper::after {
  content: '';
  display: block;
  clear: both;
  height: 0;
  overflow: hidden;
}

/* 防止多模态组件影响前面的元素 */
.multimodal-analysis-wrapper::before {
  content: '';
  display: block;
  height: 0;
  overflow: hidden;
}

/* 确保情绪分析结果组件不受多模态组件影响 */
.emotion-analysis-page .multimodal-analysis-wrapper + * {
  /* 重置可能受影响的属性，恢复原始状态 */
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  flex-basis: auto !important;
  flex-grow: initial !important;
  flex-shrink: initial !important;
  contain: none !important;
  isolation: auto !important;
}

/* 特别保护其他组件，恢复它们的原始宽度 */
.emotion-analysis-page .multimodal-analysis-wrapper ~ .audio-emotion-analysis-wrapper,
.emotion-analysis-page .multimodal-analysis-wrapper ~ .feedback-wrapper,
.emotion-analysis-page .multimodal-analysis-wrapper ~ .copyright-info {
  /* 恢复这些组件的原始布局 */
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  box-sizing: content-box !important;
  contain: none !important;
  isolation: auto !important;
  flex-shrink: initial !important;
  flex-grow: initial !important;
  flex-basis: auto !important;
}

/* 确保情绪分析结果组件保持原有宽度 */
.emotion-analysis-page .multimodal-analysis-wrapper ~ * .emotion-list-container,
.emotion-analysis-page .multimodal-analysis-wrapper ~ * .attributesContainer,
.emotion-analysis-page .multimodal-analysis-wrapper ~ * .faceCard {
  /* 恢复情绪分析结果组件的原始宽度设置 */
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  contain: none !important;
  isolation: auto !important;
}
