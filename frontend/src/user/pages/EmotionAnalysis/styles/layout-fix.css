/* 导航栏和卡片宽度一致修复 */

/* 确保导航栏和内容区域宽度一致 */
.ant-layout-header,
.emotionAnalysisContainer {
  width: 100% !important;
  max-width: 850px !important;
  box-sizing: border-box !important;
  margin: 0 auto !important;
}

/* 确保导航栏和内容区域在移动端也保持一致 */
@media screen and (max-width: 900px) {
  .ant-layout-header,
  .emotionAnalysisContainer {
    width: 100% !important;
    max-width: 850px !important;
  }
}

/* 确保导航栏和内容区域在小屏幕上也保持一致 */
@media screen and (max-width: 576px) {
  .ant-layout-header,
  .emotionAnalysisContainer {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 12px !important;
    padding-right: 12px !important;
  }
}

/* 微信浏览器中的样式不受影响 */
html.wechat-browser .ant-layout-header,
body.wx-browser .ant-layout-header,
html.wechat-browser .emotionAnalysisContainer,
body.wx-browser .emotionAnalysisContainer {
  /* 微信浏览器中的样式保持不变 */
}
