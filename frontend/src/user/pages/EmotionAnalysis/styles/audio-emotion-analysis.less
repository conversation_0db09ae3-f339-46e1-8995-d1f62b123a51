.audio-emotion-analysis-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &.dark {
    background-color: #1f1f1f;
    color: #fff;

    .ant-card-head {
      background-color: #2a2a2a;
      border-bottom: 1px solid #303030;
    }

    .ant-card-head-title {
      color: #fff;
    }

    .analysis-result {
      background-color: #2a2a2a;
    }

    .prompt-text {
      color: #3eeb34 !important; // 强制使用亮绿色
    }
  }

  .audio-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 16px;
    align-items: center;

    @media (max-width: 576px) {
      flex-direction: column;
      align-items: flex-start;

      .audio-upload,
      .audio-record,
      button {
        width: 100%;
      }
    }
  }

  .audio-player {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 12px 0;
    padding: 8px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.03);

    .dark & {
      background-color: rgba(255, 255, 255, 0.05);
    }
  }

  .analysis-result {
    padding: 8px 4px;
    border-radius: 8px;
    background-color: #f5f5f5;
    margin-top: 16px;
    white-space: pre-wrap;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;

    .dark & {
      background-color: #2a2a2a;
    }

    // 移动端优化 - 使用!important确保生效
    @media (max-width: 576px) {
      padding: 4px 2px !important;
      margin-top: 12px !important;
      font-size: 14px !important;
      line-height: 1.6 !important;
      width: 100% !important;
      max-width: 100% !important;
      box-sizing: border-box !important;
    }

    // Paragraph组件样式覆盖
    .ant-typography {
      margin: 0 !important;
      padding: 0 !important;
      width: 100% !important;
      max-width: 100% !important;
      box-sizing: border-box !important;
      line-height: 1.4 !important; // 减少行间距，使显示更紧凑

      @media (max-width: 576px) {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        line-height: 1.3 !important; // 移动端进一步减少行间距
      }
    }
  }
}
