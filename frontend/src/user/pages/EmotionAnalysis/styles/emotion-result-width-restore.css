/* 恢复情绪分析结果组件的原始宽度设置 */

/* 确保情绪分析结果组件不受多模态组件影响 */
.emotion-analysis-page .emotion-result-container,
.emotion-analysis-page .emotion-result-wrapper {
  /* 恢复原始布局属性 */
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  box-sizing: content-box !important;
  contain: none !important;
  isolation: auto !important;
  flex-shrink: initial !important;
  flex-grow: initial !important;
  flex-basis: auto !important;
}

/* 恢复情绪分析结果的主要容器 */
.emotion-analysis-page .faceCard {
  /* 恢复原始宽度设置 */
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  margin-bottom: 16px !important;
  contain: none !important;
  isolation: auto !important;
}

/* 恢复属性容器的原始布局 */
.emotion-analysis-page .attributesContainer {
  /* 恢复原始flex布局 */
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 16px !important;
  margin-bottom: 20px !important;
  justify-content: center !important;
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  contain: none !important;
  isolation: auto !important;
}

/* 恢复属性框的原始宽度 */
.emotion-analysis-page .attributeBox {
  /* 恢复原始固定宽度 */
  flex: 0 0 auto !important;
  width: 160px !important;
  max-width: 160px !important;
  min-width: 160px !important;
  background-color: #ffffff !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03) !important;
  border: 1px solid #e8e8e8 !important;
  text-align: center !important;
  contain: none !important;
  isolation: auto !important;
}

/* 恢复情绪列表容器的原始布局 */
.emotion-analysis-page .emotion-list-container {
  /* 恢复原始grid布局 */
  margin-top: 12px !important;
  margin-bottom: 12px !important;
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
  gap: 6px !important;
  overflow: hidden !important;
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  box-sizing: border-box !important;
  contain: none !important;
  isolation: auto !important;
}

/* 恢复情绪列表项的原始宽度 */
.emotion-analysis-page .emotion-list-item {
  /* 恢复原始布局 */
  display: flex !important;
  flex-direction: column !important;
  padding: 6px 10px !important;
  border-radius: 6px !important;
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  box-sizing: border-box !important;
  min-height: 60px !important;
  contain: none !important;
  isolation: auto !important;
}

/* 恢复环形图容器的原始尺寸 - 适配内部标签布局 */
.emotion-analysis-page .emotion-circle-container {
  /* 恢复原始尺寸，适配新的内部标签布局 */
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  min-height: 180px !important;
  contain: none !important;
  isolation: auto !important;
}

/* 移动端特殊处理 - 恢复原始移动端布局 */
@media screen and (max-width: 576px) {
  /* 恢复移动端属性框布局 */
  .emotion-analysis-page .faceInfo {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    justify-content: space-between !important;
    gap: 2px !important;
    padding: 0 !important;
    margin: 10px 0 !important;
    width: auto !important;
    max-width: none !important;
    min-width: auto !important;
    contain: none !important;
    isolation: auto !important;
  }

  /* 恢复移动端属性框的四列布局 */
  .emotion-analysis-page .faceInfo > div {
    min-width: 0 !important;
    max-width: 25% !important;
    flex: 1 !important;
    padding: 2px !important;
    margin-bottom: 0 !important;
    contain: none !important;
    isolation: auto !important;
  }

  /* 恢复移动端情绪列表的原始间距 */
  .emotion-analysis-page .emotion-list-container {
    gap: 4px !important;
    margin: 8px 0 !important;
    width: auto !important;
    max-width: none !important;
    min-width: auto !important;
  }

  /* 恢复移动端情绪列表项的原始尺寸 */
  .emotion-analysis-page .emotion-list-item {
    padding: 4px 8px !important;
    min-height: 50px !important;
    width: auto !important;
    max-width: none !important;
    min-width: auto !important;
  }

  /* 恢复移动端环形图的原始尺寸 - 适配内部标签 */
  .emotion-analysis-page .emotion-circle-container {
    min-height: 140px !important;
    width: auto !important;
    max-width: none !important;
    min-width: auto !important;
  }
}

/* 暗色模式下也要恢复原始宽度 */
html[data-theme='dark'] .emotion-analysis-page .faceCard,
html[data-theme='dark'] .emotion-analysis-page .attributesContainer,
html[data-theme='dark'] .emotion-analysis-page .attributeBox,
html[data-theme='dark'] .emotion-analysis-page .emotion-list-container,
html[data-theme='dark'] .emotion-analysis-page .emotion-list-item {
  /* 暗色模式下也恢复原始宽度 */
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  contain: none !important;
  isolation: auto !important;
}

/* 确保多模态组件之前的所有组件都不受影响 */
.emotion-analysis-page .multimodal-analysis-wrapper ~ * {
  /* 重置所有可能被多模态组件影响的属性 */
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  flex-basis: auto !important;
  flex-grow: initial !important;
  flex-shrink: initial !important;
  contain: none !important;
  isolation: auto !important;
  box-sizing: border-box !important;
}

/* 特别确保情绪分析结果组件的所有子元素都恢复原状 */
.emotion-analysis-page .multimodal-analysis-wrapper ~ * * {
  /* 深层重置 */
  contain: none !important;
  isolation: auto !important;
}
