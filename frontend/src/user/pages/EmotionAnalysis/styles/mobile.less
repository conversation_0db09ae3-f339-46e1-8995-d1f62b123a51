/* 移动端专用样式 */
/* 只在屏幕宽度小于576px时应用 */
@media (max-width: 576px) {
  /* 全局容器样式 */
  .emotionAnalysisContainer {
    padding: 8px 4px !important;
    width: 100% !important;
    overflow-x: hidden !important;
  }

  /* 上传卡片样式 */
  .uploadCard {
    width: 100% !important;
    max-width: 100% !important;
    padding: 10px 5px !important;
    margin-bottom: 10px !important;
  }

  /* 页面标题栏样式 */
  .pageHeader {
    width: 100% !important;
    max-width: 100% !important;
    padding: 10px 0 !important;
  }

  /* 图片框样式 */
  .imageFrames {
    flex-direction: column !important;
    margin-bottom: 10px !important;
  }

  .imageFrame {
    width: 100% !important;
    max-width: 100% !important;
    margin-bottom: 10px !important;
    min-height: 300px !important;
  }

  /* 结果信息区域 */
  .resultInfo {
    overflow-x: hidden !important;
    width: 100% !important;
    padding: 0 5px !important;

    /* 处理时间显示 */
    .resultHeader {
      flex-direction: column !important;
      padding-bottom: 5px !important;
      margin-bottom: 10px !important;
    }

    /* 人脸属性信息内容栏 */
    .faceInfo {
      display: flex !important;
      flex-wrap: wrap !important;
      justify-content: space-between !important;
      gap: 5px !important;
      padding: 0 !important;
      margin: 10px 0 !important;

      /* 每个属性框占50%宽度，确保每行两个 */
      > div {
        flex: 0 0 calc(50% - 5px) !important;
        max-width: calc(50% - 5px) !important;
        min-width: auto !important;
        margin-bottom: 5px !important;

        /* 属性标题 */
        > div:first-child {
          font-size: 12px !important;
        }

        /* 英文标题 */
        > div:nth-child(2) {
          font-size: 10px !important;
          margin-top: 0 !important;
        }

        /* 属性值容器 */
        > div:nth-child(3) {
          width: 100% !important;
          max-width: 100% !important;
          padding: 3px 5px !important;
          font-size: 14px !important;

          /* 属性值 */
          > div:first-child {
            font-size: 14px !important;
          }

          /* 属性值英文 */
          > div:last-child {
            font-size: 10px !important;
            margin-top: 0 !important;
          }
        }
      }
    }

    /* 情绪分析结果标题 */
    div[style*="marginBottom: '8px'"] {
      margin-bottom: 5px !important;

      div {
        font-size: 14px !important;

        .englishDescription {
          font-size: 11px !important;
        }
      }
    }

    /* 主要情绪和次要情绪标题 */
    div[style*="justifyContent: 'space-between'"] {
      margin-bottom: 5px !important;

      div {
        font-size: 12px !important;

        .englishDescription {
          font-size: 10px !important;
        }
      }
    }

    /* 基础环形图移动端优化 */
    div[style*='height: 180px'] {
      height: 160px !important;
    }

    /* 情绪列表 */
    div[style*='gridTemplateColumns'] {
      display: grid !important;
      grid-template-columns: repeat(2, 1fr) !important;
      gap: 4px !important;
      margin: 10px 0 !important;

      /* 每个情绪项 */
      > div {
        padding: 4px 6px !important;

        /* 情绪名称和百分比 */
        > div:first-child {
          margin-bottom: 2px !important;

          /* 情绪名称 */
          > div:first-child {
            span:first-child {
              font-size: 13px !important;
            }

            span:last-child {
              font-size: 10px !important;
              margin-left: 2px !important;
            }
          }

          /* 百分比 */
          > span:last-child {
            font-size: 13px !important;
          }
        }

        /* 进度条 */
        > div:last-child {
          height: 4px !important;
        }
      }
    }
  }

  /* 反馈按钮 */
  .feedbackButtons {
    gap: 8px !important;
    margin-top: 15px !important;
    margin-bottom: 15px !important;

    button {
      height: 36px !important;
      min-width: 80px !important;
      padding: 0 8px !important;
      font-size: 13px !important;

      .buttonIcon {
        font-size: 14px !important;
        margin-right: 4px !important;
      }
    }
  }
}
