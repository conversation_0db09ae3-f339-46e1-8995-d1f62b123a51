/* 微信浏览器中开始分析按钮样式修复 */

/* 微信浏览器中的分析按钮 */
body.wx-browser .wechat-analyze-button,
html.wechat-browser .wechat-analyze-button,
.wechat-analyze-button {
  height: 110px !important;
  width: 110px !important;
  border-radius: 8px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 6px 12px rgba(240, 86, 84, 0.25) !important;
  background: linear-gradient(145deg, #f05654, #d04057) !important;
  border-color: transparent !important;
}

/* 微信浏览器中的分析按钮内容 */
body.wx-browser .wechat-analyze-button > div,
html.wechat-browser .wechat-analyze-button > div,
.wechat-analyze-button > div {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  width: 100% !important;
}

/* 微信浏览器中的分析按钮图标 */
body.wx-browser .wechat-analyze-button .anticon,
html.wechat-browser .wechat-analyze-button .anticon,
.wechat-analyze-button .anticon {
  font-size: 24px !important;
  margin-bottom: 6px !important;
}

/* 微信浏览器中的分析按钮主文本 */
body.wx-browser .wechat-analyze-button .analyze-button-text,
html.wechat-browser .wechat-analyze-button .analyze-button-text,
.wechat-analyze-button .analyze-button-text {
  font-size: 18px !important;
  font-weight: bold !important;
  margin-top: 4px !important;
  line-height: 1.2 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  width: 100% !important;
  text-align: center !important;
}

/* 微信浏览器中的分析按钮副文本 */
body.wx-browser .wechat-analyze-button .analyze-button-subtext,
html.wechat-browser .wechat-analyze-button .analyze-button-subtext,
.wechat-analyze-button .analyze-button-subtext {
  font-size: 12px !important;
  margin-top: 2px !important;
  opacity: 0.9 !important;
  line-height: 1.2 !important;
  width: 90% !important;
  text-align: center !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 移动端样式优化 */
@media screen and (max-width: 576px) {
  body.wx-browser .wechat-analyze-button,
  html.wechat-browser .wechat-analyze-button,
  .wechat-analyze-button {
    height: 100px !important;
    width: 100px !important;
    border-radius: 8px !important;
  }

  body.wx-browser .wechat-analyze-button .anticon,
  html.wechat-browser .wechat-analyze-button .anticon,
  .wechat-analyze-button .anticon {
    font-size: 24px !important;
    margin-bottom: 4px !important;
  }

  body.wx-browser .wechat-analyze-button .analyze-button-text,
  html.wechat-browser .wechat-analyze-button .analyze-button-text,
  .wechat-analyze-button .analyze-button-text {
    font-size: 18px !important;
    margin-top: 4px !important;
  }

  body.wx-browser .wechat-analyze-button .analyze-button-subtext,
  html.wechat-browser .wechat-analyze-button .analyze-button-subtext,
  .wechat-analyze-button .analyze-button-subtext {
    font-size: 12px !important;
    margin-top: 2px !important;
  }
}

/* 确保按钮文字颜色为白色 */
body.wx-browser .wechat-analyze-button,
body.wx-browser .wechat-analyze-button *,
html.wechat-browser .wechat-analyze-button,
html.wechat-browser .wechat-analyze-button *,
.wechat-analyze-button,
.wechat-analyze-button * {
  color: white !important;
}

/* 确保按钮在禁用状态下的样式 */
body.wx-browser .wechat-analyze-button[disabled],
html.wechat-browser .wechat-analyze-button[disabled],
.wechat-analyze-button[disabled] {
  opacity: 0.6 !important;
  background: linear-gradient(145deg, #d04057, #b02a40) !important;
}

/* 确保按钮在加载状态下的样式 */
body.wx-browser .wechat-analyze-button.ant-btn-loading,
html.wechat-browser .wechat-analyze-button.ant-btn-loading,
.wechat-analyze-button.ant-btn-loading {
  opacity: 0.8 !important;
}

/* 确保按钮在悬停状态下的样式 */
body.wx-browser .wechat-analyze-button:hover:not([disabled]),
html.wechat-browser .wechat-analyze-button:hover:not([disabled]),
.wechat-analyze-button:hover:not([disabled]) {
  background: linear-gradient(145deg, #ff6b69, #e04057) !important;
}

/* 确保按钮在点击状态下的样式 */
body.wx-browser .wechat-analyze-button:active:not([disabled]),
html.wechat-browser .wechat-analyze-button:active:not([disabled]),
.wechat-analyze-button:active:not([disabled]) {
  transform: scale(0.98) !important;
  box-shadow: 0 2px 4px rgba(240, 86, 84, 0.2) !important;
  background: linear-gradient(145deg, #e04057, #d04057) !important;
}
