/* 微信浏览器中文本显示优化 */

/* 针对微信浏览器中的检测器信息文本 */
html.wechat-browser .detectorSelector,
body.wx-browser .detectorSelector {
  font-size: 12px !important; /* 缩小字体 */
  padding: 8px 10px !important; /* 减小内边距 */
  line-height: 1.3 !important; /* 减小行高 */
  letter-spacing: -0.5px !important; /* 减小字间距 */
  word-break: keep-all !important; /* 避免单词断开 */
  white-space: normal !important; /* 允许自动换行 */
  max-width: 100% !important; /* 确保不超出容器 */
  overflow-wrap: break-word !important; /* 允许长单词断行 */
  word-wrap: break-word !important; /* 兼容性写法 */
}

/* 微信浏览器中显示移动端换行符 */
html.wechat-browser .detectorSelector .mobile-break,
body.wx-browser .detectorSelector .mobile-break {
  display: block !important;
}

/* 确保英文描述也适当缩小 */
html.wechat-browser .detectorSelector .englishDescription,
body.wx-browser .detectorSelector .englishDescription {
  font-size: 11px !important;
  line-height: 1.2 !important;
  margin-top: 4px !important;
}

/* 特别处理"虹膜"文本，确保不会单独换行 */
html.wechat-browser .detectorSelector,
body.wx-browser .detectorSelector {
  word-spacing: -1px !important; /* 减小词间距 */
}

/* 使用CSS技巧防止"虹膜"被拆分 */
html.wechat-browser .detectorSelector,
body.wx-browser .detectorSelector {
  text-justify: inter-ideograph !important; /* 优化中文排版 */
}

/* 防止"虹膜"被拆分的特殊处理 */
html.wechat-browser .no-wrap,
body.wx-browser .no-wrap {
  white-space: nowrap !important; /* 禁止换行 */
  display: inline-block !important; /* 确保作为一个整体 */
}

/* 调整检测器文本的字体大小 */
html.wechat-browser .detector-text,
body.wx-browser .detector-text {
  font-size: 12px !important; /* 微信浏览器中更小的字体 */
  letter-spacing: -0.5px !important; /* 减小字间距 */
}
