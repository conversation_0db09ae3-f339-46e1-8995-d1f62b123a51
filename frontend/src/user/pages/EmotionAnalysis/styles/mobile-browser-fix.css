/* 移动端浏览器兼容性修复 */

/* 通用移动端优化 */
@media screen and (max-width: 576px) {
  /* 检测器选择器的通用移动端优化 */
  .detectorSelector {
    /* 基础布局 */
    display: block !important;
    width: 100% !important;
    max-width: 100% !important;
    min-width: 0 !important;
    box-sizing: border-box !important;

    /* 文字控制 */
    font-size: 12px !important;
    line-height: 1.3 !important;
    text-align: center !important;

    /* 间距控制 */
    padding: 8px 6px !important;
    margin: 0 auto 15px auto !important;

    /* 溢出控制 */
    overflow: hidden !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;

    /* 浏览器兼容性 */
    -webkit-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
  }

  /* 检测器文字的通用优化 */
  .detectorSelector .detector-text {
    display: block !important;
    width: 100% !important;
    max-width: 100% !important;
    font-size: 12px !important;
    line-height: 1.3 !important;
    letter-spacing: -0.3px !important;
    word-spacing: -0.5px !important;
    word-break: keep-all !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
  }

  /* 强制显示换行符 */
  .detectorSelector .mobile-break {
    display: block !important;
    content: '' !important;
    height: 0 !important;
    line-height: 0 !important;
    margin: 2px 0 !important;
    clear: both !important;
  }

  /* 防止关键词被拆分 */
  .detectorSelector .no-wrap {
    white-space: nowrap !important;
    display: inline !important;
    word-break: keep-all !important;
    overflow-wrap: normal !important;
  }

  /* 英文描述优化 */
  .detectorSelector .englishDescription {
    display: block !important;
    width: 100% !important;
    max-width: 100% !important;
    font-size: 10px !important;
    line-height: 1.2 !important;
    margin-top: 4px !important;
    opacity: 0.8 !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
  }
}

/* iOS Safari 特殊优化 */
@supports (-webkit-touch-callout: none) {
  @media screen and (max-width: 576px) {
    .detectorSelector {
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
      -webkit-transform: translateZ(0) !important;
      transform: translateZ(0) !important;
    }

    .detectorSelector .detector-text {
      -webkit-hyphens: none !important;
      hyphens: none !important;
    }
  }
}

/* Android Chrome 特殊优化 */
@media screen and (max-width: 576px) and (-webkit-min-device-pixel-ratio: 1) {
  .detectorSelector {
    text-rendering: optimizeLegibility !important;
    font-feature-settings: 'kern' 1 !important;
  }
}

/* 超小屏幕设备优化 */
@media screen and (max-width: 375px) {
  .detectorSelector {
    padding: 6px 4px !important;
    font-size: 11px !important;
    line-height: 1.2 !important;
  }

  .detectorSelector .detector-text {
    font-size: 11px !important;
    letter-spacing: -0.5px !important;
    word-spacing: -1px !important;
  }

  .detectorSelector .englishDescription {
    font-size: 9px !important;
    line-height: 1.1 !important;
  }
}

/* 极小屏幕设备优化 (iPhone SE第一代等) */
@media screen and (max-width: 320px) {
  .detectorSelector {
    padding: 4px 2px !important;
    font-size: 10px !important;
    line-height: 1.1 !important;
  }

  .detectorSelector .detector-text {
    font-size: 10px !important;
    letter-spacing: -0.8px !important;
    word-spacing: -1.5px !important;
  }

  .detectorSelector .englishDescription {
    font-size: 8px !important;
    line-height: 1 !important;
  }
}

/* 横屏模式优化 */
@media screen and (max-height: 500px) and (orientation: landscape) {
  .detectorSelector {
    padding: 4px 6px !important;
    margin-bottom: 10px !important;
    font-size: 11px !important;
  }

  .detectorSelector .detector-text {
    font-size: 11px !important;
  }

  .detectorSelector .englishDescription {
    font-size: 9px !important;
    margin-top: 2px !important;
  }
}
