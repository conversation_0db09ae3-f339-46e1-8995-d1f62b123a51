import React, { useEffect, useState, useRef } from 'react';
import { Alert } from 'antd';
import { useEmotionAnalysis } from './hooks/useEmotionAnalysis';
import { useCamera } from './hooks/useCamera';
import ImageUploader from './components/ImageUploader';
import FeatureDetection from './components/FeatureDetection';
import CameraCapture from './components/CameraCapture';
import EmotionResult from './components/EmotionResult';
import ActionButtons from './components/ActionButtons';
import MultimodalAnalysis from './components/MultimodalAnalysis';
import AudioEmotionAnalysis from './components/AudioEmotionAnalysis';
import WechatAudioEmotionAnalysis from './components/WechatAudioEmotionAnalysis';
import FeedbackSection from './components/FeedbackSection';
import AudioPromptText from './components/AudioPromptText';
import { useResponsive } from '../../../shared/utils/responsiveUtils';
import { isWechatBrowser } from '@/shared/utils/browserDetect';
import { initAndroidWechatDetection } from './utils/androidWechatDetector';
import { initAndroidBrowserDetection } from './utils/androidBrowserDetector'; // Android浏览器检测
import './styles/index.less';
import './styles/dark-theme.less';
import './styles/dark-mode-styles.less';
import './styles/audio-emotion-analysis.less';
import './styles/mobile.less';
import './styles/landmarks-tag.css';
import './styles/dark-mode-colors.css';
import './styles/dark-mode-fix.css';
import './styles/dark-mode-override.css';
import './styles/force-dark-mode.css';
import './styles/analyze-button-fix.css';
import './styles/white-text-fix.css';
import './styles/feedback-button-fix.css';
import './styles/feedback-input-dark-mode-fix.css';
import './styles/face-button-fix.css';
import './styles/mobile-fix.css'; // 移动端样式优化
import './styles/mobile-browser-fix.css'; // 移动端浏览器兼容性修复
import './styles/mobile-multimodal-fix.css'; // 移动端多模态组件显示修复
import './styles/wechat-text-fix.css'; // 微信浏览器文字修复
import './styles/emotion-result-width-restore.css';
import './styles/camera-buttons-fix.css';
import './styles/layout-fix.css';
import './styles/wechat-copyright-fix.css';
import './styles/image-display-fix.css';
import './styles/emotion-tags-mobile-fix.css'; // 情绪标签移动端显示修复
import './styles/multimodal-button-mobile-fix.css'; // 多模态组件按钮移动端显示修复
import InternalFeedbackForm from './components/InternalFeedbackForm';
import FeedbackInstructions from '@/components/FeedbackInstructions';

const EmotionAnalysis: React.FC = () => {
  const [isDarkMode, setIsDarkMode] = useState(
    document.documentElement.getAttribute('data-theme') === 'dark',
  ); // 根据当前主题设置初始值

  // 创建canvas引用，用于拍照功能
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 在组件挂载后，确保canvas元素有正确的ID
  useEffect(() => {
    if (canvasRef.current) {
      canvasRef.current.id = 'main-canvas';
    }
  }, []);

  // 使用响应式钩子获取屏幕信息
  const { isMobile, isSmallScreen, isPortrait } = useResponsive();

  // 使用自定义钩子
  const {
    fileList,
    setFileList,
    uploading,
    analyzing,
    result,
    error,
    originalImage,
    setOriginalImage,
    showFeatureImage,
    setShowFeatureImage,
    uploadImage, // 获取上传图片函数
    handleUpload, // 分析函数
  } = useEmotionAnalysis();

  const { showCamera, videoRef, startCamera, stopCamera, takePhoto } = useCamera(
    setFileList,
    setOriginalImage,
    setShowFeatureImage,
    canvasRef, // 传递canvasRef给useCamera钩子
    uploadImage, // 传递uploadImage函数给useCamera钩子，用于上传拍照的图片
  );

  // 初始化安卓微信浏览器检测和修复
  useEffect(() => {
    initAndroidWechatDetection();

    // 初始化Android普通浏览器检测和修复
    const cleanupAndroidBrowserDetection = initAndroidBrowserDetection();

    // 组件卸载时清理
    return () => {
      cleanupAndroidBrowserDetection();
    };
  }, []);

  // 检测暗黑模式
  useEffect(() => {
    const checkDarkMode = () => {
      const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
      setIsDarkMode(isDark);

      // 强制刷新暗黑模式样式
      if (isDark) {
        // 添加一个临时类，然后移除它，触发重新渲染
        document.body.classList.add('dark-mode-refresh');
        setTimeout(() => {
          document.body.classList.remove('dark-mode-refresh');
        }, 10);
      }
    };

    // 初始检测
    checkDarkMode();

    // 监听主题变化
    if (typeof window !== 'undefined' && window.MutationObserver) {
      const observer = new window.MutationObserver(checkDarkMode);
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme'],
      });

      return () => observer.disconnect();
    }
    return undefined;
  }, []);

  // 记录响应式状态变化
  useEffect(() => {
    console.log('响应式状态:', {
      isMobile,
      isSmallScreen,
      isPortrait,
      layout: isSmallScreen || (isMobile && isPortrait) ? '纵向排列' : '横向排列',
    });
  }, [isMobile, isSmallScreen, isPortrait]);

  return (
    <>
      {/* 主要情绪分析容器 */}
      <div
        className={`emotionAnalysisContainer emotion-analysis-page`}
        id="emotionAnalysisPage"
        style={{
          width: '100%',
          maxWidth: '850px',
          margin: '0 auto',
          borderRadius: '0 0 8px 8px', // 只保留底部圆角
          overflow: 'hidden',
          marginTop: '-1px', // 负边距，确保与导航栏无缝连接
          borderTop: 'none', // 移除顶部边框
          boxSizing: 'border-box', // 确保padding不会增加宽度
        }}
      >
        {/* 表头部分 */}
        <div
          className="emotion-analysis-header pageHeader"
          style={{
            textAlign: 'center',
            padding: '16px 0',
            width: '100%',
          }}
        >
          <h2 className="title">
            情绪分析
            <div className="englishTitle">Emotion Analysis</div>
          </h2>
        </div>

        <div
          className="uploadCard"
          style={{
            padding: '0 20px 25px 20px',
          }}
        >
          {/* 上传提示文字 */}
          <div
            className="description"
            style={{
              textAlign: 'left',
              fontSize: '15px',
              margin: '20px 0',
              padding: '12px 15px',
              borderRadius: '8px',
            }}
          >
            <div style={{ whiteSpace: 'normal', wordBreak: 'break-word' }}>
              上传一张包含人脸的图片或拍摄一张照片，系统将自动检测特征点图片，点击开始分析按钮自动分析情绪。
              点击执行分析按钮，多模态大模型将自动分析情绪。点击开始录音按钮，录音结束后点击分析情绪按钮，系统会自动分析情绪。
              <br />
              支持jpg/jpeg、png、gif。支持多人脸识别(10)。支持群体情绪识别。
              <br />
              支持多模态大模型的图片和语音的情绪实时分析。
            </div>
            <div
              className="englishDescription"
              style={{ whiteSpace: 'normal', wordBreak: 'break-word' }}
            >
              Upload a picture containing a human face or take a photo, and the system will
              automatically detect the feature point picture. Click the &quot;Analysis&quot; button
              to automatically analyze the emotions Supports jpg/jpeg, png and gif. Support
              multi-face recognition (10). Support group emotion recognition. Real-time emotion
              analysis of images and voices supporting multimodal large models.
            </div>
          </div>

          {/* 图片上传和特征点检测区域 */}
          <div
            style={{
              display: 'flex',
              flexDirection: isSmallScreen || (isMobile && isPortrait) ? 'column' : 'row',
              justifyContent: 'space-between',
              gap: '15px',
              marginBottom: '20px',
              marginTop: '20px',
              height: isSmallScreen || (isMobile && isPortrait) ? 'auto' : '600px', // 从450px增加到600px，确保图片完整显示
              alignItems: 'stretch', // 确保子元素拉伸填满容器高度
            }}
          >
            {/* 原始图片区域 */}
            <div
              style={{
                width: isSmallScreen || (isMobile && isPortrait) ? '100%' : '49%',
                height: isSmallScreen || (isMobile && isPortrait) ? '500px' : '100%', // 增加高度到500px
                display: 'flex',
                flexDirection: 'column',
                border: '1px solid var(--border-color)',
                borderRadius: '8px',
                overflow: 'hidden',
                marginBottom: isSmallScreen || (isMobile && isPortrait) ? '15px' : '0',
              }}
              className="image-frame-container" // 添加类名便于CSS选择器定位
            >
              <div
                style={{
                  backgroundColor: 'var(--primary-color)',
                  color: 'white',
                  padding: '8px 16px',
                  textAlign: 'center',
                  fontWeight: 'bold',
                }}
              >
                原始图片
                <div style={{ fontSize: '12px', opacity: 0.8 }}>Original Image</div>
              </div>
              <div style={{ flex: 1, overflow: 'hidden' }}>
                <ImageUploader originalImage={originalImage} />
              </div>
            </div>

            {/* 特征点检测区域 */}
            <div
              style={{
                width: isSmallScreen || (isMobile && isPortrait) ? '100%' : '49%',
                height: isSmallScreen || (isMobile && isPortrait) ? '500px' : '100%', // 增加高度到500px
                display: 'flex',
                flexDirection: 'column',
                border: '1px solid var(--border-color)',
                borderRadius: '8px',
                overflow: 'hidden',
              }}
              className="image-frame-container" // 添加类名便于CSS选择器定位
            >
              <div
                style={{
                  backgroundColor: 'var(--primary-color)',
                  color: 'white',
                  padding: '8px 16px',
                  textAlign: 'center',
                  fontWeight: 'bold',
                }}
              >
                特征点检测
                <div style={{ fontSize: '12px', opacity: 0.8 }}>Feature Detection</div>
              </div>
              <div style={{ flex: 1, overflow: 'hidden' }}>
                <FeatureDetection
                  result={result}
                  showFeatureImage={showFeatureImage}
                  isDarkMode={isDarkMode}
                  fileList={fileList}
                />
              </div>
            </div>
          </div>

          {/* 检测器信息 */}
          <div
            className="detectorSelector"
            style={{
              marginBottom: '20px',
              textAlign: 'center',
              fontSize: '14px',
              padding: '12px 15px',
              borderRadius: '8px',
            }}
          >
            <span style={{ color: '#ff4d4f', marginRight: '5px' }}>•</span>
            <span className="detector-text">
              使用高精度特征点模式，可检测478个面部关键点，
              <br className="mobile-break" />
              包括面部网格、眼睛轮廓和虹膜
            </span>

            <div className="englishDescription">
              Using high-precision feature point mode to detect 478 facial landmarks,
              <br className="mobile-break" />
              including facial mesh, eye contours and iris
            </div>
          </div>

          {/* 操作按钮区 */}
          <div className="actionButtons">
            <ActionButtons
              uploadImage={uploadImage} // 正确绑定上传图片函数
              analyzeEmotion={handleUpload} // 分析函数
              startCamera={startCamera}
              showCamera={showCamera}
              showFeatureImage={showFeatureImage}
              fileList={fileList}
              setFileList={setFileList}
              uploading={uploading}
              analyzing={analyzing}
              isDarkMode={isDarkMode}
            />
          </div>

          {/* 摄像头区域 */}
          <div
            style={{
              display: showCamera ? 'block' : 'none',
              width: '100%',
              marginBottom: '20px',
            }}
          >
            <CameraCapture
              videoRef={videoRef}
              canvasRef={canvasRef}
              takePhoto={takePhoto}
              stopCamera={stopCamera}
              isDarkMode={isDarkMode}
            />
          </div>

          {/* 示例提示 */}
          {!result && showFeatureImage && (
            <div
              className="description"
              style={{
                textAlign: 'center',
                fontSize: '15px',
                margin: '20px 0',
                padding: '15px',
                borderRadius: '8px',
                border: '1px dashed var(--border-color)',
              }}
            >
              <span style={{ fontSize: '18px', color: '#ff4d4f', marginRight: '6px' }}>ⓘ</span>{' '}
              这是示例分析结果，请上传您的图片或使用摄像头拍照进行分析
              <div className="englishDescription">
                This is an example result. Please upload your image or take a photo to analyze.
              </div>
            </div>
          )}

          {/* 错误提示 */}
          {error && (
            <Alert
              message={
                <>
                  <span style={{ fontSize: '16px', fontWeight: 500 }}>分析失败</span>{' '}
                  <span style={{ fontSize: '12px', opacity: 0.8 }}>Analysis Failed</span>
                </>
              }
              description={<div style={{ marginTop: '5px', fontSize: '14px' }}>{error}</div>}
              type="error"
              showIcon
              className="errorAlert"
            />
          )}

          {/* 分析结果标题 */}
          {result && result.success && (
            <>
              <div
                className="resultContainer"
                style={{
                  marginTop: '25px',
                  marginBottom: '15px',
                  fontSize: '18px',
                  fontWeight: 500,
                  borderBottom: '2px solid var(--primary-color)',
                  paddingBottom: '2px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  width: '100%',
                  position: 'relative',
                }}
              >
                <div style={{ width: '33%' }}></div>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'absolute',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    flexDirection: 'column',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      flexDirection: 'row',
                      whiteSpace: 'nowrap',
                      justifyContent: 'center',
                      width: '100%',
                    }}
                  >
                    <span style={{ fontSize: '16px' }}>分析结果</span>
                    <span
                      className="englishDescription"
                      style={{
                        marginLeft: '8px',
                        fontSize: '14px',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      Analysis Results
                    </span>
                  </div>
                  <div
                    className="englishDescription"
                    style={{
                      marginTop: '30px',
                      textAlign: 'center',
                      backgroundColor: 'rgba(0,0,0,0.05)',
                      padding: '2px 8px',
                      borderRadius: '4px',
                      fontSize: '13px',
                    }}
                  >
                    ID:{' '}
                    {result && result.id
                      ? // 如果是示例ID且是示例模式，显示示例ID
                        result.id === 'cxy889' &&
                        localStorage.getItem('using_example_data') === 'true'
                        ? 'cxy889'
                        : // 对于所有其他ID，显示前8位
                          result.id.length > 8
                          ? `${result.id.substring(0, 8)}...`
                          : result.id
                      : 'N/A'}
                  </div>
                </div>
                <div style={{ width: '33%' }}></div>
              </div>
            </>
          )}

          {/* 分析结果 */}
          {result && result.success && <EmotionResult result={result} isDarkMode={isDarkMode} />}

          {/* 多模态分析组件 - 放在音频情绪分析组件上方 */}
          {result && result.success && (
            <div
              className="multimodal-analysis-wrapper"
              style={{ marginTop: '20px', marginBottom: '20px' }}
            >
              <MultimodalAnalysis
                imageId={result?.id || null}
                isDarkMode={isDarkMode}
                analysisResult={result || undefined}
              />
            </div>
          )}

          {/* 调试信息 - 仅在开发环境显示 */}
          {process.env.NODE_ENV === 'development' && (
            <div style={{
              margin: '20px 0',
              padding: '10px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              backgroundColor: isDarkMode ? '#1f1f1f' : '#f5f5f5',
              fontSize: '12px'
            }}>
              <div><strong>调试信息:</strong></div>
              <div>result存在: {result ? '是' : '否'}</div>
              <div>result.success: {result?.success ? '是' : '否'}</div>
              <div>result.id: {result?.id || '无'}</div>
              <div>多模态组件显示: {result && result.success ? '是' : '否'}</div>
              {!result && <div style={{color: 'orange'}}>请先上传图片</div>}
              {result && !result.success && <div style={{color: 'orange'}}>请点击"开始分析"按钮</div>}
            </div>
          )}

          {/* 音频情绪分析组件 - 根据浏览器类型选择不同组件 */}
          <div
            className="audio-emotion-analysis-wrapper"
            style={{ marginTop: '20px', marginBottom: '10px' }}
          >
            {/* 使用更温和的组件隔离容器，避免强制重新挂载 */}
            <div
              style={{
                /* 移除可能影响多模态组件显示的属性 */
                /* isolation: 'isolate', // CSS 隔离 */
                /* contain: 'layout style', // 性能隔离 */
                position: 'relative', // 确保定位上下文
              }}
              className="audio-analysis-container"
            >
              {isWechatBrowser() ? (
                // 微信浏览器使用专用组件
                <WechatAudioEmotionAnalysis
                  key="wechat-audio-analysis"
                  isDarkMode={isDarkMode}
                  onAnalysisComplete={(result) => {
                    console.log('微信音频分析完成:', result);
                  }}
                />
              ) : (
                // 其他浏览器使用通用组件
                <AudioEmotionAnalysis
                  isDarkMode={isDarkMode}
                  key="stable-audio-analysis" // 使用稳定的key，避免不必要的重新挂载
                />
              )}
            </div>
          </div>

          {/* 音频朗读提示文字组件 - 仅在微信浏览器中显示 */}
          {isWechatBrowser() && (
            <div
              className="audio-prompt-text-wrapper"
              style={{ marginTop: '10px', marginBottom: '20px' }}
            >
              <AudioPromptText isDarkMode={isDarkMode} />
            </div>
          )}

          {/* 反馈功能模块 - 对整个分析流程进行反馈 */}
          {result && result.success && result.id && (
            <div
              className="feedback-wrapper"
              style={{
                marginTop: '10px',
                marginBottom: '20px',
                borderTop: '1px solid var(--border-color)',
                paddingTop: '15px',
              }}
            >
              <FeedbackSection
                key={`feedback-${result.id}`} // 使用稳定的key，避免不必要的重新挂载
                resultId={result.id}
                isDarkMode={isDarkMode}
                allowButtons={true}
                allowHistory={false}
              />
            </div>
          )}

          {/* 在分析结果存在时始终展示内部标注反馈表单 */}
          {result && result.id && (
            <div style={{ margin: '32px 0' }}>
              <h3 style={{ marginBottom: 12, fontSize: '16px' }}>
                标注反馈说明 Marking feedback instructions：
              </h3>
              <FeedbackInstructions
                cnParagraphs={[
                  '本项目的初衷是能精准识别长者情绪，进而能及时给予关注和照料，实现情绪价值。',
                  '您的简单标注，将用于情绪识别模型的训练，会大幅度提高长者情绪识别的精准性。',
                  '赠人玫瑰，手有余香！让我们共同体会长者之风，共同成就一片充满科技感的孝心！',
                ]}
                enParagraphs={[
                  'The original intention of this project is to accurately identify the emotions of the elderly, and then provide timely attention and care to realize the value of emotions.',
                  'Your simple annotations will be used for the training of the emotion recognition model and will significantly improve the accuracy of emotion recognition for the elderly.',
                  "A rose given to others leaves fragrance in your hand! Let's jointly experience the spirit of the elders and jointly create a filial piety full of a sense of technology!",
                ]}
              />
              <InternalFeedbackForm
                analysisId={result.id}
                imageUrl={result.original_image || ''}
                emotion={
                  result.faces && result.faces.length > 0
                    ? result.faces[0].dominant_emotion
                    : result.group_analysis?.dominant_emotion || ''
                }
              />
            </div>
          )}

          {/* 版权信息 */}
          <div
            className="copyright-info"
            style={{
              textAlign: 'center',
              fontSize: '14px',
              marginTop: '30px',
              padding: '15px 0',
              borderTop: '1px solid var(--border-color)',
              color: 'var(--text-color-secondary)',
            }}
          >
            <div
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                fontSize: '13px',
              }}
            >
              © {new Date().getFullYear()} 海南长小养智能科技有限责任公司
            </div>
            <div
              style={{
                fontSize: '12px',
                marginTop: '5px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              Hainan Changxiaoyang Intelligent Technology Co., Ltd.
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EmotionAnalysis;
