<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Android浏览器检测测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        
        .detection-result {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .detection-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .detection-item:last-child {
            border-bottom: none;
        }
        
        .detection-label {
            font-weight: 500;
            color: #333;
        }
        
        .detection-value {
            color: #1890ff;
            font-weight: bold;
        }
        
        .detection-value.true {
            color: #52c41a;
        }
        
        .detection-value.false {
            color: #ff4d4f;
        }
        
        .user-agent {
            background: #f6f8fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            word-break: break-all;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .css-classes {
            background: #fff2e8;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .class-tag {
            display: inline-block;
            background: #1890ff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            margin: 2px;
            font-size: 12px;
        }
        
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #40a9ff;
        }
        
        .instructions {
            background: #f6f8fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="title">Android浏览器检测测试</h2>
        
        <div class="detection-result" id="detectionResult">
            <h3>检测结果</h3>
            <div id="detectionItems"></div>
        </div>
        
        <div class="user-agent">
            <h4>User Agent:</h4>
            <div id="userAgent"></div>
        </div>
        
        <div class="css-classes">
            <h4>已添加的CSS类名:</h4>
            <div id="cssClasses"></div>
        </div>
        
        <div style="text-align: center;">
            <button class="test-button" onclick="runDetection()">重新检测</button>
            <button class="test-button" onclick="testManualClasses()">手动测试类名</button>
            <button class="test-button" onclick="clearClasses()">清除类名</button>
        </div>
        
        <div class="instructions">
            <h4>测试说明：</h4>
            <ul>
                <li><strong>isAndroid</strong>: 是否为Android设备</li>
                <li><strong>isAndroidWechat</strong>: 是否为Android微信浏览器</li>
                <li><strong>isAndroidBrowser</strong>: 是否为Android普通浏览器（非微信）</li>
                <li><strong>isChromeAndroid</strong>: 是否为Chrome for Android</li>
                <li><strong>isSamsungBrowser</strong>: 是否为Samsung Internet</li>
                <li><strong>isUCBrowser</strong>: 是否为UC Browser</li>
            </ul>
            
            <h4>预期行为：</h4>
            <ul>
                <li>在Android设备上，<code>isAndroid</code>应该为true</li>
                <li>如果是微信浏览器，应该添加<code>android-wechat</code>类名</li>
                <li>如果是普通Android浏览器，应该添加<code>android-browser</code>类名</li>
                <li>根据具体浏览器，应该添加相应的特定类名</li>
            </ul>
        </div>
    </div>

    <script>
        // 复制检测逻辑
        function detectAndroidBrowser() {
            const userAgent = navigator.userAgent;
            const isAndroid = /Android/i.test(userAgent);
            
            if (!isAndroid) {
                return {
                    isAndroid: false,
                    isAndroidWechat: false,
                    isAndroidBrowser: false,
                    isChromeAndroid: false,
                    isSamsungBrowser: false,
                    isUCBrowser: false,
                    browserName: 'Unknown',
                    deviceInfo: {},
                };
            }

            // 检测各种Android浏览器
            const isAndroidWechat = /MicroMessenger/i.test(userAgent) && isAndroid;
            const isChromeAndroid = /Chrome/i.test(userAgent) && isAndroid && !/MicroMessenger/i.test(userAgent);
            const isSamsungBrowser = /SamsungBrowser/i.test(userAgent);
            const isUCBrowser = /UCBrowser|UC Browser/i.test(userAgent);
            
            // 确定浏览器名称
            let browserName = 'Android Browser';
            if (isAndroidWechat) browserName = 'WeChat Browser';
            else if (isChromeAndroid) browserName = 'Chrome for Android';
            else if (isSamsungBrowser) browserName = 'Samsung Internet';
            else if (isUCBrowser) browserName = 'UC Browser';

            // 提取设备信息
            const deviceInfo = {};
            
            // 提取Android版本
            const androidVersionMatch = userAgent.match(/Android\s([0-9.]+)/);
            if (androidVersionMatch) {
                deviceInfo.version = androidVersionMatch[1];
            }

            // 提取设备型号（部分信息）
            const modelMatch = userAgent.match(/\(([^)]+)\)/);
            if (modelMatch) {
                deviceInfo.model = modelMatch[1];
            }

            return {
                isAndroid,
                isAndroidWechat,
                isAndroidBrowser: isAndroid && !isAndroidWechat,
                isChromeAndroid,
                isSamsungBrowser,
                isUCBrowser,
                browserName,
                deviceInfo,
            };
        }

        function addAndroidClasses(detection) {
            const htmlElement = document.documentElement;
            const bodyElement = document.body;
            
            // 移除之前的类名（如果有的话）
            const classesToRemove = [
                'android',
                'android-browser',
                'android-wechat',
                'chrome-android',
                'samsung-browser',
                'uc-browser',
                'android-device',
            ];
            
            classesToRemove.forEach(className => {
                htmlElement.classList.remove(className);
                if (bodyElement) bodyElement.classList.remove(className);
            });
            
            // 根据检测结果添加类名
            if (detection.isAndroid) {
                htmlElement.classList.add('android');
                if (bodyElement) bodyElement.classList.add('android');
                
                // 设置data属性
                htmlElement.setAttribute('data-android', 'true');
                
                if (detection.isAndroidWechat) {
                    htmlElement.classList.add('android-wechat');
                    if (bodyElement) bodyElement.classList.add('android-wechat');
                } else {
                    // 非微信的Android浏览器
                    htmlElement.classList.add('android-browser');
                    if (bodyElement) bodyElement.classList.add('android-browser');
                    
                    // 具体浏览器类型
                    if (detection.isChromeAndroid) {
                        htmlElement.classList.add('chrome-android');
                        if (bodyElement) bodyElement.classList.add('chrome-android');
                    } else if (detection.isSamsungBrowser) {
                        htmlElement.classList.add('samsung-browser');
                        if (bodyElement) bodyElement.classList.add('samsung-browser');
                    } else if (detection.isUCBrowser) {
                        htmlElement.classList.add('uc-browser');
                        if (bodyElement) bodyElement.classList.add('uc-browser');
                    }
                }
                
                // 添加通用Android设备类名
                htmlElement.classList.add('android-device');
                if (bodyElement) bodyElement.classList.add('android-device');
            }
        }

        function displayDetectionResults(detection) {
            // 显示检测结果
            const detectionItems = document.getElementById('detectionItems');
            detectionItems.innerHTML = '';
            
            const items = [
                { label: 'isAndroid', value: detection.isAndroid },
                { label: 'isAndroidWechat', value: detection.isAndroidWechat },
                { label: 'isAndroidBrowser', value: detection.isAndroidBrowser },
                { label: 'isChromeAndroid', value: detection.isChromeAndroid },
                { label: 'isSamsungBrowser', value: detection.isSamsungBrowser },
                { label: 'isUCBrowser', value: detection.isUCBrowser },
                { label: 'browserName', value: detection.browserName },
            ];
            
            items.forEach(item => {
                const div = document.createElement('div');
                div.className = 'detection-item';
                div.innerHTML = `
                    <span class="detection-label">${item.label}:</span>
                    <span class="detection-value ${typeof item.value === 'boolean' ? item.value : ''}">${item.value}</span>
                `;
                detectionItems.appendChild(div);
            });
            
            // 显示设备信息
            if (detection.deviceInfo.version || detection.deviceInfo.model) {
                const deviceDiv = document.createElement('div');
                deviceDiv.className = 'detection-item';
                deviceDiv.innerHTML = `
                    <span class="detection-label">设备信息:</span>
                    <span class="detection-value">Android ${detection.deviceInfo.version || 'Unknown'}</span>
                `;
                detectionItems.appendChild(deviceDiv);
            }
            
            // 显示User Agent
            document.getElementById('userAgent').textContent = navigator.userAgent;
            
            // 显示CSS类名
            const cssClassesDiv = document.getElementById('cssClasses');
            const htmlClasses = Array.from(document.documentElement.classList);
            const relevantClasses = htmlClasses.filter(cls => 
                ['android', 'android-browser', 'android-wechat', 'chrome-android', 'samsung-browser', 'uc-browser', 'android-device'].includes(cls)
            );
            
            if (relevantClasses.length > 0) {
                cssClassesDiv.innerHTML = relevantClasses.map(cls => 
                    `<span class="class-tag">${cls}</span>`
                ).join('');
            } else {
                cssClassesDiv.innerHTML = '<em>无相关CSS类名</em>';
            }
        }

        function runDetection() {
            const detection = detectAndroidBrowser();
            addAndroidClasses(detection);
            displayDetectionResults(detection);
            
            console.log('🤖 Android浏览器检测结果:', detection);
        }

        function testManualClasses() {
            // 手动添加测试类名
            const html = document.documentElement;
            html.classList.add('android', 'android-browser', 'chrome-android', 'android-device');
            html.setAttribute('data-android', 'true');
            
            // 更新显示
            const cssClassesDiv = document.getElementById('cssClasses');
            const htmlClasses = Array.from(html.classList);
            const relevantClasses = htmlClasses.filter(cls => 
                ['android', 'android-browser', 'android-wechat', 'chrome-android', 'samsung-browser', 'uc-browser', 'android-device'].includes(cls)
            );
            
            cssClassesDiv.innerHTML = relevantClasses.map(cls => 
                `<span class="class-tag">${cls}</span>`
            ).join('');
            
            alert('已手动添加测试类名，请检查环形图样式是否生效');
        }

        function clearClasses() {
            const html = document.documentElement;
            const body = document.body;
            
            const classesToRemove = [
                'android',
                'android-browser',
                'android-wechat',
                'chrome-android',
                'samsung-browser',
                'uc-browser',
                'android-device',
            ];
            
            classesToRemove.forEach(className => {
                html.classList.remove(className);
                if (body) body.classList.remove(className);
            });
            
            html.removeAttribute('data-android');
            
            // 更新显示
            document.getElementById('cssClasses').innerHTML = '<em>已清除所有类名</em>';
        }

        // 页面加载时自动运行检测
        document.addEventListener('DOMContentLoaded', runDetection);
    </script>
</body>
</html> 