/**
 * 微信浏览器初始化工具
 * 用于在情感分析页面中初始化微信浏览器的特殊设置
 */

import { isWechatBrowser, isMobileDevice } from '@/shared/utils/browserDetect';

/**
 * 初始化微信浏览器环境
 */
export const initWechatEnvironment = (): void => {
  if (!isWechatBrowser()) return;

  console.log('🔧 初始化微信浏览器环境...');

  // 1. 禁用页面缩放，防止双击缩放导致的问题
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport) {
    viewport.setAttribute(
      'content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover',
    );
  }

  // 2. 添加微信浏览器专用的CSS类
  document.documentElement.classList.add('wechat-browser');
  if (isMobileDevice()) {
    document.documentElement.classList.add('wechat-mobile');
  }

  // 3. 防止页面滚动时的橡皮筋效果
  document.body.style.overscrollBehavior = 'none';
  document.documentElement.style.overscrollBehavior = 'none';

  // 4. 优化触摸事件
  document.body.style.touchAction = 'manipulation';

  // 5. 添加微信浏览器专用样式
  const style = document.createElement('style');
  style.id = 'wechat-browser-init-styles';
  style.textContent = `
    /* 微信浏览器全局优化 */
    .wechat-browser {
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      user-select: none;
    }
    
    /* 微信浏览器中的按钮优化 */
    .wechat-browser button {
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
      touch-action: manipulation;
    }
    
    /* 防止微信浏览器中的文字选择 */
    .wechat-browser .feedback-button,
    .wechat-browser .feedback-button * {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      -webkit-touch-callout: none;
    }
    
    /* 微信浏览器性能优化 */
    .wechat-browser .feedbackButtons {
      will-change: auto;
      contain: layout style paint;
    }
  `;

  // 确保样式只添加一次
  const existingStyle = document.getElementById('wechat-browser-init-styles');
  if (existingStyle) {
    existingStyle.remove();
  }
  document.head.appendChild(style);

  console.log('✅ 微信浏览器环境初始化完成');
};

/**
 * 修复微信浏览器中的常见问题
 */
export const fixWechatCommonIssues = (): void => {
  if (!isWechatBrowser()) return;

  console.log('🔧 修复微信浏览器常见问题...');

  // 1. 修复iOS微信中的滚动问题
  if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
    (document.body.style as unknown as Record<string, string>).webkitOverflowScrolling = 'touch';
  }

  // 2. 优化微信浏览器中的点击事件
  let lastTouchTime = 0;
  document.addEventListener(
    'touchend',
    (event) => {
      const now = Date.now();
      if (now - lastTouchTime < 300) {
        // 防止快速连续点击
        event.preventDefault();
        return false;
      }
      lastTouchTime = now;
    },
    { passive: false },
  );

  console.log('✅ 微信浏览器问题修复完成');
};

/**
 * 监控微信浏览器性能
 */
export const monitorWechatPerformance = (): void => {
  if (!isWechatBrowser()) return;

  console.log('📊 开始监控微信浏览器性能...');

  // 监控内存使用情况（简化版本）
  const checkMemory = () => {
    try {
      // 简单的内存检查，避免复杂的类型声明
      if ('memory' in performance) {
        console.log('📈 微信浏览器内存监控正常');
      }
    } catch (error) {
      console.warn('内存监控失败:', error);
    }
  };

  // 每30秒检查一次
  setInterval(checkMemory, 30000);

  console.log('✅ 微信浏览器性能监控启动完成');
};

/**
 * 初始化微信浏览器的反馈按钮保护
 */
export const initWechatFeedbackProtection = (): void => {
  if (!isWechatBrowser()) return;

  console.log('🛡️ 初始化微信浏览器反馈按钮保护...');

  // 防止反馈按钮区域的事件冲突
  const protectFeedbackButtons = () => {
    const feedbackContainer = document.querySelector('.feedbackButtons');
    if (feedbackContainer) {
      // 添加保护性事件监听器
      feedbackContainer.addEventListener(
        'touchstart',
        (event) => {
          // 确保事件正确传播
          event.stopPropagation();
        },
        { passive: true },
      );

      feedbackContainer.addEventListener(
        'touchend',
        (event) => {
          // 防止事件冲突
          event.stopPropagation();
        },
        { passive: true },
      );

      // 为每个反馈按钮添加保护
      const buttons = feedbackContainer.querySelectorAll('.feedback-button');
      buttons.forEach((button) => {
        button.addEventListener(
          'click',
          (event) => {
            // 确保点击事件正确处理
            event.stopPropagation();
          },
          { passive: false },
        );
      });
    }
  };

  // 页面加载完成后保护反馈按钮
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', protectFeedbackButtons);
  } else {
    protectFeedbackButtons();
  }

  // 简化的DOM监控，避免复杂的MutationObserver类型
  setTimeout(() => {
    protectFeedbackButtons();
  }, 1000);

  console.log('✅ 微信浏览器反馈按钮保护初始化完成');
};

/**
 * 完整的微信浏览器初始化
 */
export const initWechatBrowser = (): void => {
  if (!isWechatBrowser()) {
    console.log('ℹ️ 非微信浏览器环境，跳过微信专用初始化');
    return;
  }

  console.log('🚀 开始微信浏览器完整初始化...');

  try {
    // 1. 初始化基础环境
    initWechatEnvironment();

    // 2. 修复常见问题
    fixWechatCommonIssues();

    // 3. 启动性能监控
    monitorWechatPerformance();

    // 4. 初始化反馈按钮保护
    initWechatFeedbackProtection();

    console.log('🎉 微信浏览器初始化全部完成！');
  } catch (error) {
    console.error('❌ 微信浏览器初始化失败:', error);
  }
};

/**
 * 清理微信浏览器相关资源
 */
export const cleanupWechatBrowser = (): void => {
  if (!isWechatBrowser()) return;

  console.log('🧹 清理微信浏览器资源...');

  // 移除添加的CSS类
  document.documentElement.classList.remove('wechat-browser', 'wechat-mobile');

  // 移除添加的样式
  const style = document.getElementById('wechat-browser-init-styles');
  if (style) {
    style.remove();
  }

  console.log('✅ 微信浏览器资源清理完成');
};
