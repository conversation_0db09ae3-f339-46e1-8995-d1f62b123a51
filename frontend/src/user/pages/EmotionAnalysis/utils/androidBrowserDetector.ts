/**
 * Android设备和浏览器类型检测工具
 * 用于为环形图修复添加相应的CSS类名
 */

interface AndroidDetectionResult {
  isAndroid: boolean;
  isAndroidWechat: boolean;
  isAndroidBrowser: boolean;
  isChromeAndroid: boolean;
  isSamsungBrowser: boolean;
  isUCBrowser: boolean;
  browserName: string;
  deviceInfo: {
    model?: string;
    version?: string;
  };
}

/**
 * 检测Android设备和浏览器类型
 */
export function detectAndroidBrowser(): AndroidDetectionResult {
  const userAgent = navigator.userAgent;
  const isAndroid = /Android/i.test(userAgent);

  if (!isAndroid) {
    return {
      isAndroid: false,
      isAndroidWechat: false,
      isAndroidBrowser: false,
      isChromeAndroid: false,
      isSamsungBrowser: false,
      isUCBrowser: false,
      browserName: 'Unknown',
      deviceInfo: {},
    };
  }

  // 检测各种Android浏览器
  const isAndroidWechat = /MicroMessenger/i.test(userAgent) && isAndroid;
  const isChromeAndroid =
    /Chrome/i.test(userAgent) && isAndroid && !/MicroMessenger/i.test(userAgent);
  const isSamsungBrowser = /SamsungBrowser/i.test(userAgent);
  const isUCBrowser = /UCBrowser|UC Browser/i.test(userAgent);

  // 确定浏览器名称
  let browserName = 'Android Browser';
  if (isAndroidWechat) browserName = 'WeChat Browser';
  else if (isChromeAndroid) browserName = 'Chrome for Android';
  else if (isSamsungBrowser) browserName = 'Samsung Internet';
  else if (isUCBrowser) browserName = 'UC Browser';

  // 提取设备信息
  const deviceInfo: { model?: string; version?: string } = {};

  // 提取Android版本
  const androidVersionMatch = userAgent.match(/Android\s([0-9.]+)/);
  if (androidVersionMatch) {
    deviceInfo.version = androidVersionMatch[1];
  }

  // 提取设备型号（部分信息）
  const modelMatch = userAgent.match(/\(([^)]+)\)/);
  if (modelMatch) {
    deviceInfo.model = modelMatch[1];
  }

  return {
    isAndroid,
    isAndroidWechat,
    isAndroidBrowser: isAndroid && !isAndroidWechat,
    isChromeAndroid,
    isSamsungBrowser,
    isUCBrowser,
    browserName,
    deviceInfo,
  };
}

/**
 * 为页面添加Android浏览器检测的CSS类名
 */
export function initAndroidBrowserDetection(): () => void {
  const detection = detectAndroidBrowser();

  // 获取html和body元素
  const htmlElement = document.documentElement;
  const bodyElement = document.body;

  // 移除之前的类名（如果有的话）
  const classesToRemove = [
    'android',
    'android-browser',
    'android-wechat',
    'chrome-android',
    'samsung-browser',
    'uc-browser',
    'android-device',
  ];

  classesToRemove.forEach((className) => {
    htmlElement.classList.remove(className);
    if (bodyElement) bodyElement.classList.remove(className);
  });

  // 根据检测结果添加类名
  if (detection.isAndroid) {
    htmlElement.classList.add('android');
    if (bodyElement) bodyElement.classList.add('android');

    // 设置data属性
    htmlElement.setAttribute('data-android', 'true');

    if (detection.isAndroidWechat) {
      htmlElement.classList.add('android-wechat');
      if (bodyElement) bodyElement.classList.add('android-wechat');
    } else {
      // 非微信的Android浏览器
      htmlElement.classList.add('android-browser');
      if (bodyElement) bodyElement.classList.add('android-browser');

      // 具体浏览器类型
      if (detection.isChromeAndroid) {
        htmlElement.classList.add('chrome-android');
        if (bodyElement) bodyElement.classList.add('chrome-android');
      } else if (detection.isSamsungBrowser) {
        htmlElement.classList.add('samsung-browser');
        if (bodyElement) bodyElement.classList.add('samsung-browser');
      } else if (detection.isUCBrowser) {
        htmlElement.classList.add('uc-browser');
        if (bodyElement) bodyElement.classList.add('uc-browser');
      }
    }

    // 添加通用Android设备类名
    htmlElement.classList.add('android-device');
    if (bodyElement) bodyElement.classList.add('android-device');
  }

  // 在开发环境中输出检测结果
  if (process.env.NODE_ENV === 'development') {
    console.log('🤖 Android浏览器检测结果:', {
      ...detection,
      addedClasses: Array.from(htmlElement.classList).filter((cls) =>
        [
          'android',
          'android-browser',
          'android-wechat',
          'chrome-android',
          'samsung-browser',
          'uc-browser',
        ].includes(cls),
      ),
    });
  }

  // 返回清理函数
  return () => {
    classesToRemove.forEach((className) => {
      htmlElement.classList.remove(className);
      if (bodyElement) bodyElement.classList.remove(className);
    });
    htmlElement.removeAttribute('data-android');
  };
}

/**
 * 检查当前是否为Android环境
 */
export function isAndroidEnvironment(): boolean {
  return detectAndroidBrowser().isAndroid;
}

/**
 * 检查当前是否为Android微信浏览器
 */
export function isAndroidWechatBrowser(): boolean {
  return detectAndroidBrowser().isAndroidWechat;
}

/**
 * 检查当前是否为Android普通浏览器（非微信）
 */
export function isAndroidNormalBrowser(): boolean {
  return detectAndroidBrowser().isAndroidBrowser;
}

/**
 * 获取详细的Android设备信息
 */
export function getAndroidDeviceInfo(): AndroidDetectionResult {
  return detectAndroidBrowser();
}
