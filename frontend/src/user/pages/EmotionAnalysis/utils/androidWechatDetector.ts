/**
 * 安卓微信浏览器检测器
 * 自动检测并为安卓微信浏览器添加特殊的CSS类名
 */

import {
  isAndroidWechatBrowser,
  isIOSWechatBrowser,
  isWechatBrowser,
} from '@/shared/utils/browserDetect';

/**
 * 初始化安卓微信浏览器检测
 * 为不同的微信浏览器平台添加对应的CSS类名
 */
export const initAndroidWechatDetection = (): void => {
  // 确保在浏览器环境中运行
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return;
  }

  const addClassNames = () => {
    const htmlElement = document.documentElement;
    const bodyElement = document.body;

    // 移除之前可能添加的类名
    htmlElement.classList.remove('wechat-browser', 'android-wechat', 'ios-wechat');
    bodyElement.classList.remove('wechat-browser', 'android-wechat', 'ios-wechat');

    // 检测微信浏览器类型并添加对应类名
    if (isAndroidWechatBrowser()) {
      // 安卓微信浏览器
      htmlElement.classList.add('wechat-browser', 'android-wechat');
      bodyElement.classList.add('wechat-browser', 'android-wechat');
    } else if (isIOSWechatBrowser()) {
      // iOS微信浏览器
      htmlElement.classList.add('wechat-browser', 'ios-wechat');
      bodyElement.classList.add('wechat-browser', 'ios-wechat');
    } else if (isWechatBrowser()) {
      // 其他微信浏览器
      htmlElement.classList.add('wechat-browser');
      bodyElement.classList.add('wechat-browser');
    }
  };

  // 立即执行一次
  addClassNames();

  // 确保DOM加载完成后再执行一次
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addClassNames);
  } else {
    // DOM已经加载完成，延迟执行确保所有元素都已渲染
    setTimeout(addClassNames, 100);
  }

  // 监听页面可见性变化，确保在微信浏览器中切换回来时类名仍然存在
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      setTimeout(addClassNames, 50);
    }
  });
};

/**
 * 获取当前微信浏览器类型
 * @returns {string} 微信浏览器类型
 */
export const getWechatBrowserType = (): string => {
  if (isAndroidWechatBrowser()) {
    return 'android-wechat';
  } else if (isIOSWechatBrowser()) {
    return 'ios-wechat';
  } else if (isWechatBrowser()) {
    return 'wechat-browser';
  }
  return 'normal-browser';
};

/**
 * 检查是否需要应用安卓微信浏览器修复
 * @returns {boolean} 是否需要应用修复
 */
export const needsAndroidWechatFix = (): boolean => {
  return isAndroidWechatBrowser();
};

/**
 * 强制刷新CSS类名（用于调试）
 */
export const refreshWechatBrowserClasses = (): void => {
  initAndroidWechatDetection();
};
