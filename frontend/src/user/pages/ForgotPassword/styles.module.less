.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f0f2f5;
  padding: 8px;
}

.card {
  width: 100%;
  max-width: 340px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 0;
}

.title {
  margin: 0 !important;
  color: #1890ff;
}

.description {
  text-align: center;
  font-size: 13px;
  color: rgba(0, 0, 0, 0.65);
  margin: 0;
  padding: 0;
}

.compactForm {
  :global(.ant-form-item) {
    margin-bottom: 6px;
  }
  
  :global(.ant-form-item-explain) {
    min-height: 16px;
    font-size: 12px;
  }
}

.submitButton {
  height: 32px;
}

.footer {
  margin-top: 4px;
  text-align: center;
  font-size: 12px;
}

.successMessage {
  text-align: center;
  padding: 4px 0;
}

.resendLink {
  padding: 0;
  height: auto;
}
