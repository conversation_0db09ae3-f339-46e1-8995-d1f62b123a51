import React, { useState } from 'react';
import { Form, Input, Button, message, Typography, Card, Space } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constants/routes';
import { authApi } from '@/shared/api/auth';
// 尝试使用相对路径导入，以排除路径别名解析或TS服务器缓存问题
import type { ApiResponse } from '../../types';
import styles from './styles.module.less';

const { Title, Paragraph, Text } = Typography;

const ForgotPassword: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (values: { email: string }) => {
    try {
      setLoading(true);
      // 尽管TypeScript可能推断为AxiosResponse, 但由于拦截器, 实际运行时应该是ApiResponse的内容
      const apiResult = (await authApi.forgotPassword(values.email)) as unknown as ApiResponse<{
        message: string;
      }>;

      // apiResult 的类型现在被断言为 ApiResponse<{ message: string; }>
      // 其结构是 { code: number; message: string; data: { message: string; } }
      // 我们关心的是外层的 message, 即后端 CommonResponse 直接映射的 message
      if (apiResult && apiResult.message) {
        setEmailSent(true); // 这个会切换到页面提示的UI
        // message.success(apiResult.message || '重置密码邮件已发送，请查收邮箱'); // 用户要求去除弹窗提示，只保留页面提示
      }
    } catch (error: any) {
      console.error('发送重置密码邮件失败:', error);
      message.error('发送重置密码邮件失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <Card
        className={styles.card}
        size="small"
        variant="borderless"
        bodyStyle={{ padding: '12px' }}
      >
        {/* 将 bordered={false} 替换为 variant="borderless" */}
        {!emailSent ? (
          <>
            <Title
              level={5}
              className={styles.title}
              style={{ marginBottom: 8, textAlign: 'center' }}
            >
              忘记密码
            </Title>
            <Paragraph className={styles.description} style={{ marginBottom: 8, marginTop: 0 }}>
              请输入邮箱地址接收重置链接
            </Paragraph>

            <Form
              form={form}
              name="forgot-password"
              onFinish={handleSubmit}
              layout="vertical"
              requiredMark={false}
              size="middle"
              className={styles.compactForm}
            >
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: '请输入您的邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' },
                ]}
                style={{ marginBottom: 8 }}
              >
                <Input prefix={<UserOutlined />} placeholder="邮箱" autoComplete="email" />
              </Form.Item>

              <Space direction="vertical" style={{ width: '100%', marginBottom: 0 }}>
                {loading && (
                  <Paragraph
                    type="secondary"
                    style={{ textAlign: 'center', marginBottom: '8px', fontSize: '12px' }}
                  >
                    邮件发送中，过程可能需要一些时间，请耐心等待，请勿重复点击或离开本页面。
                  </Paragraph>
                )}
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  className={styles.submitButton}
                  block
                  size="middle"
                >
                  发送重置链接
                </Button>

                <div className={styles.footer}>
                  <Text>
                    记起密码了？ <Link to={ROUTES.LOGIN}>返回登录</Link>
                  </Text>
                </div>
              </Space>
            </Form>
          </>
        ) : (
          <div className={styles.successMessage}>
            <Title level={5} style={{ marginBottom: 4 }}>
              邮件已发送
            </Title>
            <Paragraph style={{ fontSize: '13px', marginBottom: 4, marginTop: 0 }}>
              已发送重置链接到您的邮箱
            </Paragraph>
            <Space>
              <Text type="secondary" style={{ fontSize: '13px' }}>
                未收到邮件？
              </Text>
              <Button
                type="link"
                onClick={() => setEmailSent(false)}
                className={styles.resendLink}
                size="small"
              >
                重新发送
              </Button>
            </Space>

            <div className={styles.footer} style={{ marginTop: 16 }}>
              <Link to={ROUTES.LOGIN}>返回登录</Link>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default ForgotPassword;
