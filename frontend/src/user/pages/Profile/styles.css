/* 个人信息页面样式 */

/* 通用样式 */
.profile-info-container {
  margin-bottom: 24px;
  padding: 16px;
  border-radius: 4px;
}

/* 亮色模式样式 */
html[data-theme='light'] .profile-info-container {
  background: #f5f5f5;
  color: rgba(0, 0, 0, 0.85);
  border: 1px solid #e8e8e8;
}

html[data-theme='light'] .profile-info-title {
  color: rgba(0, 0, 0, 0.85);
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
  margin-bottom: 16px;
}

html[data-theme='light'] .profile-info-item {
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.65);
}

html[data-theme='light'] .profile-info-item strong {
  color: rgba(0, 0, 0, 0.85);
}

/* 暗黑模式样式 */
html[data-theme='dark'] .profile-info-container {
  background: #1f1f1f;
  color: rgba(255, 255, 255, 0.85);
  border: 1px solid #303030;
}

html[data-theme='dark'] .profile-info-title {
  color: rgba(255, 255, 255, 0.85);
  border-bottom: 1px solid #303030;
  padding-bottom: 8px;
  margin-bottom: 16px;
}

html[data-theme='dark'] .profile-info-item {
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.65);
}

html[data-theme='dark'] .profile-info-item strong {
  color: rgba(255, 255, 255, 0.85);
}

/* 加载状态样式 */
.loading-container {
  text-align: center;
  padding: 20px;
}

html[data-theme='dark'] .loading-container {
  color: rgba(255, 255, 255, 0.85);
}

html[data-theme='light'] .loading-container {
  color: rgba(0, 0, 0, 0.85);
}

/* 错误提示样式 */
.error-container {
  text-align: center;
  padding: 20px;
  margin-bottom: 24px;
  border-radius: 4px;
}

html[data-theme='light'] .error-container {
  background: #fffbe6;
  border: 1px solid #ffe58f;
}

html[data-theme='dark'] .error-container {
  background: #2b2111;
  border: 1px solid #614700;
  color: rgba(255, 255, 255, 0.85);
}

html[data-theme='light'] .error-title {
  font-size: 16px;
  color: #fa8c16;
  margin-bottom: 10px;
}

html[data-theme='dark'] .error-title {
  font-size: 16px;
  color: #ffa940;
  margin-bottom: 10px;
}

/* 密码修改部分样式 */
.password-section {
  margin-top: 24px;
}

html[data-theme='light'] .password-section-title {
  color: rgba(0, 0, 0, 0.85);
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
  margin-bottom: 16px;
}

html[data-theme='dark'] .password-section-title {
  color: rgba(255, 255, 255, 0.85);
  border-bottom: 1px solid #303030;
  padding-bottom: 8px;
  margin-bottom: 16px;
}

/* 暗黑模式下的表单控件样式 */
html[data-theme='dark'] .ant-form-item-label > label {
  color: rgba(255, 255, 255, 0.85);
}

html[data-theme='dark'] .ant-input,
html[data-theme='dark'] .ant-input-password,
html[data-theme='dark'] .ant-input-password input {
  background-color: #141414;
  border-color: #434343;
  color: rgba(255, 255, 255, 0.85);
}

html[data-theme='dark'] .ant-input:hover,
html[data-theme='dark'] .ant-input-password:hover {
  border-color: #165996;
}

html[data-theme='dark'] .ant-input:focus,
html[data-theme='dark'] .ant-input-password:focus,
html[data-theme='dark'] .ant-input-focused,
html[data-theme='dark'] .ant-input-password-focused {
  border-color: #177ddc;
  box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
}

html[data-theme='dark'] .ant-input-password-icon {
  color: rgba(255, 255, 255, 0.45);
}

html[data-theme='dark'] .ant-input-password-icon:hover {
  color: rgba(255, 255, 255, 0.85);
}

/* 暗黑模式下的输入框占位符颜色 */
html[data-theme='dark'] .ant-input::placeholder,
html[data-theme='dark'] .ant-input-password input::placeholder {
  color: rgba(255, 255, 255, 0.25);
}
