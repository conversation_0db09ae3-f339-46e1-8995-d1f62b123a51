import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Typography, Card, Space } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { ROUTES } from '@/constants/routes';
import { authApi } from '@/shared/api/auth';
import styles from './styles.module.less';

const { Title, Paragraph, Text } = Typography;

const ResetPassword: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [resetSuccess, setResetSuccess] = useState(false);
  const [token, setToken] = useState<string>('');
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // 从URL参数中获取token
    const queryParams = new URLSearchParams(location.search);
    const tokenParam = queryParams.get('token');

    if (tokenParam) {
      setToken(tokenParam);
    } else {
      message.error('无效的密码重置链接');
      navigate(ROUTES.LOGIN);
    }
  }, [location, navigate]);

  const handleSubmit = async (values: { password: string; confirmPassword: string }) => {
    if (values.password !== values.confirmPassword) {
      message.error('两次输入的密码不一致');
      return;
    }

    try {
      setLoading(true);
      const response = await authApi.resetPassword(token, values.password);

      if (response.data) {
        setResetSuccess(true);
        message.success('密码重置成功');
        // 3秒后自动跳转到登录页
        setTimeout(() => {
          navigate(ROUTES.LOGIN);
        }, 3000);
      }
    } catch (error: any) {
      console.error('重置密码失败:', error);
      message.error('重置密码失败，可能链接已过期或无效');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <Card className={styles.card} size="small" bordered={false} bodyStyle={{ padding: '12px' }}>
        {!resetSuccess ? (
          <>
            <Title
              level={5}
              className={styles.title}
              style={{ marginBottom: 8, textAlign: 'center' }}
            >
              重置密码
            </Title>
            <Paragraph className={styles.description} style={{ marginBottom: 8, marginTop: 0 }}>
              请设置新密码
            </Paragraph>

            <Form
              form={form}
              name="reset-password"
              onFinish={handleSubmit}
              layout="vertical"
              requiredMark={false}
              size="middle"
              className={styles.compactForm}
            >
              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入新密码' },
                  { min: 8, message: '密码长度不能少于8个字符' },
                ]}
                style={{ marginBottom: 8 }}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="新密码"
                  autoComplete="new-password"
                />
              </Form.Item>
              <Paragraph
                type="secondary"
                style={{
                  fontSize: '12px',
                  marginTop: '-4px',
                  marginBottom: '12px',
                  paddingLeft: '2px',
                }}
              >
                密码长度至少8位，必须包含大小写字母和数字。
              </Paragraph>
              <Form.Item
                name="confirmPassword"
                dependencies={['password']}
                rules={[
                  { required: true, message: '请确认新密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
                style={{ marginBottom: 8 }}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="确认新密码"
                  autoComplete="new-password"
                />
              </Form.Item>

              <Space direction="vertical" style={{ width: '100%', marginBottom: 0 }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  className={styles.submitButton}
                  block
                  size="middle"
                >
                  重置密码
                </Button>

                <div className={styles.footer}>
                  <Link to={ROUTES.LOGIN}>返回登录</Link>
                </div>
              </Space>
            </Form>
          </>
        ) : (
          <div className={styles.successMessage}>
            <Title level={5} style={{ marginBottom: 4 }}>
              密码重置成功
            </Title>
            <Paragraph style={{ fontSize: '13px', marginBottom: 4, marginTop: 0 }}>
              密码已重置成功，正在跳转...
            </Paragraph>

            <div className={styles.footer} style={{ marginTop: 16 }}>
              <Link to={ROUTES.LOGIN}>返回登录</Link>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default ResetPassword;
