/**
 * 浏览器检测工具
 * 用于检测当前浏览器环境，特别是微信浏览器
 */

/**
 * 检测是否为微信浏览器
 * @returns {boolean} 是否为微信浏览器
 */
export const isWechatBrowser = (): boolean => {
  const ua = navigator.userAgent.toLowerCase();
  return ua.indexOf('micromessenger') !== -1;
};

/**
 * 检测是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
export const isMobileDevice = (): boolean => {
  const ua = navigator.userAgent.toLowerCase();
  return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(ua);
};

/**
 * 检测是否为iOS设备
 * @returns {boolean} 是否为iOS设备
 */
export const isIOSDevice = (): boolean => {
  const ua = navigator.userAgent.toLowerCase();
  return /iphone|ipad|ipod/i.test(ua);
};

/**
 * 检测是否为Android设备
 * @returns {boolean} 是否为Android设备
 */
export const isAndroidDevice = (): boolean => {
  const ua = navigator.userAgent.toLowerCase();
  return /android/i.test(ua);
};

/**
 * 获取微信JSSDK对象
 * @returns {any} 微信JSSDK对象
 */
export const getWechatJSSDK = (): any => {
  if (typeof window !== 'undefined' && (window as any).wechatJSSDK) {
    return (window as any).wechatJSSDK;
  }
  return null;
};

/**
 * 检查微信JSSDK是否已加载
 * @returns {boolean} 微信JSSDK是否已加载
 */
export const isWechatJSSDKLoaded = (): boolean => {
  return typeof window !== 'undefined' && !!(window as any).wx;
};

/**
 * 等待微信JSSDK准备就绪
 * @param {number} timeout 超时时间（毫秒）
 * @returns {Promise<boolean>} 是否准备就绪
 */
export const waitForWechatJSSDK = (timeout: number = 5000): Promise<boolean> => {
  return new Promise((resolve) => {
    // 如果不是微信浏览器，直接返回false
    if (!isWechatBrowser()) {
      resolve(false);
      return;
    }

    // 如果JSSDK已加载，直接返回true
    if (isWechatJSSDKLoaded()) {
      resolve(true);
      return;
    }

    // 设置超时
    const timeoutId = setTimeout(() => {
      window.removeEventListener('wx-jssdk-ready', handleReady);
      resolve(false);
    }, timeout);

    // 监听JSSDK准备就绪事件
    const handleReady = () => {
      clearTimeout(timeoutId);
      window.removeEventListener('wx-jssdk-ready', handleReady);
      resolve(true);
    };

    window.addEventListener('wx-jssdk-ready', handleReady);
  });
};
