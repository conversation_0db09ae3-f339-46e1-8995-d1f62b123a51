/* 微信浏览器顶部导航栏修复 */

/* 修复微信浏览器中顶部导航栏按钮溢出问题 */
body.wx-browser .header,
html.wechat-browser .header,
body.wx-browser .wechat-header-fix,
html.wechat-browser .wechat-header-fix {
  width: 100% !important;
  max-width: 100% !important;
  padding: 0 10px !important;
  box-sizing: border-box !important;
  background: transparent !important; /* 微信浏览器中使用透明背景 */
  box-shadow: none !important; /* 微信浏览器中移除阴影 */
  border-bottom: none !important; /* 微信浏览器中移除底部边框 */
  border-radius: 0 !important; /* 微信浏览器中移除圆角 */
}

/* 调整 logo 大小 */
body.wx-browser .logo,
html.wechat-browser .logo,
.wechat-header-fix .logo {
  font-size: 14px !important;
  max-width: 120px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: flex !important;
  flex-direction: column !important;
  line-height: 1.2 !important;
}

/* 调整 logo 英文部分 */
body.wx-browser .logoEnglish,
html.wechat-browser .logoEnglish,
.wechat-header-fix .logoEnglish {
  font-size: 10px !important;
  opacity: 0.8 !important;
  margin-top: 1px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 调整图标按钮大小和间距 */
body.wx-browser .iconButton,
html.wechat-browser .iconButton,
.wechat-header-fix .iconButton {
  width: 28px !important;
  height: 28px !important;
  font-size: 14px !important;
  margin: 0 2px !important;
  padding: 0 !important;
}

/* 调整按钮间距 */
body.wx-browser .headerRight .ant-space,
html.wechat-browser .headerRight .ant-space,
.wechat-header-fix .headerRight .ant-space {
  gap: 4px !important;
}

/* 确保所有内容都在视口内 */
body.wx-browser,
html.wechat-browser body {
  overflow-x: hidden !important;
  max-width: 100vw !important;
}

/* 微信浏览器中的特殊处理 */
@media screen and (max-width: 576px) {
  body.wx-browser .header,
  html.wechat-browser .header,
  .wechat-header-fix {
    height: auto !important;
    min-height: 50px !important;
    padding: 5px 8px !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
  }

  body.wx-browser .headerLeft,
  html.wechat-browser .headerLeft,
  .wechat-header-fix .headerLeft {
    max-width: 40% !important;
    flex: 0 0 40% !important;
  }

  body.wx-browser .headerRight,
  html.wechat-browser .headerRight,
  .wechat-header-fix .headerRight {
    max-width: 60% !important;
    flex: 0 0 60% !important;
    display: flex !important;
    justify-content: flex-end !important;
    align-items: center !important;
  }

  /* 更小的图标按钮 */
  body.wx-browser .iconButton,
  html.wechat-browser .iconButton,
  .wechat-header-fix .iconButton {
    width: 24px !important;
    height: 24px !important;
    font-size: 12px !important;
    margin: 0 1px !important;
    padding: 0 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }

  /* 更小的间距 */
  body.wx-browser .headerRight .ant-space,
  html.wechat-browser .headerRight .ant-space,
  .wechat-header-fix .headerRight .ant-space {
    gap: 2px !important;
    display: flex !important;
    justify-content: flex-end !important;
    align-items: center !important;
    width: 100% !important;
  }

  /* 更小的头像 */
  body.wx-browser .avatar,
  html.wechat-browser .avatar,
  .wechat-header-fix .avatar {
    width: 20px !important;
    height: 20px !important;
    font-size: 12px !important;
    line-height: 20px !important;
    min-width: 20px !important;
    min-height: 20px !important;
  }

  /* 强制所有图标在一行显示 */
  body.wx-browser .headerRight .ant-space-item,
  html.wechat-browser .headerRight .ant-space-item,
  .wechat-header-fix .headerRight .ant-space-item {
    flex: 0 0 auto !important;
    width: auto !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* 强制 Tooltip 不影响布局 */
  .ant-tooltip {
    max-width: 120px !important;
  }
}
