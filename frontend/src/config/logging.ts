/**
 * 前端日志配置
 * 用于控制不同环境下的日志输出级别
 */

export interface LoggingConfig {
  enableDebugLogs: boolean;
  enableStreamLogs: boolean;
  enablePerformanceLogs: boolean;
  enableDeviceLogs: boolean;
  progressLogThreshold: number;
}

// 根据环境变量确定日志配置
const getLoggingConfig = (): LoggingConfig => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isProduction = process.env.NODE_ENV === 'production';

  // 生产环境配置
  if (isProduction) {
    return {
      enableDebugLogs: false,
      enableStreamLogs: false,
      enablePerformanceLogs: false,
      enableDeviceLogs: false,
      progressLogThreshold: 5000, // 生产环境中极少记录进度
    };
  }

  // 开发环境配置
  if (isDevelopment) {
    return {
      enableDebugLogs: true,
      enableStreamLogs: false, // 即使在开发环境中也禁用流日志
      enablePerformanceLogs: true,
      enableDeviceLogs: true,
      progressLogThreshold: 1000,
    };
  }

  // 默认配置（测试环境等）
  return {
    enableDebugLogs: false,
    enableStreamLogs: false,
    enablePerformanceLogs: false,
    enableDeviceLogs: false,
    progressLogThreshold: 2000,
  };
};

export const LOGGING_CONFIG = getLoggingConfig();

// 导出便捷方法
export const shouldLog = {
  debug: () => LOGGING_CONFIG.enableDebugLogs,
  stream: () => LOGGING_CONFIG.enableStreamLogs,
  performance: () => LOGGING_CONFIG.enablePerformanceLogs,
  device: () => LOGGING_CONFIG.enableDeviceLogs,
};
