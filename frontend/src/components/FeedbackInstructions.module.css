:root {
  --feedback-bg: #fff;
  --feedback-cn: #222;
  --feedback-en: #666;
}

[data-theme='dark'] {
  --feedback-bg: #232323;
  --feedback-cn: #eee;
  --feedback-en: #bbb;
}

.container {
  width: 90%;
  margin: 0 auto;
  padding: 2em 1em;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: var(--feedback-bg);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);
}

.cnSection, .enSection {
  width: 100%;
}

.cnSection {
  margin-bottom: 2em;
}

.cnText {
  font-size: 0.7rem;
  color: #52c41a;
  line-height: 1.8;
  text-align: left;
  margin: 0.5em 0;
  font-family: 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

[data-theme='dark'] .cnText {
  color: #3eeb34 !important;
  text-shadow: 0 0 1px rgba(62, 235, 52, 0.5) !important;
}

.enText {
  font-size: 0.6rem;
  color: var(--feedback-en);
  line-height: 1.3;
  text-align: left;
  margin: 0.2em 0;
  font-family: 'Noto Sans', 'Arial', sans-serif;
}

@media (max-width: 600px) {
  .container {
    width: 100%;
    padding: 1em 0.5em;
    border-radius: 8px;
  }
  .cnText, .enText {
    font-size: 0.8rem;
    line-height: 1.7;
  }
  .cnSection {
    margin-bottom: 1.2em;
  }
  .enText {
    font-size: 0.6rem;
  }
} 