import React from 'react';
import styles from './FeedbackInstructions.module.css';

interface FeedbackInstructionsProps {
  cnParagraphs: string[];
  enParagraphs: string[];
}

/**
 * 情绪分析页面反馈说明组件
 *
 * @param cnParagraphs 中文段落数组
 * @param enParagraphs 英文段落数组
 */
const FeedbackInstructions: React.FC<FeedbackInstructionsProps> = ({ cnParagraphs, enParagraphs }) => {
  return (
    <div className={styles.container}>
      <div className={styles.cnSection}>
        {cnParagraphs.map((text, idx) => (
          <p className={styles.cnText} key={idx}>{text}</p>
        ))}
      </div>
      <div className={styles.enSection}>
        {enParagraphs.map((text, idx) => (
          <p className={styles.enText} key={idx}>{text}</p>
        ))}
      </div>
    </div>
  );
};

export default FeedbackInstructions; 