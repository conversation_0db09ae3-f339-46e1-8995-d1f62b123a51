/**
 * 全局移动端样式优化
 * 用于提升移动端浏览器的显示效果和用户体验
 */

/* ===== 基础移动端优化 ===== */

/* 移动端视口优化 */
@media screen and (max-width: 768px) {
  html {
    /* 防止iOS Safari地址栏影响100vh */
    height: -webkit-fill-available;
    /* 优化字体渲染 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    /* 防止横屏时字体缩放 */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  body {
    /* 使用实际可用高度 */
    min-height: -webkit-fill-available;
    /* 优化滚动性能 */
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    /* 防止水平滚动 */
    overflow-x: hidden;
    max-width: 100vw;
    /* 优化触摸响应 */
    touch-action: manipulation;
  }

  /* 全局盒模型优化 */
  *,
  *::before,
  *::after {
    box-sizing: border-box;
    /* 防止元素超出屏幕宽度 */
    max-width: 100vw;
  }
}

/* ===== 触摸优化 ===== */

/* 移除默认触摸高亮 */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/* 可点击元素的触摸优化 */
button,
[role='button'],
.clickable,
.ant-btn,
.ant-menu-item,
.ant-dropdown-menu-item {
  touch-action: manipulation;
  cursor: pointer;
  /* 防止双击缩放 */
  user-select: none;
  -webkit-user-select: none;
}

/* 输入元素保持可选择 */
input,
textarea,
[contenteditable] {
  -webkit-user-select: auto;
  user-select: auto;
  touch-action: manipulation;
}

/* ===== 移动端布局优化 ===== */

@media screen and (max-width: 768px) {
  /* 容器优化 */
  .container,
  .ant-layout,
  .ant-layout-content {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }

  /* 卡片优化 */
  .ant-card {
    margin-bottom: 12px !important;
    border-radius: 8px !important;
  }

  .ant-card-body {
    padding: 12px !important;
  }

  /* 表单优化 */
  .ant-form-item {
    margin-bottom: 16px !important;
  }

  .ant-input,
  .ant-input-affix-wrapper,
  .ant-select-selector {
    height: 44px !important;
    font-size: 16px !important; /* 防止iOS缩放 */
  }

  /* 按钮优化 */
  .ant-btn {
    height: 44px !important;
    min-width: 44px !important;
    font-size: 16px !important;
    border-radius: 8px !important;
  }

  .ant-btn-lg {
    height: 52px !important;
    font-size: 18px !important;
  }

  .ant-btn-sm {
    height: 36px !important;
    font-size: 14px !important;
  }
}

/* ===== 小屏幕设备优化 ===== */

@media screen and (max-width: 576px) {
  /* 更紧凑的布局 */
  .container,
  .ant-layout-content {
    padding-left: 4px !important;
    padding-right: 4px !important;
  }

  .ant-card-body {
    padding: 8px !important;
  }

  /* 字体大小调整 */
  h1 {
    font-size: 24px !important;
  }
  h2 {
    font-size: 20px !important;
  }
  h3 {
    font-size: 18px !important;
  }
  h4 {
    font-size: 16px !important;
  }
  h5 {
    font-size: 14px !important;
  }
  h6 {
    font-size: 12px !important;
  }

  /* 表格优化 */
  .ant-table {
    font-size: 12px !important;
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 8px 4px !important;
  }
}

/* ===== 超小屏幕设备优化 ===== */

@media screen and (max-width: 480px) {
  /* 极简布局 */
  .container,
  .ant-layout-content {
    padding: 2px !important;
  }

  .ant-card {
    border-radius: 4px !important;
    margin-bottom: 8px !important;
  }

  .ant-card-body {
    padding: 6px !important;
  }

  /* 按钮尺寸调整 */
  .ant-btn {
    height: 40px !important;
    font-size: 14px !important;
  }

  /* 输入框尺寸调整 */
  .ant-input,
  .ant-input-affix-wrapper,
  .ant-select-selector {
    height: 40px !important;
    font-size: 14px !important;
  }
}

/* ===== 微信浏览器特殊优化 ===== */

.wechat-browser {
  /* 微信浏览器特殊处理 */
  -webkit-text-size-adjust: 100% !important;
}

.wechat-browser body {
  /* 微信浏览器滚动优化 */
  -webkit-overflow-scrolling: touch !important;
  overflow-x: hidden !important;
  max-width: 100vw !important;
  position: relative !important;
}

/* 微信浏览器中的图片优化 */
.wechat-browser img {
  /* 防止图片变形 */
  max-width: 100% !important;
  height: auto !important;
  /* 优化图片渲染 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
}

/* ===== 性能优化 ===== */

/* 硬件加速 */
.hardware-accelerated,
.ant-progress-circle,
.ant-spin,
.ant-modal,
.ant-drawer {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  will-change: transform;
}

/* 滚动容器优化 */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  will-change: scroll-position;
  transform: translateZ(0);
}

/* 图片懒加载优化 */
img[data-src] {
  opacity: 0;
  transition: opacity 0.3s ease;
}

img[data-src].loaded {
  opacity: 1;
}

/* ===== 网络状态优化 ===== */

/* 低带宽模式 */
.low-bandwidth-mode * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

.low-bandwidth-mode img {
  filter: blur(0.5px);
}

/* ===== 可访问性优化 ===== */

/* 焦点指示器 */
@media screen and (max-width: 768px) {
  button:focus,
  input:focus,
  textarea:focus,
  select:focus,
  .ant-btn:focus,
  .ant-input:focus {
    outline: 2px solid #1890ff;
    outline-offset: 2px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .ant-btn {
    border-width: 2px !important;
  }

  .ant-input,
  .ant-select-selector {
    border-width: 2px !important;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== 调试辅助 ===== */

/* 开发环境下的移动端调试 */
.mobile-debug-info {
  position: fixed;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;
  pointer-events: none;
  font-family: monospace;
}

/* 触摸区域可视化（调试用） */
.touch-debug .clickable::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 0, 0, 0.2);
  pointer-events: none;
  border: 1px solid red;
}
