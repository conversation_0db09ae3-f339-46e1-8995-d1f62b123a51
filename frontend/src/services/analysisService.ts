import { adminApi } from '@/admin/services/api';
import type {
  EmotionAnalysis,
  EmotionAnalysisListResponse,
  EmotionAnalysisListParams,
  ApiResponse as GenericApiResponse,
} from '../types/emotionAnalysis.d';

const API_BASE_PATH = '/admin/analysis';

/**
 * Fetches a list of emotion analysis records.
 * @param params - Parameters for pagination, sorting, and filtering.
 * @returns A promise that resolves to the list of emotion analysis records and total count.
 */
export const getEmotionAnalyses = async (
  params?: EmotionAnalysisListParams,
): Promise<EmotionAnalysisListResponse> => {
  try {
    const response = await adminApi.get<EmotionAnalysisListResponse>(`${API_BASE_PATH}/`, {
      params: params,
    });
    if (response && response.data) {
      if ('items' in response.data && 'total' in response.data) {
        return response.data as EmotionAnalysisListResponse;
      }
      // 使用更具体的类型替代any
      interface ApiResponseWrapper {
        data?: {
          items: EmotionAnalysis[];
          total: number;
        };
      }
      const potentialData = response.data as ApiResponseWrapper;
      if (potentialData.data && 'items' in potentialData.data && 'total' in potentialData.data) {
        return potentialData.data as EmotionAnalysisListResponse;
      }
    }
    console.error('Invalid response format from getEmotionAnalyses:', response);
    throw new Error('获取情绪分析数据格式错误');
  } catch (error) {
    console.error('Error fetching emotion analyses:', error);
    throw error;
  }
};

/**
 * Fetches a single emotion analysis record by its ID.
 * @param id - The UUID of the emotion analysis record.
 * @returns A promise that resolves to the emotion analysis record.
 */
export const getEmotionAnalysisById = async (id: string): Promise<EmotionAnalysis> => {
  try {
    const response = await adminApi.get<EmotionAnalysis>(`${API_BASE_PATH}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching emotion analysis with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Deletes an emotion analysis record by its ID (soft delete).
 * @param id - The UUID of the emotion analysis record to delete.
 * @returns A promise that resolves to a generic API response indicating success or failure.
 */
export const deleteEmotionAnalysis = async (id: string): Promise<GenericApiResponse> => {
  try {
    const response = await adminApi.delete<GenericApiResponse>(`${API_BASE_PATH}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting emotion analysis with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Batch deletes multiple emotion analysis records by their IDs.
 * @param ids - Array of UUIDs of the emotion analysis records to delete.
 * @returns A promise that resolves to a generic API response indicating success or failure.
 */
export const batchDeleteEmotionAnalyses = async (ids: string[]): Promise<GenericApiResponse> => {
  try {
    const response = await adminApi.delete<GenericApiResponse>(`${API_BASE_PATH}/batch/delete`, {
      data: ids, // 请求体中传递ID数组
    });
    return response.data;
  } catch (error) {
    console.error(`Error batch deleting ${ids.length} emotion analyses:`, error);
    throw error;
  }
};

// Example of how you might add a create or update function if needed in the future
/*
export interface EmotionAnalysisCreatePayload {
  // Define fields needed to create an emotion analysis record
  user_id?: string;
  session_id?: string;
  input_type: 'text' | 'audio' | 'video';
  content_url?: string;
  text_content?: string;
  emotion: string;
  intensity?: number;
  source_language?: string;
  metadata?: Record<string, any>;
}

export const createEmotionAnalysis = async (
  payload: EmotionAnalysisCreatePayload
): Promise<EmotionAnalysis> => {
  try {
    const response = await adminApi.post<EmotionAnalysis>(`${API_BASE_PATH}/`, payload);
    return response.data;
  } catch (error) {
    console.error('Error creating emotion analysis:', error);
    throw error;
  }
};
*/
