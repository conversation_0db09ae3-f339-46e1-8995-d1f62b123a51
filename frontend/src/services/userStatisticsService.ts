import axios from 'axios';

// 创建专门用于统计的axios实例，避免全局拦截器影响
const statisticsRequest = axios.create({
  baseURL: window.location.origin,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 添加请求拦截器
statisticsRequest.interceptors.request.use(
  (config) => {
    // 添加认证token
    const adminToken = localStorage.getItem('admin_token');
    if (adminToken) {
      config.headers.Authorization = `Bearer ${adminToken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// 添加响应拦截器，但不包含重定向逻辑
statisticsRequest.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 只记录错误，不触发重定向
    console.error('Statistics API Error:', error);
    return Promise.reject(error);
  },
);

// 用户统计数据接口
export interface UserStatistics {
  username: string;
  upload_count: number;
  annotation_count: number;
  period: string;
}

// 统计查询参数接口
export interface UserStatisticsQuery {
  username?: string;
  period: 'day' | 'week' | 'month';
  start_date?: string;
  end_date?: string;
}

// 统计响应接口
export interface UserStatisticsResponse {
  data: UserStatistics[];
  total_users: number;
  total_uploads: number;
  total_annotations: number;
}

/**
 * 获取用户活动统计数据
 */
export const getUserStatistics = async (
  params: UserStatisticsQuery,
): Promise<UserStatisticsResponse> => {
  try {
    const response = await statisticsRequest.get<UserStatisticsResponse>(
      '/api/v1/admin/user-statistics/',
      {
        params,
      },
    );
    return response.data;
  } catch (error) {
    console.error('获取用户统计数据失败:', error);
    throw error;
  }
};
