import { adminApi } from '@/admin/services/api';

export interface User {
  id: string;
  username: string;
  email: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at: string;
}

export interface GetUsersResponse {
  total: number;
  items: User[];
}

/**
 * 获取用户列表
 */
export const getUsers = async (params?: {
  skip?: number;
  limit?: number;
}): Promise<GetUsersResponse> => {
  try {
    const response = await adminApi.get('/admin/users', { params });
    return response.data;
  } catch (error: unknown) {
    console.error('获取用户列表错误:', error);
    const err = error as { response?: { data?: { message?: string } } };
    if (err.response) {
      throw new Error(err.response.data?.message || '获取用户列表失败');
    }
    throw new Error('网络错误，请稍后重试');
  }
};

/**
 * 根据用户名查找用户ID
 */
export const findUserIdByUsername = async (username: string): Promise<string | null> => {
  try {
    // 获取所有用户列表
    const response = await getUsers({ limit: 100 }); // 假设用户数量不会太多

    // 查找匹配的用户
    const user = response.items.find(
      (user) => user.username.toLowerCase() === username.toLowerCase(),
    );

    return user ? user.id : null;
  } catch (error) {
    console.error('查找用户ID错误:', error);
    return null;
  }
};
