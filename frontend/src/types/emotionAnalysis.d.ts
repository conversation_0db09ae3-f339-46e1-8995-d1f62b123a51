/**
 * TypeScript definitions for Emotion Analysis.
 * These should correspond to the backend Pydantic models.
 */

/**
 * Represents a single emotion feedback record.
 * Corresponds to the backend's EmotionFeedback schema.
 * 
 * 系统支持两种反馈类型：
 * 1. 评价反馈 (rating字段): 用户对分析结果的主观评价 - good(好评)/average(一般)/bad(不好)
 * 2. 标注反馈 (emotion字段): 用户对情绪的具体标注 - happy/sad/angry/fear/surprise/disgust/neutral/contempt
 *    标注反馈还可以包含人口统计学信息：姓名、性别、年龄
 */
export interface EmotionFeedback {
  id: string; // UUID
  analysis_id: string; // UUID
  emotion?: string | null; // 标注反馈：8种具体情绪标注（happy, sad, angry, fear, surprise, disgust, neutral, contempt）
  rating?: string | null; // 评价反馈：用户主观评价（good=好评, average=一般, bad=不好）
  comment?: string | null;
  // 标注反馈的人口统计学信息
  name?: string | null; // 姓名
  gender?: string | null; // 性别
  age?: number | null; // 年龄
  created_by?: string | null; // UUID
  created_at: string; // ISO 8601 datetime string
  updated_at: string; // ISO 8601 datetime string
  username?: string; // 反馈用户的用户名
}

/**
 * Represents a single emotion analysis record.
 * Corresponds to the backend's EmotionAnalysisDB schema.
 */
export interface EmotionAnalysis {
  id: string; // UUID
  user_id?: string | null; // UUID, optional if analysis is anonymous
  session_id?: string | null;
  input_type: 'text' | 'audio' | 'video' | 'IMAGE';
  content_url?: string | null; // URL to the media file if input_type is audio/video
  text_content?: string | null; // Text content if input_type is text
  primary_emotion: string; // 主要情绪
  confidence1: number; // 主要情绪的置信度
  secondary_emotion?: string | null; // 次要情绪
  confidence2?: number | null; // 次要情绪的置信度
  third_emotion?: string | null; // 第三情绪
  confidence3?: number | null; // 第三情绪的置信度
  intensity?: number | null;
  created_at: string; // ISO 8601 datetime string
  updated_at: string; // ISO 8601 datetime string
  deleted_at?: string | null; // ISO 8601 datetime string, for soft delete
  // Add any other fields that are part of EmotionAnalysisDB
  source_language?: string | null;
  translated_text?: string | null;
  analysis_version?: string | null;
  metadata?: Record<string, any> | null;
  username?: string;
  image_thumbnail_url?: string;
  emotion_details?: Record<string, number> | null; // 情绪详情，包含各种情绪的置信度
  // 反馈相关字段
  feedbacks?: EmotionFeedback[]; // 该分析记录的所有反馈
  feedback_count?: number; // 反馈数量
}

/**
 * Represents the response for a list of emotion analysis records.
 * Corresponds to the backend's EmotionAnalysisListResponse schema.
 */
export interface EmotionAnalysisListResponse {
  items: EmotionAnalysis[];
  total: number;
  // Potentially other pagination fields like page, size, pages if your backend provides them
}

/**
 * Parameters for fetching a list of emotion analysis records.
 */
export interface EmotionAnalysisListParams {
  skip?: number;
  limit?: number;
  sort?: string; // e.g., "created_at_desc"
  filter_by?: string; // e.g., "emotion:Happy,user_id:some-uuid"
}

/**
 * Generic API response structure, if needed for wrapping service responses.
 * This mirrors the backend's ApiResponse schema.
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: Record<string, string[]>;
}
