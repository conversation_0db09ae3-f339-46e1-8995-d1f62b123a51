/**
 * 移动端触摸手势优化工具
 * 用于处理移动设备上的触摸手势和交互
 */

import { isMobileDevice, isIOSDevice, isAndroidDevice } from './responsiveUtils';

/**
 * 触摸手势类型
 */
export type GestureType = 'tap' | 'doubletap' | 'longpress' | 'swipe' | 'pinch' | 'pan';

/**
 * 触摸手势配置
 */
interface GestureConfig {
  tapThreshold?: number;
  doubleTapDelay?: number;
  longPressDelay?: number;
  swipeThreshold?: number;
  pinchThreshold?: number;
  panThreshold?: number;
}

/**
 * 触摸事件信息
 */
interface TouchEventInfo {
  type: GestureType;
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  deltaX: number;
  deltaY: number;
  distance: number;
  duration: number;
  scale?: number;
  velocity?: number;
}

/**
 * 触摸手势回调函数
 */
type GestureCallback = (event: TouchEventInfo) => void;

/**
 * 默认手势配置
 */
const DEFAULT_CONFIG: Required<GestureConfig> = {
  tapThreshold: 10,
  doubleTapDelay: 300,
  longPressDelay: 500,
  swipeThreshold: 50,
  pinchThreshold: 10,
  panThreshold: 10,
};

/**
 * 触摸手势管理器
 */
class TouchGestureManager {
  private element: HTMLElement;
  private config: Required<GestureConfig>;
  private callbacks: Map<GestureType, GestureCallback[]> = new Map();
  private touchStartTime = 0;
  private touchStartX = 0;
  private touchStartY = 0;
  private lastTapTime = 0;
  private longPressTimer: number | null = null;
  private initialDistance = 0;
  private initialScale = 1;
  private isGestureActive = false;

  constructor(element: HTMLElement, config: GestureConfig = {}) {
    this.element = element;
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.init();
  }

  /**
   * 初始化触摸事件监听
   */
  private init(): void {
    if (!isMobileDevice()) return;

    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), {
      passive: false, // 需要 preventDefault，所以保持 passive: false
    });
    this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false }); // 需要preventDefault，所以保持passive: false
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
    this.element.addEventListener('touchcancel', this.handleTouchCancel.bind(this), {
      passive: true,
    });

    // 禁用默认的触摸行为
    this.element.style.touchAction = 'none';
    this.element.style.userSelect = 'none';
    this.element.style.webkitUserSelect = 'none';
  }

  /**
   * 处理触摸开始事件
   */
  private handleTouchStart(event: TouchEvent): void {
    const touch = event.touches[0];
    this.touchStartTime = Date.now();
    this.touchStartX = touch.clientX;
    this.touchStartY = touch.clientY;
    this.isGestureActive = true;

    // 处理多点触摸（捏合手势）
    if (event.touches.length === 2) {
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      this.initialDistance = this.getDistance(touch1, touch2);
      this.initialScale = 1;
    }

    // 设置长按定时器
    this.longPressTimer = window.setTimeout(() => {
      if (this.isGestureActive) {
        this.triggerGesture('longpress', {
          startX: this.touchStartX,
          startY: this.touchStartY,
          endX: this.touchStartX,
          endY: this.touchStartY,
        });
      }
    }, this.config.longPressDelay);
  }

  /**
   * 处理触摸移动事件
   */
  private handleTouchMove(event: TouchEvent): void {
    if (!this.isGestureActive) return;

    const touch = event.touches[0];
    const deltaX = touch.clientX - this.touchStartX;
    const deltaY = touch.clientY - this.touchStartY;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // 如果移动距离超过阈值，取消长按
    if (distance > this.config.tapThreshold && this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 处理捏合手势
    if (event.touches.length === 2) {
      event.preventDefault();
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      const currentDistance = this.getDistance(touch1, touch2);
      const scale = currentDistance / this.initialDistance;

      if (Math.abs(scale - this.initialScale) > this.config.pinchThreshold / 100) {
        this.triggerGesture('pinch', {
          startX: this.touchStartX,
          startY: this.touchStartY,
          endX: touch.clientX,
          endY: touch.clientY,
          scale,
        });
        this.initialScale = scale;
      }
    }

    // 处理拖拽手势
    if (event.touches.length === 1 && distance > this.config.panThreshold) {
      this.triggerGesture('pan', {
        startX: this.touchStartX,
        startY: this.touchStartY,
        endX: touch.clientX,
        endY: touch.clientY,
      });
    }
  }

  /**
   * 处理触摸结束事件
   */
  private handleTouchEnd(event: TouchEvent): void {
    if (!this.isGestureActive) return;

    const touch = event.changedTouches[0];
    const endTime = Date.now();
    const duration = endTime - this.touchStartTime;
    const deltaX = touch.clientX - this.touchStartX;
    const deltaY = touch.clientY - this.touchStartY;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    this.isGestureActive = false;

    // 清除长按定时器
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 判断手势类型
    if (distance < this.config.tapThreshold) {
      // 点击或双击
      const timeSinceLastTap = endTime - this.lastTapTime;
      if (timeSinceLastTap < this.config.doubleTapDelay) {
        this.triggerGesture('doubletap', {
          startX: this.touchStartX,
          startY: this.touchStartY,
          endX: touch.clientX,
          endY: touch.clientY,
        });
      } else {
        this.triggerGesture('tap', {
          startX: this.touchStartX,
          startY: this.touchStartY,
          endX: touch.clientX,
          endY: touch.clientY,
        });
      }
      this.lastTapTime = endTime;
    } else if (distance > this.config.swipeThreshold) {
      // 滑动手势
      const velocity = distance / duration;
      this.triggerGesture('swipe', {
        startX: this.touchStartX,
        startY: this.touchStartY,
        endX: touch.clientX,
        endY: touch.clientY,
        velocity,
      });
    }
  }

  /**
   * 处理触摸取消事件
   */
  private handleTouchCancel(): void {
    this.isGestureActive = false;
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  /**
   * 计算两点之间的距离
   */
  private getDistance(touch1: Touch, touch2: Touch): number {
    const deltaX = touch2.clientX - touch1.clientX;
    const deltaY = touch2.clientY - touch1.clientY;
    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
  }

  /**
   * 触发手势事件
   */
  private triggerGesture(type: GestureType, data: Partial<TouchEventInfo>): void {
    const callbacks = this.callbacks.get(type);
    if (!callbacks || callbacks.length === 0) return;

    const eventInfo: TouchEventInfo = {
      type,
      startX: data.startX || 0,
      startY: data.startY || 0,
      endX: data.endX || 0,
      endY: data.endY || 0,
      deltaX: (data.endX || 0) - (data.startX || 0),
      deltaY: (data.endY || 0) - (data.startY || 0),
      distance: Math.sqrt(
        Math.pow((data.endX || 0) - (data.startX || 0), 2) +
          Math.pow((data.endY || 0) - (data.startY || 0), 2),
      ),
      duration: Date.now() - this.touchStartTime,
      scale: data.scale,
      velocity: data.velocity,
    };

    callbacks.forEach((callback) => callback(eventInfo));
  }

  /**
   * 添加手势监听器
   */
  public on(type: GestureType, callback: GestureCallback): void {
    if (!this.callbacks.has(type)) {
      this.callbacks.set(type, []);
    }
    this.callbacks.get(type)!.push(callback);
  }

  /**
   * 移除手势监听器
   */
  public off(type: GestureType, callback?: GestureCallback): void {
    const callbacks = this.callbacks.get(type);
    if (!callbacks) return;

    if (callback) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    } else {
      this.callbacks.set(type, []);
    }
  }

  /**
   * 销毁手势管理器
   */
  public destroy(): void {
    this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this));
    this.element.removeEventListener('touchmove', this.handleTouchMove.bind(this));
    this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this));
    this.element.removeEventListener('touchcancel', this.handleTouchCancel.bind(this));

    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
    }

    this.callbacks.clear();
  }
}

/**
 * 创建触摸手势管理器
 */
export const createGestureManager = (
  element: HTMLElement,
  config?: GestureConfig,
): TouchGestureManager => {
  return new TouchGestureManager(element, config);
};

/**
 * 优化移动端点击响应
 */
export const optimizeMobileClick = (element: HTMLElement): void => {
  if (!isMobileDevice()) return;

  // 移除点击延迟
  element.style.touchAction = 'manipulation';

  // iOS特殊处理
  if (isIOSDevice()) {
    (element.style as any).webkitTapHighlightColor = 'transparent';
    (element.style as any).webkitTouchCallout = 'none';
  }

  // Android特殊处理
  if (isAndroidDevice()) {
    element.addEventListener('touchstart', () => {}, { passive: true });
  }
};

/**
 * 防止移动端页面滚动
 */
export const preventMobileScroll = (element: HTMLElement): void => {
  if (!isMobileDevice()) return;

  element.addEventListener(
    'touchmove',
    (event) => {
      event.preventDefault();
    },
    { passive: false },
  );
};

export { TouchGestureManager, type GestureConfig, type TouchEventInfo, type GestureCallback };
