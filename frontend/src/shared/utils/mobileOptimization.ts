/**
 * 移动端性能优化工具
 * 用于提升移动端浏览器的性能和用户体验
 */

import { isWechatBrowser, isMobileDevice, isIOSDevice, isAndroidDevice } from './responsiveUtils';

/**
 * 移动端优化配置接口
 */
interface MobileOptimizationConfig {
  enableFastClick?: boolean;
  enableImageLazyLoad?: boolean;
  enableTouchOptimization?: boolean;
  enableScrollOptimization?: boolean;
  enableMemoryOptimization?: boolean;
  enableNetworkOptimization?: boolean;
}

/**
 * 默认移动端优化配置
 */
const DEFAULT_CONFIG: MobileOptimizationConfig = {
  enableFastClick: false, // 保持禁用，避免与Image组件冲突
  enableImageLazyLoad: true,
  enableTouchOptimization: true, // 重新启用，但会排除Image组件
  enableScrollOptimization: true,
  enableMemoryOptimization: true, // 重新启用内存优化
  enableNetworkOptimization: true, // 重新启用网络优化
};

/**
 * 移动端优化管理器
 */
class MobileOptimizationManager {
  private config: MobileOptimizationConfig;
  private initialized = false;
  private observers: IntersectionObserver[] = [];
  private touchStartTime = 0;
  private lastScrollTime = 0;
  private scrollThrottle = 16; // 60fps

  constructor(config: MobileOptimizationConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * 初始化移动端优化
   */
  public init(): void {
    if (this.initialized || !isMobileDevice()) {
      return;
    }

    console.log('初始化移动端优化...');

    if (this.config.enableFastClick) {
      this.initFastClick();
    }

    if (this.config.enableImageLazyLoad) {
      this.initImageLazyLoad();
    }

    if (this.config.enableTouchOptimization) {
      this.initTouchOptimization();
    }

    if (this.config.enableScrollOptimization) {
      this.initScrollOptimization();
    }

    if (this.config.enableMemoryOptimization) {
      this.initMemoryOptimization();
    }

    if (this.config.enableNetworkOptimization) {
      this.initNetworkOptimization();
    }

    this.initialized = true;
    console.log('移动端优化初始化完成');
  }

  /**
   * 初始化快速点击优化
   */
  private initFastClick(): void {
    // 移除300ms点击延迟
    document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
    document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
  }

  /**
   * 处理触摸开始事件
   */
  private handleTouchStart(event: TouchEvent): void {
    this.touchStartTime = Date.now();
  }

  /**
   * 处理触摸结束事件
   */
  private handleTouchEnd(event: TouchEvent): void {
    const touchEndTime = Date.now();
    const touchDuration = touchEndTime - this.touchStartTime;

    // 如果触摸时间很短，认为是快速点击
    if (touchDuration < 150) {
      const target = event.target as HTMLElement;

      // 检查是否是Ant Design Image组件相关的元素
      let currentElement = target;
      let isImageComponent = false;

      while (currentElement && currentElement !== document.body) {
        if (
          currentElement.classList &&
          (currentElement.classList.contains('ant-image') ||
            currentElement.classList.contains('ant-image-preview') ||
            currentElement.classList.contains('ant-image-preview-img') ||
            currentElement.classList.contains('ant-image-mask') ||
            currentElement.classList.contains('ant-image-preview-mask') ||
            currentElement.classList.contains('ant-image-preview-wrap') ||
            currentElement.classList.contains('ant-image-preview-operations'))
        ) {
          isImageComponent = true;
          break;
        }
        currentElement = currentElement.parentElement as HTMLElement;
      }

      // 如果不是Image组件相关的元素，才进行快速点击处理
      if (!isImageComponent && target && target.click) {
        event.preventDefault();
        target.click();
      }
    }
  }

  /**
   * 初始化图片懒加载
   */
  private initImageLazyLoad(): void {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
              }
            }
          });
        },
        {
          rootMargin: '50px 0px',
          threshold: 0.1,
        },
      );

      this.observers.push(imageObserver);

      // 观察所有带有data-src属性的图片
      document.querySelectorAll('img[data-src]').forEach((img) => {
        imageObserver.observe(img);
      });
    }
  }

  /**
   * 初始化触摸优化
   */
  private initTouchOptimization(): void {
    // 禁用iOS的弹性滚动
    if (isIOSDevice()) {
      (document.body.style as any).webkitOverflowScrolling = 'touch';
    }

    // 优化触摸响应
    const style = document.createElement('style');
    style.textContent = `
      * {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
      }

      /* 恢复Ant Design Image组件的交互能力 */
      .ant-image,
      .ant-image *,
      .ant-image-preview,
      .ant-image-preview *,
      .ant-image-preview-img,
      .ant-image-preview-mask,
      .ant-image-preview-wrap,
      .ant-image-preview-operations,
      .ant-image-mask {
        -webkit-user-select: auto !important;
        user-select: auto !important;
        -webkit-touch-callout: auto !important;
        touch-action: auto !important;
        pointer-events: auto !important;
      }

      input, textarea, [contenteditable] {
        -webkit-user-select: auto;
        user-select: auto;
      }

      button, [role="button"], .clickable {
        touch-action: manipulation;
        cursor: pointer;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 初始化滚动优化
   */
  private initScrollOptimization(): void {
    // 节流滚动事件
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          this.lastScrollTime = Date.now();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    // 优化滚动性能
    const style = document.createElement('style');
    style.textContent = `
      * {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
      }

      .scroll-container {
        will-change: scroll-position;
        transform: translateZ(0);
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 初始化内存优化
   */
  private initMemoryOptimization(): void {
    // 定期清理不需要的DOM元素
    setInterval(() => {
      this.cleanupUnusedElements();
    }, 30000); // 每30秒清理一次

    // 监听内存警告
    if ('memory' in performance) {
      const checkMemory = () => {
        const memInfo = (performance as any).memory;
        if (memInfo && memInfo.usedJSHeapSize > memInfo.totalJSHeapSize * 0.8) {
          console.warn('内存使用率过高，建议刷新页面');
          this.triggerGarbageCollection();
        }
      };

      setInterval(checkMemory, 10000); // 每10秒检查一次
    }
  }

  /**
   * 初始化网络优化
   */
  private initNetworkOptimization(): void {
    // 预加载关键资源
    this.preloadCriticalResources();

    // 监听网络状态变化
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;

      const handleConnectionChange = () => {
        const effectiveType = connection.effectiveType;
        console.log('网络状态变化:', effectiveType);

        // 根据网络状态调整策略
        if (effectiveType === 'slow-2g' || effectiveType === '2g') {
          this.enableLowBandwidthMode();
        } else {
          this.disableLowBandwidthMode();
        }
      };

      connection.addEventListener('change', handleConnectionChange);
      handleConnectionChange(); // 初始检查
    }
  }

  /**
   * 清理不需要的DOM元素
   */
  private cleanupUnusedElements(): void {
    // 清理隐藏的图片
    const hiddenImages = document.querySelectorAll('img[style*="display: none"]');
    hiddenImages.forEach((img) => {
      if (img.parentNode) {
        img.parentNode.removeChild(img);
      }
    });

    // 清理空的容器
    const emptyContainers = document.querySelectorAll('div:empty, span:empty');
    emptyContainers.forEach((container) => {
      if (container.parentNode && !container.hasAttribute('data-keep')) {
        container.parentNode.removeChild(container);
      }
    });
  }

  /**
   * 触发垃圾回收
   */
  private triggerGarbageCollection(): void {
    // 清理事件监听器
    this.observers.forEach((observer) => observer.disconnect());
    this.observers = [];

    // 强制垃圾回收（如果支持）
    if ('gc' in window) {
      (window as any).gc();
    }
  }

  /**
   * 预加载关键资源
   */
  private preloadCriticalResources(): void {
    const criticalResources = ['/api/v1/auth/check', '/static/css/critical.css'];

    criticalResources.forEach((url) => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = url;
      document.head.appendChild(link);
    });
  }

  /**
   * 启用低带宽模式
   */
  private enableLowBandwidthMode(): void {
    console.log('启用低带宽模式');

    // 降低图片质量
    const images = document.querySelectorAll('img');
    images.forEach((img) => {
      if (img.src && !img.dataset.originalSrc) {
        img.dataset.originalSrc = img.src;
        // 可以在这里替换为低质量版本的图片
      }
    });

    // 禁用动画
    const style = document.createElement('style');
    style.id = 'low-bandwidth-style';
    style.textContent = `
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 禁用低带宽模式
   */
  private disableLowBandwidthMode(): void {
    console.log('禁用低带宽模式');

    // 恢复原始图片
    const images = document.querySelectorAll('img[data-original-src]');
    images.forEach((img) => {
      const imgElement = img as HTMLImageElement;
      if (imgElement.dataset.originalSrc) {
        imgElement.src = imgElement.dataset.originalSrc;
        delete imgElement.dataset.originalSrc;
      }
    });

    // 移除低带宽样式
    const lowBandwidthStyle = document.getElementById('low-bandwidth-style');
    if (lowBandwidthStyle) {
      lowBandwidthStyle.remove();
    }
  }

  /**
   * 销毁优化管理器
   */
  public destroy(): void {
    this.observers.forEach((observer) => observer.disconnect());
    this.observers = [];
    this.initialized = false;
  }
}

// 创建全局实例
const mobileOptimization = new MobileOptimizationManager();

// 自动初始化
if (typeof window !== 'undefined' && isMobileDevice()) {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => mobileOptimization.init());
  } else {
    mobileOptimization.init();
  }
}

export default mobileOptimization;
export { MobileOptimizationManager, type MobileOptimizationConfig };
