/**
 * 移动端测试工具
 * 用于测试和验证移动端功能的正常工作
 */

import {
  isMobileDevice,
  isIOSDevice,
  isAndroidDevice,
  isWechatBrowser,
  isTouchDevice,
  getNetworkInfo,
  getDeviceType,
  getBrowserType,
} from './responsiveUtils';

import { getWechatVersion, isWechatFeatureSupported } from './wechatCompat';

/**
 * 测试结果接口
 */
interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  details?: any;
}

/**
 * 设备信息接口
 */
interface DeviceInfo {
  userAgent: string;
  platform: string;
  deviceType: string;
  browserType: string;
  screenSize: string;
  pixelRatio: number;
  orientation: string;
  touchSupport: boolean;
  networkInfo: any;
  wechatVersion?: string;
}

/**
 * 移动端测试套件
 */
class MobileTestSuite {
  private results: TestResult[] = [];

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<TestResult[]> {
    console.log('开始运行移动端测试套件...');

    this.results = [];

    // 基础功能测试
    this.testDeviceDetection();
    this.testScreenInfo();
    this.testTouchSupport();
    this.testNetworkInfo();

    // 微信浏览器测试
    if (isWechatBrowser()) {
      this.testWechatFeatures();
    }

    // 性能测试
    await this.testPerformance();

    // 功能测试
    await this.testMediaAccess();

    console.log('移动端测试完成，结果:', this.results);
    return this.results;
  }

  /**
   * 测试设备检测功能
   */
  private testDeviceDetection(): void {
    const tests = [
      {
        name: '移动设备检测',
        test: () => typeof isMobileDevice() === 'boolean',
        message: `移动设备: ${isMobileDevice()}`,
      },
      {
        name: 'iOS设备检测',
        test: () => typeof isIOSDevice() === 'boolean',
        message: `iOS设备: ${isIOSDevice()}`,
      },
      {
        name: 'Android设备检测',
        test: () => typeof isAndroidDevice() === 'boolean',
        message: `Android设备: ${isAndroidDevice()}`,
      },
      {
        name: '微信浏览器检测',
        test: () => typeof isWechatBrowser() === 'boolean',
        message: `微信浏览器: ${isWechatBrowser()}`,
      },
    ];

    tests.forEach(({ name, test, message }) => {
      this.addResult(name, test(), message);
    });
  }

  /**
   * 测试屏幕信息
   */
  private testScreenInfo(): void {
    const tests = [
      {
        name: '屏幕尺寸获取',
        test: () => window.innerWidth > 0 && window.innerHeight > 0,
        message: `屏幕尺寸: ${window.innerWidth}x${window.innerHeight}`,
      },
      {
        name: '像素比获取',
        test: () => window.devicePixelRatio > 0,
        message: `像素比: ${window.devicePixelRatio}`,
      },
      {
        name: '设备类型识别',
        test: () => getDeviceType().length > 0,
        message: `设备类型: ${getDeviceType()}`,
      },
      {
        name: '浏览器类型识别',
        test: () => getBrowserType().length > 0,
        message: `浏览器类型: ${getBrowserType()}`,
      },
    ];

    tests.forEach(({ name, test, message }) => {
      this.addResult(name, test(), message);
    });
  }

  /**
   * 测试触摸支持
   */
  private testTouchSupport(): void {
    const touchSupported = isTouchDevice();
    const maxTouchPoints = navigator.maxTouchPoints || 0;

    this.addResult(
      '触摸支持检测',
      touchSupported,
      `触摸支持: ${touchSupported}, 最大触摸点: ${maxTouchPoints}`,
    );

    // 测试触摸事件
    if (touchSupported) {
      const testElement = document.createElement('div');
      let touchEventSupported = false;

      try {
        testElement.addEventListener(
          'touchstart',
          () => {
            touchEventSupported = true;
          },
          { passive: true },
        );

        // 模拟触摸事件
        const touchEvent = new TouchEvent('touchstart', {
          touches: [],
          targetTouches: [],
          changedTouches: [],
        });

        testElement.dispatchEvent(touchEvent);

        this.addResult('触摸事件支持', true, '触摸事件创建和分发正常');
      } catch (error) {
        this.addResult('触摸事件支持', false, `触摸事件测试失败: ${error}`);
      }
    }
  }

  /**
   * 测试网络信息
   */
  private testNetworkInfo(): void {
    const networkInfo = getNetworkInfo();

    this.addResult(
      '网络信息获取',
      networkInfo.type !== 'unknown',
      `网络类型: ${networkInfo.type}, 有效类型: ${networkInfo.effectiveType || '未知'}`,
    );

    // 测试在线状态
    this.addResult(
      '在线状态检测',
      typeof navigator.onLine === 'boolean',
      `在线状态: ${navigator.onLine}`,
    );
  }

  /**
   * 测试微信浏览器功能
   */
  private testWechatFeatures(): void {
    const version = getWechatVersion();

    this.addResult(
      '微信版本获取',
      version !== null,
      version ? `微信版本: ${version.version}` : '无法获取微信版本',
    );

    if (version) {
      const features = ['jssdk', 'camera', 'audio', 'location'];
      features.forEach((feature) => {
        const supported = isWechatFeatureSupported(feature);
        this.addResult(
          `微信${feature}功能支持`,
          true, // 这里只是检测函数是否正常工作
          `${feature}功能支持: ${supported}`,
        );
      });
    }

    // 测试微信JSSDK
    this.addResult(
      '微信JSSDK检测',
      typeof (window as any).wx !== 'undefined',
      `微信JSSDK: ${typeof (window as any).wx !== 'undefined' ? '已加载' : '未加载'}`,
    );
  }

  /**
   * 测试性能相关功能
   */
  private async testPerformance(): Promise<void> {
    // 测试内存信息
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.addResult(
        '内存信息获取',
        true,
        `已用内存: ${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`,
      );
    } else {
      this.addResult('内存信息获取', false, '浏览器不支持内存信息API');
    }

    // 测试IntersectionObserver支持
    this.addResult(
      'IntersectionObserver支持',
      'IntersectionObserver' in window,
      `IntersectionObserver: ${'IntersectionObserver' in window ? '支持' : '不支持'}`,
    );

    // 测试requestAnimationFrame支持
    this.addResult(
      'requestAnimationFrame支持',
      'requestAnimationFrame' in window,
      `requestAnimationFrame: ${'requestAnimationFrame' in window ? '支持' : '不支持'}`,
    );
  }

  /**
   * 测试媒体访问功能
   */
  private async testMediaAccess(): Promise<void> {
    // 测试getUserMedia支持
    const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    this.addResult(
      'getUserMedia支持',
      hasGetUserMedia,
      `getUserMedia: ${hasGetUserMedia ? '支持' : '不支持'}`,
    );

    // 测试文件API支持
    const hasFileAPI = 'File' in window && 'FileReader' in window;
    this.addResult('File API支持', hasFileAPI, `File API: ${hasFileAPI ? '支持' : '不支持'}`);

    // 测试Blob支持
    const hasBlobAPI = 'Blob' in window;
    this.addResult('Blob API支持', hasBlobAPI, `Blob API: ${hasBlobAPI ? '支持' : '不支持'}`);
  }

  /**
   * 添加测试结果
   */
  private addResult(name: string, passed: boolean, message: string, details?: any): void {
    this.results.push({
      name,
      passed,
      message,
      details,
    });
  }

  /**
   * 获取设备信息
   */
  public getDeviceInfo(): DeviceInfo {
    const wechatVersion = getWechatVersion();
    const isWechat = isWechatBrowser();

    if (isWechat) {
      // 微信浏览器中返回隐私保护的设备信息
      return {
        userAgent: 'WeChat Browser', // 简化的用户代理
        platform: 'Mobile', // 简化的平台信息
        deviceType: getDeviceType(),
        browserType: 'wechat',
        screenSize: `${window.innerWidth}x${window.innerHeight}`,
        pixelRatio: window.devicePixelRatio,
        orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
        touchSupport: isTouchDevice(),
        networkInfo: { type: 'unknown' }, // 简化网络信息
        wechatVersion: wechatVersion?.version,
      };
    }

    // 非微信浏览器返回完整设备信息
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      deviceType: getDeviceType(),
      browserType: getBrowserType(),
      screenSize: `${window.innerWidth}x${window.innerHeight}`,
      pixelRatio: window.devicePixelRatio,
      orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
      touchSupport: isTouchDevice(),
      networkInfo: getNetworkInfo(),
      wechatVersion: wechatVersion?.version,
    };
  }

  /**
   * 生成测试报告
   */
  public generateReport(): string {
    const deviceInfo = this.getDeviceInfo();
    const passedTests = this.results.filter((r) => r.passed).length;
    const totalTests = this.results.length;

    let report = '# 移动端测试报告\n\n';

    // 设备信息
    report += '## 设备信息\n\n';
    Object.entries(deviceInfo).forEach(([key, value]) => {
      report += `- **${key}**: ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
    });

    // 测试结果概览
    report += `\n## 测试结果概览\n\n`;
    report += `- 总测试数: ${totalTests}\n`;
    report += `- 通过测试: ${passedTests}\n`;
    report += `- 失败测试: ${totalTests - passedTests}\n`;
    report += `- 通过率: ${Math.round((passedTests / totalTests) * 100)}%\n\n`;

    // 详细测试结果
    report += '## 详细测试结果\n\n';
    this.results.forEach((result) => {
      const status = result.passed ? '✅' : '❌';
      report += `${status} **${result.name}**: ${result.message}\n`;
    });

    return report;
  }
}

// 创建全局测试实例
const mobileTestSuite = new MobileTestSuite();

// 导出测试工具
export default mobileTestSuite;
export { MobileTestSuite, type TestResult, type DeviceInfo };
