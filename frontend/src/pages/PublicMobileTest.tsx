/**
 * 公开的移动端兼容性测试页面
 * 无需登录即可访问，专门用于测试移动端显示问题
 */

import React, { useState } from 'react';
import { Progress, Button, Card, Typography, Divider } from 'antd';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { isWechatBrowser } from '@/shared/utils/browserDetect';

const { Text, Title } = Typography;

// 模拟情绪工具函数
const getEmotionColor = (emotion: string) => {
  const colors: Record<string, string> = {
    happy: '#52c41a',
    neutral: '#1890ff',
    sad: '#722ed1',
    angry: '#f5222d',
    fear: '#fa8c16',
    surprise: '#13c2c2',
    disgust: '#eb2f96',
    contempt: '#607D8B',
  };
  return colors[emotion] || '#1890ff';
};

const getEmotionLabel = (emotion: string) => {
  const labels: Record<string, string> = {
    happy: '开心',
    neutral: '平静',
    sad: '悲伤',
    angry: '愤怒',
    fear: '恐惧',
    surprise: '惊讶',
    disgust: '厌恶',
    contempt: '蔑视',
  };
  return labels[emotion] || '未知';
};

const PublicMobileTest: React.FC = () => {
  const [showMultimodal, setShowMultimodal] = useState(false);

  // 测试数据
  const primaryEmotion = { emotion: 'happy', value: 0.85 };
  const secondaryEmotion = { emotion: 'neutral', value: 0.1 };

  // 获取设备信息的函数，在微信浏览器中保护隐私
  const getDisplayDeviceInfo = () => {
    if (isWechatBrowser()) {
      // 微信浏览器中只显示基本的、非敏感信息
      return {
        userAgent: '微信内置浏览器',
        screenSize: `${window.innerWidth} x ${window.innerHeight}`,
        pixelRatio: window.devicePixelRatio,
        touchSupport: 'ontouchstart' in window ? '是' : '否',
        browserType: '微信浏览器',
        privacyMode: true,
      };
    }

    // 非微信浏览器显示完整信息
    return {
      userAgent: navigator.userAgent,
      screenSize: `${window.innerWidth} x ${window.innerHeight}`,
      pixelRatio: window.devicePixelRatio,
      touchSupport: 'ontouchstart' in window ? '是' : '否',
      browserType: '标准浏览器',
      privacyMode: false,
    };
  };

  const deviceInfo = getDisplayDeviceInfo();

  return (
    <ConfigProvider locale={zhCN}>
      <div
        style={{
          padding: '20px',
          maxWidth: '850px',
          margin: '0 auto',
          backgroundColor: '#f5f5f5',
          minHeight: '100vh',
        }}
      >
        <Card title="移动端兼容性测试（公开版）" style={{ marginBottom: '20px' }}>
          <Text>
            这个页面用于测试环形图和多模态组件在移动端（特别是iOS Safari）的显示情况。
            无需登录即可访问。请在不同设备上测试以下组件的显示效果。
          </Text>
        </Card>

        {/* 1. 基础环形图测试 */}
        <Card title="1. 基础环形图测试" style={{ marginBottom: '20px' }}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-around',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: '20px',
            }}
          >
            {/* 完全默认的环形图 */}
            <div style={{ textAlign: 'center' }}>
              <Title level={4}>默认环形图</Title>
              <Progress type="circle" percent={85} size={150} strokeColor="#52c41a" />
              <div style={{ marginTop: '10px' }}>85% 开心</div>
            </div>

            {/* 自定义format的环形图 */}
            <div style={{ textAlign: 'center' }}>
              <Title level={4}>自定义format</Title>
              <Progress
                type="circle"
                percent={85}
                size={150}
                strokeColor="#52c41a"
                format={(percent) => `${percent}%`}
              />
              <div style={{ marginTop: '10px' }}>85% 开心</div>
            </div>
          </div>
        </Card>

        {/* 2. 项目中使用的环形图样式 */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: 40,
            margin: '15px 0',
          }}
        >
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <div style={{ marginBottom: 8, fontWeight: 500, fontSize: 16 }}>主要情绪 Primary</div>
            <Progress type="circle" percent={85} size={150} strokeColor="#52c41a" />
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <div style={{ marginBottom: 8, fontWeight: 500, fontSize: 16 }}>次要情绪 Secondary</div>
            <Progress type="circle" percent={10} size={120} strokeColor="#1890ff" />
          </div>
        </div>

        {/* 3. 情绪条形进度条测试 */}
        <Card title="3. 情绪显示测试" style={{ marginBottom: '20px' }}>
          <div style={{ marginBottom: '15px' }}>
            <Text>测试八种情绪的简洁显示：</Text>
          </div>

          {/* 八种情绪测试数据 */}
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(2, 1fr)',
              gap: '8px',
            }}
          >
            {[
              { emotion: 'happy', value: 0.85, label: '开心' },
              { emotion: 'neutral', value: 0.15, label: '平静' },
              { emotion: 'surprise', value: 0.12, label: '惊讶' },
              { emotion: 'sad', value: 0.08, label: '悲伤' },
              { emotion: 'angry', value: 0.05, label: '愤怒' },
              { emotion: 'fear', value: 0.03, label: '恐惧' },
              { emotion: 'disgust', value: 0.02, label: '厌恶' },
              { emotion: 'contempt', value: 0.01, label: '蔑视' },
            ].map(({ emotion, value, label }) => {
              const percentage = Math.round(value * 100);
              const emotionColorValue = getEmotionColor(emotion.toLowerCase());

              return (
                <div
                  key={emotion}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    backgroundColor: '#f9f9f9',
                    padding: '8px 12px',
                    borderRadius: '6px',
                    borderLeft: `3px solid ${emotionColorValue}`,
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  {/* 左侧：情绪信息 */}
                  <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                    <div
                      style={{
                        width: '12px',
                        height: '12px',
                        borderRadius: '2px',
                        backgroundColor: emotionColorValue,
                        marginRight: '8px',
                        flexShrink: 0,
                      }}
                    />
                    <span
                      style={{
                        color: emotionColorValue,
                        fontWeight: 'bold',
                        fontSize: '15px',
                        lineHeight: '1.2',
                        marginRight: '4px',
                      }}
                    >
                      {label}
                    </span>
                    <span
                      style={{
                        fontSize: '12px',
                        opacity: 0.8,
                        fontWeight: 'normal',
                        color: emotionColorValue,
                        lineHeight: '1.2',
                      }}
                    >
                      {emotion.charAt(0).toUpperCase() + emotion.slice(1)}
                    </span>
                  </div>

                  {/* 右侧：百分比 */}
                  <span
                    style={{
                      fontWeight: 'bold',
                      color: emotionColorValue,
                      fontSize: '16px',
                      flexShrink: 0,
                    }}
                  >
                    {percentage}%
                  </span>
                </div>
              );
            })}
          </div>
        </Card>

        {/* 4. 设备信息 - 在微信浏览器中完全隐藏 */}
        {!isWechatBrowser() && (
          <Card title="4. 设备信息">
            <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
              <div>
                <strong>浏览器类型:</strong> {deviceInfo.browserType}
              </div>
              <div>
                <strong>User Agent:</strong> {deviceInfo.userAgent}
              </div>
              <div>
                <strong>屏幕尺寸:</strong> {deviceInfo.screenSize}
              </div>
              <div>
                <strong>设备像素比:</strong> {deviceInfo.pixelRatio}
              </div>
              <div>
                <strong>是否触摸设备:</strong> {deviceInfo.touchSupport}
              </div>

              {deviceInfo.privacyMode && (
                <div
                  style={{
                    marginTop: '10px',
                    padding: '8px',
                    backgroundColor: '#f0f8ff',
                    borderRadius: '4px',
                    fontSize: '12px',
                    color: '#666',
                  }}
                >
                  🔒 隐私保护模式：在微信浏览器中，部分设备信息已被隐藏以保护您的隐私
                </div>
              )}
            </div>
          </Card>
        )}
      </div>
    </ConfigProvider>
  );
};

export default PublicMobileTest;
