/**
 * 按钮显示测试页面 - 特别用于iOS Safari
 * 无需登录即可访问
 */

import React from 'react';
import { Button, Card, Typography } from 'antd';
import { UploadOutlined, CameraOutlined } from '@ant-design/icons';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import '../user/pages/EmotionAnalysis/styles/ios-button-fix.css';

const { Text } = Typography;

const ButtonsTest: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <div
        style={{
          padding: '20px',
          maxWidth: '850px',
          margin: '0 auto',
          backgroundColor: '#f5f5f5',
          minHeight: '100vh',
        }}
      >
        <Card title="按钮显示测试页面" style={{ marginBottom: '20px' }}>
          <Text>
            这个页面用于测试上传和拍照按钮在iOS Safari上的显示，查看按钮大小是否一致。
            无需登录即可访问。请在不同设备上测试以下按钮的显示效果。
          </Text>
        </Card>

        {/* 1. 原始按钮组 */}
        <Card title="1. 原始按钮组" style={{ marginBottom: '20px' }}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '20px',
              marginBottom: '20px',
            }}
          >
            <Button
              icon={<UploadOutlined />}
              style={{
                width: '110px',
                height: '40px',
              }}
            >
              上传
            </Button>
            <Button
              icon={<CameraOutlined />}
              style={{
                width: '110px',
                height: '40px',
              }}
            >
              拍照
            </Button>
          </div>
        </Card>

        {/* 2. 修复后的按钮组 */}
        <Card title="2. 修复后的按钮组" style={{ marginBottom: '20px' }}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '20px',
              marginBottom: '20px',
            }}
          >
            <div style={{ display: 'inline-block', verticalAlign: 'middle' }}>
              <Button
                className="emotion-button-fix upload-button"
                style={{
                  width: '110px',
                  height: '40px',
                  padding: '0',
                }}
              >
                <div
                  className="button-content"
                  style={{
                    textAlign: 'center',
                    lineHeight: '1.2',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    height: '100%',
                  }}
                >
                  <span
                    className="main-text"
                    style={{
                      fontSize: '16px',
                      display: 'block',
                      marginBottom: '0px',
                      fontWeight: 500,
                    }}
                  >
                    <UploadOutlined style={{ marginRight: '4px' }} />
                    上传
                  </span>
                  <span
                    className="sub-text"
                    style={{
                      fontSize: '12px',
                      opacity: 0.8,
                      display: 'block',
                    }}
                  >
                    Upload
                  </span>
                </div>
              </Button>
            </div>
            <div style={{ display: 'inline-block', verticalAlign: 'middle' }}>
              <Button
                className="emotion-button-fix camera-button"
                style={{
                  width: '110px',
                  height: '40px',
                  padding: '0',
                }}
              >
                <div
                  className="button-content"
                  style={{
                    textAlign: 'center',
                    lineHeight: '1.2',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    height: '100%',
                  }}
                >
                  <span
                    className="main-text"
                    style={{
                      fontSize: '16px',
                      display: 'block',
                      marginBottom: '0px',
                      fontWeight: 500,
                    }}
                  >
                    <CameraOutlined style={{ marginRight: '4px' }} />
                    拍照
                  </span>
                  <span
                    className="sub-text"
                    style={{
                      fontSize: '12px',
                      opacity: 0.8,
                      display: 'block',
                    }}
                  >
                    Camera
                  </span>
                </div>
              </Button>
            </div>
          </div>
        </Card>

        {/* 3. 使用Grid布局的按钮组 */}
        <Card title="3. 使用Grid布局的按钮组" style={{ marginBottom: '20px' }}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(2, 110px)',
              gap: '20px',
              justifyContent: 'center',
              marginBottom: '20px',
            }}
          >
            <Button
              className="emotion-button-fix upload-button"
              style={{
                width: '110px',
                height: '40px',
                padding: '0',
              }}
            >
              <div
                className="button-content"
                style={{
                  textAlign: 'center',
                  lineHeight: '1.2',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  height: '100%',
                }}
              >
                <span
                  className="main-text"
                  style={{
                    fontSize: '16px',
                    display: 'block',
                    marginBottom: '0px',
                    fontWeight: 500,
                  }}
                >
                  <UploadOutlined style={{ marginRight: '4px' }} />
                  上传
                </span>
                <span
                  className="sub-text"
                  style={{
                    fontSize: '12px',
                    opacity: 0.8,
                    display: 'block',
                  }}
                >
                  Upload
                </span>
              </div>
            </Button>
            <Button
              className="emotion-button-fix camera-button"
              style={{
                width: '110px',
                height: '40px',
                padding: '0',
              }}
            >
              <div
                className="button-content"
                style={{
                  textAlign: 'center',
                  lineHeight: '1.2',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  height: '100%',
                }}
              >
                <span
                  className="main-text"
                  style={{
                    fontSize: '16px',
                    display: 'block',
                    marginBottom: '0px',
                    fontWeight: 500,
                  }}
                >
                  <CameraOutlined style={{ marginRight: '4px' }} />
                  拍照
                </span>
                <span
                  className="sub-text"
                  style={{
                    fontSize: '12px',
                    opacity: 0.8,
                    display: 'block',
                  }}
                >
                  Camera
                </span>
              </div>
            </Button>
          </div>
        </Card>

        {/* 4. 设备信息 */}
        <Card title="4. 设备信息">
          <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
            <div>
              <strong>User Agent:</strong> {navigator.userAgent}
            </div>
            <div>
              <strong>屏幕尺寸:</strong> {window.innerWidth} x {window.innerHeight}
            </div>
            <div>
              <strong>设备像素比:</strong> {window.devicePixelRatio}
            </div>
            <div>
              <strong>是否触摸设备:</strong> {'ontouchstart' in window ? '是' : '否'}
            </div>
            <div>
              <strong>是否iOS设备:</strong>{' '}
              {/iPad|iPhone|iPod/.test(navigator.userAgent) ? '是' : '否'}
            </div>
            <div>
              <strong>是否Safari浏览器:</strong>{' '}
              {/^((?!chrome|android).)*safari/i.test(navigator.userAgent) ? '是' : '否'}
            </div>
          </div>
        </Card>
      </div>
    </ConfigProvider>
  );
};

export default ButtonsTest;
