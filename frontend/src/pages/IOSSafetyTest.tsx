/**
 * iOS兼容性检查页面
 * 用于测试在最老版本的iOS Safari上是否能正常加载
 * 仅使用基础的、安全的CSS属性
 */

import React from 'react';
import { Typography, Card, Button, Alert } from 'antd';

const { Title, Text } = Typography;

// 超级简单的CSS，确保兼容性
const simpleStyles = {
  container: {
    padding: '20px',
    margin: '0 auto',
    maxWidth: '600px',
  },
  card: {
    marginBottom: '20px',
  },
  button: {
    margin: '5px',
  },
  flexRow: {
    display: 'flex',
    flexDirection: 'row' as const,
    gap: '10px',
    flexWrap: 'wrap' as const,
    justifyContent: 'center',
  },
  header: {
    textAlign: 'center' as const,
    marginBottom: '20px',
  },
};

const IOSSafetyTest: React.FC = () => {
  const [iosInfo, setIosInfo] = React.useState<{
    isIOS: boolean;
    isSafari: boolean;
    supportsHas: boolean;
    userAgent: string;
  }>({
    isIOS: false,
    isSafari: false,
    supportsHas: false,
    userAgent: '',
  });

  React.useEffect(() => {
    // 检查是否是iOS设备
    // 使用更具体的类型替代any
    interface WindowWithMSStream extends globalThis.Window {
      MSStream?: unknown;
    }
    const isIOS =
      /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as WindowWithMSStream).MSStream;

    // 检查是否是Safari浏览器
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

    // 尝试检测是否支持:has选择器
    let supportsHas = false;
    try {
      // 使用window.CSS而不是直接使用CSS
      supportsHas = window.CSS && window.CSS.supports('selector(:has(*))');
    } catch (_e) {
      console.log('CSS.supports API不可用或:has选择器不受支持');
    }

    setIosInfo({
      isIOS,
      isSafari,
      supportsHas,
      userAgent: navigator.userAgent,
    });
  }, []);

  return (
    <div style={simpleStyles.container}>
      <div style={simpleStyles.header}>
        <Title level={3}>iOS兼容性检查页面</Title>
        <Text>测试基本页面在老版本iOS Safari上是否能正常加载</Text>
      </div>

      <Card title="设备信息" style={simpleStyles.card}>
        <div style={{ lineHeight: 2 }}>
          <p>
            <strong>是否iOS设备:</strong> {iosInfo.isIOS ? '是' : '否'}
          </p>
          <p>
            <strong>是否Safari浏览器:</strong> {iosInfo.isSafari ? '是' : '否'}
          </p>
          <p>
            <strong>支持:has选择器:</strong> {iosInfo.supportsHas ? '是' : '否'}
          </p>
          <p>
            <strong>用户代理:</strong> {iosInfo.userAgent}
          </p>
          <p>
            <strong>窗口尺寸:</strong> {window.innerWidth} x {window.innerHeight}
          </p>
          <p>
            <strong>设备像素比:</strong> {window.devicePixelRatio}
          </p>
        </div>
      </Card>

      <Card title="基本UI元素" style={simpleStyles.card}>
        <div style={simpleStyles.flexRow}>
          <Button type="primary" style={simpleStyles.button}>
            主按钮
          </Button>
          <Button style={simpleStyles.button}>次按钮</Button>
          <Button type="dashed" style={simpleStyles.button}>
            虚线按钮
          </Button>
        </div>

        <Alert
          message="测试提示信息"
          description="如果您能看到此消息，说明基本UI元素正常加载。"
          type="info"
          showIcon
          style={{ marginTop: '20px' }}
        />
      </Card>

      <Card title="页面加载检查" style={simpleStyles.card}>
        <p>
          如果这个页面能够正常加载和显示，说明您的设备可以正常访问情绪分析页面的简化版本。
          如果情绪分析页面仍然无法加载，可能还有其他资源或更复杂的CSS/JS兼容性问题。
        </p>

        <div style={{ textAlign: 'center', marginTop: '20px' }}>
          <Button
            type="primary"
            onClick={() => {
              try {
                window.sessionStorage.setItem('ios-compatibility-check', 'passed');
                window.location.href = '/user/emotion-analysis';
              } catch (_e) {
                // 使用window.alert而不是直接使用alert
                window.alert('无法设置会话存储。请手动返回情绪分析页面尝试。');
              }
            }}
          >
            返回情绪分析页面
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default IOSSafetyTest;
