/**
 * 管理后台常量枚举定义
 */

// 数据集类型枚举
export enum DatasetTypeEnum {
  EMOTION = 'emotion',
  FACE = 'face',
  AUDIO = 'audio',
  MULTIMODAL = 'multimodal',
  CUSTOM = 'custom',
}

// 数据集状态枚举
export enum DatasetStatusEnum {
  ACTIVE = 'active',
  PROCESSING = 'processing',
  ERROR = 'error',
  FINISHED = 'finished',
  COMPLETED = 'completed',
  SUCCESS = 'success',
  FAILED = 'failed',
  PENDING = 'pending',
}

// 用户角色枚举
export enum UserRoleEnum {
  ADMIN = 'admin',
  USER = 'user',
  MODERATOR = 'moderator',
}

// 训练状态枚举
export enum TrainingStatusEnum {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

// 模型类型枚举
export enum ModelTypeEnum {
  EMOTION_RECOGNITION = 'emotion_recognition',
  FACE_DETECTION = 'face_detection',
  AUDIO_ANALYSIS = 'audio_analysis',
  MULTIMODAL = 'multimodal',
}

// 数据格式枚举
export enum DataFormatEnum {
  JSON = 'json',
  CSV = 'csv',
  XML = 'xml',
  BINARY = 'binary',
}

// 操作类型枚举
export enum OperationTypeEnum {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  VIEW = 'view',
  EXPORT = 'export',
  IMPORT = 'import',
}
