import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Table,
  Button,
  Space,
  message,
  Popconfirm,
  Card,
  Typography,
  Tag,
  Image,
  Form,
  Input,
  Row,
  Col,
  Select,
  Modal,
  Dropdown,
  Menu,
} from 'antd';
import type { TableProps, TablePaginationConfig } from 'antd';
import {
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  FilterOutlined,
  EyeOutlined,
  FileTextOutlined,
  ExclamationCircleOutlined,
  UserDeleteOutlined,
  DownOutlined,
  SmileOutlined,
  FrownOutlined,
  MehOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import styles from './index.module.less';
import {
  getEmotionAnalyses,
  deleteEmotionAnalysis,
  batchDeleteEmotionAnalyses,
  getEmotionAnalysisById,
} from '../../../services/analysisService';
import type {
  EmotionAnalysis,
  EmotionAnalysisListParams,
  EmotionFeedback,
} from '../../../types/emotionAnalysis.d';
import UserStatisticsChart from './components/UserStatisticsChart';

const { Title } = Typography;
// const { RangePicker } = DatePicker; // Removed as unused

interface AnalysisTableDataType extends EmotionAnalysis {
  key: string;
}

interface FetchAnalysesParams {
  current?: number;
  pageSize?: number;
  sort?: string;
  filter_by?: string;
}

interface FilterFormValues {
  username?: string;
  dateRange?: [dayjs.Dayjs, dayjs.Dayjs];
  emotion?: string;
}

// 获取情绪对应的图标
const getEmotionIcon = (emotion: string) => {
  const emotionLower = emotion.toLowerCase();
  if (emotionLower.includes('happy') || emotionLower.includes('surprise')) {
    return <SmileOutlined />;
  } else if (
    emotionLower.includes('sad') ||
    emotionLower.includes('fear') ||
    emotionLower.includes('angry') ||
    emotionLower.includes('disgust')
  ) {
    return <FrownOutlined />;
  } else {
    return <MehOutlined />;
  }
};

const AnalysisManagementPage: React.FC = () => {
  const [form] = Form.useForm();
  const [analyses, setAnalyses] = useState<AnalysisTableDataType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [filterParams, setFilterParams] = useState<string | undefined>(undefined);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total) => `总共 ${total} 条记录`,
  });

  // 展开行详情相关状态
  const [expandedRowDetails, setExpandedRowDetails] = useState<Record<string, EmotionAnalysis>>({});
  const [expandedRowLoading, setExpandedRowLoading] = useState<Record<string, boolean>>({});

  // 可用的情绪选项，包含中英文和颜色
  const emotionOptions = [
    { value: 'happy', label: '开心 (Happy)', color: '#52c41a' },
    { value: 'sad', label: '悲伤 (Sad)', color: '#1890ff' },
    { value: 'angry', label: '愤怒 (Angry)', color: '#f5222d' },
    { value: 'fear', label: '恐惧 (Fear)', color: '#722ed1' },
    { value: 'surprise', label: '惊讶 (Surprise)', color: '#faad14' },
    { value: 'disgust', label: '厌恶 (Disgust)', color: '#eb2f96' },
    { value: 'neutral', label: '平静 (Neutral)', color: '#8c8c8c' },
    { value: 'contempt', label: '轻蒙 (Contempt)', color: '#fa541c' },
  ];

  // 根据情绪值获取情绪选项
  const getEmotionOption = (emotion: string) => {
    return (
      emotionOptions.find((option) => option.value.toLowerCase() === emotion.toLowerCase()) || {
        value: emotion,
        label: emotion,
        color: '#8c8c8c',
      }
    );
  };

  // 获取单页数据
  const fetchAnalyses = useCallback(
    async (params: FetchAnalysesParams = {}) => {
      setLoading(true);
      try {
        const current = params.current || pagination.current || 1;
        const pageSize = params.pageSize || pagination.pageSize || 10;
        const skip = (current - 1) * pageSize;

        // 使用传入的过滤条件或保存的过滤条件
        const filter = params.filter_by || filterParams;

        const queryParams: EmotionAnalysisListParams = {
          skip: skip,
          limit: pageSize,
          sort: params.sort,
          filter_by: filter,
        };

        console.log('发送查询参数:', queryParams);
        const response = await getEmotionAnalyses(queryParams);
        console.log('接收到的响应:', response);

        setAnalyses(response.items.map((item) => ({ ...item, key: item.id })));
        setPagination((prev) => ({
          ...prev,
          current: current,
          pageSize: pageSize,
          total: response.total,
        }));
      } catch (error) {
        message.error('获取情绪分析记录失败');
        console.error('Failed to fetch analyses:', error);
      }
      setLoading(false);
    },
    [filterParams], // 移除 pagination 依赖，避免循环依赖
  );

  // 获取所有匹配记录的函数
  const fetchAllMatchingRecords = async (
    filterString?: string,
  ): Promise<AnalysisTableDataType[]> => {
    setLoading(true);
    try {
      // 首先获取总记录数
      const initialParams: EmotionAnalysisListParams = {
        skip: 0,
        limit: 1,
        filter_by: filterString,
      };

      const initialResponse = await getEmotionAnalyses(initialParams);
      const totalRecords = initialResponse.total;
      console.log(`总共有 ${totalRecords} 条匹配记录`);

      if (totalRecords === 0) {
        return [];
      }

      // 计算需要请求的页数（后端限制每页最多200条记录）
      const pageSize = 200; // 每页获取200条记录（后端API的最大限制）
      const totalPages = Math.ceil(totalRecords / pageSize);
      console.log(`需要请求 ${totalPages} 页数据`);

      // 并行请求所有页面的数据
      const requests = [];
      for (let page = 0; page < totalPages; page++) {
        const skip = page * pageSize;
        const limit = Math.min(pageSize, totalRecords - skip); // 确保最后一页不超出总记录数
        const params: EmotionAnalysisListParams = {
          skip: skip,
          limit: limit,
          filter_by: filterString,
        };
        requests.push(getEmotionAnalyses(params));
      }

      const responses = await Promise.all(requests);

      // 合并所有页面的数据
      const allItems: AnalysisTableDataType[] = [];
      responses.forEach((response) => {
        const items = response.items.map((item) => ({ ...item, key: item.id }));
        allItems.push(...items);
      });

      console.log(`成功获取所有 ${allItems.length} 条记录`);
      return allItems;
    } catch (error) {
      message.error('获取记录失败');
      console.error('Failed to fetch all matching records:', error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // 删除所有筛选结果
  const handleDeleteAllFiltered = async () => {
    if (!filterParams) {
      message.warning('请先设置筛选条件');
      return;
    }

    try {
      // 获取所有符合筛选条件的记录
      const allRecords = await fetchAllMatchingRecords(filterParams);

      if (allRecords.length === 0) {
        message.warning('没有符合条件的记录');
        return;
      }

      // 提取所有记录的ID
      const allRecordIds = allRecords.map((record) => record.id);

      // 调用批量删除API
      setLoading(true);
      const result = await batchDeleteEmotionAnalyses(allRecordIds);

      if (result.success) {
        message.success(result.message || `成功删除 ${allRecordIds.length} 条记录`);

        // 如果有失败的记录，显示详细信息
        if (result.data && result.data.failed_ids && result.data.failed_ids.length > 0) {
          message.warning(`${result.data.failed_ids.length} 条记录删除失败，请稍后重试`);
        }

        // 重新加载数据
        fetchAnalyses({
          current: 1, // 重置到第一页
          pageSize: pagination.pageSize,
          filter_by: filterParams,
        });
      } else {
        message.error(result.message || '批量删除失败，请稍后重试');
      }
    } catch (error) {
      console.error('删除所有筛选记录失败:', error);
      message.error('删除所有筛选记录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和过滤条件变化时获取数据
  useEffect(() => {
    fetchAnalyses({ current: 1, pageSize: pagination.pageSize });
  }, [fetchAnalyses, filterParams]);

  // 用于跳过初始渲染的 ref
  const isInitialRender = useRef(true);

  // 分页变化时获取数据，但不依赖 fetchAnalyses 避免循环
  useEffect(() => {
    // 跳过初始渲染
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }

    const fetchData = async () => {
      setLoading(true);
      try {
        const current = pagination.current ?? 1;
        const pageSize = pagination.pageSize ?? 10;
        const skip = (current - 1) * pageSize;
        const queryParams: EmotionAnalysisListParams = {
          skip: skip,
          limit: pageSize,
          filter_by: filterParams,
        };

        const response = await getEmotionAnalyses(queryParams);
        setAnalyses(response.items.map((item) => ({ ...item, key: item.id })));
      } catch (error) {
        message.error('获取情绪分析记录失败');
        console.error('Failed to fetch analyses:', error);
      }
      setLoading(false);
    };

    fetchData();
  }, [pagination.current, pagination.pageSize]);

  // 加载展开行详情
  const loadExpandedRowDetail = async (id: string) => {
    // 如果已经加载过，直接返回
    if (expandedRowDetails[id]) {
      return;
    }

    setExpandedRowLoading((prev) => ({ ...prev, [id]: true }));
    try {
      const analysisDetail = await getEmotionAnalysisById(id);
      setExpandedRowDetails((prev) => ({ ...prev, [id]: analysisDetail }));
    } catch (error) {
      message.error('获取分析详情失败');
      console.error('Failed to fetch analysis detail:', error);
    } finally {
      setExpandedRowLoading((prev) => ({ ...prev, [id]: false }));
    }
  };

  // 单条记录删除
  const handleDelete = async (id: string) => {
    try {
      await deleteEmotionAnalysis(id);
      message.success('情绪分析记录删除成功');
      fetchAnalyses({ current: pagination.current, pageSize: pagination.pageSize });
    } catch (error) {
      message.error('删除情绪分析记录失败');
      console.error('Failed to delete analysis:', error);
    }
  };

  // 获取选中用户的所有记录ID
  const getSelectedUserRecordIds = async (): Promise<string[]> => {
    // 获取选中的记录
    const selectedRecords = analyses.filter((record) => selectedRowKeys.includes(record.key));

    // 提取不同的用户ID
    const userIds = new Set<string>();
    selectedRecords.forEach((record) => {
      if (record.user_id) {
        userIds.add(record.user_id);
      }
    });

    if (userIds.size === 0) {
      // 如果没有用户ID，直接返回选中的记录ID
      return selectedRowKeys.map((key) => key.toString());
    }

    // 构建用户ID筛选条件
    const userFilter = Array.from(userIds)
      .map((id) => `user_id:${id}`)
      .join(',');
    console.log(`用户筛选条件: ${userFilter}`);

    // 获取这些用户的所有记录
    const allUserRecords = await fetchAllMatchingRecords(userFilter);
    console.log(`找到用户相关记录: ${allUserRecords.length} 条`);

    // 返回所有记录的ID
    return allUserRecords.map((record) => record.id);
  };

  // 只删除选中的记录
  const handleDeleteSelectedOnly = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的记录');
      return;
    }

    setLoading(true);
    try {
      // 只删除选中的记录
      const recordIdsToDelete = selectedRowKeys.map((key) => key.toString());

      // 调用批量删除API
      const result = await batchDeleteEmotionAnalyses(recordIdsToDelete);

      if (result.success) {
        message.success(result.message || `成功删除 ${recordIdsToDelete.length} 条记录`);

        // 如果有失败的记录，显示详细信息
        if (result.data && result.data.failed_ids && result.data.failed_ids.length > 0) {
          message.warning(`${result.data.failed_ids.length} 条记录删除失败，请稍后重试`);
        }

        setSelectedRowKeys([]);
        // 重新加载数据
        fetchAnalyses({
          current: pagination.current,
          pageSize: pagination.pageSize,
          filter_by: filterParams,
        });
      } else {
        message.error(result.message || '批量删除失败，请稍后重试');
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 删除选中用户的所有记录
  const handleDeleteAllFromUsers = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的记录');
      return;
    }

    setLoading(true);
    try {
      // 获取选中用户的所有记录ID
      const recordIdsToDelete = await getSelectedUserRecordIds();

      if (recordIdsToDelete.length === selectedRowKeys.length) {
        // 如果记录数量相同，说明没有额外的记录需要删除
        message.info('没有找到额外的用户记录，只删除选中的记录');
        await handleDeleteSelectedOnly();
        return;
      }

      // 提示用户确认
      const confirmed = window.confirm(
        `您选中了 ${selectedRowKeys.length} 条记录，将删除这些用户的所有 ${recordIdsToDelete.length} 条记录。是否继续？`,
      );

      if (!confirmed) {
        setLoading(false);
        return;
      }

      // 调用批量删除API
      const result = await batchDeleteEmotionAnalyses(recordIdsToDelete);

      if (result.success) {
        message.success(result.message || `成功删除 ${recordIdsToDelete.length} 条记录`);

        // 如果有失败的记录，显示详细信息
        if (result.data && result.data.failed_ids && result.data.failed_ids.length > 0) {
          message.warning(`${result.data.failed_ids.length} 条记录删除失败，请稍后重试`);
        }

        setSelectedRowKeys([]);
        // 重新加载数据
        fetchAnalyses({
          current: pagination.current,
          pageSize: pagination.pageSize,
          filter_by: filterParams,
        });
      } else {
        message.error(result.message || '批量删除失败，请稍后重试');
      }
    } catch (error) {
      console.error('删除用户所有记录失败:', error);
      message.error('删除用户所有记录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理表单提交，构建过滤条件
  const handleFilterSubmit = async (values: FilterFormValues) => {
    console.log('提交的过滤条件:', values);
    setLoading(true);

    try {
      // 定义过滤条件数组
      const filterConditions: string[] = [];

      // 根据情绪过滤 - 这是模型的直接属性
      if (values.emotion) {
        // 根据数据库中的情绪值格式，使用首字母大写的格式
        const emotionValue =
          values.emotion.charAt(0).toUpperCase() + values.emotion.slice(1).toLowerCase();
        filterConditions.push(`primary_emotion:${emotionValue}`); // 使用 primary_emotion 进行过滤
      }

      // 保存用户名过滤条件
      const usernameFilter = values.username;

      // 构建过滤字符串 - 使用逗号分隔，符合后端期望的格式
      const filterString = filterConditions.length > 0 ? filterConditions.join(',') : undefined;
      console.log('生成的过滤字符串:', filterString);

      // 保存过滤条件
      setFilterParams(filterString);

      if (usernameFilter) {
        // 如果有用户名过滤，获取所有匹配的记录后在前端进行过滤
        console.log('开始按用户名过滤，用户名关键词:', usernameFilter);

        // 获取所有匹配情绪条件的记录
        const allRecords = await fetchAllMatchingRecords(filterString);
        console.log(`获取到 ${allRecords.length} 条记录用于用户名过滤`);

        // 在前端进行用户名过滤
        const searchTerm = usernameFilter.toLowerCase().trim();
        const filtered = allRecords.filter((item) => {
          const username = item.username ? item.username.toLowerCase() : '';
          return username.includes(searchTerm);
        });

        console.log(`用户名过滤后剩余 ${filtered.length} 条记录`);

        // 更新状态
        setAnalyses(filtered);
        setPagination((prev) => ({
          ...prev,
          current: 1,
          total: filtered.length,
        }));

        if (filtered.length === 0) {
          message.info(`没有找到用户名包含 "${usernameFilter}" 的记录`);
        } else {
          message.success(`找到 ${filtered.length} 条包含 "${usernameFilter}" 的记录`);
        }
      } else {
        // 如果没有用户名过滤，正常获取第一页数据
        await fetchAnalyses({ current: 1, pageSize: pagination.pageSize, filter_by: filterString });
      }
    } catch (error) {
      console.error('查询失败:', error);
      message.error('查询失败，请检查网络连接或联系管理员');
    } finally {
      setLoading(false);
    }
  };

  // 重置过滤条件
  const handleResetFilter = () => {
    form.resetFields();
    setFilterParams(undefined);
    fetchAnalyses({ current: 1, pageSize: pagination.pageSize, filter_by: undefined });
  };

  // 处理表格变化（分页、排序等）
  const handleTableChange: TableProps<AnalysisTableDataType>['onChange'] = (
    newPagination,
    filters,
    sorter,
  ) => {
    let sortString: string | undefined = undefined;
    if (sorter && !Array.isArray(sorter) && sorter.field && sorter.order) {
      sortString = `${String(sorter.field)}_${sorter.order === 'ascend' ? 'asc' : 'desc'}`;
    }
    fetchAnalyses({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
      sort: sortString,
      filter_by: filterParams,
    });
  };

  // 处理行选择变化
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[]) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  const columns: TableProps<AnalysisTableDataType>['columns'] = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 80, // 进一步减小用户名列宽度
      ellipsis: true, // 超出部分显示省略号
      render: (username) => username || '未知用户',
    },
    {
      title: '图片预览',
      dataIndex: 'image_thumbnail_url',
      key: 'image_thumbnail_url',
      width: 110,
      align: 'center' as const,
      render: (url: string, _record: AnalysisTableDataType) => (
        <div className={styles.imagePreviewContainer}>
          {url ? (
            <Image
              src={url}
              alt="情绪分析图片"
              className={styles.thumbnailImage}
              fallback="/images/image-placeholder.png"
              preview={{
                mask: (
                  <div>
                    <EyeOutlined /> 查看大图
                  </div>
                ),
              }}
            />
          ) : (
            <div className={styles.noImage}>
              <FileTextOutlined />
              <span>文本分析</span>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '主要情绪',
      dataIndex: 'primary_emotion',
      key: 'primary_emotion',
      width: 110,
      align: 'center' as const,
      render: (emotion: string) => {
        const option = getEmotionOption(emotion);
        return (
          <div className={styles.emotionTagContainer}>
            <Tag color={option.color} className={styles.emotionTag} icon={getEmotionIcon(emotion)}>
              {option.label}
            </Tag>
          </div>
        );
      },
    },
    {
      title: '主要置信度',
      dataIndex: 'confidence1',
      key: 'confidence1',
      width: 90,
      align: 'center' as const,
      render: (confidence: number | null) => {
        if (confidence === null || confidence === undefined) return <Tag>N/A</Tag>;
        const confidencePercent = confidence * 100;
        let color = 'green';
        if (confidencePercent < 40) color = 'red';
        else if (confidencePercent < 70) color = 'orange';
        const formattedConfidence = `${confidencePercent.toFixed(2)}%`;
        return <Tag color={color}>{formattedConfidence}</Tag>;
      },
    },
    {
      title: '次要情绪',
      dataIndex: 'secondary_emotion',
      key: 'secondary_emotion',
      width: 110,
      align: 'center' as const,
      render: (secondaryEmotion: string | null) => {
        if (!secondaryEmotion) {
          return <Tag>无</Tag>;
        }
        const option = getEmotionOption(secondaryEmotion);
        return (
          <div className={styles.emotionTagContainer}>
            <Tag
              color={option.color}
              className={styles.emotionTag}
              icon={getEmotionIcon(secondaryEmotion)}
            >
              {option.label}
            </Tag>
          </div>
        );
      },
    },
    {
      title: '次要置信度',
      dataIndex: 'confidence2',
      key: 'confidence2',
      width: 90,
      align: 'center' as const,
      render: (confidence: number | null) => {
        if (confidence === null || confidence === undefined) return <Tag>N/A</Tag>;
        const confidencePercent = confidence * 100;
        let color = 'green';
        if (confidencePercent < 40) color = 'red';
        else if (confidencePercent < 70) color = 'orange';
        const formattedConfidence = `${confidencePercent.toFixed(2)}%`;
        return <Tag color={color}>{formattedConfidence}</Tag>;
      },
    },
    {
      title: '第三情绪',
      dataIndex: 'third_emotion',
      key: 'third_emotion',
      width: 110,
      align: 'center' as const,
      render: (thirdEmotion: string | null) => {
        if (!thirdEmotion) {
          return <Tag>无</Tag>;
        }
        const option = getEmotionOption(thirdEmotion);
        return (
          <div className={styles.emotionTagContainer}>
            <Tag
              color={option.color}
              className={styles.emotionTag}
              icon={getEmotionIcon(thirdEmotion)}
            >
              {option.label}
            </Tag>
          </div>
        );
      },
    },
    {
      title: '第三置信度',
      dataIndex: 'confidence3',
      key: 'confidence3',
      width: 90,
      align: 'center' as const,
      render: (confidence: number | null) => {
        if (confidence === null || confidence === undefined) return <Tag>N/A</Tag>;
        const confidencePercent = confidence * 100;
        let color = 'green';
        if (confidencePercent < 40) color = 'red';
        else if (confidencePercent < 70) color = 'orange';
        const formattedConfidence = `${confidencePercent.toFixed(2)}%`;
        return <Tag color={color}>{formattedConfidence}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => {
        // 格式化时间，将ISO时间格式转换为更友好的格式 YYYY-MM-DD HH:MM:SS
        if (!text) return '-';
        try {
          const date = new Date(text);
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
        } catch (_e) {
          return text; // 如果解析失败，返回原始文本
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 80,
      align: 'center' as const,
      render: (_, record) => (
        <Space size="small" className={styles.actionButtonsGroup}>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.pageWrapper}>
      {/* 页面标题区域 */}
      <div className={styles.pageHeader}>
        <Title level={2}>情绪分析记录管理</Title>
        <div className={styles.pageDescription}>管理用户情绪分析记录和相关图片资源</div>
      </div>

      {/* 筛选表单卡片 */}
      <Card
        className={styles.filterCard}
        title={
          <span>
            <FilterOutlined /> 筛选条件
          </span>
        }
        extra={
          <Button type="link" onClick={handleResetFilter}>
            重置所有筛选
          </Button>
        }
      >
        <Form form={form} onFinish={handleFilterSubmit} layout="horizontal">
          <Row gutter={24}>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="username" label="用户名:">
                <Input placeholder="输入用户名" allowClear />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item name="emotion" label="情绪:">
                <Select
                  placeholder="选择情绪"
                  allowClear
                  style={{ width: '100%' }}
                  optionLabelProp="label"
                >
                  {emotionOptions.map((option) => (
                    <Select.Option key={option.value} value={option.value} label={option.label}>
                      <Tag color={option.color}>{option.label}</Tag>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col
              xs={24}
              sm={24}
              md={4}
              style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-end' }}
            >
              <Form.Item>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                  查询
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作区域卡片 */}
      <Card className={styles.actionCard}>
        <div className={styles.actionContainer}>
          <div className={styles.selectedInfo}>
            {selectedRowKeys.length > 0 ? (
              <div className={styles.selectedCount}>
                已选择 <Tag color="blue">{selectedRowKeys.length}</Tag> 条记录
              </div>
            ) : filterParams ? (
              <div>
                当前筛选条件下共有 <Tag color="blue">{pagination.total}</Tag> 条记录
              </div>
            ) : (
              <div>
                共有 <Tag color="blue">{pagination.total}</Tag> 条记录
              </div>
            )}
          </div>

          <div className={styles.actionButtons}>
            {selectedRowKeys.length > 0 && (
              <Dropdown
                overlay={
                  <Menu>
                    <Menu.Item key="1" onClick={handleDeleteSelectedOnly} icon={<DeleteOutlined />}>
                      仅删除选中项 ({selectedRowKeys.length})
                    </Menu.Item>
                    <Menu.Item
                      key="2"
                      onClick={handleDeleteAllFromUsers}
                      icon={<UserDeleteOutlined />}
                    >
                      删除用户所有记录
                    </Menu.Item>
                  </Menu>
                }
              >
                <Button type="primary" danger>
                  批量操作 <DownOutlined />
                </Button>
              </Dropdown>
            )}

            {filterParams && (
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={() => {
                  Modal.confirm({
                    title: '确认删除',
                    icon: <ExclamationCircleOutlined />,
                    content: `确定要删除所有符合当前筛选条件的 ${pagination.total} 条记录吗？`,
                    okText: '确认删除',
                    cancelText: '取消',
                    onOk: handleDeleteAllFiltered,
                  });
                }}
              >
                删除筛选结果 ({pagination.total})
              </Button>
            )}

            <Button
              type="default"
              icon={<ReloadOutlined />}
              onClick={() =>
                fetchAnalyses({ current: 1, pageSize: pagination.pageSize, filter_by: undefined })
              }
            >
              显示所有记录
            </Button>
          </div>
        </div>
      </Card>

      {/* 表格卡片 */}
      <Card className={styles.tableCard}>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={analyses}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
          rowKey="key"
          rowClassName={
            (record: AnalysisTableDataType) =>
              record.primary_emotion === 'angry' ? 'row-highlight' : '' // 使用 primary_emotion
          }
          expandable={{
            expandedRowRender: (record: AnalysisTableDataType) => {
              const detailData = expandedRowDetails[record.id];
              const isLoading = expandedRowLoading[record.id];

              return (
                <div className={styles.expandedRow}>
                  <div className={styles.expandedImage}>
                    {record.image_thumbnail_url && (
                      <Image src={record.image_thumbnail_url} alt="情绪分析图片" width={300} />
                    )}
                  </div>
                  <div className={styles.expandedDetails}>
                    {/* 基本信息 */}
                    <div style={{ marginBottom: 16 }}>
                      <h4>基本信息</h4>
                      <Row gutter={[16, 8]}>
                        <Col span={12}>
                          <p>
                            <strong>分析ID:</strong> {record.id}
                          </p>
                          <p>
                            <strong>用户名:</strong> {record.username || '未知用户'}
                          </p>
                          <p>
                            <strong>输入类型:</strong> {record.input_type}
                          </p>
                        </Col>
                        <Col span={12}>
                          <p>
                            <strong>主要置信度:</strong> {(record.confidence1 * 100).toFixed(2)}%
                          </p>
                          <p>
                            <strong>创建时间:</strong>{' '}
                            {new Date(record.created_at).toLocaleString()}
                          </p>
                          <p>
                            <strong>用户ID:</strong> {record.user_id}
                          </p>
                        </Col>
                      </Row>
                    </div>

                    {/* 用户反馈 */}
                    <div>
                      <h4>
                        用户反馈{' '}
                        {detailData?.feedback_count ? `(${detailData.feedback_count}条)` : ''}
                      </h4>
                      {isLoading ? (
                        <div style={{ textAlign: 'center', padding: '20px' }}>
                          加载反馈信息中...
                        </div>
                      ) : detailData?.feedbacks && detailData.feedbacks.length > 0 ? (
                        <div>
                          {detailData.feedbacks.map((feedback: EmotionFeedback, index: number) => (
                            <Card
                              key={feedback.id}
                              size="small"
                              style={{ marginBottom: 8 }}
                              title={
                                <div
                                  style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                  }}
                                >
                                  <span>反馈 #{index + 1}</span>
                                  <div
                                    style={{ display: 'flex', gap: '8px', alignItems: 'center' }}
                                  >
                                    {/* 评价反馈 */}
                                    {feedback.rating && (
                                      <div
                                        style={{
                                          display: 'flex',
                                          alignItems: 'center',
                                          gap: '4px',
                                        }}
                                      >
                                        <span style={{ fontSize: '12px', color: '#666' }}>
                                          评价反馈:
                                        </span>
                                        <Tag
                                          color={
                                            feedback.rating === 'good'
                                              ? 'green'
                                              : feedback.rating === 'average'
                                                ? 'orange'
                                                : 'red'
                                          }
                                        >
                                          {feedback.rating === 'good'
                                            ? '好评'
                                            : feedback.rating === 'average'
                                              ? '一般'
                                              : '不好'}
                                        </Tag>
                                      </div>
                                    )}

                                    {/* 标注反馈 */}
                                    {feedback.emotion && (
                                      <div
                                        style={{
                                          display: 'flex',
                                          alignItems: 'center',
                                          gap: '4px',
                                        }}
                                      >
                                        <span style={{ fontSize: '12px', color: '#666' }}>
                                          标注反馈:
                                        </span>
                                        <Tag
                                          color={
                                            feedback.emotion === 'happy'
                                              ? 'green'
                                              : feedback.emotion === 'sad'
                                                ? 'blue'
                                                : feedback.emotion === 'angry'
                                                  ? 'red'
                                                  : feedback.emotion === 'fear'
                                                    ? 'purple'
                                                    : feedback.emotion === 'surprise'
                                                      ? 'cyan'
                                                      : feedback.emotion === 'disgust'
                                                        ? 'orange'
                                                        : feedback.emotion === 'neutral'
                                                          ? 'default'
                                                          : 'geekblue'
                                          }
                                        >
                                          {feedback.emotion === 'happy'
                                            ? '开心'
                                            : feedback.emotion === 'sad'
                                              ? '悲伤'
                                              : feedback.emotion === 'angry'
                                                ? '愤怒'
                                                : feedback.emotion === 'fear'
                                                  ? '恐惧'
                                                  : feedback.emotion === 'surprise'
                                                    ? '惊讶'
                                                    : feedback.emotion === 'disgust'
                                                      ? '厌恶'
                                                      : feedback.emotion === 'neutral'
                                                        ? '中性'
                                                        : feedback.emotion === 'contempt'
                                                          ? '蔑视'
                                                          : feedback.emotion}
                                        </Tag>
                                      </div>
                                    )}

                                    {/* 如果既没有评价反馈也没有标注反馈，显示默认 */}
                                    {!feedback.emotion && !feedback.rating && (
                                      <Tag color="default">未知反馈</Tag>
                                    )}
                                  </div>
                                </div>
                              }
                            >
                              <Row gutter={[16, 4]}>
                                <Col span={12}>
                                  <p>
                                    <strong>反馈用户:</strong> {feedback.username || '匿名用户'}
                                  </p>
                                </Col>
                                <Col span={12}>
                                  <p>
                                    <strong>反馈时间:</strong>{' '}
                                    {new Date(feedback.created_at).toLocaleString()}
                                  </p>
                                </Col>

                                {/* 反馈类型详细信息 */}
                                <Col span={24}>
                                  <div
                                    style={{
                                      marginTop: '8px',
                                      padding: '8px',
                                      background: '#fafafa',
                                      borderRadius: '4px',
                                    }}
                                  >
                                    <Row gutter={[16, 4]}>
                                      {/* 评价反馈详情 */}
                                      {feedback.rating && (
                                        <Col span={12}>
                                          <div>
                                            <strong style={{ color: '#1890ff' }}>评价反馈:</strong>
                                            <div style={{ marginTop: '4px' }}>
                                              <Tag
                                                color={
                                                  feedback.rating === 'good'
                                                    ? 'green'
                                                    : feedback.rating === 'average'
                                                      ? 'orange'
                                                      : 'red'
                                                }
                                                style={{ marginRight: '8px' }}
                                              >
                                                {feedback.rating === 'good'
                                                  ? '好评'
                                                  : feedback.rating === 'average'
                                                    ? '一般'
                                                    : '不好'}
                                              </Tag>
                                              <span style={{ color: '#666', fontSize: '12px' }}>
                                                (用户主观评价)
                                              </span>
                                            </div>
                                          </div>
                                        </Col>
                                      )}

                                      {/* 标注反馈详情 */}
                                      {feedback.emotion && (
                                        <Col span={12}>
                                          <div>
                                            <strong style={{ color: '#52c41a' }}>标注反馈:</strong>
                                            <div style={{ marginTop: '4px' }}>
                                              <Tag
                                                color={
                                                  feedback.emotion === 'happy'
                                                    ? 'green'
                                                    : feedback.emotion === 'sad'
                                                      ? 'blue'
                                                      : feedback.emotion === 'angry'
                                                        ? 'red'
                                                        : feedback.emotion === 'fear'
                                                          ? 'purple'
                                                          : feedback.emotion === 'surprise'
                                                            ? 'cyan'
                                                            : feedback.emotion === 'disgust'
                                                              ? 'orange'
                                                              : feedback.emotion === 'neutral'
                                                                ? 'default'
                                                                : 'geekblue'
                                                }
                                                style={{ marginRight: '8px' }}
                                              >
                                                {feedback.emotion === 'happy'
                                                  ? '开心'
                                                  : feedback.emotion === 'sad'
                                                    ? '悲伤'
                                                    : feedback.emotion === 'angry'
                                                      ? '愤怒'
                                                      : feedback.emotion === 'fear'
                                                        ? '恐惧'
                                                        : feedback.emotion === 'surprise'
                                                          ? '惊讶'
                                                          : feedback.emotion === 'disgust'
                                                            ? '厌恶'
                                                            : feedback.emotion === 'neutral'
                                                              ? '中性'
                                                              : feedback.emotion === 'contempt'
                                                                ? '蔑视'
                                                                : feedback.emotion}
                                              </Tag>
                                              <span style={{ color: '#666', fontSize: '12px' }}>
                                                (情绪标注)
                                              </span>

                                              {/* 显示标注反馈的人口统计学信息 */}
                                              {(feedback.name ||
                                                feedback.gender ||
                                                feedback.age) && (
                                                <div
                                                  style={{
                                                    marginTop: '6px',
                                                    fontSize: '12px',
                                                    color: '#666',
                                                  }}
                                                >
                                                  <div
                                                    style={{
                                                      display: 'flex',
                                                      flexWrap: 'wrap',
                                                      gap: '8px',
                                                    }}
                                                  >
                                                    {feedback.name && (
                                                      <span
                                                        style={{
                                                          background: '#f0f0f0',
                                                          padding: '2px 6px',
                                                          borderRadius: '3px',
                                                          fontSize: '11px',
                                                        }}
                                                      >
                                                        姓名: {feedback.name}
                                                      </span>
                                                    )}
                                                    {feedback.gender && (
                                                      <span
                                                        style={{
                                                          background: '#f0f0f0',
                                                          padding: '2px 6px',
                                                          borderRadius: '3px',
                                                          fontSize: '11px',
                                                        }}
                                                      >
                                                        性别: {feedback.gender}
                                                      </span>
                                                    )}
                                                    {feedback.age && (
                                                      <span
                                                        style={{
                                                          background: '#f0f0f0',
                                                          padding: '2px 6px',
                                                          borderRadius: '3px',
                                                          fontSize: '11px',
                                                        }}
                                                      >
                                                        年龄: {feedback.age}岁
                                                      </span>
                                                    )}
                                                  </div>
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        </Col>
                                      )}

                                      {/* 如果既没有评价也没有标注 */}
                                      {!feedback.rating && !feedback.emotion && (
                                        <Col span={24}>
                                          <div style={{ textAlign: 'center', color: '#999' }}>
                                            <Tag color="default">无具体反馈内容</Tag>
                                          </div>
                                        </Col>
                                      )}
                                    </Row>
                                  </div>
                                </Col>

                                {feedback.comment && (
                                  <Col span={24}>
                                    <p>
                                      <strong>反馈评论:</strong>
                                    </p>
                                    <div
                                      style={{
                                        background: '#f5f5f5',
                                        padding: '6px 10px',
                                        borderRadius: '4px',
                                        marginTop: '4px',
                                        fontSize: '13px',
                                      }}
                                    >
                                      {feedback.comment}
                                    </div>
                                  </Col>
                                )}
                              </Row>
                            </Card>
                          ))}
                        </div>
                      ) : detailData ? (
                        <div style={{ textAlign: 'center', color: '#999', padding: '10px' }}>
                          暂无用户反馈
                        </div>
                      ) : (
                        <div style={{ textAlign: 'center', color: '#999', padding: '10px' }}>
                          点击展开时将自动加载反馈信息
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            },
            onExpand: (expanded, record) => {
              if (expanded) {
                loadExpandedRowDetail(record.id);
              }
            },
          }}
        />
      </Card>

      {/* 用户活动统计图表 */}
      <UserStatisticsChart />
    </div>
  );
};

export default AnalysisManagementPage;
