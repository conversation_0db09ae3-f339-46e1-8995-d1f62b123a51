/* 情绪分析管理页面样式 */
.pageWrapper {
  padding: 24px;

  /* 页面标题区域 */
  .pageHeader {
    background: linear-gradient(to right, #1890ff, #52c41a);
    padding: 20px 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    color: white;

    h2 {
      color: white;
      margin-bottom: 8px;
    }

    .pageDescription {
      color: rgba(255, 255, 255, 0.85);
      font-size: 14px;
    }
  }

  /* 筛选表单卡片 */
  .filterCard {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    overflow: hidden;

    :global {
      .ant-card-head {
        background: #fafafa;
        border-bottom: 1px solid #f0f0f0;
      }

      .ant-form-item {
        margin-bottom: 16px;
      }

      .ant-form-item-label > label {
        font-weight: 500;
      }

      .ant-select-selector {
        border-radius: 4px !important;
      }

      .ant-input,
      .ant-picker {
        border-radius: 4px;
      }
    }
  }

  /* 操作区域卡片 */
  .actionCard {
    margin-bottom: 24px;
    border-radius: 8px;
    background: #f9f9f9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);

    .actionContainer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;

      .selectedInfo {
        font-size: 14px;
        display: flex;
        align-items: center;

        .selectedCount {
          margin-left: 8px;
        }
      }

      .actionButtons {
        display: flex;
        gap: 8px;
      }
    }
  }

  /* 数据表格卡片 */
  .tableCard {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    overflow: hidden;

    :global {
      .ant-table-thead > tr > th {
        background: #f5f5f5;
        font-weight: 600;
      }

      .ant-table-row:hover {
        background-color: #f9f9f9;
      }

      .row-highlight {
        background-color: rgba(245, 34, 45, 0.05);
      }
    }
  }

  /* 图片预览容器 */
  .imagePreviewContainer {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    overflow: hidden;
    background: #f5f5f5;
    margin: 0 auto;

    .thumbnailImage {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .noImage {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #bfbfbf;

      :global {
        .anticon {
          font-size: 24px;
          margin-bottom: 4px;
        }
      }
    }
  }

  /* 情绪标签容器 */
  .emotionTagContainer {
    display: flex;
    flex-direction: column;
    align-items: center;

    .emotionTag {
      padding: 0 8px;
      border-radius: 4px;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .secondaryEmotion {
      opacity: 0.7;
    }
  }

  /* 操作按钮组 */
  .actionButtonsGroup {
    display: flex;
    gap: 8px;
  }

  /* 扩展行内容 */
  .expandedRow {
    display: flex;
    padding: 16px;

    .expandedImage {
      margin-right: 24px;
    }

    .expandedDetails {
      flex: 1;

      .emotionDetailsContainer {
        margin-top: 8px;
        margin-bottom: 16px;
        background-color: #f9f9f9;
        border-radius: 6px;
        padding: 12px;

        .emotionDetailsList {
          list-style: none;
          padding: 0;
          margin: 0;

          .emotionDetailItem {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            border-bottom: 1px dashed #eee;

            &:last-child {
              border-bottom: none;
            }

            .emotionName {
              font-weight: 500;
              text-transform: capitalize;
              margin-right: 10px;
            }
          }
        }

        .noEmotionDetails {
          color: #999;
          font-style: italic;
          text-align: center;
          padding: 10px;
        }
      }
    }
  }
}
