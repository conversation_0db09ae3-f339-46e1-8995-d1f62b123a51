import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Card, Form, Input, Select, Button, Row, Col, DatePicker, Spin, message } from 'antd';
import { SearchOutlined, ReloadOutlined, BarChartOutlined } from '@ant-design/icons';
import * as echarts from 'echarts';
import { isAxiosError, type AxiosResponse } from 'axios';
import type { UserStatisticsResponse } from '../../../../services/userStatisticsService';
import { TooltipFormatterCallback } from 'echarts/types/dist/shared';
import type { Dayjs } from 'dayjs';
import styles from './UserStatisticsChart.module.less';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface ChartTooltipParam {
  name?: string;
  marker?: string;
  seriesName?: string;
  value?: number | string; // Value as directly used in the formatter
  // Consider adding other ECharts common properties if they might be used or for future-proofing
  // dataIndex?: number;
  // componentType?: string;
  // seriesType?: string;
  // seriesIndex?: number;
  // data?: unknown;
  // color?: string;
}

interface UserStatistics {
  username: string;
  upload_count: number;
  annotation_count: number;
  period: string;
}

interface StatisticsQueryParams {
  username?: string;
  period: 'day' | 'week' | 'month';
  start_date?: string;
  end_date?: string;
}

interface StatisticsFormValues {
  username?: string;
  period: 'day' | 'week' | 'month';
  dateRange?: [Dayjs, Dayjs];
}

const IsolatedChart: React.FC<{
  onMount: (element: HTMLDivElement) => void;
  onUnmount: () => void;
}> = ({ onMount, onUnmount }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const isMountedRef = useRef(false);

  useEffect(() => {
    if (chartRef.current && !isMountedRef.current) {
      isMountedRef.current = true;
      onMount(chartRef.current);
    }

    return () => {
      if (isMountedRef.current) {
        isMountedRef.current = false;
        onUnmount();
      }
    };
  }, [onMount, onUnmount]);

  return <div ref={chartRef} style={{ width: '100%', height: '2000px' }} />;
};

const UserStatisticsChart: React.FC = () => {
  const [form] = Form.useForm<StatisticsFormValues>();
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState<UserStatistics[]>([]);
  const chartInstanceRef = useRef<echarts.ECharts | null>(null);
  const isDestroyingRef = useRef(false);

  // 在组件挂载时添加被动事件监听器
  useEffect(() => {
    // 定义一个全局属性类型
    interface WindowWithPatch extends Window {
      _echartEventListenerPatched?: boolean;
    }

    // 添加一个全局的事件监听器补丁，覆盖原生的 addEventListener
    const originalAddEventListener = Element.prototype.addEventListener;
    // 保存原始方法引用
    const windowWithPatch = window as WindowWithPatch;
    
    if (!windowWithPatch._echartEventListenerPatched) {
      Element.prototype.addEventListener = function<K extends keyof ElementEventMap>(
        type: string, 
        listener: EventListenerOrEventListenerObject, 
        options?: boolean | AddEventListenerOptions
      ): void {
        if (type === 'wheel' || type === 'mousewheel') {
          // 强制使用被动式事件监听器
          if (typeof options === 'object') {
            options = { ...options, passive: true };
          } else if (options === undefined) {
            options = { passive: true };
          }
          return originalAddEventListener.call(this, type, listener, options);
        }
        return originalAddEventListener.call(this, type, listener, options);
      };
      windowWithPatch._echartEventListenerPatched = true;
    }
    
    // 清理函数
    return () => {
      // 如果需要恢复原始方法，可以在这里添加代码
      // 但通常不需要恢复，因为这个补丁对整个应用都有益
    };
  }, []);

  const handleChartMount = useCallback((element: HTMLDivElement) => {
    isDestroyingRef.current = false;

    try {
      if (chartInstanceRef.current) {
        try {
          chartInstanceRef.current.dispose();
        } catch (e) {
          console.warn('清理现有图表实例时出错:', e);
        }
        chartInstanceRef.current = null;
      }

      // 初始化 ECharts
      const chart = echarts.init(element, undefined, {
        renderer: 'canvas',
        useDirtyRect: true
      });
      chartInstanceRef.current = chart;

      const handleResize = () => {
        if (
          chartInstanceRef.current &&
          !chartInstanceRef.current.isDisposed() &&
          !isDestroyingRef.current
        ) {
          try {
            chartInstanceRef.current.resize();
          } catch (e) {
            console.warn('图表resize时出错:', e);
          }
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
      };
    } catch (error) {
      console.error('图表初始化失败:', error);
    }
  }, []);

  const handleChartUnmount = useCallback(() => {
    isDestroyingRef.current = true;

    if (chartInstanceRef.current) {
      try {
        if (!chartInstanceRef.current.isDisposed()) {
          chartInstanceRef.current.dispose();
        }
      } catch (e) {
        console.warn('图表销毁时出错:', e);
      } finally {
        chartInstanceRef.current = null;
      }
    }
  }, []);

  const updateChart = useCallback(() => {
    if (
      !chartInstanceRef.current ||
      chartInstanceRef.current.isDisposed() ||
      isDestroyingRef.current
    ) {
      return;
    }

    if (chartData.length === 0) {
      try {
        chartInstanceRef.current.clear();
      } catch (e) {
        console.warn('清空图表时出错:', e);
      }
      return;
    }

    const usernames = chartData.map((item) => item.username);
    const uploadCounts = chartData.map((item) => item.upload_count);
    const annotationCounts = chartData.map((item) => item.annotation_count);

    const option = {
      title: {
        text: '用户活动统计',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params: ChartTooltipParam | ChartTooltipParam[]) => {
          const currentParamsArray = (
            Array.isArray(params) ? params : [params]
          ) as ChartTooltipParam[];

          if (
            !currentParamsArray ||
            currentParamsArray.length === 0 ||
            !currentParamsArray[0] ||
            currentParamsArray[0].name === undefined
          ) {
            return '';
          }
          let result = `${currentParamsArray[0].name}<br/>`;
          currentParamsArray.forEach((param: ChartTooltipParam) => {
            result += `${param.marker}${param.seriesName}: ${param.value}<br/>`;
          });
          return result;
        },
      },
      legend: {
        data: ['上传图片数量', '标注数量'],
        top: 30,
      },
      grid: {
        left: '15%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        name: '数量',
        nameLocation: 'middle',
        nameGap: 30,
      },
      yAxis: {
        type: 'category',
        data: usernames,
        name: '用户名',
        nameLocation: 'middle',
        nameGap: 80,
        axisLabel: {
          interval: 0,
          rotate: 0,
          fontSize: 12,
          margin: 10,
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      series: [
        {
          name: '上传图片数量',
          type: 'bar',
          data: uploadCounts,
          itemStyle: {
            color: '#1890ff',
          },
          barWidth: '30%',
          barGap: '10%',
        },
        {
          name: '标注数量',
          type: 'bar',
          data: annotationCounts,
          itemStyle: {
            color: '#52c41a',
          },
          barWidth: '30%',
        },
      ],
    };

    try {
      chartInstanceRef.current.setOption(option, true);
    } catch (e) {
      console.warn('更新图表时出错:', e);
    }
  }, [chartData]);

  useEffect(() => {
    if (!isDestroyingRef.current) {
      const timer = setTimeout(updateChart, 100); // 延迟更新避免冲突
      return () => clearTimeout(timer);
    }
  }, [chartData, updateChart]);

  const fetchStatistics = async (params: StatisticsQueryParams) => {
    const adminToken = localStorage.getItem('admin_token');
    const adminLoggedIn = localStorage.getItem('adminLoggedIn') === 'true';

    if (!adminToken || !adminLoggedIn) {
      message.warning('请先登录管理员账户以查看统计数据');
      return;
    }

    setLoading(true);
    try {
      const { getUserStatistics } = await import('../../../../services/userStatisticsService');
      const response: UserStatisticsResponse = await getUserStatistics(params);

      if (!isDestroyingRef.current) {
        setChartData(response.data);

        if (response.data.length > 0) {
          message.success(`获取到 ${response.data.length} 条统计数据`);
        } else {
          message.info('未找到符合条件的统计数据');
        }
      }
    } catch (error: unknown) {
      if (isDestroyingRef.current) return;

      if (isAxiosError(error)) {
        if (error.response?.status === 401) {
          message.warning('管理员登录状态已过期，请重新登录后再查看统计数据');
        } else if (error.response?.status === 403) {
          message.warning('您没有权限访问统计数据，请联系系统管理员');
        } else if (
          error.code === 'ECONNABORTED' ||
          (error.message && error.message.includes('timeout'))
        ) {
          message.warning('请求超时，请检查网络连接后重试');
        } else {
          message.error('获取统计数据失败，请稍后重试');
        }
      } else if (error instanceof Error) {
        message.error(`获取统计数据失败: ${error.message}`);
      } else {
        message.error('获取统计数据失败，发生未知错误');
      }
      console.error('获取统计数据失败:', error);
      setChartData([]);
    } finally {
      if (!isDestroyingRef.current) {
        setLoading(false);
      }
    }
  };

  const handleSubmit = (values: StatisticsFormValues) => {
    const params: StatisticsQueryParams = {
      period: values.period || 'day',
      username: values.username,
    };

    if (values.dateRange) {
      params.start_date = values.dateRange[0].format('YYYY-MM-DD');
      params.end_date = values.dateRange[1].format('YYYY-MM-DD');
    }

    fetchStatistics(params);
  };

  const handleReset = () => {
    form.resetFields();
    setChartData([]);
    if (
      chartInstanceRef.current &&
      !chartInstanceRef.current.isDisposed() &&
      !isDestroyingRef.current
    ) {
      try {
        chartInstanceRef.current.clear();
      } catch (e) {
        console.warn('重置图表时出错:', e);
      }
    }
  };

  const chartComponent = useMemo(
    () => <IsolatedChart onMount={handleChartMount} onUnmount={handleChartUnmount} />,
    [chartData, handleChartMount, handleChartUnmount],
  );

  return (
    <Card
      title={
        <span>
          <BarChartOutlined style={{ marginRight: 8 }} />
          用户活动统计
        </span>
      }
      className={styles.statisticsCard}
      styles={{
        body: { paddingTop: 0 },
        header: { marginBottom: 0, paddingBottom: 8 }
      }}
    >
      <Form form={form} layout="horizontal" onFinish={handleSubmit} className={styles.queryForm} style={{ marginTop: 0 }}>
        <Row gutter={16}>
          <Col xs={24} sm={8} md={6}>
            <Form.Item name="username" label="用户名">
              <Input placeholder="输入用户名进行筛选" allowClear />
            </Form.Item>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Form.Item name="period" label="统计周期" initialValue="day">
              <Select>
                <Option value="day">按日统计</Option>
                <Option value="week">按周统计</Option>
                <Option value="month">按月统计</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} sm={8} md={8}>
            <Form.Item name="dateRange" label="日期范围">
              <RangePicker format="YYYY-MM-DD" placeholder={['开始日期', '结束日期']} />
            </Form.Item>
          </Col>
          <Col xs={24} sm={24} md={4}>
            <Form.Item>
              <div className={styles.buttonGroup}>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                  loading={loading}
                >
                  查询
                </Button>
                <Button onClick={handleReset} icon={<ReloadOutlined />} style={{ marginLeft: 8 }}>
                  重置
                </Button>
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <Spin spinning={loading}>
        <div style={{ width: '100%', height: '2000px', marginTop: '-8px', position: 'relative' }}>
          {!loading && chartData.length === 0 ? (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                color: '#999',
                fontSize: '16px',
              }}
            >
              <div style={{ marginBottom: '8px' }}>📊</div>
              <div>点击&ldquo;查询&rdquo;按钮获取用户活动统计数据</div>
            </div>
          ) : (
            chartComponent
          )}
        </div>
      </Spin>

      {chartData.length > 0 && (
        <div className={styles.statisticsInfo}>
          <Row gutter={16} style={{ marginTop: '16px' }}>
            <Col span={8}>
              <div className={styles.statItem}>
                <span className={styles.statLabel}>总用户数:</span>
                <span className={styles.statValue}>{chartData.length}</span>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles.statItem}>
                <span className={styles.statLabel}>总上传数:</span>
                <span className={styles.statValue}>
                  {chartData.reduce((sum, item) => sum + item.upload_count, 0)}
                </span>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles.statItem}>
                <span className={styles.statLabel}>总标注数:</span>
                <span className={styles.statValue}>
                  {chartData.reduce((sum, item) => sum + item.annotation_count, 0)}
                </span>
              </div>
            </Col>
          </Row>
        </div>
      )}
    </Card>
  );
};

export default UserStatisticsChart;
