.statisticsCard {
  margin-top: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);

  :global {
    .ant-card-head {
      background: linear-gradient(to right, #722ed1, #1890ff);
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        color: white;
        font-weight: 600;
      }
    }
  }
}

.queryForm {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;

  :global {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-form-item-label > label {
      font-weight: 500;
    }

    .ant-select-selector,
    .ant-input,
    .ant-picker {
      border-radius: 4px;
    }
  }
}

.buttonGroup {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;

  @media (max-width: 768px) {
    justify-content: center;
  }
}

.chartContainer {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
  padding: 16px;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:empty::before {
    content: '暂无数据，请先进行查询';
    color: #999;
    font-size: 14px;
  }
}

.statisticsInfo {
  margin-top: 16px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.statItem {
  text-align: center;
  padding: 8px;

  .statLabel {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
  }

  .statValue {
    display: block;
    font-size: 20px;
    font-weight: 600;
    color: #1890ff;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .queryForm {
    :global {
      .ant-form-item {
        margin-bottom: 12px;
      }
    }
  }

  .chartContainer {
    height: 300px !important;
  }

  .statisticsInfo {
    .statItem {
      margin-bottom: 8px;

      .statValue {
        font-size: 18px;
      }
    }
  }
}

@media (max-width: 576px) {
  .queryForm {
    padding: 12px;
  }

  .chartContainer {
    height: 250px !important;
    padding: 12px;
  }

  .buttonGroup {
    justify-content: center;
    width: 100%;
  }
}
