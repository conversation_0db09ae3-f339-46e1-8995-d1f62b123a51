import React, { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';
import { StagewiseToolbar } from '@stagewise/toolbar-react';

// 基本工具栏配置
const stagewiseConfig = {
  plugins: [],
};

/**
 * Stagewise开发工具组件
 * 该组件会创建一个单独的React根，避免干扰主应用
 */
export const StagewiseDevTools: React.FC = () => {
  const [_mounted, setMounted] = useState(false);

  useEffect(() => {
    // 确保只在客户端和开发环境中运行
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      // 创建一个新的DOM元素作为工具栏的容器
      const stagewiseContainer = document.createElement('div');
      stagewiseContainer.id = 'stagewise-toolbar-container';
      document.body.appendChild(stagewiseContainer);

      // 创建一个新的React根
      const stagewiseRoot = createRoot(stagewiseContainer);

      // 渲染工具栏
      stagewiseRoot.render(<StagewiseToolbar config={stagewiseConfig} />);

      setMounted(true);

      // 清理函数
      return () => {
        stagewiseRoot.unmount();
        if (document.body.contains(stagewiseContainer)) {
          document.body.removeChild(stagewiseContainer);
        }
      };
    }
  }, []);

  // 这个组件不会渲染任何内容
  return null;
};

/**
 * 初始化Stagewise开发工具
 * 只在开发环境中运行
 */
export const initStagewiseDevTools = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('初始化Stagewise开发工具...');

    // 创建一个新的DOM元素作为工具栏的容器
    const stagewiseContainer = document.createElement('div');
    stagewiseContainer.id = 'stagewise-toolbar-root';
    document.body.appendChild(stagewiseContainer);

    // 创建一个新的React根并渲染工具栏
    const stagewiseRoot = createRoot(stagewiseContainer);
    stagewiseRoot.render(<StagewiseToolbar config={stagewiseConfig} />);

    console.log('Stagewise开发工具已初始化');
  }
};
