# Git
.git
.gitignore

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
package-lock.json # pnpm-lock.yaml is used

# Build output (dist is copied from builder stage, so ignore from context)
dist/
build/
.cache/
.DS_Store
coverage/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
Thumbs.db

# Test files and reports (if not needed in image)
*.test.ts
*.spec.ts
*.test.tsx
*.spec.tsx
eslint-report.json
# test-*.html (example: test-mobile-compatibility.html)

# Markdown documents (unless specifically needed in the image)
*.md
# Keep README.md if it's useful, otherwise can be ignored.
# For example:
# !README.md

# Scripts (if only for local dev)
# dev.sh
# restart-dev.sh
# start-*.sh
# *.mjs (example: add-ts-nocheck.mjs)

# Other temporary or local files
*.tmp
*.bak