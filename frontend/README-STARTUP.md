# 🚀 前端开发环境启动指南

本项目提供了**两种独立的**前端开发环境启动方式：

## 📦 方式1：正常启动（推荐日常使用）

**使用场景**：日常前端开发工作（纯净开发环境）

```bash
cd frontend
pnpm start
```

**特点**：

- ✅ 启动速度快
- ✅ 开发环境干净
- ✅ 内存占用少
- ✅ 适合纯代码开发

## 🎨 方式2：Stagewise启动（设计协作时使用）

**使用场景**：需要Stagewise工具进行设计协作和界面调试

```bash
cd frontend
pnpm start:stagewise
```

**特点**：

- ✅ 包含完整Stagewise工具栏
- ✅ 支持设计协作功能
- ✅ 界面调试和标注
- ⚠️ 启动稍慢，环境较重

---

## 📋 详细启动选项

### 正常启动方式

```bash
# 推荐：使用预设脚本
pnpm start

# 或者：直接执行脚本文件
./start-dev.sh

# 或者：直接使用vite
pnpm dev
```

### Stagewise启动方式

```bash
# 推荐：使用预设脚本
pnpm start:stagewise

# 或者：直接执行脚本文件
./start-dev-stagewise.sh

# 或者：手动设置环境变量
VITE_ENABLE_STAGEWISE=true pnpm dev
```

## 🎯 什么时候使用Stagewise？

使用 `pnpm start:stagewise` 当您需要：

- 与设计师协作调试界面
- 使用Cursor编辑器的Stagewise扩展
- 进行响应式设计调试
- 需要界面标注和反馈功能

使用 `pnpm start` 当您进行：

- 日常代码开发
- 性能调试
- 生产环境测试
- 需要更快的启动速度

## 🔧 技术细节

两种启动方式的区别：

| 特性            | 正常启动 | Stagewise启动 |
| --------------- | -------- | ------------- |
| Stagewise工具栏 | ❌       | ✅            |
| 启动速度        | 快       | 稍慢          |
| 内存占用        | 低       | 中等          |
| 开发体验        | 纯净     | 增强          |
| 适用场景        | 日常开发 | 设计协作      |

## 🌐 访问地址

无论使用哪种启动方式，都可以通过以下地址访问：

- **本地访问**: http://localhost:3000
- **网络访问**: http://[您的IP]:3000

## 🐛 故障排除

### 如果启动失败

1. 确保已安装依赖：

```bash
pnpm install
```

2. 清除缓存：

```bash
rm -rf node_modules/.vite
```

3. 检查Node.js版本（需要 >= 18）

### 如果Stagewise工具不显示

确认使用了正确的启动命令：

```bash
pnpm start:stagewise  # 而不是 pnpm start
```

## ⚙️ 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0
- 现代浏览器（Chrome、Firefox、Safari、Edge）

---

💡 **推荐工作流程**：

- 日常开发使用：`pnpm start`
- 设计协作时使用：`pnpm start:stagewise`

📚 **更多详细信息**：请参阅 [README-STAGEWISE.md](./README-STAGEWISE.md) 了解Stagewise工具的详细功能说明。
