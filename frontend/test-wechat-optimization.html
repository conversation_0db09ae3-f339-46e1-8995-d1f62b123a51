<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>微信浏览器优化测试</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 600px;
        margin: 0 auto;
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      .test-item {
        margin: 15px 0;
        padding: 15px;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        background: #fafafa;
      }
      .test-title {
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
      }
      .test-result {
        font-size: 14px;
        color: #666;
      }
      .success {
        color: #52c41a;
      }
      .warning {
        color: #fa8c16;
      }
      .error {
        color: #f5222d;
      }
      .privacy-notice {
        background: #e6f7ff;
        border: 1px solid #91d5ff;
        border-radius: 6px;
        padding: 12px;
        margin: 15px 0;
        font-size: 13px;
        color: #0050b3;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🔧 微信浏览器优化测试</h1>
      <p>此页面用于测试微信浏览器优化功能是否正常工作</p>

      <div id="test-results"></div>

      <div class="privacy-notice">
        🔒 <strong>隐私保护说明：</strong>
        在微信浏览器中，系统会自动隐藏敏感的设备信息以保护您的隐私。
        这包括详细的UserAgent信息、设备指纹等敏感数据。
      </div>
    </div>

    <script>
      // 模拟微信浏览器检测函数
      function isWechatBrowser() {
        return /MicroMessenger/i.test(navigator.userAgent);
      }

      // 模拟设备信息获取函数
      function getWechatSafeDeviceInfo() {
        const deviceId = localStorage.getItem('device_id') || `wechat_device_${Date.now()}`;

        if (isWechatBrowser()) {
          return {
            device_id: deviceId,
            user_agent: 'WeChat Browser',
            platform: 'Mobile',
            browser_type: 'wechat',
            privacy_protected: true,
          };
        }

        return {
          device_id: deviceId,
          user_agent: navigator.userAgent,
          platform: navigator.platform,
          browser_type: 'standard',
          privacy_protected: false,
        };
      }

      // 模拟登录设备信息获取函数
      function getWechatLoginDeviceInfo() {
        const deviceId = localStorage.getItem('device_id') || `wechat_device_${Date.now()}`;

        if (isWechatBrowser()) {
          console.log('🔒 微信浏览器：使用隐私保护的设备信息');

          return {
            device_id: deviceId,
            user_agent: 'WeChat',
            platform: 'WeChat',
            browser_type: 'wechat',
            timestamp: Date.now().toString(),
          };
        }

        console.log('📱 标准浏览器：使用完整设备信息');
        return {
          device_id: deviceId,
          user_agent: navigator.userAgent,
          platform: navigator.platform,
          browser_type: 'standard',
          timestamp: Date.now().toString(),
        };
      }

      // 运行测试
      function runTests() {
        const resultsContainer = document.getElementById('test-results');
        const tests = [];

        // 测试1：微信浏览器检测
        const isWechat = isWechatBrowser();
        tests.push({
          title: '微信浏览器检测',
          result: isWechat ? '✅ 检测到微信浏览器' : '❌ 非微信浏览器',
          class: isWechat ? 'success' : 'warning',
        });

        // 测试2：设备信息隐私保护
        const deviceInfo = getWechatSafeDeviceInfo();
        const isPrivacyProtected = deviceInfo.privacy_protected;
        tests.push({
          title: '设备信息隐私保护',
          result: isPrivacyProtected
            ? '✅ 隐私保护已启用，敏感信息已隐藏'
            : '⚠️ 标准模式，显示完整设备信息',
          class: isPrivacyProtected ? 'success' : 'warning',
        });

        // 测试3：登录设备信息处理
        const loginInfo = getWechatLoginDeviceInfo();
        const isLoginProtected = loginInfo.browser_type === 'wechat';
        tests.push({
          title: '登录设备信息处理',
          result: isLoginProtected ? '✅ 登录时使用简化设备信息' : '⚠️ 登录时使用完整设备信息',
          class: isLoginProtected ? 'success' : 'warning',
        });

        // 测试4：UserAgent信息处理
        const userAgent = deviceInfo.user_agent;
        const isUserAgentSimplified = userAgent === 'WeChat Browser' || userAgent === 'WeChat';
        tests.push({
          title: 'UserAgent信息处理',
          result: isUserAgentSimplified
            ? `✅ UserAgent已简化: ${userAgent}`
            : `⚠️ 显示完整UserAgent: ${userAgent.substring(0, 50)}...`,
          class: isUserAgentSimplified ? 'success' : 'warning',
        });

        // 测试5：平台信息处理
        const platform = deviceInfo.platform;
        const isPlatformSimplified = platform === 'Mobile' || platform === 'WeChat';
        tests.push({
          title: '平台信息处理',
          result: isPlatformSimplified
            ? `✅ 平台信息已简化: ${platform}`
            : `⚠️ 显示完整平台信息: ${platform}`,
          class: isPlatformSimplified ? 'success' : 'warning',
        });

        // 渲染测试结果
        resultsContainer.innerHTML = tests
          .map(
            (test) => `
                <div class="test-item">
                    <div class="test-title">${test.title}</div>
                    <div class="test-result ${test.class}">${test.result}</div>
                </div>
            `,
          )
          .join('');

        // 显示详细信息
        const detailsHtml = `
                <div class="test-item">
                    <div class="test-title">详细设备信息</div>
                    <div class="test-result">
                        <pre style="font-size: 12px; background: #f0f0f0; padding: 10px; border-radius: 4px; overflow-x: auto;">
设备信息: ${JSON.stringify(deviceInfo, null, 2)}

登录信息: ${JSON.stringify(loginInfo, null, 2)}

浏览器环境:
- 是否微信浏览器: ${isWechat}
- 屏幕尺寸: ${window.innerWidth}x${window.innerHeight}
- 设备像素比: ${window.devicePixelRatio}
- 触摸支持: ${'ontouchstart' in window}
- 在线状态: ${navigator.onLine}
                        </pre>
                    </div>
                </div>
            `;

        resultsContainer.innerHTML += detailsHtml;

        // 控制台输出
        console.log('🔧 微信浏览器优化测试结果:');
        console.log('设备信息:', deviceInfo);
        console.log('登录信息:', loginInfo);
        console.log('测试完成时间:', new Date().toISOString());
      }

      // 页面加载完成后运行测试
      document.addEventListener('DOMContentLoaded', runTests);
    </script>
  </body>
</html>
