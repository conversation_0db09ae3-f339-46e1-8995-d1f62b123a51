# 多阶段构建 - Node.js构建阶段
FROM node:20-alpine3.18@sha256:f20b033422222222222222222222222222222222222222222222222222222222 as builder

# 设置工作目录
WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm

# 复制package文件
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建参数
ARG VITE_API_URL=http://localhost:8000
ARG VITE_PUBLIC_URL=""
ARG NODE_ENV=production

# 设置环境变量
ENV VITE_API_URL=${VITE_API_URL}
ENV VITE_PUBLIC_URL=${VITE_PUBLIC_URL}
ENV NODE_ENV=${NODE_ENV}

# 构建应用
RUN pnpm run build

# 生产阶段 - 仅包含静态文件
FROM alpine:latest as production

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 这个阶段的镜像主要用于被外部Nginx服务挂载静态文件
# 或者作为其他Nginx镜像的基础层。
# 它本身不运行Nginx服务。
# 如果需要一个能独立运行的前端服务（包含Nginx），
# 应该确保其Nginx配置与生产环境的Nginx服务一致。

# 开发阶段
FROM node:20-alpine3.18@sha256:f20b033422222222222222222222222222222222222222222222222222222222 as development

# 设置工作目录
WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm

# 复制package文件
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install

# 暴露端口
EXPOSE 3000 3002

# 开发启动命令
CMD ["pnpm", "dev", "--host", "0.0.0.0", "--port", "3000"]