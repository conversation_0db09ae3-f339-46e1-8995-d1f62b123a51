name: ema-macos-m4
channels:
  - conda-forge
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - aiofiles=22.1.0 # 异步文件操作库
  - alembic=1.15.2 # 数据库迁移工具 (SQLAlchemy)
  - annotated-types=0.6.0 # Pydantic 和 FastAPI 中用于类型注解的工具
  - anyio=4.7.0 # 异步编程的通用后端 (如 asyncio, trio)
  - async-timeout=5.0.1 # 异步操作超时控制
  - asyncpg=0.30.0 # 异步 PostgreSQL 驱动
  - black=24.3.0 # Python 代码格式化工具
  - blas=1.0 # 基础线性代数子程序 (BLAS) - 元包
  - bottleneck=1.4.2 # 优化的数值计算函数 (NumPy 加速)
  - brotli-python=1.0.9 # Brotli 压缩算法的 Python 绑定
  - bzip2=1.0.8 # bzip2 压缩库
  - ca-certificates=2025.2.25 # CA 证书包
  - certifi=2025.1.31 # Python SSL 证书包
  - cffi=1.17.1 # C Foreign Function Interface for Python
  - cfgv=3.4.0 # 配置文件验证工具 (pre-commit 使用)
  - click=8.1.8 # 创建命令行界面的库
  - contourpy=1.3.1 # Matplotlib 依赖，用于生成等高线
  - cryptography=44.0.1 # 加密库
  - cycler=0.11.0 # Matplotlib 依赖，用于颜色循环等
  - distlib=0.3.8 # Python 打包分发工具库
  - dnspython=2.4.2 # DNS 工具包
  - email-validator=2.2.0 # 电子邮件地址验证
  - email_validator=2.2.0 # 电子邮件地址验证 (与上一行包名大小写不同，功能相同)
  - exceptiongroup=1.2.0 # Python 异常组支持 (PEP 654)
  - fastapi=0.115.12 # 高性能 Web 框架
  - fastapi-cli=0.0.7 # FastAPI 命令行工具
  - filelock=3.17.0 # 平台无关的文件锁
  - fonttools=4.55.3 # 处理字体的库
  - freetype=2.13.3 # 字体渲染引擎
  - greenlet=3.1.1 # 轻量级并发编程 (gevent 基础)
  - h11=0.14.0 # HTTP/1.1 协议实现 (uvicorn/hypercorn 依赖)
  - httpcore=1.0.2 # HTTP 客户端核心库 (httpx 依赖)
  - httptools=0.6.4 # HTTP 解析器 (uvloop 依赖)
  - httpx=0.27.0 # 现代的异步 HTTP 客户端
  - icu=75.1 # International Components for Unicode (Unicode 支持)
  - identify=2.5.5 # 文件类型识别 (pre-commit 使用)
  - idna=3.7 # Internationalized Domain Names in Applications (IDNA)
  - iniconfig=1.1.1 # .ini 文件解析 (pytest 依赖)
  - isort=6.0.1 # Python import 排序工具
  - jinja2=3.1.6 # 模板引擎
  - kiwisolver=1.4.8 # Matplotlib 依赖，高效的约束求解器
  - lcms2=2.17 # Little CMS 2，色彩管理引擎
  - lerc=4.0.0 # Limited Error Raster Compression (图像压缩)
  - libcxx=20.1.3 # LLVM C++ 标准库
  - libdeflate=1.23 # DEFLATE 压缩/解压缩库
  - libexpat=2.7.0 # Expat XML 解析库
  - libffi=3.4.4 # Foreign Function Interface 库
  - libfreetype=2.13.3 # FreeType 字体渲染引擎 (与 freetype 功能类似)
  - libfreetype6=2.13.3 # FreeType 字体渲染引擎 (可能是不同渠道或构建的包)
  - libgfortran=5.0.0 # GNU Fortran 库
  - libgfortran5=14.2.0 # GNU Fortran 库 (版本5)
  - libjpeg-turbo=3.0.3 # JPEG 图像编解码库 (高性能)
  - liblzma=5.8.1 # LZMA 压缩库 (xz)
  - libopenblas=0.3.27 # OpenBLAS 线性代数库
  - libpng=1.6.47 # PNG 图像库
  - libsqlite=3.49.1 # SQLite 数据库引擎
  - libtiff=4.7.0 # TIFF 图像库
  - libuv=1.50.0 # 多平台异步 I/O 库 (Node.js, uvloop 使用)
  - libwebp-base=1.5.0 # WebP 图像格式库
  - libxcb=1.17.0 # X C Binding (X Window 系统)
  - libzlib=1.3.1 # zlib 压缩库 (与 zlib 功能类似)
  - llvm-openmp=20.1.3 # LLVM OpenMP 运行时库
  - mako=1.2.3 # Python 模板库 (Alembic 依赖)
  - markupsafe=3.0.2 # Jinja2 依赖，用于处理 HTML/XML 字符串转义
  - mccabe=0.7.0 # McCabe 复杂度检查插件 (flake8 依赖)
  - mypy=1.9.0 # Python 静态类型检查器
  - mypy_extensions=1.0.0 # mypy 的实验性扩展
  - ncurses=6.5 # 终端字符界面库
  - nodeenv=1.9.1 # Node.js 虚拟环境工具 (pre-commit 使用)
  - nodejs=22.13.0 # Node.js 运行时
  - numexpr=2.10.1 # NumPy 表达式快速求值器
  - openblas=0.3.27 # OpenBLAS 线性代数库 (与 libopenblas 功能类似)
  - openjpeg=2.5.3 # JPEG 2000 图像编解码库
  - openssl=3.5.0 # OpenSSL 加密工具包
  - packaging=24.2 # Python 包核心工具
  - pathspec=0.10.3 # .gitignore 风格路径匹配 (black, isort 使用)
  - platformdirs=4.3.7 # 获取平台特定目录 (如用户数据目录)
  - pluggy=1.5.0 # 插件系统 (pytest, tox 使用)
  - pre-commit=4.2.0 # Git 钩子管理工具
  - pthread-stubs=0.3 # POSIX 线程存根 (用于 X11)
  - pycparser=2.21 # C 语言解析器 (cffi 依赖)
  - pygments=2.19.1 # 代码语法高亮库 (rich 依赖)
  - pyparsing=3.2.0 # 通用解析模块 (packaging 依赖)
  - python=3.10.16 # Python 解释器
  - python-dateutil=2.9.0post0 # 日期时间处理扩展
  - python-dotenv=1.1.0 # 从 .env 文件加载环境变量
  - python-multipart=0.0.20 # FastAPI 依赖，用于解析 multipart/form-data
  - python-tzdata=2023.3 # IANA 时区数据库的 Python 封装 (与 pytz 类似)
  - python_abi=3.10 # Python ABI 标记 (conda 元数据)
  - pytz=2024.1 # 世界时区定义
  - pyyaml=6.0.2 # YAML 解析器和生成器
  - readline=8.2 # GNU readline 库接口
  - rich=13.9.4 # 在终端中创建富文本和美观格式的库
  - ruff=0.11.7 # 极快的 Python linter 和格式化工具
  - scipy=1.15.2 # 科学计算库
  - setuptools=75.8.0 # Python 包构建和分发工具
  - shellingham=1.5.0 # 检测当前 shell 环境 (typer, rich 使用)
  - sniffio=1.3.0 # 异步库嗅探器 (anyio, httpcore 使用)
  - starlette=0.46.2 # 轻量级 ASGI 框架/工具包 (FastAPI 基础)
  - tk=8.6.13 # Tcl/Tk GUI 工具包
  - tomli=2.0.1 # TOML 解析器 (用于 pyproject.toml)
  - tornado=6.4.2 # Web 框架和异步网络库
  - tqdm=4.67.1 # 进度条库
  - typer=0.15.3 # 基于 Python 类型提示构建 CLI 应用的库 (基于 Click)
  - typer-slim=0.15.3 # Typer 的精简版本
  - typer-slim-standard=0.15.3 # Typer 精简版的标准依赖集合
  - typing-inspection=0.4.0 # Python 类型提示检查工具
  - tzdata=2025a # IANA 时区数据库 (与 python-tzdata 类似)
  - ukkonen=1.0.1 # Ukkonen 算法实现 (用于模糊字符串匹配)
  - unicodedata2=15.1.0 # Unicode 数据库接口 (Python unicodedata 模块的替代)
  - uvicorn=0.34.2 # ASGI 服务器 (FastAPI 常用)
  - uvicorn-standard=0.34.2 # Uvicorn 标准依赖集合
  - uvloop=0.21.0 # asyncio 事件循环的快速替代品 (基于 libuv)
  - virtualenv=20.28.0 # Python 虚拟环境创建工具
  - watchfiles=0.24.0 # 文件系统变更监控 (uvicorn --reload 使用)
  - websockets=15.0.1 # WebSocket 协议实现
  - wheel=0.45.1 # Python wheel 包格式构建工具
  - xorg-libxau=1.0.12 # X.Org X11 X授权库
  - xorg-libxdmcp=1.1.5 # X.Org X11 X显示管理器控制协议库
  - yaml=0.2.5 # YAML C 库 (PyYAML 可能使用)
  - zlib=1.3.1 # zlib 压缩库
  - zstd=1.5.7 # Zstandard 压缩算法库
  - pip:
    - absl-py==2.2.2 # Abseil Python 公共库 (TensorFlow 使用)
    - aiohappyeyeballs==2.6.1 # 异步 Happy Eyeballs 算法实现 (aiohttp 依赖)
    - aiohttp==3.12.2 # 异步 HTTP 客户端/服务器框架
    - aiosignal==1.3.2 # 异步信号处理 (aiohttp 依赖)
    - aiosmtplib==3.0.2 # 异步 SMTP 客户端
    - albumentations==1.3.1 # 图像增强库
    - argcomplete==3.5.3 # argparse 参数自动补全
    - arrow==1.3.0 # 更好的 Python 日期和时间处理库
    - asttokens==3.0.0 # 将 AST 节点与 Python 源代码关联
    - astunparse==1.6.3 # AST 反向解析为 Python 源代码 (TensorFlow 使用)
    - attrs==25.3.0 # 简化 Python 类定义的库
    - bandit==1.7.5 # Python 代码安全漏洞扫描工具
    - bcrypt==4.3.0 # bcrypt 密码哈希算法
    - beautifulsoup4==4.13.4 # HTML 和 XML 解析库
    - binaryornot==0.4.4 # 判断文件是二进制还是文本 (cookiecutter 使用)
    - blinker==1.9.0 # Python 信号/事件分发库
    - build==1.2.2.post1 # Python 项目构建前端 (PEP 517)
    - cachetools==5.5.2 # 可扩展的缓存工具 (google-auth 使用)
    - chardet==5.2.0 # 字符编码检测库 (binaryornot 使用)
    - charset-normalizer==3.4.1 # 字符编码检测库 (requests 依赖)
    - code2flow==2.5.1 # Python/JavaScript 代码流程图生成器
    - colorama==0.4.6 # 跨平台彩色终端文本
    - coloredlogs==15.0.1 # Python logging 模块的彩色日志输出
    - commitizen==4.6.0 # Git 提交规范工具
    - cookiecutter==2.6.0 # 项目模板创建工具
    - copier==9.7.1 # 项目模板工具 (类似 cookiecutter)
    - cython==3.0.12 # C-Extensions for Python 编译器
    - decli==0.6.2 # 声明式命令行界面库
    - decorator==5.2.1 # 简化装饰器编写的库
    - deepface==0.0.93 # 轻量级人脸识别和分析框架
    - deprecated==1.2.18 # 标记弃用函数/类的装饰器
    - diffusers==0.32.2 # Hugging Face Diffusers (扩散模型)
    - dunamai==1.23.1 # 从 Git/Mercurial/Darcs/etc. 生成版本字符串
    - easydict==1.13 # 类似字典的点属性访问
    - ecdsa==0.19.1 # ECDSA 签名库 (python-jose 使用)
    - emotionai==0.1.0 # 项目自定义包 (情绪分析核心逻辑)
    - executing==2.2.0 # 获取当前执行的 AST 节点 (stack_data 使用)
    - fastapi-mail==1.5.0 # FastAPI 邮件发送库
    - fire==0.7.0 # 自动生成 CLI 的库 (Google)
    - flake8==7.2.0 # Python 代码风格和错误检查工具
    - flask==3.1.0 # 轻量级 Web 框架 (DeepFace 可能使用)
    - flask-cors==5.0.1 # Flask CORS 支持 (DeepFace 可能使用)
    - flatbuffers==25.2.10 # 高效跨平台序列化库 (TensorFlow Lite 使用)
    - frozenlist==1.6.0 # 不可变列表 (aiohttp 依赖)
    - fsspec==2025.3.2 # 文件系统规范 (pandas, huggingface-hub 使用)
    - funcy==2.0 # 函数式编程工具库
    - gast==0.4.0 # 通用 AST (TensorFlow 使用)
    - gdown==5.2.0 # 从 Google Drive 下载文件的工具
    - gitdb==4.0.12 # Git 对象数据库 (GitPython 依赖)
    - gitpython==3.1.44 # 与 Git 仓库交互的 Python 库
    - google-ai-generativelanguage==0.6.15 # Google AI 生成语言 API 客户端
    - google-api-core==2.25.0rc1 # Google Cloud 客户端库核心
    - google-api-python-client==2.169.0 # Google API Python 客户端库
    - google-auth==2.40.1 # Google Authentication Library for Python
    - google-auth-httplib2==0.2.0 # google-auth 与 httplib2 的集成
    - google-auth-oauthlib==1.0.0 # google-auth 与 oauthlib 的集成
    - google-generativeai==0.8.5 # Google Generative AI SDK
    - google-pasta==0.2.0 # Python AST 操作库 (TensorFlow 使用)
    - googleapis-common-protos==1.70.0 # Google API 通用 Protocol Buffers
    - grpcio==1.71.0 # gRPC 远程过程调用框架
    - gunicorn==23.0.0 # WSGI HTTP 服务器 (生产环境常用)
    - h5py==3.13.0 # HDF5 文件读写库 (Keras 模型保存使用)
    - httplib2==0.22.0 # HTTP 客户端库
    - huggingface-hub==0.30.2 # Hugging Face Hub 模型和数据集交互库
    - humanfriendly==10.0 # 人性化文本格式化 (coloredlogs 使用)
    - imageio==2.37.0 # 图像读写库
    - importlib-metadata==8.7.0 # Python 包元数据读取
    - insightface==0.7.3 # 人脸识别分析库
    - itsdangerous==2.2.0 # 安全数据序列化 (Flask 使用)
    - jax==0.4.34 # 高性能数值计算和机器学习研究库 (Google)
    - jaxlib==0.4.34 # JAX 的底层 C++ 库
    - jedi==0.19.2 # Python 代码自动补全和静态分析库
    - jinja2-ansible-filters==1.3.2 # Jinja2 模板的 Ansible 过滤器
    - joblib==1.4.2 # Python 函数的轻量级流水线操作 (scikit-learn 使用)
    - jsonpickle==4.0.5 # Python 对象与 JSON 之间的序列化/反序列化
    - keras==3.9.2 # 深度学习 API (TensorFlow, JAX, PyTorch 后端)
    - lazy-loader==0.4 # 模块和属性的惰性加载
    - libclang==18.1.1 # Clang C++ 库的 Python 绑定
    - limits==5.1.0 # Python 速率限制库 (slowapi 依赖)
    - lz4==4.4.4 # LZ4 压缩算法的 Python 绑定
    - markdown==3.8 # Python Markdown 解析器
    - markdown-it-py==3.0.0 # Python Markdown 解析器 (CommonMark 规范)
    - matplotlib==3.10.1 # Python 绘图库
    - matplotlib-inline==0.1.7 # Matplotlib 在 IPython/Jupyter 中的内联后端
    - mdurl==0.1.2 # Markdown URL/Link 处理 (markdown-it-py 依赖)
    - mediapipe==0.10.21 # Google MediaPipe (多模态机器学习解决方案)
    - ml-dtypes==0.3.2 # 机器学习数据类型 (如 bfloat16, TensorFlow/JAX 使用)
    - mpmath==1.3.0 # Python 任意精度浮点算术库 (sympy 依赖)
    - mtcnn==1.0.0 # MTCNN 人脸检测模型
    - multidict==6.4.4 # 类似字典的多值数据结构 (aiohttp 依赖)
    - namex==0.0.9 # Keras 依赖，用于名称管理
    - networkx==3.4.2 # 图论和复杂网络库 (pyvis 依赖)
    - numpy==1.26.4 # Python 科学计算基础包
    - oauthlib==3.2.2 # OAuth 协议实现 (requests-oauthlib 依赖)
    - onnx==1.17.0 # Open Neural Network Exchange (开放神经网络交换格式)
    - onnxruntime==1.21.1 # ONNX 模型运行时 (推理引擎)
    - opencv-python-headless==********* # OpenCV 计算机视觉库 (无 GUI)
    - opt-einsum==3.4.0 # 优化的 Einstein 求和 (TensorFlow 使用)
    - optree==0.15.0 # 优化的 PyTree 操作 (JAX 使用)
    - pandas==2.0.3 # 数据分析和处理库
    - parso==0.8.4 # Python 解析器 (jedi 依赖)
    - passlib==1.7.4 # 密码哈希库
    - pbr==6.1.1 # Python Build Reasonableness (setuptools 扩展, stevedore 使用)
    - pexpect==4.9.0 # 控制和自动化其他程序的 Python 模块
    - pgvector==0.4.1 # PostgreSQL 向量相似性搜索扩展的 Python 客户端
    - pillow==11.2.1 # Python 图像处理库 (PIL fork)
    - pip==25.1.1 # Python 包安装器
    - pip-tools==7.4.1 # Python 依赖管理工具 (生成 requirements 文件)
    - pipdeptree==2.26.1 # 显示 Python 包依赖树
    - plumbum==1.9.0 # Shell 组合和 CLI 应用构建库
    - prettytable==3.16.0 # 在终端中创建美观的表格
    - prometheus-client==0.21.1 # Prometheus 监控客户端
    - prometheus-fastapi-instrumentator==7.1.0 # FastAPI 的 Prometheus 监控集成
    - prompt-toolkit==3.0.51 # 构建交互式命令行应用的库 (questionary 使用)
    - propcache==0.3.1 # 属性缓存库
    - proto-plus==1.26.1 # Protocol Buffers 增强 (google-cloud 客户端使用)
    - protobuf==4.25.7 # Google Protocol Buffers (数据序列化)
    - psutil==7.0.0 # 系统和进程信息
    - psycopg2-binary==2.9.10 # PostgreSQL 数据库适配器 (二进制包)
    - ptyprocess==0.7.0 # 伪终端进程管理 (pexpect 依赖)
    - pure-eval==0.2.3 # 安全地评估 Python 表达式的子集 (stack_data 使用)
    - py-cpuinfo==9.0.0 # 获取 CPU 信息的库
    - pyan3==1.2.0 # Python 代码静态分析，生成调用图
    - pyasn1==0.6.1 # ASN.1 库 (google-auth, rsa 使用)
    - pyasn1-modules==0.4.2 # ASN.1 模块集合 (pyasn1 扩展)
    - pycodestyle==2.13.0 # Python 代码风格检查 (PEP 8) (flake8 依赖)
    - pydantic==2.11.4 # 数据验证和设置管理库
    - pydantic-core==2.33.2 # Pydantic 核心验证逻辑 (Rust 实现)
    - pydantic-settings==2.9.1 # Pydantic 模型设置管理
    - pyflakes==3.3.2 # Python 代码错误检查 (flake8 依赖)
    - pyjwt==2.10.1 # JSON Web Token (JWT) 实现
    - pyproject-hooks==1.2.0 # PEP 517 构建后端钩子 (build 使用)
    - pysocks==1.7.1 # SOCKS 代理客户端 (urllib3 使用)
    - pytest==8.3.5 # Python 测试框架
    - python-jose==3.4.0 # JavaScript 对象签名和加密(JOSE)实现
    - python-slugify==8.0.4 # 将字符串转换为 slug (URL友好)
    - pyvis==0.3.2 # 网络可视化库 (基于 vis.js)
    - qudida==0.0.4 # Albumentations 依赖
    - questionary==2.1.0 # 构建交互式命令行提示
    - redis==6.0.0 # Redis 客户端库
    - redislite==6.2.912183 # 轻量级 Redis (嵌入式)
    - regex==2024.11.6 # 增强的正则表达式库 (替代 re)
    - requests==2.32.3 # HTTP 请求库
    - requests-oauthlib==2.0.0 # requests 与 oauthlib 的集成
    - retina-face==0.0.17 # RetinaFace 人脸检测模型
    - rich-toolkit==0.14.6 # Rich 库的工具集
    - rsa==4.9.1 # RSA 加密/解密库 (google-auth 使用)
    - safetensors==0.5.3 # 安全快速的张量序列化格式
    - scikit-image==0.25.2 # 图像处理库
    - scikit-learn==1.6.1 # 机器学习库
    - seaborn==0.13.2 # 基于 Matplotlib 的统计数据可视化库
    - sentencepiece==0.2.0 # 无监督文本分词器 (Transformers 使用)
    - simsimd==6.2.1 # SIMD 加速的相似性度量 (如余弦相似度)
    - six==1.15.0 # Python 2 和 3 兼容性库 (许多旧库依赖)
    - slowapi==0.1.9 # FastAPI 速率限制库
    - smmap==5.0.2 # 内存映射文件管理 (gitdb 使用)
    - sounddevice==0.5.1 # PortAudio 库的 Python 绑定 (音频录制和播放)
    - soundfile==0.13.1 # 音频文件读写库 (基于 libsndfile)
    - soupsieve==2.7 # CSS 选择器实现 (BeautifulSoup4 依赖)
    - sqlalchemy==2.0.41 # SQL 工具包和对象关系映射器 (ORM)
    - stack-data==0.6.3 # 从堆栈帧中提取信息 (executing, pure_eval 依赖)
    - stevedore==5.4.1 # 动态插件管理 (OpenStack 项目常用)
    - stringzilla==3.12.5 # 高性能字符串处理库 (SIMD 加速)
    - sympy==1.13.1 # Python 符号计算库
    - tensorboard==2.16.2 # TensorFlow 可视化工具包
    - tensorboard-data-server==0.7.2 # TensorBoard 数据后端服务器
    - tensorboard-plugin-wit==1.8.1 # TensorBoard What-If Tool 插件
    - tensorflow==2.16.2 # 深度学习框架 (Google)
    - tensorflow-estimator==2.13.0 # TensorFlow Estimator API (高级 API)
    - tensorflow-io-gcs-filesystem==0.37.1 # TensorFlow Google Cloud Storage 文件系统支持
    - tensorflow-metal==1.1.0 # TensorFlow Metal PluggableDevice for GPU acceleration on Apple Silicon
    - termcolor==2.5.0 # ANSII 彩色终端文本格式化 (fire, tensorflow 使用)
    - text-unidecode==1.3 # 将 Unicode 文本转换为 ASCII 表示 (python-slugify 使用)
    - tf-keras==2.16.0 # Keras API for TensorFlow (独立包)
    - threadpoolctl==3.6.0 # 控制线程池大小 (scikit-learn, joblib 使用)
    - tifffile==2025.3.30 # TIFF 文件读写库 (scikit-image 使用)
    - timm==1.0.15 # PyTorch Image Models (图像模型库)
    - tokenizers==0.21.1 # Hugging Face Tokenizers (快速文本分词)
    - tomlkit==0.13.2 # TOML 解析和写入库 (commitizen 使用)
    - torch==2.6.0 # PyTorch 深度学习框架
    - torchaudio==2.6.0 # PyTorch 音频处理库
    - torchvision==0.21.0 # PyTorch 计算机视觉库
    - traitlets==5.14.3 # IPython/Jupyter 配置系统组件
    - transformers==4.51.3 # Hugging Face Transformers (NLP 模型)
    - types-python-dateutil==2.9.0.20241206 # python-dateutil 的类型提示
    - types-pytz==2025.2.0.20250326 # pytz 的类型提示
    - types-requests==2.32.0.20250328 # requests 的类型提示
    - types-setuptools==80.0.0.20250429 # setuptools 的类型提示
    - typing-extensions==4.13.2 # Python 类型提示的向后兼容扩展
    - ultralytics==8.3.120 # YOLOv8 等目标检测模型
    - ultralytics-thop==2.0.14 # Ultralytics 模型的 FLOPs 计算工具 (基于 THOP)
    - uritemplate==4.1.1 # URI 模板 (RFC 6570) 实现 (google-api-python-client 使用)
    - urllib3==2.4.0 # HTTP 客户端库 (requests 依赖)
    - wcwidth==0.2.13 # 确定字符在终端中的宽度的函数 (prompt_toolkit 使用)
    - werkzeug==3.1.3 # WSGI 工具库 (Flask 依赖)
    - wrapt==1.17.2 # Python 装饰器和猴子补丁工具 (deprecated 使用)
    - yarl==1.20.0 # URL 解析和操作库 (aiohttp 依赖)
    - zipp==3.21.0 # zip 文件读取 (importlib_metadata 依赖)
prefix: /Volumes/acasis/miniconda3/miniconda3/envs/ema-macos-m4