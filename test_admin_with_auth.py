#!/usr/bin/env python3
"""
带认证的管理员API测试
"""

import asyncio
import httpx
import json
from datetime import datetime

# API 基础配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

async def login_as_admin():
    """登录获取管理员token"""
    async with httpx.AsyncClient() as client:
        # 尝试登录
        login_data = {
            "username": "admin",  # 假设有admin用户
            "password": "admin123"  # 假设密码
        }
        
        try:
            response = await client.post(f"{API_BASE}/auth/login", json=login_data)
            if response.status_code == 200:
                result = response.json()
                return result.get("access_token")
            else:
                print(f"登录失败: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"登录异常: {e}")
            return None

async def test_admin_analysis_with_auth():
    """使用认证测试管理员分析API"""
    print("🧪 使用认证测试管理员分析API...")
    print("=" * 50)
    
    # 先尝试登录
    token = await login_as_admin()
    if not token:
        print("❌ 无法获取管理员token，跳过认证测试")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    
    async with httpx.AsyncClient() as client:
        try:
            # 访问管理员分析API
            response = await client.get(f"{API_BASE}/admin/analysis/", headers=headers)
            
            print(f"📥 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                analyses = result.get('items', [])
                total = result.get('total', 0)
                
                print(f"✅ 获取分析记录成功")
                print(f"   总记录数: {total}")
                print(f"   当前页记录数: {len(analyses)}")
                
                if analyses:
                    print(f"\n📋 分析记录详情:")
                    for i, analysis in enumerate(analyses[:3], 1):  # 只显示前3条
                        print(f"   记录 {i}:")
                        print(f"     ID: {analysis.get('id', 'N/A')}")
                        print(f"     用户名: {analysis.get('username', 'N/A')}")
                        print(f"     情绪: {analysis.get('emotion', 'N/A')}")
                        print(f"     置信度: {analysis.get('confidence', 'N/A')}")
                        print(f"     创建时间: {analysis.get('created_at', 'N/A')}")
                        print()
                        
                        # 检查用户名是否正确显示
                        username = analysis.get('username')
                        if username and username != "未知用户" and username is not None:
                            print(f"     ✅ 用户名显示正常: {username}")
                        else:
                            print(f"     ❌ 用户名显示异常: {username}")
                else:
                    print("   📝 没有找到分析记录")
                    
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

async def test_direct_database_check():
    """直接检查数据库中的用户名"""
    print("\n🔍 直接检查数据库中的用户名...")
    print("=" * 50)
    
    try:
        import sys
        sys.path.append('/Volumes/acasis/ema2_20250417/backend')
        
        from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
        from sqlalchemy.orm import sessionmaker, selectinload
        from sqlalchemy import select
        from emotionai.core.config import settings
        from emotionai.models.ml.emotion import EmotionAnalysis
        
        # 创建异步数据库引擎
        engine = create_async_engine(settings.DATABASE_URL)
        async_session = sessionmaker(engine, class_=AsyncSession)
        
        async with async_session() as session:
            # 查询分析记录并加载用户关系
            statement = select(EmotionAnalysis).options(selectinload(EmotionAnalysis.user)).limit(5)
            result = await session.execute(statement)
            analyses = result.scalars().all()
            
            print(f"✅ 查询到 {len(analyses)} 条分析记录")
            
            for i, analysis in enumerate(analyses, 1):
                print(f"   记录 {i}:")
                print(f"     分析ID: {analysis.id}")
                print(f"     用户ID: {analysis.user_id}")
                
                # 检查用户关系和用户名
                try:
                    if hasattr(analysis, 'user') and analysis.user:
                        print(f"     用户对象: {analysis.user}")
                        print(f"     用户名: {analysis.user.username}")
                        print(f"     用户邮箱: {analysis.user.email}")
                        print(f"     ✅ 用户关系和用户名正常")
                    else:
                        print(f"     ❌ 用户关系缺失或用户不存在")
                except Exception as e:
                    print(f"     ❌ 访问用户关系出错: {e}")
                print()
                
        await engine.dispose()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")

async def main():
    """主函数"""
    print("🚀 开始测试管理员分析页面用户名显示（带认证）")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    await test_admin_analysis_with_auth()
    await test_direct_database_check()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成！")
    print("\n📝 修复状态:")
    print("   - EmotionAnalysis模型的user关系已恢复")
    print("   - CRUD中的user关系加载已启用")
    print("   - API中的用户名获取逻辑已恢复")
    print("   - 如果前端仍显示'未知用户'，可能需要:")
    print("     1. 重启后端服务")
    print("     2. 清除前端缓存")
    print("     3. 检查前端API调用")

if __name__ == "__main__":
    asyncio.run(main()) 