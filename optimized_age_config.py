#!/usr/bin/env python3
"""
优化的年龄预测配置

基于分析结果提供的年龄预测优化方案
"""

import numpy as np
from typing import Dict, List, Tuple, Optional

class OptimizedAgeConfig:
    """优化的年龄预测配置"""
    
    # 优化后的模型权重配置
    OPTIMIZED_WEIGHTS = {
        "face_attributes_ensemble": {
            "deepface": 0.25,      # 提高DeepFace权重，因为修正后效果更好
            "insightface": 0.75,   # 降低InsightFace权重，避免过度依赖
        },
        "improved_age_ensemble": {
            "deepface": 0.30,      # 平衡权重分配
            "insightface": 0.40,   # 主要模型但不过度依赖
            "custom_regression": 0.20,  # 如果有自训练模型
            "custom_multitask": 0.10,   # 辅助模型
        }
    }
    
    # 改进的年龄修正算法
    @staticmethod
    def improved_age_correction(
        age: float, 
        confidence: float = 0.5,
        face_quality: float = 1.0,
        model_name: str = "deepface"
    ) -> float:
        """
        改进的年龄修正算法
        
        Args:
            age: 原始年龄预测
            confidence: 预测置信度 (0-1)
            face_quality: 人脸质量评分 (0-1)
            model_name: 模型名称
            
        Returns:
            修正后的年龄
        """
        if age is None:
            return 25.0  # 默认年龄
            
        # 基础修正系数（根据模型特性调整）
        if model_name.lower() == "deepface":
            # DeepFace倾向于预测偏年轻，需要上调
            if age < 20:
                base_correction = 1.3
            elif age < 30:
                base_correction = 1.2
            elif age < 45:
                base_correction = 1.15
            elif age < 60:
                base_correction = 1.08
            else:
                base_correction = 1.03
        else:
            # 其他模型的修正系数
            base_correction = 1.0
        
        # 置信度调整：低置信度时修正幅度减小
        confidence_factor = 0.5 + 0.5 * confidence
        
        # 人脸质量调整：质量差时修正幅度减小
        quality_factor = 0.7 + 0.3 * face_quality
        
        # 综合修正系数
        final_correction = 1.0 + (base_correction - 1.0) * confidence_factor * quality_factor
        
        # 应用修正
        corrected_age = age * final_correction
        
        # 限制在合理范围内
        corrected_age = max(1, min(corrected_age, 100))
        
        return corrected_age
    
    # 集成策略优化
    @staticmethod
    def advanced_ensemble_strategy(
        predictions: Dict[str, float],
        confidences: Dict[str, float],
        weights: Dict[str, float]
    ) -> Tuple[float, float]:
        """
        高级集成策略
        
        Args:
            predictions: 各模型预测结果
            confidences: 各模型置信度
            weights: 模型权重
            
        Returns:
            (最终年龄, 集成置信度)
        """
        if not predictions:
            return 25.0, 0.0
        
        # 1. 动态权重调整（基于置信度）
        dynamic_weights = {}
        for model, pred in predictions.items():
            base_weight = weights.get(model, 0.0)
            confidence = confidences.get(model, 0.5)
            # 置信度高的模型获得更高权重
            dynamic_weights[model] = base_weight * (0.5 + 0.5 * confidence)
        
        # 归一化权重
        total_weight = sum(dynamic_weights.values())
        if total_weight > 0:
            for model in dynamic_weights:
                dynamic_weights[model] /= total_weight
        
        # 2. 异常值检测和处理
        ages = list(predictions.values())
        if len(ages) > 2:
            # 使用IQR方法检测异常值
            q1, q3 = np.percentile(ages, [25, 75])
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            # 过滤异常值
            filtered_predictions = {}
            filtered_confidences = {}
            filtered_weights = {}
            
            for model, age in predictions.items():
                if lower_bound <= age <= upper_bound:
                    filtered_predictions[model] = age
                    filtered_confidences[model] = confidences.get(model, 0.5)
                    filtered_weights[model] = dynamic_weights.get(model, 0.0)
            
            if filtered_predictions:
                predictions = filtered_predictions
                confidences = filtered_confidences
                dynamic_weights = filtered_weights
                
                # 重新归一化权重
                total_weight = sum(dynamic_weights.values())
                if total_weight > 0:
                    for model in dynamic_weights:
                        dynamic_weights[model] /= total_weight
        
        # 3. 加权平均
        weighted_sum = 0.0
        weight_sum = 0.0
        
        for model, age in predictions.items():
            weight = dynamic_weights.get(model, 0.0)
            weighted_sum += age * weight
            weight_sum += weight
        
        if weight_sum > 0:
            final_age = weighted_sum / weight_sum
        else:
            final_age = np.mean(list(predictions.values()))
        
        # 4. 计算集成置信度
        # 基于预测一致性和平均置信度
        ages = list(predictions.values())
        age_std = np.std(ages)
        age_mean = np.mean(ages)
        
        # 一致性得分
        consistency = max(0, 1 - age_std / (age_mean * 0.3))
        
        # 平均置信度
        avg_confidence = np.mean(list(confidences.values()))
        
        # 模型数量得分
        num_models_score = min(1.0, len(predictions) / 3.0)
        
        # 综合置信度
        ensemble_confidence = (
            0.4 * consistency +
            0.4 * avg_confidence +
            0.2 * num_models_score
        )
        
        return final_age, min(1.0, max(0.0, ensemble_confidence))

# 年龄预测质量评估
class AgeQualityAssessment:
    """年龄预测质量评估"""
    
    @staticmethod
    def assess_face_quality(face_image: np.ndarray) -> float:
        """
        评估人脸图像质量
        
        Args:
            face_image: 人脸图像
            
        Returns:
            质量评分 (0-1)
        """
        if face_image is None or face_image.size == 0:
            return 0.0
        
        # 1. 图像清晰度评估（基于拉普拉斯算子）
        gray = cv2.cvtColor(face_image, cv2.COLOR_BGR2GRAY) if len(face_image.shape) == 3 else face_image
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        sharpness_score = min(1.0, laplacian_var / 1000.0)  # 归一化
        
        # 2. 亮度评估
        brightness = np.mean(gray)
        brightness_score = 1.0 - abs(brightness - 128) / 128.0  # 理想亮度为128
        
        # 3. 对比度评估
        contrast = np.std(gray)
        contrast_score = min(1.0, contrast / 64.0)  # 归一化
        
        # 4. 图像尺寸评估
        height, width = gray.shape
        size_score = min(1.0, min(height, width) / 112.0)  # 112是常见的人脸尺寸
        
        # 综合评分
        quality_score = (
            0.4 * sharpness_score +
            0.2 * brightness_score +
            0.2 * contrast_score +
            0.2 * size_score
        )
        
        return max(0.0, min(1.0, quality_score))
    
    @staticmethod
    def assess_prediction_reliability(
        predictions: Dict[str, float],
        confidences: Dict[str, float]
    ) -> float:
        """
        评估预测可靠性
        
        Args:
            predictions: 各模型预测结果
            confidences: 各模型置信度
            
        Returns:
            可靠性评分 (0-1)
        """
        if not predictions:
            return 0.0
        
        # 1. 预测一致性
        ages = list(predictions.values())
        if len(ages) > 1:
            age_std = np.std(ages)
            age_mean = np.mean(ages)
            consistency = max(0, 1 - age_std / (age_mean * 0.2))
        else:
            consistency = 0.5
        
        # 2. 平均置信度
        avg_confidence = np.mean(list(confidences.values()))
        
        # 3. 模型数量
        num_models = len(predictions)
        model_diversity = min(1.0, num_models / 3.0)
        
        # 综合可靠性
        reliability = (
            0.5 * consistency +
            0.3 * avg_confidence +
            0.2 * model_diversity
        )
        
        return max(0.0, min(1.0, reliability))

# 使用示例和配置建议
OPTIMIZATION_RECOMMENDATIONS = {
    "immediate_actions": [
        "调整FaceAttributesEnsemble中DeepFace权重从0.1提升到0.25",
        "使用改进的年龄修正算法，考虑置信度和人脸质量",
        "实现异常值检测，过滤明显错误的预测",
        "添加人脸质量评估，低质量图像降低修正幅度"
    ],
    
    "medium_term_improvements": [
        "训练自定义年龄回归模型，使用项目特定数据",
        "收集和标注更多年龄数据，特别是中国人脸数据",
        "实现多尺度人脸检测，提高小脸检测精度",
        "添加年龄预测的不确定性估计"
    ],
    
    "long_term_goals": [
        "使用Vision Transformer等更先进的架构",
        "实现端到端的多任务学习（年龄+性别+表情）",
        "添加时序信息利用（视频中的年龄一致性）",
        "实现个性化年龄预测（基于用户反馈调整）"
    ]
}

if __name__ == "__main__":
    print("年龄预测优化配置")
    print("=" * 50)
    
    # 展示优化配置
    config = OptimizedAgeConfig()
    print("优化后的权重配置:")
    for ensemble_type, weights in config.OPTIMIZED_WEIGHTS.items():
        print(f"\n{ensemble_type}:")
        for model, weight in weights.items():
            print(f"  {model}: {weight:.2f}")
    
    print("\n优化建议:")
    for category, actions in OPTIMIZATION_RECOMMENDATIONS.items():
        print(f"\n{category.replace('_', ' ').title()}:")
        for i, action in enumerate(actions, 1):
            print(f"  {i}. {action}") 