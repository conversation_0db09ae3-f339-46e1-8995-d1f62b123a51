#!/usr/bin/env python
# -*- coding: utf-8 -*-

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
import sys
import time

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def test_mobile_reset():
    """测试手机端密码重置邮件发送"""
    logger.info("开始测试手机端密码重置邮件发送...")
    
    # 配置信息
    smtp_host = "smtp.qq.com"
    smtp_port = 465  # SSL端口
    sender = "<EMAIL>"
    password = "qweamijhnidebaji"  # 授权码
    receiver = "<EMAIL>"  # 修改为您的邮箱
    
    # 使用生产环境URL
    production_url = "https://cxyai.shenzhuo.vip"
    reset_path = "/reset-password?token=test_mobile_token"
    absolute_url = f"{production_url}{reset_path}"
    
    # 创建邮件
    message = MIMEMultipart()
    message['From'] = sender
    message['To'] = receiver
    message['Subject'] = "EmotionAI - 手机端密码重置测试"
    
    # 创建HTML内容，使用生产环境URL
    html_content = f"""
    <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
                <h2 style="color: #333; text-align: center;">密码重置</h2>
                <p>您好 测试用户,</p>
                <p>我们收到了您的密码重置请求。请点击以下链接来设置您的新密码：</p>
                <p style="text-align: center;">
                    <a href='{absolute_url}' style="display: inline-block; padding: 10px 20px; background-color: #1890ff; color: white; text-decoration: none; border-radius: 4px;">重置密码</a>
                </p>
                <p>如果上面的按钮不起作用，请复制以下链接到浏览器地址栏：</p>
                <p style="background-color: #f5f5f5; padding: 10px; word-break: break-all;">{absolute_url}</p>
                <p>如果您没有请求重置密码，请忽略此邮件。</p>
                <p>此链接将在24小时后失效。</p>
                <p>祝好，</p>
                <p>EmotionAI 团队</p>
            </div>
        </body>
    </html>
    """
    
    # 添加HTML内容，明确指定UTF-8编码
    part = MIMEText(html_content, 'html', 'utf-8')
    message.attach(part)
    
    # 记录邮件发送状态
    email_sent_successfully = False
    
    try:
        # 使用SSL连接
        logger.info(f"尝试连接到{smtp_host}:{smtp_port}...")
        context = ssl.create_default_context()
        
        # 不使用with语句，手动管理连接关闭
        server = smtplib.SMTP_SSL(smtp_host, smtp_port, context=context)
        try:
            logger.info(f"连接成功，尝试登录...")
            server.login(sender, password)
            logger.info(f"登录成功，尝试发送邮件到{receiver}...")
            
            # 尝试发送邮件
            server.sendmail(sender, receiver, message.as_string())
            # 邮件发送成功，设置状态变量
            email_sent_successfully = True
            logger.info("邮件发送成功！")
            
            # 安全关闭连接
            try:
                server.quit()
                logger.info("SMTP连接已安全关闭")
            except Exception as close_error:
                # QQ邮箱特有的连接关闭错误，可以忽略
                if b'\x00\x00\x00' in str(close_error).encode():
                    logger.warning(f"忽略QQ邮箱特有的连接关闭错误: {close_error}")
                else:
                    logger.warning(f"关闭SMTP连接时出现非致命错误: {close_error}")
                
        except Exception as e:
            logger.error(f"邮件发送失败: {e}", exc_info=True)
            
            # 确保在异常情况下也尝试关闭连接
            try:
                server.quit()
            except:
                pass
                
    except Exception as e:
        logger.error(f"连接SMTP服务器失败: {e}", exc_info=True)
    
    # 返回邮件发送状态
    return email_sent_successfully

if __name__ == "__main__":
    if test_mobile_reset():
        print("\n✅ 测试成功！邮件已发送。请检查您的邮箱。")
    else:
        print("\n❌ 测试失败！邮件未能发送。请检查错误信息。")
