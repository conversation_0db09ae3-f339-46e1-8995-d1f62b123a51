import sqlite3
import os

# 尝试查找SQLite数据库文件
def find_sqlite_db():
    base_dir = "/Volumes/acasis/ema2_20250417"
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.db') or file.endswith('.sqlite') or file.endswith('.sqlite3'):
                return os.path.join(root, file)
    return None

def main():
    db_path = find_sqlite_db()
    if not db_path:
        print("未找到SQLite数据库文件")
        return
    
    print(f"找到数据库文件: {db_path}")
    
    try:
        # 连接到数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取表列表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"数据库中的表: {tables}")
        
        # 查找emotion_analysis表
        if ('emotion_analysis',) in tables:
            print("找到emotion_analysis表")
            
            # 获取表结构
            cursor.execute("PRAGMA table_info(emotion_analysis);")
            columns = cursor.fetchall()
            print(f"emotion_analysis表结构: {columns}")
            
            # 查询emotion字段的不同值
            cursor.execute("SELECT DISTINCT emotion FROM emotion_analysis LIMIT 10;")
            emotions = cursor.fetchall()
            print(f"emotion字段的不同值: {emotions}")
            
            # 查询记录总数
            cursor.execute("SELECT COUNT(*) FROM emotion_analysis;")
            count = cursor.fetchone()[0]
            print(f"记录总数: {count}")
            
            # 查询每种情绪的记录数
            if emotions:
                for emotion in emotions:
                    emotion_value = emotion[0]
                    cursor.execute(f"SELECT COUNT(*) FROM emotion_analysis WHERE emotion = ?;", (emotion_value,))
                    emotion_count = cursor.fetchone()[0]
                    print(f"情绪 '{emotion_value}' 的记录数: {emotion_count}")
        else:
            print("未找到emotion_analysis表")
        
        conn.close()
    except Exception as e:
        print(f"查询数据库时出错: {e}")

if __name__ == "__main__":
    main()
