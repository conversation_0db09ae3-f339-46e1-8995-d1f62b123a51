version: '3.8'

services:
  # 开发环境数据库
  postgres:
    extends:
      file: docker-compose.yml
      service: postgres
    environment:
      POSTGRES_DB: emotion_ai_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5433:5432"

  # 开发环境Redis
  redis:
    extends:
      file: docker-compose.yml
      service: redis
    ports:
      - "6380:6379"

  # 开发环境后端（热重载）
  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: ema-backend-dev
    environment:
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=dev_user
      - POSTGRES_PASSWORD=dev_password
      - POSTGRES_DB=emotion_ai_dev
      - REDIS_HOST=redis
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - PYTHONPATH=/app
    volumes:
      - ./backend:/app
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - ./models:/app/models
    ports:
      - "8001:8000"
    networks:
      - ema-network
    depends_on:
      - postgres
      - redis
    command: uvicorn emotionai.main:app --host 0.0.0.0 --port 8000 --reload
    restart: unless-stopped

  # 开发环境前端（热重载）
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: ema-frontend-dev
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:8001
      - VITE_HMR_PORT=3001
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3001:3000"
      - "3002:3002"  # HMR端口
    networks:
      - ema-network
    depends_on:
      - backend-dev
    command: pnpm dev --host 0.0.0.0 --port 3000
    restart: unless-stopped

networks:
  ema-network:
    external: true 