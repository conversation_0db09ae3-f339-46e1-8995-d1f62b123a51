#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psycopg2
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接参数
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': os.getenv('DB_PORT', '5432'),
    'database': os.getenv('DB_NAME', 'emotion_ai'),
    'user': os.getenv('DB_USER', 'dom'),
    'password': os.getenv('DB_PASSWORD', '')
}

try:
    # 连接数据库
    conn = psycopg2.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('检查 emotion_feedback 表结构...')
    cursor.execute("""
        SELECT column_name, data_type, is_nullable, column_default 
        FROM information_schema.columns 
        WHERE table_name = 'emotion_feedback' 
        ORDER BY ordinal_position;
    """)
    
    columns = cursor.fetchall()
    print('表结构:')
    for col in columns:
        print(f'  {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})')
    
    print('\n检查 emotion_feedback 表数据...')
    cursor.execute('SELECT COUNT(*) FROM emotion_feedback;')
    count = cursor.fetchone()[0]
    print(f'emotion_feedback 表总记录数: {count}')
    
    if count > 0:
        print('\n最近的反馈记录:')
        cursor.execute("""
            SELECT id, analysis_id, emotion, comment, created_at, created_by 
            FROM emotion_feedback 
            ORDER BY created_at DESC 
            LIMIT 5;
        """)
        
        records = cursor.fetchall()
        for record in records:
            print(f'  ID: {record[0]}')
            print(f'  Analysis ID: {record[1]}')
            print(f'  Emotion: {record[2]}')
            print(f'  Comment: {record[3]}')
            print(f'  Created At: {record[4]}')
            print(f'  Created By: {record[5]}')
            print('  ---')
    else:
        print('emotion_feedback 表中没有数据')
    
    # 检查是否有外键约束问题
    print('\n检查外键约束...')
    cursor.execute("""
        SELECT conname, contype, confrelid::regclass as referenced_table
        FROM pg_constraint 
        WHERE conrelid = 'emotion_feedback'::regclass;
    """)
    
    constraints = cursor.fetchall()
    print('外键约束:')
    for constraint in constraints:
        print(f'  {constraint[0]}: {constraint[1]} -> {constraint[2]}')
    
    # 检查emotion_analysis表中是否有数据
    print('\n检查 emotion_analysis 表数据...')
    cursor.execute('SELECT COUNT(*) FROM emotion_analysis;')
    analysis_count = cursor.fetchone()[0]
    print(f'emotion_analysis 表总记录数: {analysis_count}')
    
    if analysis_count > 0:
        print('\n最近的分析记录:')
        cursor.execute("""
            SELECT id, emotion, confidence, created_at, user_id 
            FROM emotion_analysis 
            WHERE deleted_at IS NULL
            ORDER BY created_at DESC 
            LIMIT 3;
        """)
        
        analysis_records = cursor.fetchall()
        for record in analysis_records:
            print(f'  ID: {record[0]}')
            print(f'  Emotion: {record[1]}')
            print(f'  Confidence: {record[2]}')
            print(f'  Created At: {record[3]}')
            print(f'  User ID: {record[4]}')
            print('  ---')
    
    # 检查用户表
    print('\n检查 users 表数据...')
    cursor.execute('SELECT COUNT(*) FROM users;')
    users_count = cursor.fetchone()[0]
    print(f'users 表总记录数: {users_count}')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'数据库连接或查询错误: {e}') 