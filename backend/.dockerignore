# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
htmlcov/
venv/
.venv/
env/
.env
instance/
pip-wheel-metadata/
.eggs/
develop-eggs/
*.egg-info/
dist/
build/

# Logs and Uploads
logs/
uploads/
*.log

# Test data and results (if not needed in image)
test_data/
test_images/
test_results/
# 如果某些测试数据需要在运行时被应用访问，则不要在此处排除

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
Thumbs.db

# Other
*.bak
*.tmp
# alembic/versions/* # 通常需要保留迁移脚本
# migrations/* # 如果这是另一个迁移目录
# data/* # 如果data目录包含运行时需要的数据，不要排除
# models/* # 如果models目录包含运行时需要的数据，不要排除

# Specific files from listing if not needed
# add_batch_fields.sql (if only for manual use)
# check_*.py (if only for local checks)
# download_*.py (if models are pre-downloaded or handled differently in Docker)
# pre_migration_backup_*.dump
# start_redis_manually.py
# start_server_no_redis.sh
# test_*.py (if tests are not run inside the final image)
# warnings_filter.py