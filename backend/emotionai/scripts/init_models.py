#!/usr/bin/env python
"""
初始化模型数据脚本
"""

import sys
import uuid
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from sqlalchemy.orm import Session

from emotionai.core.deps.database import get_db
from emotionai.models.ml.model import MLModel
from emotionai.models.user import User


def init_models():
    """初始化模型数据"""
    # 获取数据库会话
    db: Session = next(get_db())

    # 获取第一个超级管理员用户
    admin_user = db.query(User).filter(User.is_superuser).first()
    if not admin_user:
        print("未找到超级管理员用户，无法初始化模型数据")
        return

    # 清空现有模型数据（可选）
    db.query(MLModel).delete()

    # 定义模型数据
    models_data = [
        {
            "id": uuid.uuid4(),
            "name": "InsightFace",
            "type": "face_detection",
            "version": "buffalo_l",
            "status": "installed",
            "description": "高精度人脸检测和识别模型",
            "path": "/models/face_detection/insightface/buffalo_l/det_10g.onnx",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "InsightFace",
            "type": "face_landmarks",
            "version": "buffalo_l",
            "status": "installed",
            "description": "人脸特征点检测模型",
            "path": "/models/face_landmarks/insightface/buffalo_l/1k3d68.onnx",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "InsightFace",
            "type": "face_recognition",
            "version": "buffalo_l",
            "status": "installed",
            "description": "人脸识别模型",
            "path": "/models/face_recognition/insightface/buffalo_l/w600k_r50.onnx",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "InsightFace",
            "type": "face_attributes",
            "version": "buffalo_l",
            "status": "installed",
            "description": "人脸属性（年龄、性别）检测模型",
            "path": "/models/face_attributes/insightface/buffalo_l/genderage.onnx",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "HSEmotion",
            "type": "emotion_recognition",
            "version": "enet_b0_8_best_afew",
            "status": "installed",
            "description": "情绪识别模型，支持7种基本情绪",
            "path": "/models/emotion_recognition/hsemotion/enet_b0_8_best_afew.pt",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "ElenaRyumina",
            "type": "emotion_recognition",
            "version": "face_emotion_recognition",
            "status": "installed",
            "description": "Hugging Face情绪识别模型",
            "path": "/models/emotion_recognition/huggingface/ElenaRyumina_face_emotion_recognition/FER_static_ResNet50_AffectNet.pt",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "MahmoudWSegni",
            "type": "emotion_recognition",
            "version": "swin-tiny",
            "status": "installed",
            "description": "Hugging Face Swin Transformer情绪识别模型",
            "path": "/models/emotion_recognition/huggingface/models--MahmoudWSegni--swin-tiny/pytorch_model.bin",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "Rajaram",
            "type": "emotion_recognition",
            "version": "facial_emo",
            "status": "installed",
            "description": "Hugging Face Rajaram情绪识别模型",
            "path": "/models/emotion_recognition/huggingface/rajaram_facial_emo/pytorch_model.bin",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "YOLOv8",
            "type": "face_detection",
            "version": "l-face",
            "status": "installed",
            "description": "YOLOv8l-face人脸检测模型",
            "path": "/models/face_detection/yolo/yolov8l-face.pt",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "YOLOv8",
            "type": "face_detection",
            "version": "m-face",
            "status": "installed",
            "description": "YOLOv8m-face人脸检测模型",
            "path": "/models/face_detection/yolo/yolov8m-face.pt",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "YOLOv8",
            "type": "face_detection",
            "version": "n-face",
            "status": "installed",
            "description": "YOLOv8n-face人脸检测模型",
            "path": "/models/face_detection/yolo/yolov8n-face.pt",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "MediaPipe",
            "type": "face_mesh",
            "version": "0.8.10",
            "status": "installed",
            "description": "Google的人脸网格检测模型",
            "path": "mediapipe内置",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "DeepFace",
            "type": "face_attributes",
            "version": "age_model",
            "status": "installed",
            "description": "DeepFace年龄检测模型",
            "path": "/models/face_attributes/deepface/age_model_weights.h5",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "id": uuid.uuid4(),
            "name": "DeepFace",
            "type": "face_attributes",
            "version": "gender_model",
            "status": "installed",
            "description": "DeepFace性别检测模型",
            "path": "/models/face_attributes/deepface/gender_model_weights.h5",
            "is_active": True,
            "created_by": admin_user.id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
    ]

    # 创建模型记录
    for model_data in models_data:
        model = MLModel(**model_data)
        db.add(model)

    # 提交事务
    db.commit()

    print(f"成功初始化了 {len(models_data)} 个模型记录")


if __name__ == "__main__":
    init_models()
