import logging
import uuid
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from emotionai.models.user import User

logger = logging.getLogger(__name__)


async def get_user_by_id(db: AsyncSession, user_id: uuid.UUID) -> Optional[User]:
    statement = select(User).where(User.id == user_id)
    result = await db.execute(statement)
    return result.scalar_one_or_none()


async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    logger.info(f"Attempting to find user by email: '{email}'")
    statement = select(User).where(User.email == email)
    result = await db.execute(statement)
    user = result.scalar_one_or_none()
    if user:
        logger.info(f"User found: ID={user.id}, Email='{user.email}'")
    else:
        logger.warning(f"User not found for email: '{email}'")
    return user


# async def create_user(db: AsyncSession, user_in: UserCreate) -> User:
#     from emotionai.core.security import get_password_hash
#     hashed_password = get_password_hash(user_in.password)
#     db_user = User(
#         email=user_in.email,
#         username=user_in.username,
#         hashed_password=hashed_password,
#         # ... other fields from UserCreate
#     )
#     db.add(db_user)
#     await db.commit()
#     await db.refresh(db_user)
#     return db_user


async def update_user_password(
    db: AsyncSession, user: User, new_hashed_password: str
) -> User:
    """
    更新用户的密码。
    传入的 new_hashed_password 应该是已经哈希过的密码。
    """
    user.hashed_password = new_hashed_password
    db.add(user)
    await db.commit()
    await db.refresh(user)
    return user
