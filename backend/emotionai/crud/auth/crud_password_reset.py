# /Volumes/acasis/ema2_20250417/backend/emotionai/crud/auth/crud_password_reset.py
import secrets
import uuid
from datetime import timedelta

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from emotionai.core.utils.time_utils import get_beijing_now  # 假设的工具函数路径
from emotionai.models.auth.password_reset import PasswordResetToken

# TODO: 考虑从配置中读取此值
RESET_TOKEN_EXPIRE_MINUTES = 60


async def create_password_reset_token(
    db: AsyncSession, *, user_id: uuid.UUID
) -> PasswordResetToken:
    """
    为用户创建密码重置令牌。
    如果用户已有未过期的未使用令牌，则先将其标记为已使用。
    """
    now = get_beijing_now()
    # 将用户之前未使用的、未过期的令牌标记为已使用
    existing_tokens_stmt = select(PasswordResetToken).where(
        PasswordResetToken.user_id == user_id,
        not PasswordResetToken.is_used,
        PasswordResetToken.expires_at > now,
    )
    existing_tokens_result = await db.execute(existing_tokens_stmt)
    for token_instance in existing_tokens_result.scalars().all():
        token_instance.is_used = True
        db.add(token_instance)

    token_str = secrets.token_urlsafe(32)  # 生成一个安全的随机令牌
    expires_at = now + timedelta(minutes=RESET_TOKEN_EXPIRE_MINUTES)

    db_token = PasswordResetToken(
        token=token_str,
        user_id=user_id,
        created_at=now,  # 明确设置创建时间
        expires_at=expires_at,
        is_used=False,  # 明确设置未使用
    )
    db.add(db_token)
    await db.commit()
    await db.refresh(db_token)
    return db_token


async def get_password_reset_token_by_token(
    db: AsyncSession, *, token: str
) -> PasswordResetToken | None:
    """
    通过令牌字符串获取 PasswordResetToken 对象。
    """
    statement = select(PasswordResetToken).where(PasswordResetToken.token == token)
    result = await db.execute(statement)
    return result.scalar_one_or_none()


async def use_password_reset_token(
    db: AsyncSession, *, token_obj: PasswordResetToken
) -> PasswordResetToken:
    """
    将密码重置令牌标记为已使用并立即使其过期。
    """
    now = get_beijing_now()
    token_obj.is_used = True
    token_obj.expires_at = now  # 使其立即过期
    db.add(token_obj)
    await db.commit()
    await db.refresh(token_obj)
    return token_obj
