#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
情绪反馈CRUD操作
"""

from typing import List, Optional
from uuid import UUID

from sqlalchemy import desc, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from emotionai.models.ml.emotion_feedback import EmotionFeedback


class CRUDEmotionFeedback:
    """情绪反馈CRUD操作类"""

    def __init__(self):
        self.model = EmotionFeedback

    async def get(self, db: AsyncSession, id: UUID) -> Optional[EmotionFeedback]:
        """根据ID获取单个反馈记录"""
        query = select(self.model).where(self.model.id == id)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_by_analysis_id(
        self, db: AsyncSession, analysis_id: UUID
    ) -> List[EmotionFeedback]:
        """根据分析ID获取所有反馈记录"""
        query = (
            select(self.model)
            .where(
                self.model.analysis_id == analysis_id, self.model.deleted_at.is_(None)
            )
            .order_by(desc(self.model.created_at))
        )
        result = await db.execute(query)
        return list(result.scalars().all())

    async def get_by_analysis_id_with_user(
        self, db: AsyncSession, analysis_id: UUID
    ) -> List[EmotionFeedback]:
        """根据分析ID获取所有反馈记录，包含用户信息"""
        try:
            query = (
                select(self.model)
                .options(selectinload(self.model.user))
                .where(
                    self.model.analysis_id == analysis_id,
                    self.model.deleted_at.is_(None),
                )
                .order_by(desc(self.model.created_at))
            )
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception:
            # 如果关系加载失败，回退到不加载用户信息的查询
            return await self.get_by_analysis_id(db, analysis_id)

    async def get_by_user_id(
        self, db: AsyncSession, user_id: UUID, analysis_id: Optional[UUID] = None
    ) -> List[EmotionFeedback]:
        """根据用户ID获取反馈记录"""
        query = select(self.model).where(
            self.model.created_by == user_id, self.model.deleted_at.is_(None)
        )

        if analysis_id:
            query = query.where(self.model.analysis_id == analysis_id)

        query = query.order_by(desc(self.model.created_at))
        result = await db.execute(query)
        return list(result.scalars().all())

    async def count_by_analysis_id(self, db: AsyncSession, analysis_id: UUID) -> int:
        """统计指定分析记录的反馈数量"""
        query = select(self.model).where(
            self.model.analysis_id == analysis_id, self.model.deleted_at.is_(None)
        )
        result = await db.execute(query)
        return len(list(result.scalars().all()))

    async def create(self, db: AsyncSession, *, obj_in: dict) -> EmotionFeedback:
        """创建新的反馈记录"""
        db_obj = self.model(**obj_in)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def remove(self, db: AsyncSession, *, id: UUID) -> Optional[EmotionFeedback]:
        """软删除反馈记录"""
        db_obj = await self.get(db, id=id)
        if db_obj:
            from datetime import datetime

            db_obj.deleted_at = datetime.utcnow()
            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)
        return db_obj


# 创建全局实例
cemotion_feedback = CRUDEmotionFeedback()
