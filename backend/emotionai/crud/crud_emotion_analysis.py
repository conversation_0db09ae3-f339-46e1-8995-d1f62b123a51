from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from sqlalchemy import asc, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from emotionai.models.ml.emotion import EmotionAnalysis
from emotionai.schemas.admin.emotion_analysis import (
    EmotionAnalysisCreate,
    EmotionAnalysisUpdate,
)


class CRUDEmotionAnalysis:
    model = EmotionAnalysis

    async def get(self, db: AsyncSession, id: UUID) -> Optional[EmotionAnalysis]:
        result = await db.execute(
            select(self.model)
            .filter(self.model.id == id)
            .filter(self.model.deleted_at.is_(None))
        )
        return result.scalars().first()

    async def get_multi(
        self,
        db: AsyncSession,
        *,
        skip: int = 0,
        limit: int = 100,
        sort: Optional[str] = None,
        filter_by: Optional[str] = None,
    ) -> List[EmotionAnalysis]:
        statement = select(self.model).filter(self.model.deleted_at.is_(None))
        # 重新启用user关系加载
        statement = statement.options(selectinload(self.model.user))

        if filter_by:
            filters = filter_by.split(",")
            for f in filters:
                col, val = f.split(":", 1)
                if hasattr(self.model, col):
                    column_attr = getattr(self.model, col)
                    if "id" in col and column_attr.type.python_type == UUID:
                        try:
                            val = UUID(val)
                        except ValueError:
                            continue
                    statement = statement.filter(column_attr == val)

        if sort:
            try:
                sort_col, sort_dir = sort.rsplit("_", 1)
                if hasattr(self.model, sort_col) and sort_dir in ["asc", "desc"]:
                    column_to_sort = getattr(self.model, sort_col)
                    if sort_dir == "desc":
                        statement = statement.order_by(desc(column_to_sort))
                    else:
                        statement = statement.order_by(asc(column_to_sort))
                else:
                    statement = statement.order_by(desc(self.model.created_at))
            except ValueError:
                statement = statement.order_by(desc(self.model.created_at))
        else:
            statement = statement.order_by(desc(self.model.created_at))

        statement = statement.offset(skip).limit(limit)
        result = await db.execute(statement)
        return result.scalars().all()

    async def get_count(
        self, db: AsyncSession, *, filter_by: Optional[str] = None
    ) -> int:
        statement = (
            select(func.count())
            .select_from(self.model)
            .filter(self.model.deleted_at.is_(None))
        )

        if filter_by:
            filters = filter_by.split(",")
            for f in filters:
                col, val = f.split(":", 1)
                if hasattr(self.model, col):
                    column_attr = getattr(self.model, col)
                    if "id" in col and column_attr.type.python_type == UUID:
                        try:
                            val = UUID(val)
                        except ValueError:
                            continue
                    statement = statement.filter(column_attr == val)

        result = await db.execute(statement)
        return result.scalar_one()

    async def create(
        self, db: AsyncSession, *, obj_in: EmotionAnalysisCreate
    ) -> EmotionAnalysis:
        db_obj = self.model(**obj_in.model_dump(exclude_unset=True))
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: EmotionAnalysis,
        obj_in: Union[EmotionAnalysisUpdate, Dict[str, Any]],
    ) -> EmotionAnalysis:
        update_data = (
            obj_in
            if isinstance(obj_in, dict)
            else obj_in.model_dump(exclude_unset=True)
        )
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def remove(self, db: AsyncSession, *, id: UUID) -> Optional[EmotionAnalysis]:
        db_obj = await self.get(db, id=id)
        if db_obj:
            db_obj.deleted_at = func.now()
            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)
        return db_obj


cemotion_analysis = CRUDEmotionAnalysis()
