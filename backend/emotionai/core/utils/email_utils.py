import logging
from typing import Any

from fastapi_mail import ConnectionConfig, FastMail, MessageSchema
from fastapi_mail.errors import ConnectionErrors
from pydantic import EmailStr

from emotionai.core.config import settings  # 正确导入

# 配置日志记录器
logger = logging.getLogger(__name__)

# 与 crud_password_reset.py 中的定义保持一致
# TODO: 理想情况下，此值应从中央配置读取
RESET_TOKEN_EXPIRE_MINUTES = 60


async def send_email_async(
    email_to: EmailStr,
    subject_template: str = "",
    html_template: str = "",
    environment: dict[str, Any] | None = None,
    sender_email: str = settings.EMAILS_FROM_EMAIL,  # type: ignore
    sender_name: str | None = settings.EMAILS_FROM_NAME,  # type: ignore
    smtp_host: str = settings.SMTP_HOST,  # type: ignore
    smtp_port: int = settings.SMTP_PORT,  # type: ignore
    smtp_user: str = settings.SMTP_USER,  # type: ignore
    smtp_password: str = settings.SMTP_PASSWORD,  # type: ignore
    smtp_tls: bool = settings.SMTP_TLS,  # type: ignore # 显式TLS (STARTTLS)
    smtp_ssl: bool = settings.SMTP_SSL,  # type: ignore # 隐式TLS/SSL
) -> None:
    """异步发送邮件"""
    logger.info(
        f"[send_email_async] Attempting to send email to: {email_to}, subject: {subject_template}"
    )  # Enhanced log
    if not environment:
        environment = {}

    if not settings.EMAILS_FROM_EMAIL:
        raise ValueError("EMAILS_FROM_EMAIL must be configured")
    if not settings.SMTP_HOST:
        raise ValueError("SMTP_HOST must be configured")
    if not settings.SMTP_PORT:
        raise ValueError("SMTP_PORT must be configured")

    # 确保 environment 中有 project_name
    environment.setdefault("project_name", settings.PROJECT_NAME)
    environment.setdefault("email_to", email_to)

    # 注释：发件人信息已在 ConnectionConfig 中设置
    # 不需要单独构建 from_header

    message = MessageSchema(
        subject=subject_template.format(**environment),
        recipients=[email_to],
        body=html_template,
        subtype="html",
    )

    # 准备 FastAPI-Mail 配置
    # 根据 settings.SMTP_SSL 和 settings.SMTP_TLS 决定连接方式
    use_ssl = smtp_ssl
    use_tls = smtp_tls if not use_ssl else False  # 如果使用SSL，则不应再使用STARTTLS

    conf = ConnectionConfig(
        MAIL_USERNAME=smtp_user,
        MAIL_PASSWORD=smtp_password,
        MAIL_FROM=sender_email,
        MAIL_PORT=smtp_port,
        MAIL_SERVER=smtp_host,
        MAIL_STARTTLS=use_tls,
        MAIL_SSL_TLS=use_ssl,
        USE_CREDENTIALS=True,
        VALIDATE_CERTS=True if settings.ENVIRONMENT != "development" else False,
        MAIL_FROM_NAME=sender_name,
        TEMPLATE_FOLDER=None,  # MODIFIED: Set to None as HTML is provided directly
    )

    fm = FastMail(conf)
    logger.info(
        f"[send_email_async] FastMail configured. Attempting to send for {email_to}."
    )  # Added log

    try:
        await fm.send_message(
            message, template_name=None
        )  # Assuming html_template is full HTML
        logger.info(
            f"[send_email_async] Email sent to {email_to} with subject: {subject_template}"
        )  # Enhanced log
    except ConnectionErrors as e:
        logger.error(
            f"[send_email_async] Failed to connect to SMTP server for {email_to}: {e}",
            exc_info=True,
        )  # Added exc_info
    except Exception as e:
        logger.error(
            f"[send_email_async] An unexpected error occurred while sending email to {email_to}: {e}",
            exc_info=True,
        )  # Added exc_info


async def send_password_reset_email(email_to: str, username: str, token: str) -> None:
    """发送密码重置邮件"""
    logger.info(
        f"[send_password_reset_email] Called for email: {email_to}, username: {username}, token: {token}"
    )  # Enhanced log
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - 重置您的密码"
    # 构建重置链接，前端 URL 需要从配置中获取
    # 例如: http://localhost:3001/reset-password?token=YOUR_TOKEN
    # TODO: 确认 settings.FRONTEND_URL 是否正确
    reset_url = f"{settings.FRONTEND_URL}/reset-password?token={token}"
    valid_hours = settings.ACCESS_TOKEN_EXPIRE_MINUTES // 60

    environment = {
        "project_name": project_name,
        "username": username,
        "token": token,
        "valid_hours": valid_hours,
        "reset_url": reset_url,
    }
    logger.info(
        f"[send_password_reset_email] Environment for template: {environment}"
    )  # Added log

    html_body = f"""
    <html>
    <body>
        <p>您好 {username},</p>
        <p>我们收到了一个重置您 EmotionAI 账户密码的请求。</p>
        <p>请点击以下链接来设置您的新密码：</p>
        <p><a href="{reset_url}">{reset_url}</a></p>
        <p>如果您没有请求重置密码，请忽略此邮件。</p>
        <p>此链接将在 {RESET_TOKEN_EXPIRE_MINUTES} 分钟后过期。</p>
        <p>谢谢,</p>
        <p>EmotionAI 团队</p>
    </body>
    </html>
    """

    try:
        await send_email_async(
            email_to=email_to,
            subject_template=subject,
            html_template=html_body,
            environment=environment,  # Pass full environment for send_email_async if it uses it, or specific parts
        )
        logger.info(
            f"[send_password_reset_email] Email task for {email_to} should have been processed by send_email_async."
        )  # Added log
    except Exception as e:
        logger.error(
            f"[send_password_reset_email] Error sending password reset email to {email_to}: {e}",
            exc_info=True,
        )  # Added exc_info
