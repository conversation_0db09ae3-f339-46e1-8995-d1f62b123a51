import logging
import smtplib
import ssl
import time
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from emotionai.core.config.settings import settings

logger = logging.getLogger(__name__)


def send_email_sync(
    email_to: str,
    subject: str,
    html_content: str,
    environment: str = settings.ENVIRONMENT,
    cc: list[str] = None,
    bcc: list[str] = None,
) -> bool:
    """
    同步发送邮件

    Args:
        email_to: 收件人邮箱
        subject: 邮件主题
        html_content: HTML内容
        environment: 环境
        cc: 抄送邮箱列表
        bcc: 密送邮箱列表

    Returns:
        bool: 发送是否成功
    """
    # 记录邮件发送状态
    email_sent_successfully = False
    # 如果是测试环境，记录邮件内容但不实际发送
    if environment == "test":
        logger.info(
            f"[TEST MODE] Would send email to {email_to} with subject: {subject}"
        )
        logger.debug(f"[TEST MODE] Email content: {html_content}")
        return True

    # 创建邮件
    message = MIMEMultipart("alternative")
    message["Subject"] = subject
    # 使用简单的From格式，避免编码问题
    message["From"] = settings.EMAILS_FROM_EMAIL
    message["To"] = email_to

    if cc:
        message["Cc"] = ", ".join(cc)
    if bcc:
        message["Bcc"] = ", ".join(bcc)

    # 添加HTML内容 - 明确指定编码为UTF-8
    part = MIMEText(html_content, "html", "utf-8")
    message.attach(part)

    recipients_for_sendmail = [email_to]
    if cc:
        recipients_for_sendmail.extend(cc)
    if bcc:
        recipients_for_sendmail.extend(bcc)

    try:
        logger.info(
            f"[send_email_sync] Attempting to send email to {email_to} using {'SSL' if settings.SMTP_SSL else 'TLS/plain'} connection"
        )
        logger.info(
            f"[send_email_sync] SMTP settings: Host={settings.SMTP_HOST}, Port={settings.SMTP_PORT}, User={settings.SMTP_USER}"
        )

        # 根据配置选择SSL或TLS
        if settings.SMTP_SSL:
            context = ssl.create_default_context()
            logger.info(
                f"[send_email_sync] Creating SMTP_SSL connection to {settings.SMTP_HOST}:{settings.SMTP_PORT}"
            )
            # 不使用with语句，手动管理连接关闭
            server = smtplib.SMTP_SSL(
                settings.SMTP_HOST,
                settings.SMTP_PORT,
                context=context,
                timeout=30,  # 添加超时设置
            )
            logger.info(
                "[send_email_sync] SMTP_SSL connection established, attempting login"
            )
            server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
            logger.info("[send_email_sync] Login successful, sending email")

            recipients = [email_to]
            if cc:
                recipients.extend(cc)
            if bcc:
                recipients.extend(bcc)

            # 添加重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # 确保邮件内容正确编码
                    message_str = message.as_string()
                    server.sendmail(settings.EMAILS_FROM_EMAIL, recipients, message_str)
                    # 邮件发送成功，设置状态变量
                    email_sent_successfully = True
                    logger.info(
                        f"[send_email_sync] Email sent successfully on attempt {attempt+1}"
                    )
                    # 邮件发送成功后尝试安全关闭连接
                    try:
                        server.quit()
                    except Exception as close_error:
                        # QQ邮箱特有的连接关闭错误，可以忽略
                        if b"\x00\x00\x00" in str(close_error).encode():
                            logger.warning(
                                f"[send_email_sync] Ignoring QQ Mail specific connection closing error: {close_error}"
                            )
                        else:
                            logger.warning(
                                f"[send_email_sync] Non-critical error when closing SMTP connection: {close_error}"
                            )
                    break
                except UnicodeEncodeError as ue:
                    logger.error(f"[send_email_sync] Unicode encoding error: {ue}")
                    # 尝试重新编码邮件内容
                    try:
                        part = MIMEText(html_content, "html", "utf-8")
                        message.attach(part)
                        if attempt < max_retries - 1:
                            time.sleep(2)
                        else:
                            raise
                    except Exception as e2:
                        logger.error(
                            f"[send_email_sync] Failed to re-encode email: {e2}"
                        )
                        raise
                except Exception as e:
                    logger.warning(f"[send_email_sync] Attempt {attempt+1} failed: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(2)  # 等待2秒后重试
                    else:
                        raise
        else:
            # 不使用with语句，手动管理连接关闭
            server = smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT)
            try:
                if settings.SMTP_TLS:
                    server.starttls()
                server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
                recipients = [email_to]
                if cc:
                    recipients.extend(cc)
                if bcc:
                    recipients.extend(bcc)

                message_str_tls = message.as_string()
                server.sendmail(settings.EMAILS_FROM_EMAIL, recipients, message_str_tls)
                email_sent_successfully = True  # Mark as successful for TLS path
                # 安全关闭连接
                try:
                    server.quit()
                except Exception as close_error:
                    logger.warning(
                        f"[send_email_sync] Non-critical error when closing SMTP connection: {close_error}"
                    )
            except Exception as e:
                # 确保在异常情况下也尝试关闭连接
                try:
                    server.quit()
                except smtplib.SMTPException as quit_exc:
                    logger.warning(
                        f"Error during server.quit() in cleanup after another exception: {quit_exc}"
                    )
                raise e

        # 如果邮件已经成功发送，则返回成功
        if email_sent_successfully:
            logger.info(
                f"[send_email_sync] Email successfully sent to {email_to} with subject: {subject}"
            )
            return True
        else:
            logger.error(
                f"[send_email_sync] Failed to send email to {email_to} with subject: {subject}"
            )
            return False

    except Exception as e:
        logger.error(
            f"[send_email_sync] Failed to send email to {email_to}: {e}", exc_info=True
        )
        # 检查是否是连接问题
        if "Connection refused" in str(e) or "timeout" in str(e).lower():
            logger.error(
                f"[send_email_sync] Connection issue detected. Please check if SMTP server {settings.SMTP_HOST}:{settings.SMTP_PORT} is accessible"
            )
        elif "authentication" in str(e).lower() or "login" in str(e).lower():
            logger.error(
                "[send_email_sync] Authentication issue detected. Please verify SMTP credentials"
            )

        return False


def send_password_reset_email(email_to: str, username: str, token: str) -> bool:
    """
    发送密码重置邮件

    Args:
        email_to: 用户邮箱
        username: 用户名
        token: 重置密码令牌

    Returns:
        bool: 发送是否成功
    """
    # 始终使用生产环境URL，无论当前环境是什么
    production_url = "https://cxyai.shenzhuo.vip"
    reset_path = f"/reset-password?token={token}"
    absolute_url = f"{production_url}{reset_path}"

    logger.info(
        f"[send_password_reset_email] Generated reset URL: {absolute_url} (using production URL)"
    )

    subject = "EmotionAI - 重置您的密码"
    html_content = f"""
    <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
                <h2 style="color: #333; text-align: center;">密码重置</h2>
                <p>您好 {username},</p>
                <p>我们收到了您的密码重置请求。请点击以下链接来设置您的新密码：</p>
                <p style="text-align: center;">
                    <a href='{absolute_url}' style="display: inline-block; padding: 10px 20px; background-color: #1890ff; color: white; text-decoration: none; border-radius: 4px;">重置密码</a>
                </p>
                <p>如果上面的按钮不起作用，请复制以下链接到浏览器地址栏：</p>
                <p style="background-color: #f5f5f5; padding: 10px; word-break: break-all;">{absolute_url}</p>
                <p>如果您没有请求重置密码，请忽略此邮件。</p>
                <p>此链接将在24小时后失效。</p>
                <p>祝好，</p>
                <p>EmotionAI 团队</p>
            </div>
        </body>
    </html>
    """

    return send_email_sync(
        email_to=email_to, subject=subject, html_content=html_content
    )
