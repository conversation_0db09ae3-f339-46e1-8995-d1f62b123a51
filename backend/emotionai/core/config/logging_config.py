"""
日志配置模块
用于控制不同模块的日志级别，减少不必要的日志输出
"""

import logging
from typing import Dict

# 模块日志级别配置
MODULE_LOG_LEVELS: Dict[str, int] = {
    # 核心应用模块
    "emotionai.app": logging.INFO,
    "emotionai.api": logging.INFO,
    "emotionai.core": logging.INFO,
    # 情绪分析相关模块
    "emotionai.api.v1.endpoints.emotion.router": logging.INFO,  # 减少多模态分析的DEBUG日志
    "emotionai.core.ml": logging.INFO,
    "emotionai.core.ml.emotion_ensemble": logging.INFO,
    "emotionai.core.ml.pytorch_emotion_analyzer": logging.INFO,
    # 数据库相关
    "emotionai.core.database": logging.INFO,
    "sqlalchemy": logging.WARNING,
    # 第三方库
    "uvicorn": logging.WARNING,
    "fastapi": logging.INFO,
    "httpx": logging.WARNING,
    "requests": logging.WARNING,
    "urllib3": logging.WARNING,
    "asyncio": logging.WARNING,
    # 机器学习库
    "torch": logging.ERROR,
    "tensorflow": logging.ERROR,
    "onnxruntime": logging.ERROR,
    "ultralytics": logging.WARNING,
    "mediapipe": logging.WARNING,
    "deepface": logging.WARNING,
    "insightface": logging.WARNING,
    # 图像处理库
    "PIL": logging.WARNING,
    "cv2": logging.WARNING,
    "matplotlib": logging.WARNING,
    # 其他工具库
    "python_multipart": logging.WARNING,
    "passlib": logging.WARNING,
}

# 生产环境特殊配置
PRODUCTION_LOG_LEVELS: Dict[str, int] = {
    # 在生产环境中进一步减少日志输出
    "emotionai.api.v1.endpoints.emotion.router": logging.WARNING,  # 只记录警告和错误
    "emotionai.core.ml": logging.WARNING,
    "emotionai.core.ml.emotion_ensemble": logging.WARNING,
}


def apply_module_log_levels(is_production: bool = False) -> None:
    """
    应用模块日志级别配置

    Args:
        is_production: 是否为生产环境
    """
    # 选择配置
    config = MODULE_LOG_LEVELS.copy()
    if is_production:
        config.update(PRODUCTION_LOG_LEVELS)

    # 应用配置
    for module_name, level in config.items():
        logger = logging.getLogger(module_name)
        logger.setLevel(level)

    # 特别处理多模态分析的流式传输日志
    multimodal_logger = logging.getLogger("emotionai.api.v1.endpoints.emotion.router")
    if is_production:
        # 生产环境中只记录重要信息
        multimodal_logger.setLevel(logging.WARNING)
    else:
        # 开发环境中记录INFO级别，但不记录DEBUG
        multimodal_logger.setLevel(logging.INFO)


def get_logger_config() -> Dict[str, int]:
    """
    获取当前的日志级别配置

    Returns:
        模块日志级别配置字典
    """
    return MODULE_LOG_LEVELS.copy()


def set_multimodal_log_level(level: int) -> None:
    """
    专门设置多模态分析的日志级别

    Args:
        level: 日志级别 (logging.DEBUG, logging.INFO, logging.WARNING, logging.ERROR)
    """
    logger = logging.getLogger("emotionai.api.v1.endpoints.emotion.router")
    logger.setLevel(level)

    # 同时设置相关的子模块
    for module in [
        "emotionai.api.v1.endpoints.emotion",
        "emotionai.core.services.multimodal",
    ]:
        sub_logger = logging.getLogger(module)
        sub_logger.setLevel(level)
