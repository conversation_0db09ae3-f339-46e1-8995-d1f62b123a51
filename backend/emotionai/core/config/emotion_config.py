"""
情绪分析配置文件

集中管理情绪分析相关的配置参数，便于调优和维护

🎯 配置作用范围：
- 仅影响情绪分析相关功能
- 主要被 emotion_ensemble.py 使用
- 不影响系统其他模块

📝 可修改的配置项：
1. MODEL_WEIGHTS - 模型权重配置
2. EMOTION_CONFIDENCE_THRESHOLD - 置信度阈值
3. NEUTRAL_CONFIDENCE_PENALTY - Neutral惩罚因子
4. ALTERNATIVE_EMOTION_THRESHOLD - 替代情绪阈值
5. ENABLE_SMART_FALLBACK - 智能回退开关
6. ENABLE_DETAILED_LOGGING - 详细日志开关
7. EMOTION_LABELS - 情绪标签列表
8. EMOTION_DISPLAY_NAMES - 情绪显示名称
9. EMOTION_COLORS - UI颜色配置

⚠️  修改后需要重启后端服务才能生效
"""

# 模型权重配置
MODEL_WEIGHTS = {
    "deepface": 0.2,  # DeepFace权重，降低因为倾向于预测neutral
    "trakov_vit": 0.4,  # Trakov ViT权重，专业情绪模型
    "rajaram": 0.4,  # Rajaram权重，专业情绪模型
}

# 情绪置信度阈值
EMOTION_CONFIDENCE_THRESHOLD = 0.15  # 最低置信度阈值

# Neutral情绪特殊处理配置
NEUTRAL_CONFIDENCE_PENALTY = 0.8  # Neutral情绪置信度惩罚因子
ALTERNATIVE_EMOTION_THRESHOLD = 0.8  # 选择替代情绪的阈值（相对于neutral的比例）

# 智能回退策略配置
FALLBACK_NEUTRAL_WEIGHT = 0.8  # 回退时neutral的权重比例
ENABLE_SMART_FALLBACK = True  # 是否启用智能回退策略

# 日志配置
ENABLE_DETAILED_LOGGING = True  # 是否启用详细的预测日志
LOG_TOP_EMOTIONS_COUNT = 3  # 记录前N个情绪的分布

# 情绪标签映射（标准化）
EMOTION_LABELS = [
    "angry",
    "disgust",
    "fear",
    "happy",
    "neutral",
    "sad",
    "surprise",
    "contempt",
]

# 情绪名称映射（用于显示）
EMOTION_DISPLAY_NAMES = {
    "angry": "愤怒",
    "disgust": "厌恶",
    "fear": "恐惧",
    "happy": "快乐",
    "neutral": "平静",
    "sad": "悲伤",
    "surprise": "惊讶",
    "contempt": "轻蔑",
}

# 情绪颜色映射（用于UI显示）
EMOTION_COLORS = {
    "angry": "#FF6B6B",  # 红色
    "disgust": "#4ECDC4",  # 青色
    "fear": "#45B7D1",  # 蓝色
    "happy": "#96CEB4",  # 绿色
    "neutral": "#FFEAA7",  # 黄色
    "sad": "#DDA0DD",  # 紫色
    "surprise": "#FF7675",  # 橙红色
    "contempt": "#6C5CE7",  # 深紫色
}
