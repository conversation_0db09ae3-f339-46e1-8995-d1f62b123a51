"""
管理员服务
"""

from typing import Any, Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from emotionai.core.services.base.base import BaseService
from emotionai.models.system.system import SystemSettings
from emotionai.models.user.user import User
from emotionai.schemas.admin.admin import Admin
from emotionai.schemas.admin.settings import SystemSettingsCreate, SystemSettingsUpdate
from emotionai.schemas.user.user import UserStats


class AdminService(BaseService):
    """管理员服务"""

    def __init__(self, db: AsyncSession):
        """初始化管理员服务

        Args:
            db: 数据库会话
        """
        super().__init__(db, Admin)

    @classmethod
    async def get_dashboard_stats(cls, db: AsyncSession) -> dict[str, Any]:
        """获取仪表盘统计信息

        Returns:
            统计信息，格式与前端期望的DashboardStats匹配
        """
        from datetime import datetime, timedelta

        import pytz
        from sqlalchemy import and_, func, select

        from emotionai.models.invite.invite_code import InviteCode
        from emotionai.models.ml.emotion import EmotionAnalysis
        from emotionai.models.ml.emotion_feedback import EmotionFeedback
        from emotionai.models.user.user import User
        from emotionai.schemas.admin.dashboard import (
            AnnotationStats,
            EmotionAnalysisItem,
            EmotionStats,
            InviteStats,
            PerformanceStats,
            RecentActivity,
            SystemStats,
            UserStats,
        )

        # 获取当前时间和今天的开始时间（北京时间）
        # 统一使用北京时间
        beijing_tz = pytz.timezone("Asia/Shanghai")
        now = datetime.now(beijing_tz)
        today_start = datetime(now.year, now.month, now.day, tzinfo=beijing_tz)
        today_start_naive = today_start.replace(tzinfo=None)

        # 1. 获取用户统计数据
        # 总用户数
        total_users_result = await db.execute(select(func.count()).select_from(User))
        total_users = total_users_result.scalar() or 0

        # 活跃用户数（过去30天有登录记录的用户）
        thirty_days_ago = (now - timedelta(days=30)).replace(tzinfo=None)
        active_users_result = await db.execute(
            select(func.count())
            .select_from(User)
            .where(User.last_active_at >= thirty_days_ago)
        )
        active_users = active_users_result.scalar() or 0

        # 在线用户数（过去30分钟有活动的用户）
        thirty_minutes_ago = (now - timedelta(minutes=30)).replace(tzinfo=None)
        online_users_result = await db.execute(
            select(func.count())
            .select_from(User)
            .where(User.last_active_at >= thirty_minutes_ago)
        )
        online_users = online_users_result.scalar() or 0

        # 今日新增用户
        today_users_result = await db.execute(
            select(func.count()).select_from(User).where(User.created_at >= today_start_naive)
        )
        today_users = today_users_result.scalar() or 0

        user_stats = UserStats(
            total=total_users,
            active=active_users,
            online=online_users,
            today=today_users,
        )

        # 2. 获取邀请码统计数据
        # 总邀请码数
        total_invites_result = await db.execute(
            select(func.count()).select_from(InviteCode)
        )
        total_invites = total_invites_result.scalar() or 0

        # 已使用邀请码数
        used_invites_result = await db.execute(
            select(func.count())
            .select_from(InviteCode)
            .where(InviteCode.used_by.isnot(None))
        )
        used_invites = used_invites_result.scalar() or 0

        # 可用邀请码数
        available_invites = total_invites - used_invites

        invite_stats = InviteStats(
            total=total_invites, used=used_invites, available=available_invites
        )

        # 3. 获取情绪分析统计数据
        # 总分析数
        total_analyses_result = await db.execute(
            select(func.count()).select_from(EmotionAnalysis)
        )
        total_analyses = total_analyses_result.scalar() or 0

        # 今日分析数
        today_analyses_result = await db.execute(
            select(func.count())
            .select_from(EmotionAnalysis)
            .where(EmotionAnalysis.created_at >= today_start_naive)
        )
        today_analyses = today_analyses_result.scalar() or 0

        # 情绪分布
        emotion_distribution_result = await db.execute(
            select(
                EmotionAnalysis.primary_emotion, func.count()
            )  # 使用 primary_emotion
            .select_from(EmotionAnalysis)
            .group_by(EmotionAnalysis.primary_emotion)  # 使用 primary_emotion
        )
        emotion_distribution = {}
        for emotion, count in emotion_distribution_result.all():
            if emotion:  # 确保情绪值不为 None
                emotion_distribution[emotion] = count

        # 计算次要情绪分布
        secondary_emotion_distribution_result = await db.execute(
            select(EmotionAnalysis.secondary_emotion, func.count())
            .select_from(EmotionAnalysis)
            .where(EmotionAnalysis.secondary_emotion.isnot(None))  # 仅统计非空情绪
            .group_by(EmotionAnalysis.secondary_emotion)
        )
        secondary_emotion_distribution = {}
        for emotion, count in secondary_emotion_distribution_result.all():
            if emotion:  # 确保情绪值不为 None
                secondary_emotion_distribution[emotion] = count

        # 计算第三情绪分布
        third_emotion_distribution_result = await db.execute(
            select(EmotionAnalysis.third_emotion, func.count())
            .select_from(EmotionAnalysis)
            .where(EmotionAnalysis.third_emotion.isnot(None))  # 仅统计非空情绪
            .group_by(EmotionAnalysis.third_emotion)
        )
        third_emotion_distribution = {}
        for emotion, count in third_emotion_distribution_result.all():
            if emotion:  # 确保情绪值不为 None
                third_emotion_distribution[emotion] = count

        # 确保所有8种标准情绪都有值 (为主、次、第三情绪分布都进行处理)
        standard_emotions = [
            "愤怒",
            "恐惧",
            "中立",
            "悲伤",
            "厌恶",
            "快乐",
            "惊喜",
            "蔑视",
            "Angry",
            "Fear",
            "Neutral",
            "Sad",
            "Disgust",
            "Happy",
            "Surprise",
            "Contempt",  # 也包含英文，以防数据源不一致
        ]

        for emotion_dict in [
            emotion_distribution,
            secondary_emotion_distribution,
            third_emotion_distribution,
        ]:
            for emotion_key in standard_emotions:
                # 检查中文和英文两种key，因为前端映射时可能会产生这两种
                # 后端数据库存储的可能是英文，也可能是中文，取决于数据源
                # 这里确保统计时，如果标准情绪不存在于字典中，则添加计数为0
                # 优先使用字典中已有的key（可能是中文或英文）
                found = False
                for existing_key in list(
                    emotion_dict.keys()
                ):  # 使用 list() 避免在迭代时修改字典
                    if existing_key and emotion_key.lower() == existing_key.lower():
                        found = True
                        break
                if (
                    not found and emotion_key not in emotion_dict
                ):  # 如果标准情绪的任何形式都不在字典中
                    # 检查 emotion_key 是否是标准情绪列表中的一个，如果是，则添加
                    is_standard_emotion_key = any(
                        std_emo.lower() == emotion_key.lower()
                        for std_emo in standard_emotions
                    )
                    if is_standard_emotion_key:
                        emotion_dict[emotion_key] = 0

        # 准确率（这里使用固定值，实际应该根据反馈数据计算）
        accuracy = 0.92

        # 获取最新的情绪分析记录
        recent_analyses = []

        try:
            # 查询最新的10条情绪分析记录
            query = (
                select(
                    EmotionAnalysis.id,
                    EmotionAnalysis.primary_emotion,
                    EmotionAnalysis.confidence1,
                    EmotionAnalysis.secondary_emotion,  # 新增
                    EmotionAnalysis.confidence2,  # 新增
                    EmotionAnalysis.third_emotion,  # 新增
                    EmotionAnalysis.confidence3,  # 新增
                    EmotionAnalysis.input_type,
                    EmotionAnalysis.created_at,
                    User.username.label("user_name"),
                )
                .join(User, EmotionAnalysis.user_id == User.id)
                .order_by(EmotionAnalysis.created_at.desc())
                .limit(10)
            )

            result = await db.execute(query)
            analyses = result.fetchall()

            # 将查询结果转换为 EmotionAnalysisItem 对象
            for record in analyses:
                item = EmotionAnalysisItem(
                    id=str(record.id),
                    emotion=record.primary_emotion,
                    confidence=record.confidence1,
                    secondary_emotion=record.secondary_emotion,  # 新增
                    confidence2=record.confidence2,  # 新增
                    third_emotion=record.third_emotion,  # 新增
                    confidence3=record.confidence3,  # 新增
                    input_type=record.input_type,
                    created_at=record.created_at,
                    user_name=record.user_name or "未知用户",
                )
                recent_analyses.append(item)

        except Exception as e:
            print(f"查询情绪分析记录时出错: {e}")
            import traceback

            traceback.print_exc()

        # 如果没有查询到记录，只返回空列表
        if not recent_analyses:
            print("No emotion analysis records found in database")

        emotion_stats = EmotionStats(
            total=total_analyses,
            today_count=today_analyses,
            distribution=emotion_distribution,
            secondary_emotion_distribution=secondary_emotion_distribution,  # 新增
            third_emotion_distribution=third_emotion_distribution,  # 新增
            accuracy=accuracy,
            recent_analyses=recent_analyses,
        )

        # 4. 获取最近活动数据
        recent_activity = []
        for i in range(14, -1, -1):
            date = now - timedelta(days=i)
            date_str = date.strftime("%Y-%m-%d")
            date_start = datetime(
                date.year, date.month, date.day, tzinfo=pytz.timezone("Asia/Shanghai")
            )
            date_end = date_start + timedelta(days=1)

            # 当日新增用户数
            day_users_result = await db.execute(
                select(func.count())
                .select_from(User)
                .where(and_(User.created_at >= date_start, User.created_at < date_end))
            )
            day_users = day_users_result.scalar() or 0

            # 当日分析数
            day_analyses_result = await db.execute(
                select(func.count())
                .select_from(EmotionAnalysis)
                .where(
                    and_(
                        EmotionAnalysis.created_at >= date_start,
                        EmotionAnalysis.created_at < date_end,
                    )
                )
            )
            day_analyses = day_analyses_result.scalar() or 0

            # 当日活跃用户数
            day_active_users_result = await db.execute(
                select(func.count())
                .select_from(User)
                .where(
                    and_(
                        User.last_active_at >= date_start,
                        User.last_active_at < date_end,
                    )
                )
            )
            day_active_users = day_active_users_result.scalar() or 0

            recent_activity.append(
                RecentActivity(
                    date=date_str,
                    user_count=day_users,
                    analysis_count=day_analyses,
                    active_user_count=day_active_users,
                )
            )

        # 5. 获取性能统计数据（这里使用模拟数据，实际应该从监控系统获取）
        # 生成过去24小时的时间戳
        timestamps = []
        response_times = []
        qps_values = []

        for i in range(23, -1, -1):
            hour_time = now - timedelta(hours=i)
            timestamps.append(hour_time.strftime("%Y-%m-%d %H:%M:%S"))

            # 这里使用模拟数据，实际应该从监控系统获取
            # 响应时间在50-200ms之间波动
            import random

            response_time = round((50 + random.random() * 150), 1)
            response_times.append(response_time)

            # QPS在1-10之间波动
            qps = round((1 + random.random() * 9), 1)
            qps_values.append(qps)

        performance_stats = PerformanceStats(
            response_time=response_times, qps=qps_values, timestamps=timestamps
        )

        # 6. 获取系统统计数据（这里使用模拟数据，实际应该从系统获取）
        # 模拟系统资源使用情况
        import psutil

        try:
            # 尝试使用psutil获取真实系统数据
            cpu_usage = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            disk = psutil.disk_usage("/")
            disk_usage = disk.percent
            # 获取系统启动时间
            boot_time = datetime.fromtimestamp(
                psutil.boot_time(), tz=beijing_tz
            )
            uptime = int((now - boot_time).total_seconds())
        except Exception as e:
            # 如果获取失败，使用模拟数据
            print(f"获取系统数据失败: {e}")
            cpu_usage = random.uniform(10, 60)
            memory_usage = random.uniform(30, 80)
            disk_usage = random.uniform(40, 90)
            uptime = 3600 * 24 * random.randint(1, 30)  # 1-30天的秒数
            boot_time = now - timedelta(seconds=uptime)

        # 模拟网络使用情况
        network_usage = random.uniform(5, 50)  # 模拟5-50%的网络使用率

        system_stats = SystemStats(
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            disk_usage=disk_usage,
            network_usage=network_usage,
            uptime=uptime,
            last_boot=boot_time,
            extra_info={
                "os": "macOS",
                "python_version": "3.10",
                "processes": random.randint(100, 300),
                "threads": random.randint(500, 1500),
            },
        )

        # 转换为字典并打印返回的数据结构
        # 调试日志已移除

        # 创建一个列表来存储序列化后的记录
        serialized_analyses = []
        for item in recent_analyses:
            # 将 Pydantic 模型转换为字典
            analysis_dict = {
                "id": str(item.id),
                "emotion": item.emotion,
                "confidence": (
                    float(item.confidence) * 100 if item.confidence is not None else 0.0
                ),
                "secondary_emotion": item.secondary_emotion,  # 新增
                "confidence2": (
                    float(item.confidence2) * 100
                    if item.confidence2 is not None
                    else None
                ),  # 新增，处理None
                "third_emotion": item.third_emotion,  # 新增
                "confidence3": (
                    float(item.confidence3) * 100
                    if item.confidence3 is not None
                    else None
                ),  # 新增，处理None
                "input_type": item.input_type,
                "created_at": item.created_at.isoformat(),
                "user_name": item.user_name,
            }
            serialized_analyses.append(analysis_dict)

        # 创建带有序列化记录的情绪统计对象
        emotion_stats_dict = {
            "total": emotion_stats.total,
            "today_count": emotion_stats.today_count,
            "distribution": emotion_stats.distribution,
            "secondary_emotion_distribution": emotion_stats.secondary_emotion_distribution,  # 新增
            "third_emotion_distribution": emotion_stats.third_emotion_distribution,  # 新增
            "accuracy": emotion_stats.accuracy,
            "recent_analyses": serialized_analyses,
        }

        # 调试日志已移除

        # 使用序列化后的情绪分析记录创建新的 EmotionStats 对象
        # 注意：EmotionStats schema 已经更新，可以直接传递新的分布数据
        emotion_stats = EmotionStats(
            total=emotion_stats.total,
            today_count=emotion_stats.today_count,
            distribution=emotion_stats.distribution,
            secondary_emotion_distribution=emotion_stats.secondary_emotion_distribution,
            third_emotion_distribution=emotion_stats.third_emotion_distribution,
            accuracy=emotion_stats.accuracy,
            recent_analyses=serialized_analyses,  # recent_analyses 已经是序列化后的列表
        )

        # 调试日志已移除

        # 创建结果对象
        # EmotionStats 对象在调用 .dict() 时，会根据其 schema 自动包含所有字段
        emotion_stats_dict = emotion_stats.dict()

        # 打印转换后的字典字段
        # 调试日志已移除

        # 手动确保 recent_analyses 字段存在 (这一步理论上不再需要，因为 emotion_stats_dict 是从完整的 EmotionStats 模型转换而来)
        # if "recent_analyses" not in emotion_stats_dict:
        #     print("警告: recent_analyses 字段在字典中不存在，手动添加")
        #     emotion_stats_dict["recent_analyses"] = serialized_analyses

        # 4. 获取标注统计数据
        # 标注总数（基于 emotion_feedback 表中有 emotion 字段的记录）
        total_annotations_result = await db.execute(
            select(func.count())
            .select_from(EmotionFeedback)
            .where(EmotionFeedback.emotion.isnot(None))
        )
        total_annotations = total_annotations_result.scalar() or 0

        # 今日标注数
        today_annotations_result = await db.execute(
            select(func.count())
            .select_from(EmotionFeedback)
            .where(
                and_(
                    EmotionFeedback.emotion.isnot(None),
                    EmotionFeedback.created_at >= today_start_naive,
                )
            )
        )
        today_annotations = today_annotations_result.scalar() or 0

        # 创建标注统计对象
        annotation_stats = AnnotationStats(
            total=total_annotations,
            today_count=today_annotations,
        )

        # 创建结果对象
        result = {
            "user_stats": user_stats.dict(),
            "invite_stats": invite_stats.dict(),
            "emotion_stats": emotion_stats_dict,
            "annotation_stats": annotation_stats.dict(),
            "recent_activity": [item.dict() for item in recent_activity],
            "performance_stats": performance_stats.dict(),
            "system_stats": system_stats.dict(),
        }

        # 检查recent_analyses字段是否存在
        if "recent_analyses" not in result["emotion_stats"]:
            print("警告: recent_analyses 字段不存在于 emotion_stats 中!")

        # 返回完整的仪表盘统计数据
        return result

    async def _get_user_count(self) -> int:
        """获取用户总数"""
        result = await self.db.execute(select(User))
        return len(result.scalars().all())

    async def _get_role_count(self) -> int:
        """获取角色总数"""
        # TODO: 实现角色统计
        return 0

    async def _get_permission_count(self) -> int:
        """获取权限总数"""
        # TODO: 实现权限统计
        return 0

    async def _get_system_stats(self) -> dict[str, Any]:
        """获取系统资源统计"""
        # TODO: 实现系统资源统计
        return {
            "cpu_usage": 0.0,
            "memory_usage": 0.0,
            "disk_usage": 0.0,
            "network_usage": 0.0,
        }

    async def _get_user_stats(self) -> UserStats:
        """获取用户统计"""
        # TODO: 实现用户统计
        return UserStats(
            total=0,
            active=0,
            inactive=0,
            superusers=0,
            last_7_days=0,
            last_30_days=0,
            growth_rate=0.0,
        )

    async def _get_role_stats(self) -> dict[str, int]:
        """获取角色统计"""
        # TODO: 实现角色统计
        return {
            "total_roles": 0,
            "active_roles": 0,
            "inactive_roles": 0,
        }

    async def get_system_settings(self) -> Optional[SystemSettings]:
        """获取系统设置

        Returns:
            系统设置对象，如果不存在则返回None
        """
        result = await self.db.execute(select(SystemSettings).limit(1))
        return result.scalars().first()

    async def create_system_settings(
        self, settings_data: SystemSettingsCreate
    ) -> SystemSettings:
        """创建系统设置

        Args:
            settings_data: 系统设置数据

        Returns:
            创建的系统设置对象
        """
        # 创建系统设置对象
        settings = SystemSettings(
            system_name=settings_data.system_name,
            system_version=settings_data.system_version,
            system_description=settings_data.system_description,
            maintenance_mode=settings_data.maintenance_mode,
            maintenance_message=settings_data.maintenance_message,
            announcement=settings_data.announcement,
            show_announcement=settings_data.show_announcement,
            max_users=settings_data.max_users,
            max_analysis_per_user=settings_data.max_analysis_per_user,
            allow_registration=settings_data.allow_registration,
            require_invite_code=settings_data.require_invite_code,
            extra_settings=settings_data.extra_settings,
        )

        # 保存到数据库
        self.db.add(settings)
        await self.db.commit()
        await self.db.refresh(settings)

        return settings

    async def update_system_settings(
        self, settings_data: SystemSettingsUpdate
    ) -> SystemSettings:
        """更新系统设置

        Args:
            settings_data: 系统设置更新数据

        Returns:
            更新后的系统设置对象
        """
        # 获取当前系统设置
        settings = await self.get_system_settings()
        if not settings:
            raise ValueError("系统设置不存在")

        # 更新字段（只更新非None的字段）
        update_data = settings_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            if value is not None:  # 只更新非None的字段
                setattr(settings, key, value)

        # 保存到数据库
        await self.db.commit()
        await self.db.refresh(settings)

        return settings
