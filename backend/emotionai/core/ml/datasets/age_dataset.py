"""
年龄识别数据集处理模块

支持多种年龄数据集格式，包括：
- UTKFace数据集
- IMDB-WIKI数据集
- FairFace数据集
- 自定义年龄数据集
"""

import logging
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
import torch
from PIL import Image
from torch.utils.data import Dataset
from torchvision import transforms

logger = logging.getLogger(__name__)


class AgeDataset(Dataset):
    """年龄识别数据集基类"""

    def __init__(
        self,
        data_dir: str,
        csv_file: Optional[str] = None,
        transform: Optional[transforms.Compose] = None,
        target_size: Tuple[int, int] = (224, 224),
        age_range: Tuple[int, int] = (0, 100),
        mode: str = "regression",  # regression, classification
        num_age_groups: int = 8,
    ):
        """
        初始化年龄数据集

        Args:
            data_dir: 数据目录
            csv_file: CSV标注文件路径
            transform: 数据变换
            target_size: 目标图像尺寸
            age_range: 年龄范围
            mode: 模式（回归或分类）
            num_age_groups: 年龄组数量（分类模式下使用）
        """
        self.data_dir = Path(data_dir)
        self.csv_file = csv_file
        self.target_size = target_size
        self.age_range = age_range
        self.mode = mode
        self.num_age_groups = num_age_groups

        # 设置默认变换
        if transform is None:
            self.transform = self._get_default_transform()
        else:
            self.transform = transform

        # 加载数据
        self.samples = self._load_samples()

        logger.info(f"加载年龄数据集完成，共 {len(self.samples)} 个样本")
        logger.info(f"模式: {mode}, 年龄范围: {age_range}")

        if mode == "classification":
            logger.info(f"年龄组数量: {num_age_groups}")

    def _get_default_transform(self) -> transforms.Compose:
        """获取默认数据变换"""
        return transforms.Compose(
            [
                transforms.Resize(self.target_size),
                transforms.ToTensor(),
                transforms.Normalize(
                    mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]
                ),
            ]
        )

    def _load_samples(self) -> List[Dict[str, Any]]:
        """加载样本数据，由子类实现"""
        raise NotImplementedError("子类必须实现 _load_samples 方法")

    def _age_to_group(self, age: float) -> int:
        """将年龄转换为年龄组"""
        # 定义年龄组边界
        if self.num_age_groups == 8:
            boundaries = [0, 3, 10, 20, 30, 40, 50, 60, 100]
        elif self.num_age_groups == 5:
            boundaries = [0, 18, 30, 45, 60, 100]
        else:
            # 均匀分割
            step = (self.age_range[1] - self.age_range[0]) / self.num_age_groups
            boundaries = [
                self.age_range[0] + i * step for i in range(self.num_age_groups + 1)
            ]

        for i in range(len(boundaries) - 1):
            if boundaries[i] <= age < boundaries[i + 1]:
                return i
        return len(boundaries) - 2  # 最后一组

    def __len__(self) -> int:
        return len(self.samples)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, Union[float, int]]:
        sample = self.samples[idx]

        # 加载图像
        image_path = sample["image_path"]
        try:
            image = Image.open(image_path).convert("RGB")
        except Exception as e:
            logger.error(f"加载图像失败: {image_path}, 错误: {e}")
            # 返回黑色图像作为备用
            image = Image.new("RGB", self.target_size, (0, 0, 0))

        # 应用变换
        if self.transform:
            image = self.transform(image)

        # 处理标签
        age = sample["age"]
        if self.mode == "regression":
            target = float(age)
        else:  # classification
            target = self._age_to_group(age)

        return image, target


class UTKFaceDataset(AgeDataset):
    """UTKFace数据集"""

    def _load_samples(self) -> List[Dict[str, Any]]:
        """
        加载UTKFace数据集
        文件名格式: [age]_[gender]_[race]_[date&time].jpg
        """
        samples = []

        # 遍历所有图像文件
        for image_file in self.data_dir.glob("*.jpg"):
            try:
                # 解析文件名
                filename = image_file.stem
                parts = filename.split("_")

                if len(parts) >= 3:
                    age = int(parts[0])
                    gender = int(parts[1])  # 0: male, 1: female
                    race = int(
                        parts[2]
                    )  # 0: White, 1: Black, 2: Asian, 3: Indian, 4: Others

                    # 过滤年龄范围
                    if self.age_range[0] <= age <= self.age_range[1]:
                        samples.append(
                            {
                                "image_path": str(image_file),
                                "age": age,
                                "gender": gender,
                                "race": race,
                                "filename": filename,
                            }
                        )
            except (ValueError, IndexError) as e:
                logger.warning(f"解析文件名失败: {image_file}, 错误: {e}")
                continue

        return samples


class IMDBWIKIDataset(AgeDataset):
    """IMDB-WIKI数据集"""

    def _load_samples(self) -> List[Dict[str, Any]]:
        """
        加载IMDB-WIKI数据集
        需要提供CSV文件，包含image_path和age列
        """
        if not self.csv_file:
            raise ValueError("IMDB-WIKI数据集需要提供CSV文件")

        samples = []
        df = pd.read_csv(self.csv_file)

        for _, row in df.iterrows():
            try:
                image_path = os.path.join(self.data_dir, row["image_path"])
                age = float(row["age"])

                # 检查文件是否存在
                if (
                    os.path.exists(image_path)
                    and self.age_range[0] <= age <= self.age_range[1]
                ):
                    samples.append(
                        {
                            "image_path": image_path,
                            "age": age,
                            "filename": os.path.basename(image_path),
                        }
                    )
            except (KeyError, ValueError) as e:
                logger.warning(f"处理行数据失败: {row}, 错误: {e}")
                continue

        return samples


class FairFaceDataset(AgeDataset):
    """FairFace数据集"""

    def _load_samples(self) -> List[Dict[str, Any]]:
        """
        加载FairFace数据集
        需要提供CSV文件，包含file, age, gender, race列
        """
        if not self.csv_file:
            raise ValueError("FairFace数据集需要提供CSV文件")

        samples = []
        df = pd.read_csv(self.csv_file)

        for _, row in df.iterrows():
            try:
                image_path = os.path.join(self.data_dir, row["file"])
                age_group = row["age"]

                # 将年龄组转换为数值
                age = self._age_group_to_value(age_group)

                # 检查文件是否存在
                if (
                    os.path.exists(image_path)
                    and self.age_range[0] <= age <= self.age_range[1]
                ):
                    samples.append(
                        {
                            "image_path": image_path,
                            "age": age,
                            "age_group": age_group,
                            "gender": row.get("gender", "unknown"),
                            "race": row.get("race", "unknown"),
                            "filename": row["file"],
                        }
                    )
            except (KeyError, ValueError) as e:
                logger.warning(f"处理行数据失败: {row}, 错误: {e}")
                continue

        return samples

    def _age_group_to_value(self, age_group: str) -> float:
        """将FairFace年龄组转换为数值"""
        age_mapping = {
            "0-2": 1,
            "3-9": 6,
            "10-19": 15,
            "20-29": 25,
            "30-39": 35,
            "40-49": 45,
            "50-59": 55,
            "60-69": 65,
            "70+": 75,
        }
        return age_mapping.get(age_group, 25)  # 默认25岁


class CustomAgeDataset(AgeDataset):
    """自定义年龄数据集"""

    def _load_samples(self) -> List[Dict[str, Any]]:
        """
        加载自定义年龄数据集
        支持多种格式：
        1. CSV文件格式
        2. 目录结构格式（按年龄组织）
        3. 文件名包含年龄信息
        """
        samples = []

        if self.csv_file and os.path.exists(self.csv_file):
            # CSV文件格式
            samples = self._load_from_csv()
        else:
            # 尝试从目录结构或文件名加载
            samples = self._load_from_directory()

        return samples

    def _load_from_csv(self) -> List[Dict[str, Any]]:
        """从CSV文件加载"""
        samples = []
        df = pd.read_csv(self.csv_file)

        required_columns = ["image_path", "age"]
        if not all(col in df.columns for col in required_columns):
            raise ValueError(f"CSV文件必须包含列: {required_columns}")

        for _, row in df.iterrows():
            try:
                image_path = os.path.join(self.data_dir, row["image_path"])
                age = float(row["age"])

                if (
                    os.path.exists(image_path)
                    and self.age_range[0] <= age <= self.age_range[1]
                ):
                    sample = {
                        "image_path": image_path,
                        "age": age,
                        "filename": os.path.basename(image_path),
                    }

                    # 添加可选字段
                    for col in ["gender", "race", "emotion"]:
                        if col in row:
                            sample[col] = row[col]

                    samples.append(sample)
            except (KeyError, ValueError) as e:
                logger.warning(f"处理行数据失败: {row}, 错误: {e}")
                continue

        return samples

    def _load_from_directory(self) -> List[Dict[str, Any]]:
        """从目录结构加载"""
        samples = []

        # 支持的图像格式
        image_extensions = {".jpg", ".jpeg", ".png", ".bmp", ".tiff"}

        # 遍历所有图像文件
        for image_file in self.data_dir.rglob("*"):
            if image_file.suffix.lower() in image_extensions:
                try:
                    # 尝试从文件名提取年龄
                    age = self._extract_age_from_filename(image_file.name)

                    if (
                        age is not None
                        and self.age_range[0] <= age <= self.age_range[1]
                    ):
                        samples.append(
                            {
                                "image_path": str(image_file),
                                "age": age,
                                "filename": image_file.name,
                            }
                        )
                except Exception as e:
                    logger.warning(f"处理文件失败: {image_file}, 错误: {e}")
                    continue

        return samples

    def _extract_age_from_filename(self, filename: str) -> Optional[float]:
        """从文件名提取年龄"""
        # 尝试多种模式
        patterns = [
            r"age_(\d+)",  # age_25.jpg
            r"(\d+)_age",  # 25_age.jpg
            r"(\d+)y",  # 25y.jpg
            r"(\d+)years",  # 25years.jpg
            r"age(\d+)",  # age25.jpg
            r"^(\d+)_",  # 25_xxx.jpg
            r"_(\d+)_",  # xxx_25_xxx.jpg
            r"_(\d+)\.",  # xxx_25.jpg
        ]

        for pattern in patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                try:
                    age = int(match.group(1))
                    return float(age)
                except ValueError:
                    continue

        return None


class AgeDatasetFactory:
    """年龄数据集工厂类"""

    @staticmethod
    def create_dataset(
        dataset_type: str, data_dir: str, csv_file: Optional[str] = None, **kwargs
    ) -> AgeDataset:
        """
        创建年龄数据集

        Args:
            dataset_type: 数据集类型 (utkface, imdb_wiki, fairface, custom)
            data_dir: 数据目录
            csv_file: CSV文件路径
            **kwargs: 其他参数

        Returns:
            年龄数据集实例
        """
        dataset_type = dataset_type.lower()

        if dataset_type == "utkface":
            return UTKFaceDataset(data_dir=data_dir, csv_file=csv_file, **kwargs)
        elif dataset_type == "imdb_wiki":
            return IMDBWIKIDataset(data_dir=data_dir, csv_file=csv_file, **kwargs)
        elif dataset_type == "fairface":
            return FairFaceDataset(data_dir=data_dir, csv_file=csv_file, **kwargs)
        elif dataset_type == "custom":
            return CustomAgeDataset(data_dir=data_dir, csv_file=csv_file, **kwargs)
        else:
            raise ValueError(f"不支持的数据集类型: {dataset_type}")


def create_age_transforms(
    target_size: Tuple[int, int] = (224, 224),
    is_training: bool = True,
    augmentation_strength: float = 0.5,
) -> transforms.Compose:
    """
    创建年龄识别的数据变换

    Args:
        target_size: 目标尺寸
        is_training: 是否为训练模式
        augmentation_strength: 数据增强强度

    Returns:
        数据变换组合
    """
    if is_training:
        # 训练时的数据增强
        transform_list = [
            transforms.Resize((int(target_size[0] * 1.1), int(target_size[1] * 1.1))),
            transforms.RandomCrop(target_size),
            transforms.RandomHorizontalFlip(p=0.5),
        ]

        # 根据增强强度添加更多变换
        if augmentation_strength > 0.3:
            transform_list.extend(
                [
                    transforms.ColorJitter(
                        brightness=0.2 * augmentation_strength,
                        contrast=0.2 * augmentation_strength,
                        saturation=0.2 * augmentation_strength,
                        hue=0.1 * augmentation_strength,
                    ),
                    transforms.RandomRotation(degrees=10 * augmentation_strength),
                ]
            )

        if augmentation_strength > 0.6:
            transform_list.extend(
                [
                    transforms.RandomAffine(
                        degrees=0,
                        translate=(
                            0.1 * augmentation_strength,
                            0.1 * augmentation_strength,
                        ),
                        scale=(
                            1 - 0.1 * augmentation_strength,
                            1 + 0.1 * augmentation_strength,
                        ),
                    ),
                    transforms.RandomErasing(p=0.2 * augmentation_strength),
                ]
            )

        transform_list.extend(
            [
                transforms.ToTensor(),
                transforms.Normalize(
                    mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]
                ),
            ]
        )
    else:
        # 验证/测试时的变换
        transform_list = [
            transforms.Resize(target_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ]

    return transforms.Compose(transform_list)
