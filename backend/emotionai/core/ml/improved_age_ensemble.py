"""
改进的年龄识别集成模型

结合多种预训练模型和自训练模型，提供更准确的年龄预测：
1. DeepFace年龄模型
2. InsightFace年龄模型
3. 自训练的年龄回归模型
4. 自训练的多任务模型
5. 基于Transformer的年龄模型
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import cv2
import numpy as np
import torch
import torch.nn.functional as F
from PIL import Image
from torchvision import transforms

from emotionai.core.ml.deepface_wrapper import DeepFaceWrapper
from emotionai.core.ml.trainers.age_trainer import AgeRegressionModel, MultiTaskAgeModel
from emotionai.core.ml.utils import safe_torch_load
from emotionai.core.utils.singleton import Singleton

logger = logging.getLogger(__name__)


class ImprovedAgeEnsemble(metaclass=Singleton):
    """改进的年龄识别集成模型"""

    def __init__(
        self,
        model_dir: str = "models/age_recognition",
        device: Optional[str] = None,
        enable_models: Optional[List[str]] = None,
    ):
        """
        初始化改进的年龄集成模型

        Args:
            model_dir: 模型目录
            device: 计算设备
            enable_models: 启用的模型列表
        """
        self.model_dir = Path(model_dir)

        # 设置设备
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)

        # 默认启用的模型
        if enable_models is None:
            enable_models = [
                "deepface",
                "insightface",
                "custom_regression",
                "custom_multitask",
            ]

        self.enable_models = enable_models

        # 初始化各个模型
        self.models = {}
        self.model_weights = {}

        # 数据预处理
        self.transform = transforms.Compose(
            [
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(
                    mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]
                ),
            ]
        )

        self._initialize_models()
        self._set_ensemble_weights()

        logger.info(f"改进年龄集成模型初始化完成，启用模型: {list(self.models.keys())}")

    def _initialize_models(self):
        """初始化各个模型"""
        # 1. DeepFace模型
        if "deepface" in self.enable_models:
            try:
                self.models["deepface"] = DeepFaceWrapper()
                logger.info("DeepFace年龄模型加载成功")
            except Exception as e:
                logger.warning(f"DeepFace年龄模型加载失败: {e}")

        # 2. InsightFace模型
        if "insightface" in self.enable_models:
            try:
                from insightface.app import FaceAnalysis

                self.models["insightface"] = FaceAnalysis(name="buffalo_l")
                self.models["insightface"].prepare(ctx_id=0, det_size=(320, 320))
                logger.info("InsightFace年龄模型加载成功")
            except Exception as e:
                logger.warning(f"InsightFace年龄模型加载失败: {e}")

        # 3. 自训练回归模型
        if "custom_regression" in self.enable_models:
            try:
                model_path = self.model_dir / "regression" / "best_model.pth"
                if model_path.exists():
                    model = AgeRegressionModel(backbone="resnet50", pretrained=False)
                    checkpoint = safe_torch_load(model_path, map_location=self.device)
                    model.load_state_dict(checkpoint["model_state_dict"])
                    model.to(self.device)
                    model.eval()
                    self.models["custom_regression"] = model
                    logger.info("自训练回归模型加载成功")
                else:
                    logger.warning(f"自训练回归模型文件不存在: {model_path}")
            except Exception as e:
                logger.warning(f"自训练回归模型加载失败: {e}")

        # 4. 自训练多任务模型
        if "custom_multitask" in self.enable_models:
            try:
                model_path = self.model_dir / "multitask" / "best_model.pth"
                if model_path.exists():
                    model = MultiTaskAgeModel(backbone="resnet50", pretrained=False)
                    checkpoint = safe_torch_load(model_path, map_location=self.device)
                    model.load_state_dict(checkpoint["model_state_dict"])
                    model.to(self.device)
                    model.eval()
                    self.models["custom_multitask"] = model
                    logger.info("自训练多任务模型加载成功")
                else:
                    logger.warning(f"自训练多任务模型文件不存在: {model_path}")
            except Exception as e:
                logger.warning(f"自训练多任务模型加载失败: {e}")

        # 5. EfficientNet模型
        if "efficientnet" in self.enable_models:
            try:
                model_path = self.model_dir / "efficientnet" / "best_model.pth"
                if model_path.exists():
                    model = AgeRegressionModel(
                        backbone="efficientnet_b0", pretrained=False
                    )
                    checkpoint = safe_torch_load(model_path, map_location=self.device)
                    model.load_state_dict(checkpoint["model_state_dict"])
                    model.to(self.device)
                    model.eval()
                    self.models["efficientnet"] = model
                    logger.info("EfficientNet年龄模型加载成功")
                else:
                    logger.warning(f"EfficientNet模型文件不存在: {model_path}")
            except Exception as e:
                logger.warning(f"EfficientNet模型加载失败: {e}")

    def _set_ensemble_weights(self):
        """设置集成权重"""
        # 基于模型性能设置权重 - 优化后的配置
        default_weights = {
            "deepface": 0.30,  # 从0.15提升到0.30，因为修正后效果更好
            "insightface": 0.40,  # 从0.25提升到0.40，主要模型但不过度依赖
            "custom_regression": 0.20,  # 保持不变
            "custom_multitask": 0.10,  # 从0.20降低到0.10，辅助模型
            "efficientnet": 0.00,  # 暂时禁用，从0.15降低到0.00
        }

        # 只保留已加载模型的权重
        total_weight = 0
        for model_name in self.models.keys():
            if model_name in default_weights:
                self.model_weights[model_name] = default_weights[model_name]
                total_weight += default_weights[model_name]

        # 归一化权重
        if total_weight > 0:
            for model_name in self.model_weights:
                self.model_weights[model_name] /= total_weight

        logger.info(f"集成权重设置: {self.model_weights}")

    def predict_age(self, face_image: np.ndarray) -> Dict[str, Any]:
        """
        预测年龄

        Args:
            face_image: 人脸图像 (BGR格式)

        Returns:
            包含年龄预测结果的字典
        """
        predictions = {}
        confidences = {}

        # 转换为RGB格式
        if len(face_image.shape) == 3 and face_image.shape[2] == 3:
            rgb_image = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)
        else:
            rgb_image = face_image

        # 1. DeepFace预测
        if "deepface" in self.models:
            try:
                result = self.models["deepface"].analyze(rgb_image)
                age = result.get("age")
                if age is not None:
                    # 应用改进的年龄修正
                    confidence = 0.7  # DeepFace的默认置信度
                    corrected_age = self._correct_deepface_age(
                        age, confidence=confidence, face_quality=1.0
                    )
                    predictions["deepface"] = corrected_age
                    confidences["deepface"] = confidence
                    logger.debug(
                        f"DeepFace年龄预测: {age} -> {corrected_age} (置信度: {confidence})"
                    )
            except Exception as e:
                logger.warning(f"DeepFace年龄预测失败: {e}")

        # 2. InsightFace预测
        if "insightface" in self.models:
            try:
                faces = self.models["insightface"].get(rgb_image)
                if faces:
                    age = faces[0].age
                    predictions["insightface"] = float(age)
                    confidences["insightface"] = 0.8  # InsightFace通常更准确
                    logger.debug(f"InsightFace年龄预测: {age}")
            except Exception as e:
                logger.warning(f"InsightFace年龄预测失败: {e}")

        # 3. 自训练模型预测
        pil_image = Image.fromarray(rgb_image)
        tensor_image = self.transform(pil_image).unsqueeze(0).to(self.device)

        # 自训练回归模型
        if "custom_regression" in self.models:
            try:
                with torch.no_grad():
                    age_pred = self.models["custom_regression"](tensor_image)
                    age = float(age_pred.cpu().numpy()[0])
                    predictions["custom_regression"] = age
                    confidences["custom_regression"] = 0.85
                    logger.debug(f"自训练回归模型年龄预测: {age}")
            except Exception as e:
                logger.warning(f"自训练回归模型预测失败: {e}")

        # 自训练多任务模型
        if "custom_multitask" in self.models:
            try:
                with torch.no_grad():
                    age_regression, age_classification = self.models[
                        "custom_multitask"
                    ](tensor_image)

                    # 回归结果
                    age_reg = float(age_regression.cpu().numpy()[0])

                    # 分类结果转换为年龄
                    age_probs = F.softmax(age_classification, dim=1)
                    # 移除未使用的变量 age_groups
                    age_group_values = torch.tensor(
                        [1, 6, 15, 25, 35, 45, 55, 65],
                        device=self.device,
                        dtype=torch.float32,
                    )
                    age_cls = torch.sum(
                        age_probs * age_group_values.unsqueeze(0), dim=1
                    )
                    age_cls = float(age_cls.cpu().numpy()[0])

                    # 融合回归和分类结果
                    age_final = 0.7 * age_reg + 0.3 * age_cls
                    predictions["custom_multitask"] = age_final
                    confidences["custom_multitask"] = 0.9
                    logger.debug(
                        f"多任务模型年龄预测: 回归={age_reg:.1f}, 分类={age_cls:.1f}, 融合={age_final:.1f}"
                    )
            except Exception as e:
                logger.warning(f"自训练多任务模型预测失败: {e}")

        # EfficientNet模型
        if "efficientnet" in self.models:
            try:
                with torch.no_grad():
                    age_pred = self.models["efficientnet"](tensor_image)
                    age = float(age_pred.cpu().numpy()[0])
                    predictions["efficientnet"] = age
                    confidences["efficientnet"] = 0.8
                    logger.debug(f"EfficientNet年龄预测: {age}")
            except Exception as e:
                logger.warning(f"EfficientNet模型预测失败: {e}")

        # 集成预测结果
        final_age, ensemble_confidence = self._ensemble_predictions(
            predictions, confidences
        )

        return {
            "age": final_age,
            "confidence": ensemble_confidence,
            "individual_predictions": predictions,
            "individual_confidences": confidences,
            "model_weights": self.model_weights,
        }

    def _correct_deepface_age(
        self, age: float, confidence: float = 0.7, face_quality: float = 1.0
    ) -> float:
        """
        改进的DeepFace年龄修正算法

        Args:
            age: 原始年龄预测
            confidence: 预测置信度 (0-1)
            face_quality: 人脸质量评分 (0-1)

        Returns:
            修正后的年龄
        """
        if age is None:
            return 25.0

        # 更保守的基础修正系数
        if age < 20:
            base_correction = 1.25  # 降低从1.4到1.25
        elif age < 30:
            base_correction = 1.15  # 降低从1.25到1.15
        elif age < 45:
            base_correction = 1.10  # 降低从1.25到1.10
        elif age < 60:
            base_correction = 1.05  # 降低从1.1到1.05
        else:
            base_correction = 1.02  # 降低从1.05到1.02

        # 置信度调整：低置信度时修正幅度减小
        confidence_factor = 0.5 + 0.5 * confidence

        # 人脸质量调整：质量差时修正幅度减小
        quality_factor = 0.7 + 0.3 * face_quality

        # 综合修正系数
        final_correction = (
            1.0 + (base_correction - 1.0) * confidence_factor * quality_factor
        )

        # 应用修正
        corrected_age = age * final_correction

        # 限制在合理范围内
        return max(1, min(corrected_age, 100))

    def _ensemble_predictions(
        self, predictions: Dict[str, float], confidences: Dict[str, float]
    ) -> Tuple[float, float]:
        """
        集成多个模型的预测结果

        Args:
            predictions: 各模型的预测结果
            confidences: 各模型的置信度

        Returns:
            (最终年龄, 集成置信度)
        """
        if not predictions:
            return 25.0, 0.0  # 默认年龄

        # 方法1: 加权平均
        weighted_sum = 0.0
        weight_sum = 0.0

        for model_name, age in predictions.items():
            if model_name in self.model_weights:
                weight = self.model_weights[model_name] * confidences.get(
                    model_name, 0.5
                )
                weighted_sum += age * weight
                weight_sum += weight

        if weight_sum > 0:
            ensemble_age_weighted = weighted_sum / weight_sum
        else:
            ensemble_age_weighted = np.mean(list(predictions.values()))

        # 方法2: 中位数（鲁棒性更好）
        ensemble_age_median = np.median(list(predictions.values()))

        # 方法3: 去除异常值后的平均
        ages = list(predictions.values())
        if len(ages) > 2:
            # 计算四分位数，去除异常值
            q1, q3 = np.percentile(ages, [25, 75])
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr

            filtered_ages = [age for age in ages if lower_bound <= age <= upper_bound]
            if filtered_ages:
                ensemble_age_filtered = np.mean(filtered_ages)
            else:
                ensemble_age_filtered = ensemble_age_median
        else:
            ensemble_age_filtered = np.mean(ages)

        # 最终集成：结合三种方法
        final_age = (
            0.5 * ensemble_age_weighted
            + 0.3 * ensemble_age_median
            + 0.2 * ensemble_age_filtered
        )

        # 计算集成置信度
        ensemble_confidence = self._calculate_ensemble_confidence(
            predictions, confidences
        )

        # 应用年龄范围约束
        final_age = max(0, min(final_age, 100))

        logger.info(
            f"年龄集成结果: 加权={ensemble_age_weighted:.1f}, 中位数={ensemble_age_median:.1f}, "
            f"过滤={ensemble_age_filtered:.1f}, 最终={final_age:.1f}, 置信度={ensemble_confidence:.3f}"
        )

        return final_age, ensemble_confidence

    def _calculate_ensemble_confidence(
        self, predictions: Dict[str, float], confidences: Dict[str, float]
    ) -> float:
        """计算集成置信度"""
        if len(predictions) < 2:
            return list(confidences.values())[0] if confidences else 0.5

        # 计算预测的一致性
        ages = list(predictions.values())
        age_std = np.std(ages)
        age_mean = np.mean(ages)

        # 标准化的变异系数
        cv = age_std / (age_mean + 1e-6)

        # 一致性得分（变异系数越小，一致性越高）
        consistency_score = max(0, 1 - cv / 0.5)  # 假设CV=0.5时一致性为0

        # 模型数量得分
        num_models_score = min(1.0, len(predictions) / 5.0)  # 5个模型时得分为1

        # 平均置信度
        avg_confidence = np.mean(list(confidences.values()))

        # 综合置信度
        ensemble_confidence = (
            0.4 * consistency_score + 0.3 * avg_confidence + 0.3 * num_models_score
        )

        return min(1.0, max(0.0, ensemble_confidence))

    def update_model_weights(self, new_weights: Dict[str, float]):
        """更新模型权重"""
        for model_name, weight in new_weights.items():
            if model_name in self.model_weights:
                self.model_weights[model_name] = weight

        # 重新归一化
        total_weight = sum(self.model_weights.values())
        if total_weight > 0:
            for model_name in self.model_weights:
                self.model_weights[model_name] /= total_weight

        logger.info(f"模型权重已更新: {self.model_weights}")

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "loaded_models": list(self.models.keys()),
            "model_weights": self.model_weights,
            "device": str(self.device),
            "model_dir": str(self.model_dir),
        }
