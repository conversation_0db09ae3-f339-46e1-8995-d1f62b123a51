"""
基础训练器类

提供深度学习模型训练的通用功能和接口
"""

import logging
import time
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Optional, Tuple

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm

from emotionai.core.ml.utils import safe_torch_load

logger = logging.getLogger(__name__)


class BaseTrainer(ABC):
    """基础训练器类"""

    def __init__(
        self,
        model: nn.Module,
        train_loader: DataLoader,
        val_loader: Optional[DataLoader] = None,
        device: Optional[str] = None,
        save_dir: str = "checkpoints",
        log_dir: str = "logs",
        **kwargs,
    ):
        """
        初始化基础训练器

        Args:
            model: 要训练的模型
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            device: 训练设备
            save_dir: 模型保存目录
            log_dir: 日志保存目录
        """
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader

        # 设置设备
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)

        self.model.to(self.device)

        # 创建保存目录
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)

        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # 初始化TensorBoard
        self.writer = SummaryWriter(log_dir=str(self.log_dir))

        # 训练状态
        self.current_epoch = 0
        self.best_val_loss = float("inf")
        self.best_val_metric = 0.0
        self.train_losses = []
        self.val_losses = []

        logger.info(f"训练器初始化完成，使用设备: {self.device}")

    @abstractmethod
    def create_optimizer(self, **kwargs) -> optim.Optimizer:
        """创建优化器"""
        pass

    @abstractmethod
    def create_criterion(self, **kwargs) -> nn.Module:
        """创建损失函数"""
        pass

    @abstractmethod
    def compute_metrics(
        self, outputs: torch.Tensor, targets: torch.Tensor
    ) -> Dict[str, float]:
        """计算评估指标"""
        pass

    def train_epoch(self) -> float:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = len(self.train_loader)

        progress_bar = tqdm(self.train_loader, desc=f"Epoch {self.current_epoch + 1}")

        for batch_idx, (data, targets) in enumerate(progress_bar):
            data, targets = data.to(self.device), targets.to(self.device)

            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(data)
            loss = self.criterion(outputs, targets)

            # 反向传播
            loss.backward()

            # 梯度裁剪
            if hasattr(self, "max_grad_norm"):
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), self.max_grad_norm
                )

            self.optimizer.step()

            total_loss += loss.item()

            # 更新进度条
            progress_bar.set_postfix(
                {
                    "loss": f"{loss.item():.4f}",
                    "avg_loss": f"{total_loss / (batch_idx + 1):.4f}",
                }
            )

            # 记录到TensorBoard
            global_step = self.current_epoch * num_batches + batch_idx
            self.writer.add_scalar("Train/BatchLoss", loss.item(), global_step)

        avg_loss = total_loss / num_batches
        self.train_losses.append(avg_loss)

        return avg_loss

    def validate_epoch(self) -> Tuple[float, Dict[str, float]]:
        """验证一个epoch"""
        if self.val_loader is None:
            return 0.0, {}

        self.model.eval()
        total_loss = 0.0
        all_outputs = []
        all_targets = []

        with torch.no_grad():
            for data, targets in tqdm(self.val_loader, desc="Validation"):
                data, targets = data.to(self.device), targets.to(self.device)

                outputs = self.model(data)
                loss = self.criterion(outputs, targets)

                total_loss += loss.item()
                all_outputs.append(outputs.cpu())
                all_targets.append(targets.cpu())

        avg_loss = total_loss / len(self.val_loader)
        self.val_losses.append(avg_loss)

        # 计算指标
        all_outputs = torch.cat(all_outputs, dim=0)
        all_targets = torch.cat(all_targets, dim=0)
        metrics = self.compute_metrics(all_outputs, all_targets)

        return avg_loss, metrics

    def save_checkpoint(self, is_best: bool = False, **kwargs):
        """保存检查点"""
        checkpoint = {
            "epoch": self.current_epoch,
            "model_state_dict": self.model.state_dict(),
            "optimizer_state_dict": self.optimizer.state_dict(),
            "best_val_loss": self.best_val_loss,
            "best_val_metric": self.best_val_metric,
            "train_losses": self.train_losses,
            "val_losses": self.val_losses,
            **kwargs,
        }

        # 保存最新检查点
        checkpoint_path = self.save_dir / f"checkpoint_epoch_{self.current_epoch}.pth"
        torch.save(checkpoint, checkpoint_path)

        # 保存最佳模型
        if is_best:
            best_path = self.save_dir / "best_model.pth"
            torch.save(checkpoint, best_path)
            logger.info(f"保存最佳模型到: {best_path}")

    def load_checkpoint(self, checkpoint_path: str) -> bool:
        """加载检查点"""
        try:
            checkpoint = safe_torch_load(checkpoint_path, map_location=self.device)

            self.model.load_state_dict(checkpoint["model_state_dict"])
            self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
            self.current_epoch = checkpoint["epoch"]
            self.best_val_loss = checkpoint.get("best_val_loss", float("inf"))
            self.best_val_metric = checkpoint.get("best_val_metric", 0.0)
            self.train_losses = checkpoint.get("train_losses", [])
            self.val_losses = checkpoint.get("val_losses", [])

            logger.info(f"成功加载检查点: {checkpoint_path}")
            return True
        except Exception as e:
            logger.error(f"加载检查点失败: {e}")
            return False

    def train(
        self,
        epochs: int,
        learning_rate: float = 1e-3,
        patience: int = 10,
        save_every: int = 5,
        **kwargs,
    ):
        """
        训练模型

        Args:
            epochs: 训练轮数
            learning_rate: 学习率
            patience: 早停耐心值
            save_every: 每隔多少轮保存一次
        """
        # 创建优化器和损失函数
        self.optimizer = self.create_optimizer(learning_rate=learning_rate, **kwargs)
        self.criterion = self.create_criterion(**kwargs)

        # 学习率调度器
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode="min", patience=patience // 2, factor=0.5, verbose=True
        )

        # 早停计数器
        patience_counter = 0

        logger.info(f"开始训练，总轮数: {epochs}")
        start_time = time.time()

        for epoch in range(epochs):
            self.current_epoch = epoch

            # 训练
            train_loss = self.train_epoch()

            # 验证
            val_loss, val_metrics = self.validate_epoch()

            # 记录到TensorBoard
            self.writer.add_scalar("Train/EpochLoss", train_loss, epoch)
            if val_loss > 0:
                self.writer.add_scalar("Val/EpochLoss", val_loss, epoch)
                for metric_name, metric_value in val_metrics.items():
                    self.writer.add_scalar(f"Val/{metric_name}", metric_value, epoch)

            # 学习率调度
            if val_loss > 0:
                scheduler.step(val_loss)
            else:
                scheduler.step(train_loss)

            # 检查是否是最佳模型
            is_best = False
            if val_loss > 0 and val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                is_best = True
                patience_counter = 0
            elif val_loss == 0 and train_loss < self.best_val_loss:
                self.best_val_loss = train_loss
                is_best = True
                patience_counter = 0
            else:
                patience_counter += 1

            # 保存检查点
            if epoch % save_every == 0 or is_best:
                self.save_checkpoint(is_best=is_best)

            # 打印训练信息
            log_msg = f"Epoch {epoch + 1}/{epochs} - Train Loss: {train_loss:.4f}"
            if val_loss > 0:
                log_msg += f", Val Loss: {val_loss:.4f}"
                for metric_name, metric_value in val_metrics.items():
                    log_msg += f", {metric_name}: {metric_value:.4f}"

            logger.info(log_msg)

            # 早停检查
            if patience_counter >= patience:
                logger.info(f"验证损失连续 {patience} 轮未改善，提前停止训练")
                break

        total_time = time.time() - start_time
        logger.info(f"训练完成，总耗时: {total_time:.2f}秒")

        # 关闭TensorBoard
        self.writer.close()

    def __del__(self):
        """析构函数"""
        if hasattr(self, "writer"):
            self.writer.close()
