"""
年龄识别模型训练器

提供年龄识别模型的训练功能，支持多种网络架构和训练策略
"""

import logging
import math
from typing import Dict, Optional

import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.metrics import mean_absolute_error, mean_squared_error
from torch.utils.data import DataLoader
from tqdm import tqdm

from emotionai.core.ml.trainers.base_trainer import BaseTrainer

logger = logging.getLogger(__name__)


class AgeModelTrainer(BaseTrainer):
    """年龄识别模型训练器"""

    def __init__(
        self,
        model: nn.Module,
        train_loader: DataLoader,
        val_loader: Optional[DataLoader] = None,
        device: Optional[str] = None,
        save_dir: str = "checkpoints/age_model",
        log_dir: str = "logs/age_model",
        loss_type: str = "mse",  # mse, mae, smooth_l1, focal_mse
        age_range: tuple = (0, 100),
        **kwargs,
    ):
        """
        初始化年龄识别模型训练器

        Args:
            model: 年龄识别模型
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            device: 训练设备
            save_dir: 模型保存目录
            log_dir: 日志保存目录
            loss_type: 损失函数类型
            age_range: 年龄范围
        """
        super().__init__(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            device=device,
            save_dir=save_dir,
            log_dir=log_dir,
            **kwargs,
        )

        self.loss_type = loss_type
        self.age_range = age_range
        self.max_grad_norm = 1.0  # 梯度裁剪阈值

        logger.info(
            f"年龄识别训练器初始化完成，损失函数: {loss_type}, 年龄范围: {age_range}"
        )

    def create_optimizer(
        self, learning_rate: float = 1e-3, weight_decay: float = 1e-4, **kwargs
    ) -> optim.Optimizer:
        """创建优化器"""
        optimizer_type = kwargs.get("optimizer", "adamw")

        if optimizer_type.lower() == "adamw":
            optimizer = optim.AdamW(
                self.model.parameters(),
                lr=learning_rate,
                weight_decay=weight_decay,
                betas=(0.9, 0.999),
            )
        elif optimizer_type.lower() == "adam":
            optimizer = optim.Adam(
                self.model.parameters(), lr=learning_rate, weight_decay=weight_decay
            )
        elif optimizer_type.lower() == "sgd":
            optimizer = optim.SGD(
                self.model.parameters(),
                lr=learning_rate,
                weight_decay=weight_decay,
                momentum=0.9,
                nesterov=True,
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")

        logger.info(
            f"创建优化器: {optimizer_type}, 学习率: {learning_rate}, 权重衰减: {weight_decay}"
        )
        return optimizer

    def create_criterion(self, **kwargs) -> nn.Module:
        """创建损失函数"""
        if self.loss_type == "mse":
            criterion = nn.MSELoss()
        elif self.loss_type == "mae":
            criterion = nn.L1Loss()
        elif self.loss_type == "smooth_l1":
            criterion = nn.SmoothL1Loss(beta=1.0)
        elif self.loss_type == "focal_mse":
            criterion = FocalMSELoss(alpha=2.0)
        else:
            raise ValueError(f"不支持的损失函数类型: {self.loss_type}")

        logger.info(f"创建损失函数: {self.loss_type}")
        return criterion

    def compute_metrics(
        self, outputs: torch.Tensor, targets: torch.Tensor
    ) -> Dict[str, float]:
        """计算评估指标"""
        # 将输出和目标转换为numpy数组
        if outputs.dim() > 1:
            # 如果是分类输出，转换为年龄值
            if outputs.size(1) > 1:
                # 假设是年龄组分类，计算期望年龄
                age_groups = torch.arange(
                    outputs.size(1), device=outputs.device, dtype=torch.float32
                )
                age_groups = (
                    age_groups
                    * (self.age_range[1] - self.age_range[0])
                    / (outputs.size(1) - 1)
                    + self.age_range[0]
                )
                outputs = torch.sum(
                    torch.softmax(outputs, dim=1) * age_groups.unsqueeze(0), dim=1
                )
            else:
                outputs = outputs.squeeze()

        outputs_np = outputs.detach().cpu().numpy()
        targets_np = targets.detach().cpu().numpy()

        # 计算各种指标
        mae = mean_absolute_error(targets_np, outputs_np)
        mse = mean_squared_error(targets_np, outputs_np)
        rmse = math.sqrt(mse)

        # 计算准确率（允许一定误差范围）
        accuracy_1 = (abs(outputs_np - targets_np) <= 1).mean() * 100
        accuracy_3 = (abs(outputs_np - targets_np) <= 3).mean() * 100
        accuracy_5 = (abs(outputs_np - targets_np) <= 5).mean() * 100

        return {
            "MAE": mae,
            "MSE": mse,
            "RMSE": rmse,
            "Accuracy@1": accuracy_1,
            "Accuracy@3": accuracy_3,
            "Accuracy@5": accuracy_5,
        }


class FocalMSELoss(nn.Module):
    """Focal MSE Loss - 对困难样本给予更多关注"""

    def __init__(self, alpha: float = 2.0):
        super().__init__()
        self.alpha = alpha

    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        mse = (inputs - targets) ** 2
        focal_weight = torch.abs(inputs - targets) ** self.alpha
        return torch.mean(focal_weight * mse)


class AgeRegressionModel(nn.Module):
    """年龄回归模型"""

    def __init__(
        self,
        backbone: str = "resnet50",
        pretrained: bool = True,
        num_classes: int = 1,
        dropout: float = 0.5,
    ):
        super().__init__()

        if backbone == "resnet50":
            import torchvision.models as models

            self.backbone = models.resnet50(pretrained=pretrained)
            feature_dim = self.backbone.fc.in_features
            self.backbone.fc = nn.Identity()
        elif backbone == "efficientnet_b0":
            import torchvision.models as models

            self.backbone = models.efficientnet_b0(pretrained=pretrained)
            feature_dim = self.backbone.classifier[1].in_features
            self.backbone.classifier = nn.Identity()
        else:
            raise ValueError(f"不支持的backbone: {backbone}")

        # 年龄回归头
        self.age_head = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(feature_dim, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(256, num_classes),
        )

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """初始化权重"""
        for m in self.age_head.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        features = self.backbone(x)
        age = self.age_head(features)
        return age.squeeze(-1) if age.size(-1) == 1 else age


class MultiTaskAgeModel(nn.Module):
    """多任务年龄模型（年龄回归 + 年龄组分类）"""

    def __init__(
        self,
        backbone: str = "resnet50",
        pretrained: bool = True,
        num_age_groups: int = 8,
        dropout: float = 0.5,
    ):
        super().__init__()

        if backbone == "resnet50":
            import torchvision.models as models

            self.backbone = models.resnet50(pretrained=pretrained)
            feature_dim = self.backbone.fc.in_features
            self.backbone.fc = nn.Identity()
        else:
            raise ValueError(f"不支持的backbone: {backbone}")

        # 共享特征提取器
        self.shared_head = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(feature_dim, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
        )

        # 年龄回归头
        self.regression_head = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(256, 1),
        )

        # 年龄组分类头
        self.classification_head = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(256, num_age_groups),
        )

        self._init_weights()

    def _init_weights(self):
        """初始化权重"""
        for module in [
            self.shared_head,
            self.regression_head,
            self.classification_head,
        ]:
            for m in module.modules():
                if isinstance(m, nn.Linear):
                    nn.init.xavier_uniform_(m.weight)
                    if m.bias is not None:
                        nn.init.constant_(m.bias, 0)

    def forward(self, x: torch.Tensor) -> tuple:
        features = self.backbone(x)
        shared_features = self.shared_head(features)

        age_regression = self.regression_head(shared_features).squeeze(-1)
        age_classification = self.classification_head(shared_features)

        return age_regression, age_classification


class MultiTaskAgeTrainer(AgeModelTrainer):
    """多任务年龄训练器"""

    def __init__(
        self,
        model: MultiTaskAgeModel,
        train_loader: DataLoader,
        val_loader: Optional[DataLoader] = None,
        device: Optional[str] = None,
        save_dir: str = "checkpoints/multitask_age_model",
        log_dir: str = "logs/multitask_age_model",
        regression_weight: float = 1.0,
        classification_weight: float = 0.5,
        **kwargs,
    ):
        super().__init__(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            device=device,
            save_dir=save_dir,
            log_dir=log_dir,
            **kwargs,
        )

        self.regression_weight = regression_weight
        self.classification_weight = classification_weight

        # 创建两个损失函数
        self.regression_criterion = nn.MSELoss()
        self.classification_criterion = nn.CrossEntropyLoss()

    def create_criterion(self, **kwargs):
        """多任务模型不需要单独的损失函数"""
        return None

    def train_epoch(self) -> float:
        """训练一个epoch（多任务版本）"""
        self.model.train()
        total_loss = 0.0
        total_reg_loss = 0.0
        total_cls_loss = 0.0
        num_batches = len(self.train_loader)

        progress_bar = tqdm(self.train_loader, desc=f"Epoch {self.current_epoch + 1}")

        for batch_idx, (data, targets) in enumerate(progress_bar):
            data, targets = data.to(self.device), targets.to(self.device)

            # 假设targets包含年龄值和年龄组标签
            if isinstance(targets, (list, tuple)):
                age_targets, age_group_targets = targets
            else:
                age_targets = targets
                # 将年龄值转换为年龄组标签
                age_group_targets = self._age_to_group(age_targets)

            # 前向传播
            self.optimizer.zero_grad()
            age_regression, age_classification = self.model(data)

            # 计算损失
            reg_loss = self.regression_criterion(age_regression, age_targets.float())
            cls_loss = self.classification_criterion(
                age_classification, age_group_targets.long()
            )

            total_loss_batch = (
                self.regression_weight * reg_loss
                + self.classification_weight * cls_loss
            )

            # 反向传播
            total_loss_batch.backward()

            # 梯度裁剪
            if hasattr(self, "max_grad_norm"):
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), self.max_grad_norm
                )

            self.optimizer.step()

            total_loss += total_loss_batch.item()
            total_reg_loss += reg_loss.item()
            total_cls_loss += cls_loss.item()

            # 更新进度条
            progress_bar.set_postfix(
                {
                    "total_loss": f"{total_loss_batch.item():.4f}",
                    "reg_loss": f"{reg_loss.item():.4f}",
                    "cls_loss": f"{cls_loss.item():.4f}",
                }
            )

            # 记录到TensorBoard
            global_step = self.current_epoch * num_batches + batch_idx
            self.writer.add_scalar(
                "Train/TotalLoss", total_loss_batch.item(), global_step
            )
            self.writer.add_scalar("Train/RegressionLoss", reg_loss.item(), global_step)
            self.writer.add_scalar(
                "Train/ClassificationLoss", cls_loss.item(), global_step
            )

        avg_loss = total_loss / num_batches
        avg_reg_loss = total_reg_loss / num_batches
        avg_cls_loss = total_cls_loss / num_batches

        logger.info(
            f"训练损失 - 总损失: {avg_loss:.4f}, 回归损失: {avg_reg_loss:.4f}, 分类损失: {avg_cls_loss:.4f}"
        )

        return avg_loss

    def _age_to_group(self, ages: torch.Tensor) -> torch.Tensor:
        """将年龄值转换为年龄组标签"""
        # 定义年龄组边界
        age_boundaries = [0, 3, 10, 20, 30, 40, 50, 60, 70, 100]

        age_groups = torch.zeros_like(ages, dtype=torch.long)
        for i in range(len(age_boundaries) - 1):
            mask = (ages >= age_boundaries[i]) & (ages < age_boundaries[i + 1])
            age_groups[mask] = i

        return age_groups
