import logging
import os
from typing import Dict, List, Tuple

import torch
import torch.nn as nn
import torch.optim as optim
from PIL import Image
from sklearn.model_selection import train_test_split
from torch.utils.data import DataLoader, Dataset
from tqdm import tqdm
from transformers import ViTForImageClassification, ViTImageProcessor

# 配置日志记录
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class AgeDataset(Dataset):
    """年龄预测数据集"""

    def __init__(
        self, image_paths: List[str], labels: List[int], processor: ViTImageProcessor
    ):
        self.image_paths = image_paths
        self.labels = labels
        self.processor = processor

    def __len__(self) -> int:
        return len(self.image_paths)

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        try:
            image = Image.open(self.image_paths[idx]).convert("RGB")
            # 使用 ViTImageProcessor 处理图像
            inputs = self.processor(images=image, return_tensors="pt")
            # inputs['pixel_values'] 的形状是 [1, 3, 224, 224]，需要移除批次维度
            pixel_values = inputs["pixel_values"].squeeze(0)
            label = torch.tensor(self.labels[idx], dtype=torch.long)
            return {"pixel_values": pixel_values, "labels": label}
        except Exception as e:
            logger.error(f"加载图像失败 {self.image_paths[idx]}: {e}")
            # 返回一个占位符或跳过该样本
            # 这里我们简单地返回一个None，之后需要在DataLoader中处理
            return None  # type: ignore


def collate_fn(batch: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
    """
    自定义 collate_fn 以处理 AgeDataset 中可能返回 None 的情况。
    """
    batch = [item for item in batch if item is not None]  # 过滤掉 None 值
    if not batch:
        return {
            "pixel_values": torch.empty(0),
            "labels": torch.empty(0),
        }  # 返回空字典或张量

    pixel_values = torch.stack([item["pixel_values"] for item in batch])
    labels = torch.stack([item["labels"] for item in batch])
    return {"pixel_values": pixel_values, "labels": labels}


class ViTAgeTrainer:
    """基于 Vision Transformer 的年龄预测模型训练器"""

    def __init__(
        self,
        model_name_or_path: str = "google/vit-base-patch16-224-in21k",
        num_labels: int = 101,  # 假设年龄范围 0-100
        learning_rate: float = 5e-5,
        batch_size: int = 16,
        num_epochs: int = 10,
        device: str = "cuda" if torch.cuda.is_available() else "cpu",
        model_save_path: str = "vit_age_model.pth",
        data_dir: str = "path/to/your/age/dataset",  # 请替换为您的数据集路径
        test_size: float = 0.2,
        random_state: int = 42,
    ):
        """
        初始化训练器。

        Args:
            model_name_or_path (str): 预训练 ViT 模型的名称或路径。
            num_labels (int): 年龄类别数量。
            learning_rate (float): 学习率。
            batch_size (int): 批处理大小。
            num_epochs (int): 训练轮数。
            device (str): 训练设备 ("cuda" 或 "cpu")。
            model_save_path (str): 模型保存路径。
            data_dir (str): 包含图像和标签的数据集目录。
                           期望的结构: data_dir/age/image.jpg (其中 age 是一个整数目录名)
            test_size (float): 测试集所占比例。
            random_state (int): 随机种子，用于数据分割。
        """
        self.model_name_or_path = model_name_or_path
        self.num_labels = num_labels
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.num_epochs = num_epochs
        self.device = torch.device(device)
        self.model_save_path = model_save_path
        self.data_dir = data_dir
        self.test_size = test_size
        self.random_state = random_state

        logger.info(f"使用设备: {self.device}")

        # 初始化模型和图像处理器
        self.processor = ViTImageProcessor.from_pretrained(self.model_name_or_path)
        self.model = ViTForImageClassification.from_pretrained(
            self.model_name_or_path,
            num_labels=self.num_labels,
            ignore_mismatched_sizes=True,  # 如果预训练模型的头部与num_labels不匹配，则忽略
        ).to(self.device)

        # 初始化优化器和损失函数
        self.optimizer = optim.AdamW(self.model.parameters(), lr=self.learning_rate)
        self.criterion = nn.CrossEntropyLoss()  # 对于分类任务

        self.train_loader: DataLoader = None  # type: ignore
        self.val_loader: DataLoader = None  # type: ignore

    def _load_data(self) -> Tuple[List[str], List[int]]:
        """
        从指定目录加载图像路径和标签。
        期望的目录结构: data_dir/age_group/image_name.jpg
        例如: dataset/25/img1.jpg, dataset/30/img2.jpg
        """
        image_paths: List[str] = []
        labels: List[int] = []

        if not os.path.isdir(self.data_dir):
            logger.error(f"数据目录不存在: {self.data_dir}")
            return image_paths, labels

        for age_folder in os.listdir(self.data_dir):
            age_folder_path = os.path.join(self.data_dir, age_folder)
            if os.path.isdir(age_folder_path):
                try:
                    age = int(age_folder)  # 文件夹名称即为年龄
                    for image_name in os.listdir(age_folder_path):
                        if image_name.lower().endswith((".png", ".jpg", ".jpeg")):
                            image_paths.append(
                                os.path.join(age_folder_path, image_name)
                            )
                            labels.append(age)
                except ValueError:
                    logger.warning(f"跳过非年龄格式的文件夹: {age_folder}")

        if not image_paths:
            logger.warning(f"在 {self.data_dir} 中未找到图像数据。")

        return image_paths, labels

    def prepare_dataloaders(self):
        """准备训练和验证数据加载器"""
        image_paths, labels = self._load_data()
        if not image_paths or not labels:
            logger.error("数据加载失败，无法创建数据加载器。")
            return

        # 划分训练集和验证集
        train_paths, val_paths, train_labels, val_labels = train_test_split(
            image_paths,
            labels,
            test_size=self.test_size,
            random_state=self.random_state,
            stratify=labels if len(set(labels)) > 1 else None,
        )

        if not train_paths or not val_paths:
            logger.error(
                "数据划分后，训练集或验证集为空。请检查数据集大小和 test_size 参数。"
            )
            return

        train_dataset = AgeDataset(train_paths, train_labels, self.processor)
        val_dataset = AgeDataset(val_paths, val_labels, self.processor)

        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=4,  # 根据您的系统调整
            pin_memory=True if self.device == torch.device("cuda") else False,
            collate_fn=collate_fn,
        )
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=4,
            pin_memory=True if self.device == torch.device("cuda") else False,
            collate_fn=collate_fn,
        )
        logger.info(
            f"数据加载完成。训练样本数: {len(train_dataset)}, 验证样本数: {len(val_dataset)}"
        )

    def train_epoch(self, epoch_num: int) -> float:
        """训练一个 epoch"""
        self.model.train()
        total_loss = 0.0
        progress_bar = tqdm(
            self.train_loader,
            desc=f"Epoch {epoch_num+1}/{self.num_epochs} [训练中]",
            unit="batch",
        )

        for batch in progress_bar:
            if not batch["pixel_values"].numel():  # 检查批次是否为空
                continue

            pixel_values = batch["pixel_values"].to(self.device)
            labels = batch["labels"].to(self.device)

            self.optimizer.zero_grad()
            outputs = self.model(pixel_values=pixel_values)
            logits = outputs.logits
            loss = self.criterion(logits, labels)

            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()
            progress_bar.set_postfix(loss=loss.item())

        avg_loss = (
            total_loss / len(self.train_loader) if len(self.train_loader) > 0 else 0.0
        )
        logger.info(f"Epoch {epoch_num+1} 训练完成。平均损失: {avg_loss:.4f}")
        return avg_loss

    def evaluate(self, epoch_num: int) -> Tuple[float, float]:
        """在验证集上评估模型"""
        self.model.eval()
        total_loss = 0.0
        correct_predictions = 0
        total_samples = 0

        progress_bar = tqdm(
            self.val_loader,
            desc=f"Epoch {epoch_num+1}/{self.num_epochs} [验证中]",
            unit="batch",
        )

        with torch.no_grad():
            for batch in progress_bar:
                if not batch["pixel_values"].numel():  # 检查批次是否为空
                    continue

                pixel_values = batch["pixel_values"].to(self.device)
                labels = batch["labels"].to(self.device)

                outputs = self.model(pixel_values=pixel_values)
                logits = outputs.logits
                loss = self.criterion(logits, labels)
                total_loss += loss.item()

                _, predicted_labels = torch.max(logits, 1)
                correct_predictions += (predicted_labels == labels).sum().item()
                total_samples += labels.size(0)

                if total_samples > 0:
                    accuracy = correct_predictions / total_samples
                    progress_bar.set_postfix(loss=loss.item(), accuracy=accuracy)

        avg_loss = (
            total_loss / len(self.val_loader) if len(self.val_loader) > 0 else 0.0
        )
        accuracy = correct_predictions / total_samples if total_samples > 0 else 0.0
        logger.info(
            f"Epoch {epoch_num+1} 验证完成。平均损失: {avg_loss:.4f}, 准确率: {accuracy:.4f}"
        )
        return avg_loss, accuracy

    def train(self):
        """执行完整的训练过程"""
        self.prepare_dataloaders()
        if self.train_loader is None or self.val_loader is None:
            logger.error("数据加载器未正确初始化，无法开始训练。")
            return

        if len(self.train_loader) == 0:
            logger.error("训练数据加载器为空，无法开始训练。请检查数据集和路径。")
            return

        best_val_accuracy = 0.0
        for epoch in range(self.num_epochs):
            train_loss = self.train_epoch(epoch)

            if len(self.val_loader) > 0:
                val_loss, val_accuracy = self.evaluate(epoch)
                logger.info(
                    f"Epoch {epoch+1}/{self.num_epochs} | "
                    f"训练损失: {train_loss:.4f} | "
                    f"验证损失: {val_loss:.4f} | "
                    f"验证准确率: {val_accuracy:.4f}"
                )

                # 保存最佳模型
                if val_accuracy > best_val_accuracy:
                    best_val_accuracy = val_accuracy
                    torch.save(self.model.state_dict(), self.model_save_path)
                    logger.info(
                        f"模型已保存到 {self.model_save_path} (最佳验证准确率: {best_val_accuracy:.4f})"
                    )
            else:
                logger.info(
                    f"Epoch {epoch+1}/{self.num_epochs} | 训练损失: {train_loss:.4f} | 验证集为空，跳过评估。"
                )
                # 如果没有验证集，可以考虑在每个epoch后都保存模型，或者根据训练损失保存
                torch.save(
                    self.model.state_dict(),
                    f"{os.path.splitext(self.model_save_path)[0]}_epoch_{epoch+1}.pth",
                )
                logger.info(
                    f"模型已保存到 {os.path.splitext(self.model_save_path)[0]}_epoch_{epoch+1}.pth (无验证)"
                )

        logger.info("训练完成。")

    def predict(self, image_path: str) -> int:
        """
        对单张图像进行年龄预测。

        Args:
            image_path (str): 图像文件路径。

        Returns:
            int: 预测的年龄。
        """
        try:
            image = Image.open(image_path).convert("RGB")
        except FileNotFoundError:
            logger.error(f"图像文件未找到: {image_path}")
            return -1  # 表示错误
        except Exception as e:
            logger.error(f"加载图像失败 {image_path}: {e}")
            return -1

        self.model.eval()
        with torch.no_grad():
            inputs = self.processor(images=image, return_tensors="pt").to(self.device)
            pixel_values = inputs["pixel_values"]

            outputs = self.model(pixel_values=pixel_values)
            logits = outputs.logits
            predicted_class_idx = torch.argmax(logits, dim=1).item()

        logger.info(f"图像 {image_path} 的预测年龄为: {predicted_class_idx}")
        return predicted_class_idx


if __name__ == "__main__":
    # --- 配置训练参数 ---
    # 请确保替换 'path/to/your/age/dataset' 为实际的数据集路径
    # 数据集目录结构示例:
    # age_dataset/
    # ├── 20/
    # │   ├── image1.jpg
    # │   └── image2.png
    # ├── 25/
    # │   ├── image3.jpeg
    # │   └── image4.jpg
    # └── 30/
    #     └── ...

    DATA_DIRECTORY = "path/to/your/age/dataset"  # <--- 修改这里
    MODEL_SAVE_PATH = "vit_age_predictor_final.pth"
    NUM_EPOCHS = 5  # 示例值，根据需要调整
    BATCH_SIZE = 8  # 示例值，根据您的 GPU 内存调整
    LEARNING_RATE = 2e-5  # 示例值

    # 检查数据集路径是否存在
    if DATA_DIRECTORY == "path/to/your/age/dataset" or not os.path.exists(
        DATA_DIRECTORY
    ):
        logger.error("=" * 50)
        logger.error("错误：请修改 'DATA_DIRECTORY' 为您的年龄数据集的实际路径。")
        logger.error(f"当前路径 '{DATA_DIRECTORY}' 无效或未设置。")
        logger.error(
            "数据集应该包含以年龄命名的子文件夹，每个子文件夹中包含对应的图像。"
        )
        logger.error("例如：your_dataset_root/25/image1.jpg")
        logger.error("=" * 50)
    else:
        trainer = ViTAgeTrainer(
            data_dir=DATA_DIRECTORY,
            model_save_path=MODEL_SAVE_PATH,
            num_epochs=NUM_EPOCHS,
            batch_size=BATCH_SIZE,
            learning_rate=LEARNING_RATE,
            num_labels=101,  # 假设年龄范围是0-100岁
        )

        # 开始训练
        trainer.train()

        logger.info("训练流程结束。")

        # --- 训练后进行预测示例 (可选) ---
        # # 假设模型已训练并保存
        # trained_model_path = MODEL_SAVE_PATH
        # if os.path.exists(trained_model_path):
        #     logger.info(f"加载已训练模型 {trained_model_path} 进行预测。")
        #     # 首先，创建一个模型实例，结构要与保存时一致
        #     predictor_model = ViTForImageClassification.from_pretrained(
        #         "google/vit-base-patch16-224-in21k", # 或者您训练时使用的基础模型
        #         num_labels=101, # 确保与训练时一致
        #         ignore_mismatched_sizes=True
        #     )
        #     # 加载状态字典
        #     predictor_model.load_state_dict(torch.load(trained_model_path, map_location=torch.device('cuda' if torch.cuda.is_available() else 'cpu')))
        #     predictor_model.eval() # 设置为评估模式

        #     # 创建一个临时的 ViTAgeTrainer 实例用于预测，或者直接使用模型和处理器
        #     # 为了简单起见，这里我们重新利用 ViTAgeTrainer 的 predict 方法，
        #     # 但需要确保其内部模型是加载了权重的模型。
        #     temp_predictor_for_inference = ViTAgeTrainer(
        #          model_name_or_path="google/vit-base-patch16-224-in21k", # 基础模型名
        #          num_labels=101,
        #          model_save_path=trained_model_path # 仅用于日志或元数据，实际模型已加载
        #     )
        #     temp_predictor_for_inference.model = predictor_model.to(temp_predictor_for_inference.device)
        #     temp_predictor_for_inference.processor = ViTImageProcessor.from_pretrained(
        #         "google/vit-base-patch16-224-in21k" # 确保处理器与模型对应
        #     )

        #     # 示例图像路径 (请替换为您的测试图像)
        #     sample_image_path = "path/to/your/sample_image.jpg" # <--- 修改这里
        #     if sample_image_path == "path/to/your/sample_image.jpg" or not os.path.exists(sample_image_path):
        #         logger.warning(f"示例预测图像路径 '{sample_image_path}' 无效或未设置。跳过预测示例。")
        #     else:
        #         predicted_age = temp_predictor_for_inference.predict(sample_image_path)
        #         if predicted_age != -1:
        #             logger.info(f"对图像 {sample_image_path} 的预测年龄是: {predicted_age}")
        # else:
        #     logger.warning(f"未找到已训练的模型 {trained_model_path}，无法进行预测示例。")
