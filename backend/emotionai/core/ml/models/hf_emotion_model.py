"""
使用Hugging Face预训练模型的情绪识别器
"""

import logging
import os
import platform

import cv2
import numpy as np
import torch
from transformers import AutoFeatureExtractor

logger = logging.getLogger(__name__)


class HFEmotionRecognizer:
    """使用Hugging Face预训练模型的情绪识别器"""

    def __init__(
        self,
        model_name="ElenaRyumina/face_emotion_recognition",
        device=None,
        use_local=True,
    ):
        """
        初始化情绪识别器
        :param model_name: 模型名称
        :param device: 设备
        :param use_local: 是否使用本地模型
        """
        self.model_name = model_name
        # 设置设备
        if device is not None:
            self.device = device
        else:
            # 检查是否可以使用MPS (Apple Silicon GPU)
            if (
                platform.system() == "Darwin"
                and hasattr(torch.backends, "mps")
                and torch.backends.mps.is_available()
            ):
                self.device = torch.device("mps")
                logger.info("检测到Apple Silicon芯片，使用MPS加速")
            elif torch.cuda.is_available():
                self.device = torch.device("cuda")
                logger.info("检测到NVIDIA GPU，使用CUDA加速")
            else:
                self.device = torch.device("cpu")
                logger.info("未检测到GPU，使用CPU模式")

        logger.info(f"加载Hugging Face情绪识别模型: {model_name}")

        # 检查本地模型路径
        local_model_path = None
        if use_local:
            # 构建本地模型路径
            project_root = os.path.abspath(
                os.path.join(os.path.dirname(__file__), "../../../..")
            )

            # 检查模型路径
            local_model_name = model_name.replace("/", "_")
            # 只检查两个主要位置：当前项目目录和相对路径
            model_locations = [
                # 项目目录中的模型
                os.path.join(
                    project_root,
                    "backend/emotionai/core/ml/models/huggingface",
                    local_model_name,
                ),
                # 相对路径
                os.path.join(
                    os.path.dirname(os.path.dirname(__file__)),
                    "models",
                    "huggingface",
                    local_model_name,
                ),
            ]

            # 选择第一个存在的路径
            local_model_path = None
            for path in model_locations:
                if os.path.exists(path):
                    local_model_path = path
                    logger.info(f"使用模型路径: {path}")
                    break

            if os.path.exists(local_model_path):
                logger.info(f"使用本地模型: {local_model_path}")
            else:
                logger.warning(f"未找到本地模型: {local_model_path}")
                logger.warning(
                    "请运行 python backend/download_models.py --model huggingface 下载模型"
                )
                local_model_path = None

        # 加载模型
        if model_name == "ElenaRyumina/face_emotion_recognition":
            # 加载PyTorch模型
            if local_model_path and os.path.exists(local_model_path):
                # 查找静态模型文件
                static_model_file = None
                for file in os.listdir(local_model_path):
                    if file.endswith(".pt") and "static" in file:
                        static_model_file = os.path.join(local_model_path, file)
                        break

                if static_model_file:
                    logger.info(f"加载静态PyTorch模型: {static_model_file}")
                    try:
                        # 使用异常处理加载模型
                        try:
                            # 使用安全的方式加载模型，只加载状态字典
                            # 创建模型实例
                            from transformers import AutoModelForImageClassification

                            self.model = (
                                AutoModelForImageClassification.from_pretrained(
                                    "Rajaram1996/FacialEmoRecog", local_files_only=True
                                )
                            )
                            # 加载状态字典
                            state_dict = torch.load(  # nosec B614
                                static_model_file, map_location=self.device
                            )
                            self.model.load_state_dict(state_dict)
                            self.model.eval()
                            logger.info(f"模型加载成功: {static_model_file}")
                        except Exception as e:
                            logger.error(f"加载模型时出错: {str(e)}")
                            # 尝试使用torch.jit.load加载
                            try:
                                logger.info(
                                    f"尝试使用torch.jit.load加载模型: {static_model_file}"
                                )
                                # 使用安全的方式加载 JIT 模型
                                # 验证模型文件路径
                                if not os.path.normpath(static_model_file).startswith(
                                    os.path.normpath(os.path.dirname(__file__))
                                ):
                                    raise ValueError(
                                        f"不允许从非项目目录加载模型: {static_model_file}"
                                    )

                                self.model = torch.jit.load(  # nosec B614
                                    static_model_file, map_location=self.device
                                )
                                self.model.eval()
                                logger.info(
                                    f"使用torch.jit.load模型加载成功: {static_model_file}"
                                )
                            except Exception as e2:
                                logger.error(
                                    f"使用torch.jit.load加载模型时出错: {str(e2)}"
                                )
                                raise

                        # 创建简单的特征提取器
                        self.feature_extractor = None

                        # 创建虚拟的id2label和label2id字典
                        self.id2label = {
                            0: "Anger",
                            1: "Disgust",
                            2: "Fear",
                            3: "Happiness",
                            4: "Neutral",
                            5: "Sadness",
                            6: "Surprise",
                        }
                        self.label2id = {v: k for k, v in self.id2label.items()}

                        logger.info(
                            f"Hugging Face情绪识别模型加载完成，标签: {self.id2label}"
                        )
                        return
                    except Exception as e:
                        logger.error(f"加载模型时出错: {str(e)}")
                        raise
                else:
                    logger.error(f"未找到静态模型文件在: {local_model_path}")
                    raise FileNotFoundError(f"未找到静态模型文件在: {local_model_path}")
            else:
                logger.error(f"未找到本地模型路径: {local_model_path}")
                raise FileNotFoundError(f"未找到本地模型路径: {local_model_path}")
        else:
            # 加载标准Transformers模型
            if local_model_path:
                self.feature_extractor = AutoFeatureExtractor.from_pretrained(
                    local_model_path
                )
                self.model = AutoModelForImageClassification.from_pretrained(
                    local_model_path
                )
            else:
                self.feature_extractor = AutoFeatureExtractor.from_pretrained(
                    model_name
                )
                self.model = AutoModelForImageClassification.from_pretrained(model_name)

            self.model = self.model.to(self.device)
            self.model.eval()

        # 获取标签映射
        self.id2label = self.model.config.id2label
        self.label2id = self.model.config.label2id

        logger.info(f"Hugging Face情绪识别模型加载完成，标签: {self.id2label}")

    def predict_emotions(self, image, logits=False):
        """
        预测情绪
        :param image: 输入图像（numpy数组）
        :param logits: 是否返回logits
        :return: 情绪标签和概率
        """
        # 确保图像是RGB格式
        if len(image.shape) == 2:  # 灰度图
            image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        elif image.shape[2] == 4:  # RGBA
            image = cv2.cvtColor(image, cv2.COLOR_RGBA2RGB)
        elif image.shape[2] == 3 and image.dtype == np.uint8:
            # 检查是否为BGR格式（OpenCV默认）
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        if self.model_name == "ElenaRyumina/face_emotion_recognition":
            # 处理PyTorch模型
            # 转换为PIL图像
            from PIL import Image

            pil_image = Image.fromarray(image)

            # 调整图像大小为224x224
            from torchvision import transforms

            transform = transforms.Compose(
                [
                    transforms.Resize((224, 224)),
                    transforms.ToTensor(),
                    transforms.Normalize(
                        mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]
                    ),
                ]
            )

            # 将图像转换为模型输入
            img_tensor = transform(pil_image).unsqueeze(0).to(self.device)

            # 进行推理
            with torch.no_grad():
                outputs = self.model(img_tensor)

            # 获取预测结果
            logits_tensor = (
                outputs if isinstance(outputs, torch.Tensor) else outputs.logits
            )
            # 模型的类别映射
            class_mapping = {
                0: "Anger",
                1: "Disgust",
                2: "Fear",
                3: "Happiness",
                4: "Neutral",
                5: "Sadness",
                6: "Surprise",
            }

            # 获取最可能的情绪
            predicted_class_id = logits_tensor.argmax(-1).item()
            predicted_label = class_mapping[predicted_class_id]

            # 转换为标准化的情绪标签
            emotion_labels = get_emotion_labels(self.model_name)
            if predicted_label in emotion_labels:
                predicted_label = emotion_labels[predicted_label]

            # 获取概率
            probs = (
                logits_tensor.cpu().numpy()[0]
                if logits
                else torch.softmax(logits_tensor, dim=1).cpu().numpy()[0]
            )

            # 返回结果
            return predicted_label, probs
        else:
            # 处理标准Transformers模型
            # 提取特征
            inputs = self.feature_extractor(images=image, return_tensors="pt")
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # 预测
            with torch.no_grad():
                outputs = self.model(**inputs)

            # 获取预测结果
            logits_tensor = outputs.logits
            probs = (
                logits_tensor.cpu().numpy()[0]
                if logits
                else torch.softmax(logits_tensor, dim=1).cpu().numpy()[0]
            )
            # 获取最可能的情绪
            predicted_class_id = logits_tensor.argmax(-1).item()
            predicted_label = self.id2label[predicted_class_id]

            return predicted_label, probs

    def predict_multi_emotions(self, images, logits=False):
        """
        批量预测情绪
        :param images: 输入图像列表
        :param logits: 是否返回logits
        :return: 情绪标签列表和概率列表
        """
        results = []
        scores = []

        for image in images:
            emotion, probs = self.predict_emotions(image, logits)
            results.append(emotion)
            scores.append(probs)

        return results, scores


# 预定义的情绪标签映射
EMOTION_LABELS_MAPPING = {
    # ElenaRyumina/face_emotion_recognition模型的标签映射
    "ElenaRyumina/face_emotion_recognition": {
        "Anger": "anger",
        "Disgust": "disgust",
        "Fear": "fear",
        "Happiness": "happiness",
        "Neutral": "neutral",
        "Sadness": "sadness",
        "Surprise": "surprise",
    },
    # Rajaram1996/FacialEmoRecog模型的标签映射
    "Rajaram1996/FacialEmoRecog": {
        "angry": "anger",
        "disgust": "disgust",
        "fear": "fear",
        "happy": "happiness",
        "neutral": "neutral",
        "sad": "sadness",
        "surprise": "surprise",
    },
    # joeddav/distilbert-base-uncased-go-emotions-student模型的标签映射
    "joeddav/distilbert-base-uncased-go-emotions-student": {
        "admiration": "admiration",
        "amusement": "amusement",
        "anger": "anger",
        "annoyance": "annoyance",
        "approval": "approval",
        "caring": "caring",
        "confusion": "confusion",
        "curiosity": "curiosity",
        "desire": "desire",
        "disappointment": "disappointment",
        "disapproval": "disapproval",
        "disgust": "disgust",
        "embarrassment": "embarrassment",
        "excitement": "excitement",
        "fear": "fear",
        "gratitude": "gratitude",
        "grief": "grief",
        "joy": "joy",
        "love": "love",
        "nervousness": "nervousness",
        "optimism": "optimism",
        "pride": "pride",
        "realization": "realization",
        "relief": "relief",
        "remorse": "remorse",
        "sadness": "sadness",
        "surprise": "surprise",
        "neutral": "neutral",
    },
}


def get_emotion_recognizer(
    model_name="ElenaRyumina/face_emotion_recognition", device=None, use_local=True
):
    """
    获取情绪识别器
    :param model_name: 模型名称
    :param device: 设备
    :param use_local: 是否使用本地模型
    :return: 情绪识别器
    """
    return HFEmotionRecognizer(
        model_name=model_name, device=device, use_local=use_local
    )


def get_emotion_labels(model_name="ElenaRyumina/face_emotion_recognition"):
    """
    获取情绪标签映射
    :param model_name: 模型名称
    :return: 情绪标签映射
    """
    if model_name in EMOTION_LABELS_MAPPING:
        return EMOTION_LABELS_MAPPING[model_name]
    else:
        # 如果没有预定义的映射，则使用默认映射
        return EMOTION_LABELS_MAPPING["ElenaRyumina/face_emotion_recognition"]
