"""
Vision Transformer Age Estimation Model
基于最新研究的高精度年龄识别模型
"""

import math
from typing import List

import torch
import torch.nn as nn
import torch.nn.functional as F


class MultiHeadAttention(nn.Module):
    """多头注意力机制"""

    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        super().__init__()
        if d_model % num_heads != 0:
            raise ValueError(
                f"d_model ({d_model}) 必须是 num_heads ({num_heads}) 的整数倍"
            )

        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads

        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)

        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.d_k)

    def forward(self, query, key, value, mask=None):
        batch_size = query.size(0)

        # Linear transformations
        Q = (
            self.w_q(query)
            .view(batch_size, -1, self.num_heads, self.d_k)
            .transpose(1, 2)
        )
        K = self.w_k(key).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = (
            self.w_v(value)
            .view(batch_size, -1, self.num_heads, self.d_k)
            .transpose(1, 2)
        )

        # Attention
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale

        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)

        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        context = torch.matmul(attention_weights, V)
        context = (
            context.transpose(1, 2).contiguous().view(batch_size, -1, self.d_model)
        )

        output = self.w_o(context)
        return output, attention_weights


class PatchEmbedding(nn.Module):
    """图像块嵌入"""

    def __init__(
        self,
        img_size: int = 224,
        patch_size: int = 16,
        in_channels: int = 3,
        embed_dim: int = 768,
    ):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2

        self.projection = nn.Conv2d(
            in_channels, embed_dim, kernel_size=patch_size, stride=patch_size
        )

    def forward(self, x):
        # x: (B, C, H, W) -> (B, embed_dim, H//patch_size, W//patch_size)
        x = self.projection(x)
        # (B, embed_dim, H//patch_size, W//patch_size) -> (B, num_patches, embed_dim)
        x = x.flatten(2).transpose(1, 2)
        return x


class MultiLevelChannelAttention(nn.Module):
    """多级通道注意力模块 - 基于SwinFace论文"""

    def __init__(self, channels: List[int], reduction: int = 16):
        super().__init__()
        self.channels = channels
        self.num_levels = len(channels)

        # 为每个级别创建通道注意力
        self.channel_attentions = nn.ModuleList()
        for ch in channels:
            self.channel_attentions.append(
                nn.Sequential(
                    nn.AdaptiveAvgPool2d(1),
                    nn.Conv2d(ch, ch // reduction, 1),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(ch // reduction, ch, 1),
                    nn.Sigmoid(),
                )
            )

        # 级别权重学习
        self.level_weights = nn.Parameter(torch.ones(self.num_levels))

    def forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        """
        Args:
            features: 不同级别的特征列表
        Returns:
            融合后的特征
        """
        attended_features = []

        for i, (feat, ca) in enumerate(zip(features, self.channel_attentions)):
            # 应用通道注意力
            attention = ca(feat)
            attended_feat = feat * attention

            # 全局平均池化
            pooled_feat = F.adaptive_avg_pool2d(attended_feat, 1).flatten(1)
            attended_features.append(pooled_feat)

        # 加权融合
        weights = F.softmax(self.level_weights, dim=0)
        fused_feature = sum(w * feat for w, feat in zip(weights, attended_features))

        return fused_feature


class TransformerBlock(nn.Module):
    """Transformer块"""

    def __init__(
        self, d_model: int, num_heads: int, mlp_ratio: float = 4.0, dropout: float = 0.1
    ):
        super().__init__()
        self.norm1 = nn.LayerNorm(d_model)
        self.attention = MultiHeadAttention(d_model, num_heads, dropout)
        self.norm2 = nn.LayerNorm(d_model)

        mlp_hidden_dim = int(d_model * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(d_model, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(mlp_hidden_dim, d_model),
            nn.Dropout(dropout),
        )

    def forward(self, x):
        # Self-attention
        norm_x = self.norm1(x)
        attn_output, attn_weights = self.attention(norm_x, norm_x, norm_x)
        x = x + attn_output

        # MLP
        norm_x = self.norm2(x)
        mlp_output = self.mlp(norm_x)
        x = x + mlp_output

        return x, attn_weights


class GlobalLocalTransformer(nn.Module):
    """全局-局部Transformer - 基于最新研究"""

    def __init__(
        self,
        img_size: int = 224,
        patch_size: int = 16,
        in_channels: int = 3,
        embed_dim: int = 768,
        num_layers: int = 12,
        num_heads: int = 12,
        mlp_ratio: float = 4.0,
        dropout: float = 0.1,
    ):
        super().__init__()

        # Patch embedding
        self.patch_embed = PatchEmbedding(img_size, patch_size, in_channels, embed_dim)
        num_patches = self.patch_embed.num_patches

        # Position embedding
        self.pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, embed_dim))
        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))

        # Transformer blocks
        self.blocks = nn.ModuleList(
            [
                TransformerBlock(embed_dim, num_heads, mlp_ratio, dropout)
                for _ in range(num_layers)
            ]
        )

        self.norm = nn.LayerNorm(embed_dim)
        self.dropout = nn.Dropout(dropout)

        # 初始化权重
        nn.init.trunc_normal_(self.pos_embed, std=0.02)
        nn.init.trunc_normal_(self.cls_token, std=0.02)

    def forward(self, x):
        B = x.shape[0]

        # Patch embedding
        x = self.patch_embed(x)  # (B, num_patches, embed_dim)

        # Add cls token
        cls_tokens = self.cls_token.expand(B, -1, -1)
        x = torch.cat([cls_tokens, x], dim=1)

        # Add position embedding
        x = x + self.pos_embed
        x = self.dropout(x)

        # Transformer blocks
        attention_weights = []
        for block in self.blocks:
            x, attn_weights = block(x)
            attention_weights.append(attn_weights)

        x = self.norm(x)

        # Return cls token and all patch tokens
        return x[:, 0], x[:, 1:], attention_weights


class AgeRegressionHead(nn.Module):
    """年龄回归头"""

    def __init__(self, embed_dim: int, num_classes: int = 1, dropout: float = 0.1):
        super().__init__()
        self.head = nn.Sequential(
            nn.LayerNorm(embed_dim),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 2, num_classes),
        )

    def forward(self, x):
        return self.head(x)


class AgeClassificationHead(nn.Module):
    """年龄分类头（年龄组）"""

    def __init__(self, embed_dim: int, num_age_groups: int = 8, dropout: float = 0.1):
        super().__init__()
        self.head = nn.Sequential(
            nn.LayerNorm(embed_dim),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 2, num_age_groups),
        )

    def forward(self, x):
        return self.head(x)


class VisionTransformerAge(nn.Module):
    """基于Vision Transformer的年龄识别模型"""

    def __init__(
        self,
        img_size: int = 224,
        patch_size: int = 16,
        in_channels: int = 3,
        embed_dim: int = 768,
        num_layers: int = 12,
        num_heads: int = 12,
        mlp_ratio: float = 4.0,
        dropout: float = 0.1,
        num_age_groups: int = 8,
        use_multi_task: bool = True,
    ):
        super().__init__()

        self.use_multi_task = use_multi_task

        # Backbone
        self.backbone = GlobalLocalTransformer(
            img_size=img_size,
            patch_size=patch_size,
            in_channels=in_channels,
            embed_dim=embed_dim,
            num_layers=num_layers,
            num_heads=num_heads,
            mlp_ratio=mlp_ratio,
            dropout=dropout,
        )

        # Task heads
        self.age_regression_head = AgeRegressionHead(embed_dim, 1, dropout)

        if use_multi_task:
            self.age_classification_head = AgeClassificationHead(
                embed_dim, num_age_groups, dropout
            )
            self.gender_head = nn.Sequential(
                nn.LayerNorm(embed_dim),
                nn.Dropout(dropout),
                nn.Linear(embed_dim, 2),  # 性别分类
            )

    def forward(self, x):
        # Extract features
        cls_token, patch_tokens, attention_weights = self.backbone(x)

        # Age regression
        age_pred = self.age_regression_head(cls_token)

        outputs = {"age": age_pred, "attention_weights": attention_weights}

        if self.use_multi_task:
            # Age group classification
            age_group_pred = self.age_classification_head(cls_token)
            gender_pred = self.gender_head(cls_token)

            outputs.update({"age_group": age_group_pred, "gender": gender_pred})

        return outputs


class FocalMSELoss(nn.Module):
    """Focal MSE Loss - 专注于困难样本"""

    def __init__(self, alpha: float = 2.0, beta: float = 0.5):
        super().__init__()
        self.alpha = alpha
        self.beta = beta

    def forward(self, pred, target):
        mse = F.mse_loss(pred, target, reduction="none")
        focal_weight = torch.pow(torch.abs(pred - target), self.alpha)
        focal_mse = focal_weight * mse
        return torch.mean(focal_mse)


class AgeEstimationLoss(nn.Module):
    """多任务年龄估计损失函数"""

    def __init__(
        self,
        regression_weight: float = 1.0,
        classification_weight: float = 0.5,
        gender_weight: float = 0.3,
        use_focal: bool = True,
    ):
        super().__init__()
        self.regression_weight = regression_weight
        self.classification_weight = classification_weight
        self.gender_weight = gender_weight

        if use_focal:
            self.regression_loss = FocalMSELoss()
        else:
            self.regression_loss = nn.MSELoss()

        self.classification_loss = nn.CrossEntropyLoss()
        self.gender_loss = nn.CrossEntropyLoss()

    def forward(self, outputs, targets):
        """
        Args:
            outputs: 模型输出字典
            targets: 目标字典，包含 'age', 'age_group', 'gender'
        """
        total_loss = 0
        losses = {}

        # Age regression loss
        if "age" in outputs and "age" in targets:
            reg_loss = self.regression_loss(outputs["age"], targets["age"])
            total_loss += self.regression_weight * reg_loss
            losses["regression"] = reg_loss

        # Age group classification loss
        if "age_group" in outputs and "age_group" in targets:
            cls_loss = self.classification_loss(
                outputs["age_group"], targets["age_group"]
            )
            total_loss += self.classification_weight * cls_loss
            losses["classification"] = cls_loss

        # Gender classification loss
        if "gender" in outputs and "gender" in targets:
            gender_loss = self.gender_loss(outputs["gender"], targets["gender"])
            total_loss += self.gender_weight * gender_loss
            losses["gender"] = gender_loss

        losses["total"] = total_loss
        return total_loss, losses


def create_age_groups(age: float) -> int:
    """将年龄转换为年龄组"""
    if age < 3:
        return 0  # 婴儿
    elif age < 12:
        return 1  # 儿童
    elif age < 18:
        return 2  # 青少年
    elif age < 25:
        return 3  # 青年早期
    elif age < 35:
        return 4  # 青年
    elif age < 50:
        return 5  # 中年早期
    elif age < 65:
        return 6  # 中年
    else:
        return 7  # 老年


# 模型工厂函数
def create_vit_age_model(model_size: str = "base", **kwargs):
    """创建不同大小的ViT年龄识别模型"""

    configs = {
        "tiny": {"embed_dim": 192, "num_layers": 12, "num_heads": 3, "mlp_ratio": 4.0},
        "small": {"embed_dim": 384, "num_layers": 12, "num_heads": 6, "mlp_ratio": 4.0},
        "base": {"embed_dim": 768, "num_layers": 12, "num_heads": 12, "mlp_ratio": 4.0},
        "large": {
            "embed_dim": 1024,
            "num_layers": 24,
            "num_heads": 16,
            "mlp_ratio": 4.0,
        },
    }

    config = configs.get(model_size, configs["base"])
    config.update(kwargs)

    return VisionTransformerAge(**config)


if __name__ == "__main__":
    # 测试模型
    model = create_vit_age_model("base")
    x = torch.randn(2, 3, 224, 224)

    with torch.no_grad():
        outputs = model(x)
        print("Model outputs:")
        for key, value in outputs.items():
            if key != "attention_weights":
                print(f"{key}: {value.shape}")
            else:
                print(f"{key}: {len(value)} layers")
