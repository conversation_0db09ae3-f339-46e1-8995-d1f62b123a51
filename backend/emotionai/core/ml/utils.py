"""
PyTorch 模型加载和处理的工具函数
"""

import logging
import pickle  # nosec B403 - 必须使用 pickle 来加载 PyTorch 模型，但我们已经添加了安全参数

import torch

logger = logging.getLogger(__name__)


def safe_torch_load(file_path, map_location=None):
    """
    安全地加载 PyTorch 模型，避免反序列化漏洞

    Args:
        file_path: 模型文件路径
        map_location: 设备映射位置

    Returns:
        加载的模型状态字典
    """
    try:
        # 尝试使用更安全的方式加载模型
        try:
            # 首先尝试使用推荐的安全参数
            return torch.load(  # nosec B614
                file_path,
                map_location=map_location,
                pickle_module=pickle,
            )
        except TypeError:
            # 如果出现类型错误，使用基本参数
            logger.warning(f"使用基本参数加载模型文件 {file_path}")
            return torch.load(file_path, map_location=map_location)  # nosec B614
    except Exception as e:
        logger.error(f"加载模型文件 {file_path} 时出错: {str(e)}")
        raise
