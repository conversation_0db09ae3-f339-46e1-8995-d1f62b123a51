"""
依赖项导出模块
这个模块重新导出了core.deps中的依赖项，以便在其他模块中使用
"""

# 导出管理员依赖项
from emotionai.core.deps.admin import get_admin_service

# 导出认证依赖项
from emotionai.core.deps.auth import (
    get_current_active_superuser,
    get_current_active_user,
    get_current_admin_user,
    get_current_user,
)

# 导出数据库依赖项
from emotionai.core.deps.database import get_async_db as get_db

__all__ = [
    "get_db",
    "get_current_user",
    "get_current_active_user",
    "get_current_active_superuser",
    "get_current_admin_user",
    "get_admin_service",
]
