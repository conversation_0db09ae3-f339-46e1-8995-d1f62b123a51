"""文件上传大小限制中间件"""

from fastapi import HTTPException, Request, status
from starlette.middleware.base import BaseHTTPMiddleware

from emotionai.core.config import get_settings
from emotionai.core.loggers import logger

settings = get_settings()


class UploadLimitMiddleware(BaseHTTPMiddleware):
    """文件上传大小限制中间件"""

    async def dispatch(self, request: Request, call_next):
        """处理请求并检查内容长度"""
        # 检查是否是文件上传请求
        content_type = request.headers.get("content-type", "")
        is_multipart = "multipart/form-data" in content_type

        if is_multipart:
            # 获取内容长度
            content_length = request.headers.get("content-length")
            if content_length:
                content_length = int(content_length)
                # 记录上传文件大小
                logger.info(
                    "文件上传请求 | %s %s | 大小: %.2f MB",
                    request.method,
                    request.url.path,
                    content_length / 1024 / 1024,
                )

                # 检查是否超过最大上传大小
                if content_length > settings.MAX_UPLOAD_SIZE:
                    logger.warning(
                        "文件上传大小超限 | %s %s | 大小: %.2f MB, 限制: %.2f MB",
                        request.method,
                        request.url.path,
                        content_length / 1024 / 1024,
                        settings.MAX_UPLOAD_SIZE / 1024 / 1024,
                    )
                    # 返回413错误
                    raise HTTPException(
                        status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                        detail=f"文件大小超过限制，最大允许 {settings.MAX_UPLOAD_SIZE / 1024 / 1024:.1f}MB",
                    )
                elif content_length > 10 * 1024 * 1024:  # 如果大于10MB，记录警告
                    logger.warning(
                        "大文件上传 | %s %s | 大小: %.2f MB",
                        request.method,
                        request.url.path,
                        content_length / 1024 / 1024,
                    )

        # 继续处理请求
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            # 记录异常
            logger.error(
                "处理上传请求时出错 | %s %s | 错误: %s",
                request.method,
                request.url.path,
                str(e),
                exc_info=True,
            )
            # 重新抛出异常
            raise
