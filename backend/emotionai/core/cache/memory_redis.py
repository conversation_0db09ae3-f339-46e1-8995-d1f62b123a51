"""
内存模拟Redis客户端
用于在Redis连接失败时提供备用方案
"""

import asyncio
import logging
import time
from datetime import timedelta
from typing import Any, Optional, Union

# 使用内置类型代替已弃用的typing类型
# Dict -> dict, List -> list

logger = logging.getLogger(__name__)


class MemoryRedisClient:
    """内存模拟Redis客户端，用于在Redis连接失败时提供备用方案"""

    def __init__(self):
        """初始化内存模拟Redis客户端"""
        self._data: dict[str, Any] = {}
        self._expires: dict[str, float] = {}
        logger.info("创建内存模拟Redis客户端")

    async def ping(self) -> bool:
        """测试连接"""
        return True

    async def get(self, key: str) -> Optional[str]:
        """获取键值"""
        self._check_expires(key)
        value = self._data.get(key)
        if value is not None:
            return value
        return None

    async def set(self, key: str, value: Any) -> bool:
        """设置键值"""
        self._data[key] = value
        return True

    async def setex(self, key: str, seconds: Union[int, timedelta], value: Any) -> bool:
        """设置键值并设置过期时间"""
        self._data[key] = value
        if isinstance(seconds, timedelta):
            seconds = seconds.total_seconds()
        self._expires[key] = time.time() + seconds
        return True

    async def delete(self, key: str) -> int:
        """删除键值"""
        if key in self._data:
            del self._data[key]
            if key in self._expires:
                del self._expires[key]
            return 1
        return 0

    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        self._check_expires(key)
        return key in self._data

    async def keys(self, pattern: str) -> list[str]:
        """获取匹配的键"""
        # 简单实现，只支持*通配符
        if pattern == "*":
            return list(self._data.keys())
        elif pattern.endswith("*"):
            prefix = pattern[:-1]
            return [k for k in self._data.keys() if k.startswith(prefix)]
        return []

    async def hset(self, key: str, mapping: dict[str, Any]) -> int:
        """设置哈希表字段"""
        if key not in self._data:
            self._data[key] = {}
        if not isinstance(self._data[key], dict):
            self._data[key] = {}
        self._data[key].update(mapping)
        return len(mapping)

    async def hget(self, key: str, field: str) -> Optional[str]:
        """获取哈希表字段"""
        self._check_expires(key)
        if key in self._data and isinstance(self._data[key], dict):
            return self._data[key].get(field)
        return None

    async def hgetall(self, key: str) -> dict[str, Any]:
        """获取哈希表所有字段"""
        self._check_expires(key)
        if key in self._data and isinstance(self._data[key], dict):
            return self._data[key]
        return {}

    async def expire(self, key: str, seconds: Union[int, timedelta]) -> bool:
        """设置过期时间"""
        if key not in self._data:
            return False
        if isinstance(seconds, timedelta):
            seconds = seconds.total_seconds()
        self._expires[key] = time.time() + seconds
        return True

    async def ttl(self, key: str) -> int:
        """获取过期时间"""
        self._check_expires(key)
        if key not in self._data:
            return -2
        if key not in self._expires:
            return -1
        ttl = int(self._expires[key] - time.time())
        return ttl if ttl > 0 else -2

    async def select(self, _db: int) -> bool:
        """选择数据库"""
        # 内存模拟客户端不支持多数据库，忽略db参数
        return True

    def _check_expires(self, key: str) -> None:
        """检查并清理过期键"""
        if key in self._expires and time.time() > self._expires[key]:
            del self._data[key]
            del self._expires[key]

    # 定期清理过期键的任务
    async def _cleanup_task(self) -> None:
        """定期清理过期键"""
        while True:
            # 复制键列表，避免在迭代过程中修改字典
            keys = list(self._expires.keys())
            for key in keys:
                self._check_expires(key)
            # 每60秒清理一次
            await asyncio.sleep(60)

    def start_cleanup(self) -> None:
        """启动清理任务"""
        asyncio.create_task(self._cleanup_task())
