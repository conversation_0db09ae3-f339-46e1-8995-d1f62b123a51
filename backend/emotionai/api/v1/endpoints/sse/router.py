"""
SSE (Server-Sent Events) 路由
用于实时通知客户端
"""

import asyncio
import json
import logging
from datetime import datetime
from uuid import UUID, uuid4

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session

from emotionai.core.config import settings
from emotionai.core.deps.database import get_db
from emotionai.core.security.jwt import verify_token
from emotionai.models.user import User
from emotionai.schemas.auth.token import TokenPayload

# 获取日志记录器
logger = logging.getLogger(__name__)
# 防止日志重复
logger.propagate = False

router = APIRouter()

# 存储所有活跃的 SSE 连接
# 格式: {user_id: {connection_id: queue}}
active_connections: dict[str, dict[str, asyncio.Queue]] = {}

# 配置日志处理器
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.WARNING)  # 将日志级别设置为WARNING，减少不必要的日志输出


async def send_delayed_message(
    queue: asyncio.Queue, message: dict, delay_seconds: float = 0.5
):
    """
    延迟发送消息
    :param queue: 消息队列
    :param message: 消息内容
    :param delay_seconds: 延迟时间（秒）
    """
    try:
        # 先发送一个心跳消息，确保连接活跃
        ping_message = {
            "event": "ping",
            "data": json.dumps(
                {"timestamp": datetime.now().timestamp(), "type": "pre-logout"}
            ),
        }
        await queue.put(ping_message)

        # 等待指定时间
        await asyncio.sleep(delay_seconds)

        # 发送实际消息
        await queue.put(message)

        # 再发送一个心跳消息，确保消息被接收
        post_message = {
            "event": "ping",
            "data": json.dumps(
                {"timestamp": datetime.now().timestamp(), "type": "post-logout"}
            ),
        }
        await queue.put(post_message)

        logger.info(f"已发送延迟消息: {message.get('event', 'unknown')}")
    except Exception as e:
        logger.error(f"发送延迟消息时出错: {e}")


async def sse_stream(
    request: Request, user_id: str, connection_id: str, queue: asyncio.Queue
):
    """
    SSE 流处理函数
    :param request: 请求对象
    :param user_id: 用户ID
    :param connection_id: 连接ID
    :param queue: 消息队列
    :return: 事件流
    """
    try:
        # 发送初始连接成功消息
        yield "event: connected\n"
        yield f'data: {{"status": "connected", "connection_id": "{connection_id}"}}\n\n'

        # 立即发送一个心跳，确保连接保持活跃
        yield "event: ping\n"
        timestamp = datetime.now().timestamp()
        yield f'data: {{"timestamp": {timestamp}, "message": "initial ping"}}\n\n'

        # 发送一个注释行，确保浏览器不会缓存
        yield ": initial comment\n\n"

        # 持续监听消息队列
        while True:
            if await request.is_disconnected():
                logger.info(f"客户端断开连接: 用户 {user_id}, 连接 {connection_id}")
                # 清理连接
                if user_id in active_connections:
                    if connection_id in active_connections[user_id]:
                        # 确保只删除与当前用户ID匹配的连接
                        del active_connections[user_id][connection_id]
                        logger.info(f"已清理连接: 用户 {user_id}, 连接 {connection_id}")

                        # 如果用户没有其他连接，删除用户条目
                        if not active_connections[user_id]:
                            del active_connections[user_id]
                            logger.info(f"用户 {user_id} 没有活跃连接，删除用户条目")
                    else:
                        logger.warning(
                            f"断开连接时，连接 {connection_id} 不存在于用户 {user_id} 的连接列表中"
                        )
                else:
                    # 检查是否有其他用户拥有这个连接ID
                    connection_found = False
                    for other_user_id, connections in active_connections.items():
                        if connection_id in connections:
                            logger.warning(
                                f"断开连接时，发现连接 {connection_id} 属于用户 {other_user_id}，而不是 {user_id}"
                            )

                            # 如果当前用户ID是占位符，而其他用户ID也是占位符，则可以删除连接
                            if (
                                user_id == "11111111-1111-1111-1111-111111111111"
                                and other_user_id
                                == "11111111-1111-1111-1111-111111111111"
                            ):
                                logger.info(
                                    f"断开连接时，两个用户ID都是占位符，删除连接 {connection_id}"
                                )
                                del active_connections[other_user_id][connection_id]
                                if not active_connections[other_user_id]:
                                    del active_connections[other_user_id]
                                    logger.info(
                                        f"用户 {other_user_id} 没有活跃连接，删除用户条目"
                                    )

                            connection_found = True
                            break

                    if not connection_found:
                        logger.warning(
                            f"断开连接时，用户 {user_id} 不存在于活跃连接字典中"
                        )
                break

            # 等待消息，设置超时以便定期检查连接状态
            try:
                # 使用更短的超时时间，确保心跳更频繁
                message = await asyncio.wait_for(
                    queue.get(), timeout=1.0
                )  # 缩短超时时间到 1 秒

                if message == "close":
                    logger.info(f"收到关闭消息: 用户 {user_id}, 连接 {connection_id}")
                    break

                # 发送消息
                logger.info(
                    f"发送消息: 用户 {user_id}, 连接 {connection_id}, 事件 {message.get('event', 'message')}"
                )
                yield f"event: {message.get('event', 'message')}\n"
                yield f"data: {message.get('data', '{}')}\n\n"

                # 每次发送消息后都发送一个心跳，确保连接保持活跃
                yield "event: ping\n"
                timestamp = datetime.now().timestamp()
                yield f'data: {{"timestamp": {timestamp}, "type": "post-message"}}\n\n'
            except asyncio.TimeoutError:
                # 发送心跳消息保持连接
                logger.debug(f"发送心跳: 用户 {user_id}, 连接 {connection_id}")
                yield "event: ping\n"
                timestamp = datetime.now().timestamp()
                yield f'data: {{"timestamp": {timestamp}, "type": "heartbeat"}}\n\n'  # 添加时间戳和类型，避免浏览器缓存

                # 发送心跳后立即发送一个注释行，确保浏览器不会缓存
                yield ": keepalive comment\n\n"
    except Exception as e:
        logger.error(f"SSE 流处理出错: {e}")
        # 即使出错也尝试发送一个错误消息
        yield "event: error\n"
        yield f'data: {{"message": "Internal server error", "error": "{str(e)}"}}\n\n'
    finally:
        # 清理连接
        logger.info(f"清理连接: 用户 {user_id}, 连接 {connection_id}")

        # 检查连接是否存在于活跃连接字典中
        if user_id in active_connections:
            if connection_id in active_connections[user_id]:
                # 确保只删除与当前用户ID匹配的连接
                del active_connections[user_id][connection_id]
                logger.info(f"已删除连接: 用户 {user_id}, 连接 {connection_id}")

                # 如果用户没有其他连接，删除用户条目
                if not active_connections[user_id]:
                    del active_connections[user_id]
                    logger.info(f"用户 {user_id} 没有活跃连接，删除用户条目")
            else:
                logger.warning(
                    f"连接 {connection_id} 不存在于用户 {user_id} 的连接列表中"
                )
        else:
            # 检查是否有其他用户拥有这个连接ID（可能是由于设备ID重用）
            connection_found = False
            for other_user_id, connections in active_connections.items():
                if connection_id in connections:
                    logger.warning(
                        f"发现连接 {connection_id} 属于用户 {other_user_id}，而不是 {user_id}"
                    )

                    # 如果当前用户ID是占位符，而其他用户ID也是占位符，则可以删除连接
                    if (
                        user_id == "11111111-1111-1111-1111-111111111111"
                        and other_user_id == "11111111-1111-1111-1111-111111111111"
                    ):
                        logger.info(f"两个用户ID都是占位符，删除连接 {connection_id}")
                        del active_connections[other_user_id][connection_id]
                        if not active_connections[other_user_id]:
                            del active_connections[other_user_id]
                            logger.info(
                                f"用户 {other_user_id} 没有活跃连接，删除用户条目"
                            )
                    else:
                        # 不要删除其他用户的连接
                        logger.info("不删除其他用户的连接")

                    connection_found = True
                    return

            logger.warning(f"用户 {user_id} 不存在于活跃连接字典中")


@router.get("/connect")
async def connect(
    request: Request,
    token: str = Query(None),
    device_id: str = Query(None),
    db: Session = Depends(get_db),
):
    """
    建立 SSE 连接
    :param request: 请求对象
    :param token: JWT 令牌（通过查询参数传递）
    :param device_id: 设备ID（通过查询参数传递）
    :param db: 数据库会话
    :return: 事件流
    """
    # 验证令牌
    if not token:
        logger.warning("没有提供令牌，拒绝 SSE 连接")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供有效的认证令牌",
        )

    try:
        # 验证令牌
        payload = verify_token(token, settings.SECRET_KEY)
        token_data = TokenPayload(**payload)

        # 获取用户ID
        user_id = token_data.sub
        if not user_id:
            logger.warning("令牌中没有用户ID，拒绝 SSE 连接")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证令牌",
            )

        # 检查令牌是否过期
        exp_time = datetime.fromtimestamp(payload.get("exp", 0))
        now = datetime.now()
        if exp_time < now:
            logger.warning(f"令牌已过期: 过期时间 {exp_time}, 当前时间 {now}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已过期，请重新登录",
            )

        # 获取用户
        # 对于超级管理员ID，我们不检查用户是否存在
        if user_id != "11111111-1111-1111-1111-111111111111":
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                logger.warning(f"用户 {user_id} 不存在，拒绝 SSE 连接")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
                )

        # 验证令牌是否有效
        from emotionai.core.services.auth.session import session_service

        is_admin = payload.get("is_admin", False)

        try:
            # 验证令牌
            valid_token = await session_service.validate_token(token, is_admin)

            if not valid_token:
                # 对于超级管理员或管理员令牌，我们更加宽容
                if user_id == "11111111-1111-1111-1111-111111111111" or is_admin:
                    logger.debug(f"用户 {user_id} 是超级管理员或管理员，创建临时会话")
                    # 创建一个临时会话，以便后续的会话检查能够通过
                    await session_service.create_session(
                        UUID(user_id), device_id, token, is_admin
                    )
                else:
                    # 只有在不是超级管理员或管理员令牌的情况下才抛出异常
                    logger.warning(
                        f"用户 {user_id} 的令牌无效 (session_service.validate_token 返回 false)，拒绝 SSE 连接"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="令牌已失效，请重新登录",
                    )

            # 如果没有提供设备ID，使用随机生成的ID
            if not device_id:
                device_id = str(uuid4())
                logger.info(
                    f"用户 {user_id} 没有提供设备ID，使用随机生成的ID: {device_id}"
                )

            # 验证会话是否存在
            session_exists = await session_service.check_session_exists(
                UUID(user_id), device_id, is_admin
            )

            # 如果会话不存在，尝试使用设备ID的前缀查找会话
            if not session_exists and device_id and device_id.startswith("device_"):
                # 提取设备ID的前缀
                device_prefix = (
                    device_id.split("_")[1] if len(device_id.split("_")) > 1 else ""
                )
                if device_prefix:
                    # 获取用户的所有会话
                    sessions = await session_service.get_user_sessions(
                        UUID(user_id), is_admin
                    )
                    for session in sessions:
                        session_device_id = session.get("device_id", "")
                        if (
                            session_device_id.startswith("device_")
                            and session_device_id.split("_")[1] == device_prefix
                        ):
                            # 找到匹配的会话，更新设备ID
                            device_id = session_device_id
                            session_exists = True
                            logger.info(f"找到匹配的会话，使用设备ID: {device_id}")
                            break

            if not session_exists:
                logger.warning(
                    f"用户 {user_id} (设备 {device_id}) 的会话不存在或已失效。尝试创建/更新会话。"
                )
                try:
                    # 尝试为所有通过JWT验证的用户创建/更新会话
                    await session_service.create_session(
                        UUID(user_id), device_id, token, is_admin
                    )
                    logger.info(
                        f"为用户 {user_id} (设备 {device_id}) 创建/更新了会话。"
                    )
                except Exception as create_session_exc:
                    logger.error(
                        f"为用户 {user_id} 创建/更新会话失败: {create_session_exc}"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="无法建立会话，请稍后重试",
                    )
        except Exception as e:
            logger.debug(f"验证用户 {user_id} 的令牌或会话时出错: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="验证令牌时出错，请重新登录",
            )

        # 如果执行到这里，说明令牌有效
    except Exception as e:
        logger.warning(f"验证令牌时出现未处理的异常: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无法验证认证信息",
        )

    # 使用设备ID作为连接ID，确保同一设备只有一个连接
    connection_id = device_id

    # 创建消息队列
    queue: asyncio.Queue = asyncio.Queue()

    # 存储连接
    if user_id not in active_connections:
        active_connections[user_id] = {}
    active_connections[user_id][connection_id] = queue

    logger.info(f"用户 {user_id} 的 SSE 连接已创建, 连接 ID: {connection_id}")

    # 检查是否有存储的登出事件
    try:
        from emotionai.core.cache.redis import redis_client

        logout_event_key = f"user_logout_event:{user_id}"
        logout_event_data = await redis_client.get(logout_event_key)

        if logout_event_data:
            # 如果有存储的登出事件，则发送给客户端
            try:
                logout_event = json.loads(logout_event_data)
                logger.info(f"发现存储的登出事件，发送给用户 {user_id}")

                # 构造消息
                event_data = {
                    "event": "logout",
                    "data": json.dumps(
                        {
                            "message": logout_event.get(
                                "message", "您的账号在其他设备登录，请重新登录"
                            ),
                            "code": "remote_login",
                        }
                    ),
                }

                # 将消息放入队列，延迟 0.5 秒发送，确保客户端收到连接成功消息后才收到登出消息
                asyncio.create_task(send_delayed_message(queue, event_data, 0.5))

                # 删除存储的登出事件
                await redis_client.delete(logout_event_key)
            except Exception as e:
                logger.error(f"处理存储的登出事件时出错: {e}")
    except Exception as e:
        logger.error(f"检查存储的登出事件时出错: {e}")

    # 返回事件流
    return StreamingResponse(
        sse_stream(request, user_id, connection_id, queue),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache, no-transform",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # 禁用 Nginx 缓冲
            "Content-Type": "text/event-stream",
            "Transfer-Encoding": "chunked",
            "Access-Control-Allow-Origin": "*",  # 允许跨域
        },
    )


async def send_logout_event(
    user_id: str,
    message: str = "您的账号在其他设备登录，请重新登录",
    device_id: str = None,
):
    """
    发送登出事件
    只向不同设备指纹的设备发送登出事件
    :param user_id: 用户ID
    :param message: 消息内容
    :param device_id: 当前设备ID
    """
    logger.info(f"尝试发送登出事件给用户 {user_id}")

    # 即使用户没有活跃的连接，也记录一下信息
    if user_id not in active_connections:
        logger.warning(f"用户 {user_id} 没有活跃的 SSE 连接，但仍然记录登出事件")
        # 将登出事件存储到 Redis，以便用户下次连接时可以收到
        try:
            from emotionai.core.cache.redis import redis_client

            # 存储登出事件，设置 5 分钟过期时间
            logout_event_key = f"user_logout_event:{user_id}"
            logout_event_data = json.dumps(
                {
                    "message": message,
                    "code": "remote_login",
                    "timestamp": datetime.now().isoformat(),
                }
            )
            await redis_client.set(
                logout_event_key, logout_event_data, ex=300
            )  # 5分钟过期
            logger.info(f"已将登出事件存储到 Redis，键为 {logout_event_key}")

            # 检查并清理可能存在的旧连接
            from emotionai.core.services.auth.session import session_service

            # 获取用户的所有会话
            sessions = await session_service.get_user_sessions(UUID(user_id), False)
            logger.info(f"用户 {user_id} 有 {len(sessions)} 个活跃会话")

            # 如果没有活跃会话，则清理可能存在的旧连接
            if not sessions and device_id in active_connections.get(user_id, {}):
                logger.warning(f"发现用户 {user_id} 的旧连接 {device_id}，正在清理")
                del active_connections[user_id][device_id]
                if not active_connections[user_id]:
                    del active_connections[user_id]
                    logger.info(f"用户 {user_id} 没有活跃连接，删除用户条目")
        except Exception as e:
            logger.error(f"存储登出事件到 Redis 时出错: {e}")
        return

    # 构造消息
    event_data = {
        "event": "logout",
        "data": json.dumps({"message": message, "code": "remote_login"}),
    }

    logger.info(f"发送登出事件到 {len(active_connections[user_id])} 个连接")

    # 导入会话服务
    from emotionai.core.services.auth.session import session_service

    # 发送消息到不同设备指纹的连接
    current_fingerprint = (
        session_service.get_device_fingerprint(device_id) if device_id else ""
    )
    logger.info(f"当前设备指纹: {current_fingerprint}")

    for connection_id, queue in active_connections[user_id].items():
        try:
            # 获取连接的设备指纹
            connection_fingerprint = session_service.get_device_fingerprint(
                connection_id
            )
            logger.info(f"连接 {connection_id} 的设备指纹: {connection_fingerprint}")

            # 判断是否是同一设备
            is_same_device = False

            # 如果设备ID相同，则是同一设备
            if device_id == connection_id:
                is_same_device = True
                logger.info(f"设备ID相同: {device_id} == {connection_id}")
            # 如果设备指纹相同且不为空，则是同一设备
            elif current_fingerprint and current_fingerprint == connection_fingerprint:
                is_same_device = True
                logger.info(
                    f"设备指纹相同: {current_fingerprint} == {connection_fingerprint}"
                )
            # 如果设备ID都以 'device_' 开头但指纹为空，则比较设备ID的前缀
            elif (
                device_id
                and connection_id
                and isinstance(device_id, str)
                and isinstance(connection_id, str)
                and device_id.startswith("device_")
                and connection_id.startswith("device_")
            ):
                # 提取设备ID的前缀（如果有的话）
                device_prefix = (
                    device_id.split("_")[1] if len(device_id.split("_")) > 1 else ""
                )
                connection_prefix = (
                    connection_id.split("_")[1]
                    if len(connection_id.split("_")) > 1
                    else ""
                )

                # 如果前缀相同且不为空，则是同一设备
                if device_prefix and device_prefix == connection_prefix:
                    is_same_device = True
                    logger.info(
                        f"设备ID前缀相同: {device_prefix} == {connection_prefix}"
                    )

            # 只向不同设备发送登出事件
            if not is_same_device:
                await queue.put(event_data)
                logger.info(f"成功发送登出事件到连接 {connection_id} (不同设备)")
            else:
                logger.info(f"跳过发送登出事件到连接 {connection_id} (同一设备)")
        except Exception as e:
            logger.error(f"发送登出事件到连接 {connection_id} 时出错: {e}")
