"""管理模块路由"""

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

# 导入情绪分析数据管理路由
from emotionai.api.v1.endpoints.admin.analysis_router import router as analysis_router

# 导入标注管理路由
from emotionai.api.v1.endpoints.admin.annotations import router as annotations_router

# 导入数据集管理路由
from emotionai.api.v1.endpoints.admin.datasets import router as datasets_router

# 导入模型管理路由
from emotionai.api.v1.endpoints.admin.models import router as models_router

# 导入系统设置路由
from emotionai.api.v1.endpoints.admin.settings import router as settings_router

# 导入用户管理路由
from emotionai.api.v1.endpoints.admin.users import router as users_router
from emotionai.core.database.session import get_async_db, get_db
from emotionai.core.deps import get_current_active_superuser, get_current_active_user
from emotionai.core.services.user.admin import AdminService
from emotionai.models.user import User
from emotionai.schemas.admin import (
    DashboardStats,
    RoleStats,
    SystemResourceStats,
    UserStats,
)

router = APIRouter()

# 包含用户管理路由
router.include_router(users_router, prefix="", tags=["admin-users"])

# 包含模型管理路由
router.include_router(models_router, prefix="", tags=["admin-models"])

# 包含标注管理路由（移到数据集路由之前）
router.include_router(annotations_router, prefix="", tags=["admin-annotations"])

# 包含数据集管理路由
router.include_router(datasets_router, prefix="", tags=["admin-datasets"])

# 包含情绪分析数据管理路由
router.include_router(
    analysis_router, prefix="/analysis", tags=["admin-emotion-analysis"]
)

# 包含系统设置路由
router.include_router(settings_router, prefix="/settings", tags=["admin-settings"])


@router.get("/dashboard/stats", response_model=DashboardStats)
async def get_dashboard_stats(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db),
):
    """获取仪表盘统计数据"""
    return await AdminService.get_dashboard_stats(db)


@router.get("/stats", response_model=SystemResourceStats)
async def get_system_stats(current_user: User = Depends(get_current_active_superuser)):
    """获取系统统计数据"""
    # 使用 SystemService 替代 AdminService

    # 修正导入路径
    # from emotionai.api.deps import get_db
    from emotionai.core.services.system.system import SystemService

    db = next(get_db())
    system_service = SystemService(db)
    return system_service.get_system_status()


@router.get("/users", response_model=UserStats)
async def get_user_stats(current_user: User = Depends(get_current_active_superuser)):
    """获取用户统计数据"""
    # 使用 UserService 替代 AdminService

    # 修正导入路径
    # from emotionai.api.deps import get_db
    from emotionai.core.services.user.user import UserService

    db = next(get_db())
    user_service = UserService(db)

    # 返回用户统计数据
    return {
        "total": len(user_service.get_multi()),
        "active": len(user_service.get_active_users()),
        "inactive": len(user_service.get_multi())
        - len(user_service.get_active_users()),
        "superusers": 1,  # 默认值
    }


@router.get("/roles", response_model=RoleStats)
async def get_role_stats(current_user: User = Depends(get_current_active_superuser)):
    """获取角色统计数据"""
    # 返回角色统计数据
    return {
        "total": 3,  # 默认值
        "admin": 1,  # 默认值
        "user": 2,  # 默认值
    }


@router.get("/permissions")
async def get_permissions(current_user: User = Depends(get_current_active_superuser)):
    """获取权限列表"""
    # 返回默认权限列表
    return [
        {"id": 1, "name": "read", "description": "读取权限"},
        {"id": 2, "name": "write", "description": "写入权限"},
        {"id": 3, "name": "admin", "description": "管理员权限"},
    ]
