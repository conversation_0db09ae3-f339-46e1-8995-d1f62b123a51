"""
数据集管理API
"""

from typing import Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import select, text
from sqlalchemy.orm import Session

from emotionai.core.database.session import get_db
from emotionai.core.deps import get_current_active_user
from emotionai.models.ml.dataset import Dataset
from emotionai.models.user import User

router = APIRouter()


@router.get("/datasets", response_model=list[dict[str, Any]])
def get_datasets(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    _: User = Depends(get_current_active_user),
) -> Any:
    """
    获取数据集列表
    """
    # 查询数据集
    datasets = db.execute(select(Dataset).offset(skip).limit(limit)).scalars().all()

    # 转换为字典列表
    result = []
    for dataset in datasets:
        result.append(
            {
                "id": str(dataset.id),
                "name": dataset.name,
                "description": dataset.description,
                "type": dataset.type,
                "sample_count": dataset.sample_count,
                "format": dataset.format,
                "path": dataset.path,
                "labels_distribution": dataset.labels_distribution,
                "created_at": dataset.created_at,
                "updated_at": dataset.updated_at,
            }
        )

    return result


@router.get("/datasets/{dataset_id}", response_model=dict[str, Any])
def get_dataset(
    dataset_id: UUID,
    db: Session = Depends(get_db),
    _: User = Depends(get_current_active_user),
) -> Any:
    """
    获取数据集详情
    """
    # 查询数据集
    dataset = db.execute(
        select(Dataset).where(Dataset.id == dataset_id)
    ).scalar_one_or_none()

    if not dataset:
        raise HTTPException(status_code=404, detail="数据集不存在")

    # 转换为字典
    result = {
        "id": str(dataset.id),
        "name": dataset.name,
        "description": dataset.description,
        "type": dataset.type,
        "sample_count": dataset.sample_count,
        "format": dataset.format,
        "path": dataset.path,
        "labels_distribution": dataset.labels_distribution,
        "created_at": dataset.created_at,
        "updated_at": dataset.updated_at,
    }

    return result


@router.get("/datasets/{dataset_id}/samples", response_model=dict[str, Any])
def get_dataset_samples(
    dataset_id: UUID,
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db),
    _: User = Depends(get_current_active_user),
) -> Any:
    """
    获取数据集样本
    """
    # 查询数据集
    dataset = db.execute(
        select(Dataset).where(Dataset.id == dataset_id)
    ).scalar_one_or_none()

    if not dataset:
        raise HTTPException(status_code=404, detail="数据集不存在")

    # 查询样本
    samples = db.execute(
        text(
            """
        SELECT id, file_path, label, confidence, metadata, created_at
        FROM dataset_samples
        WHERE dataset_id = :dataset_id
        ORDER BY created_at
        LIMIT :limit OFFSET :skip
        """
        ),
        {"dataset_id": str(dataset_id), "limit": limit, "skip": skip},
    ).fetchall()

    # 查询样本总数
    total = db.execute(
        text(
            """
        SELECT COUNT(*)
        FROM dataset_samples
        WHERE dataset_id = :dataset_id
        """
        ),
        {"dataset_id": str(dataset_id)},
    ).scalar_one()

    # 转换为字典列表
    sample_list = []
    for sample in samples:
        sample_list.append(
            {
                "id": str(sample[0]),
                "file_path": sample[1],
                "label": sample[2],
                "confidence": sample[3],
                "metadata": sample[4],
                "created_at": sample[5],
            }
        )

    return {
        "dataset": {
            "id": str(dataset.id),
            "name": dataset.name,
            "type": dataset.type,
        },
        "samples": sample_list,
        "total": total,
        "skip": skip,
        "limit": limit,
    }
