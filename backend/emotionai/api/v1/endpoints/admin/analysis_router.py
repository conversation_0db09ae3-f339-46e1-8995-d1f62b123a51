import logging
import os
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Body, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from emotionai.api import deps
from emotionai.crud.crud_emotion_analysis import cemotion_analysis
from emotionai.crud.crud_emotion_feedback import cemotion_feedback
from emotionai.models.user import User
from emotionai.schemas.admin.emotion_analysis import (
    EmotionAnalysis,
    EmotionAnalysisListResponse,
)
from emotionai.schemas.common import ApiResponse

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get(
    "/",
    response_model=EmotionAnalysisListResponse,
    summary="获取情绪分析记录列表",
    dependencies=[Depends(deps.get_current_active_superuser)],
)
async def read_emotion_analyses(
    db: AsyncSession = Depends(deps.get_async_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(10, ge=1, le=200, description="每页记录数"),
    sort_by: Optional[str] = Query(
        "created_at", description="排序字段, 例如: created_at, emotion, input_type"
    ),
    sort_order: Optional[str] = Query("desc", description="排序顺序 (asc 或 desc)"),
    input_type: Optional[str] = Query(None, description="按输入类型过滤"),
    emotion: Optional[str] = Query(None, description="按主要情绪过滤"),
    user_id: Optional[UUID] = Query(None, description="按用户ID过滤"),
    session_id: Optional[UUID] = Query(None, description="Filter by session ID"),
    input_path_contains: Optional[str] = Query(
        None, description="Filter by input path (contains)"
    ),
    filter_by: Optional[str] = Query(
        None, description="Filter by conditions (e.g., 'user_id:uuid,status:completed')"
    ),
    current_user: User = Depends(deps.get_current_active_superuser),
) -> EmotionAnalysisListResponse:
    """
    Retrieve emotion analyses with pagination, sorting, and filtering.
    - **skip**: Number of records to skip (for pagination).
    - **limit**: Maximum number of records to return.
    - **sort_by**: Column to sort by (e.g., 'created_at', 'updated_at').
    - **sort_order**: Sort order ('asc' or 'desc').
    - **filter_by**: Filter conditions string (e.g., 'user_id:uuid_value,status:completed').
                     The individual filter parameters (input_type, emotion, etc.) are currently not used
                     if a 'filter_by' string is provided. This function directly passes the 'filter_by' string.
    """
    sort_string: Optional[str] = None
    if sort_by and sort_order:
        sort_string = f"{sort_by}_{sort_order.lower()}"

    analyses_orm = await cemotion_analysis.get_multi(
        db, skip=skip, limit=limit, sort=sort_string, filter_by=filter_by
    )
    total_count = await cemotion_analysis.get_count(db, filter_by=filter_by)

    # 将 ORM 对象转换为 Pydantic 模式对象，并填充额外字段
    validated_items = []
    for db_obj in analyses_orm:
        item_data = db_obj.__dict__  # Start with ORM object's attributes
        # 重新启用user关系访问
        if db_obj.user:
            item_data["username"] = db_obj.user.username
        else:
            item_data["username"] = "未知用户"  # 处理用户可能缺失的情况

        # 确保 emotion_details 字段被包含
        if hasattr(db_obj, "emotion_details"):
            item_data["emotion_details"] = db_obj.emotion_details
        else:
            item_data["emotion_details"] = None

        # 添加新的情绪和置信度字段
        if hasattr(db_obj, "primary_emotion"):
            item_data["primary_emotion"] = db_obj.primary_emotion
        if hasattr(db_obj, "confidence1"):
            item_data["confidence1"] = db_obj.confidence1
        if hasattr(db_obj, "secondary_emotion"):
            item_data["secondary_emotion"] = db_obj.secondary_emotion
        if hasattr(db_obj, "confidence2"):
            item_data["confidence2"] = db_obj.confidence2
        if hasattr(db_obj, "third_emotion"):
            item_data["third_emotion"] = db_obj.third_emotion
        if hasattr(db_obj, "confidence3"):
            item_data["confidence3"] = db_obj.confidence3

        item_data["image_thumbnail_url"] = None  # Default to None
        # 基于 input_path 而不是 input_type 来判断记录是否包含图片
        if db_obj.input_path:  # 只要有 input_path，就认为是图片
            # 使用logger.debug替代print，避免控制台输出大量日志
            # logger.debug(f"input_path = {db_obj.input_path}, type = {type(db_obj.input_path)}")

            # 处理多种可能的路径格式
            absolute_image_path = str(db_obj.input_path)

            # 定义可能的路径前缀列表
            possible_prefixes = [
                "/Volumes/acasis/ema2_20250417/backend/uploads/",
                "/Volumes/acasis/ema2_20250417/uploads/",
                "uploads/",
            ]

            # 处理相对路径
            relative_path = None

            # 尝试匹配各种前缀
            for prefix in possible_prefixes:
                if absolute_image_path.startswith(prefix):
                    relative_path = absolute_image_path[len(prefix) :]
                    break

            # 如果路径以 "original/" 开头，直接使用
            if not relative_path and absolute_image_path.startswith("original/"):
                relative_path = absolute_image_path

            # 如果路径包含 "original/"，提取该部分及之后的内容
            if not relative_path and "original/" in absolute_image_path:
                relative_path = absolute_image_path[
                    absolute_image_path.index("original/") :
                ]

            # 如果我们成功提取了相对路径
            if relative_path:
                item_data["image_thumbnail_url"] = f"/static/{relative_path}"
                # logger.debug(f"Generated image_thumbnail_url = {item_data['image_thumbnail_url']}")
            else:
                # 如果所有匹配都失败，使用原始路径作为回退方案
                logger.warning(
                    f"Could not extract relative path from {absolute_image_path}, using as-is"
                )
                # 如果路径以 UUID 格式结尾，尝试直接使用
                if absolute_image_path.endswith(".jpg") and "-" in absolute_image_path:
                    item_data["image_thumbnail_url"] = (
                        f"/static/original/{absolute_image_path}"
                    )
                else:
                    item_data["image_thumbnail_url"] = f"/static/{absolute_image_path}"
            # For debugging, you can uncomment the line below to see the generated URL in backend logs:
            # logger.debug(f"Generated image_thumbnail_url for input {db_obj.input_path}: {item_data['image_thumbnail_url']}")

        # Use model_validate with the enriched dictionary
        # Pydantic will pick the fields defined in the EmotionAnalysis schema
        validated_items.append(EmotionAnalysis.model_validate(item_data))

    return EmotionAnalysisListResponse(total=total_count, items=validated_items)


@router.get(
    "/{analysis_id}",
    response_model=EmotionAnalysis,
    summary="获取单个情绪分析记录详情",
    dependencies=[Depends(deps.get_current_active_superuser)],
)
async def read_emotion_analysis(
    analysis_id: UUID,
    db: AsyncSession = Depends(deps.get_async_db),
) -> EmotionAnalysis:
    """
    根据ID获取单个情绪分析记录的详细信息，包含用户反馈。
    """
    analysis = await cemotion_analysis.get(db, id=analysis_id)
    if not analysis:
        raise HTTPException(status_code=404, detail="情绪分析记录未找到")

    # 获取该分析记录的所有反馈
    feedbacks_orm = await cemotion_feedback.get_by_analysis_id_with_user(
        db, analysis_id
    )
    logger.info(f"获取到 {len(feedbacks_orm)} 条反馈记录，分析ID: {analysis_id}")

    # 构建分析记录数据
    analysis_data = {}

    # 复制基本属性
    for attr in [
        "id",
        "input_type",
        "input_path",
        "primary_emotion",  # Updated field name
        "confidence1",  # Updated field name
        "secondary_emotion",
        "confidence2",
        "third_emotion",
        "confidence3",
        "emotion_details",
        "gender",
        "age",
        "race",
        "user_id",
        "session_id",
        "device_info",
        "analysis_time",
        "model_version",
        "additional_info",
        "created_at",
        "updated_at",
        "deleted_at",
    ]:
        if hasattr(analysis, attr):
            analysis_data[attr] = getattr(analysis, attr)

    # 添加用户名
    if hasattr(analysis, "user") and analysis.user:
        analysis_data["username"] = analysis.user.username
    else:
        analysis_data["username"] = "未知用户"

    # 处理图片缩略图URL（复用列表API的逻辑）
    analysis_data["image_thumbnail_url"] = None
    if analysis.input_path:
        absolute_image_path = str(analysis.input_path)
        possible_prefixes = [
            "/Volumes/acasis/ema2_20250417/backend/uploads/",
            "/Volumes/acasis/ema2_20250417/uploads/",
            "uploads/",
        ]

        relative_path = None
        for prefix in possible_prefixes:
            if absolute_image_path.startswith(prefix):
                relative_path = absolute_image_path[len(prefix) :]
                break

        if not relative_path and absolute_image_path.startswith("original/"):
            relative_path = absolute_image_path

        if not relative_path and "original/" in absolute_image_path:
            relative_path = absolute_image_path[
                absolute_image_path.index("original/") :
            ]

        if relative_path:
            analysis_data["image_thumbnail_url"] = f"/static/{relative_path}"
        else:
            if absolute_image_path.endswith(".jpg") and "-" in absolute_image_path:
                analysis_data["image_thumbnail_url"] = (
                    f"/static/original/{absolute_image_path}"
                )
            else:
                analysis_data["image_thumbnail_url"] = f"/static/{absolute_image_path}"

    # 构建反馈数据
    feedbacks_data = []
    for feedback_orm in feedbacks_orm:
        feedback_data = {}
        # 复制反馈基本属性，包含rating字段和标注反馈的人口统计学信息
        for attr in [
            "id",
            "analysis_id",
            "emotion",
            "rating",
            "comment",
            "name",
            "gender",
            "age",
            "created_by",
            "created_at",
            "updated_at",
        ]:
            if hasattr(feedback_orm, attr):
                feedback_data[attr] = getattr(feedback_orm, attr)

        # 添加反馈用户名
        if hasattr(feedback_orm, "user") and feedback_orm.user:
            feedback_data["username"] = feedback_orm.user.username
        else:
            feedback_data["username"] = "未知用户"
        feedbacks_data.append(feedback_data)

    # 添加反馈信息到分析数据
    analysis_data["feedbacks"] = feedbacks_data
    analysis_data["feedback_count"] = len(feedbacks_data)

    # 使用Pydantic模型验证并返回
    return EmotionAnalysis.model_validate(analysis_data)


@router.delete(
    "/{analysis_id}",
    response_model=ApiResponse,
    summary="删除情绪分析记录",
    description="通过ID删除一个情绪分析记录及其图片文件",
    status_code=200,
    dependencies=[Depends(deps.get_current_active_superuser)],
)
async def delete_emotion_analysis(
    analysis_id: UUID,
    db: AsyncSession = Depends(deps.get_async_db),
    current_user: User = Depends(deps.get_current_active_superuser),
) -> ApiResponse:
    """Delete an emotion analysis record by its ID and its associated image files."""
    analysis = await cemotion_analysis.get(db, id=analysis_id)
    if not analysis:
        raise HTTPException(status_code=404, detail="EmotionAnalysis not found")

    # 删除关联的图片文件
    if analysis.input_path:
        # 处理相对路径的情况
        input_path = analysis.input_path
        if input_path.startswith("uploads/"):
            # 转换为绝对路径
            input_path = os.path.join(
                "/Volumes/acasis/ema2_20250417/backend", input_path
            )
            logger.info(f"将相对路径转换为绝对路径: {input_path}")

        if os.path.exists(input_path):
            try:
                os.remove(input_path)
                logger.info(f"已删除原始图片文件: {input_path}")

                # 检查并删除标记后的图片文件（如果存在）
                marked_path = input_path.replace("/original/", "/marked/")
                if os.path.exists(marked_path):
                    os.remove(marked_path)
                    logger.info(f"已删除标记图片文件: {marked_path}")
            except Exception as e:
                logger.error(f"删除图片文件失败: {str(e)}")
        else:
            logger.warning(f"图片文件不存在: {input_path}")

    # 删除数据库记录
    await cemotion_analysis.remove(db, id=analysis_id)
    return ApiResponse(message="情绪分析记录及相关图片文件已成功删除")


@router.delete(
    "/batch/delete",
    response_model=ApiResponse,
    summary="批量删除情绪分析记录",
    description="批量删除多个情绪分析记录及其图片文件",
    status_code=200,
    dependencies=[Depends(deps.get_current_active_superuser)],
)
async def batch_delete_emotion_analyses(
    analysis_ids: List[UUID] = Body(..., description="要删除的情绪分析记录ID列表"),
    db: AsyncSession = Depends(deps.get_async_db),
    current_user: User = Depends(deps.get_current_active_superuser),
) -> ApiResponse:
    """Batch delete multiple emotion analysis records and their associated image files."""
    if not analysis_ids:
        raise HTTPException(status_code=400, detail="未提供要删除的记录ID")

    deleted_count = 0
    failed_ids = []

    for analysis_id in analysis_ids:
        try:
            analysis = await cemotion_analysis.get(db, id=analysis_id)
            if not analysis:
                failed_ids.append(str(analysis_id))
                continue

            # 删除关联的图片文件
            if analysis.input_path:
                # 处理相对路径的情况
                input_path = analysis.input_path
                if input_path.startswith("uploads/"):
                    # 转换为绝对路径
                    input_path = os.path.join(
                        "/Volumes/acasis/ema2_20250417/backend", input_path
                    )
                    logger.info(f"将相对路径转换为绝对路径: {input_path}")

                if os.path.exists(input_path):
                    try:
                        os.remove(input_path)
                        logger.info(f"已删除原始图片文件: {input_path}")

                        # 检查并删除标记后的图片文件（如果存在）
                        marked_path = input_path.replace("/original/", "/marked/")
                        if os.path.exists(marked_path):
                            os.remove(marked_path)
                            logger.info(f"已删除标记图片文件: {marked_path}")
                    except Exception as e:
                        logger.error(f"删除图片文件失败: {str(e)}")
                else:
                    logger.warning(f"图片文件不存在: {input_path}")

            # 删除数据库记录
            await cemotion_analysis.remove(db, id=analysis_id)
            deleted_count += 1
        except Exception as e:
            logger.error(f"删除记录 {analysis_id} 失败: {str(e)}")
            failed_ids.append(str(analysis_id))

    if failed_ids:
        return ApiResponse(
            success=True,
            message=f"已成功删除 {deleted_count} 条记录，{len(failed_ids)} 条记录删除失败",
            data={"failed_ids": failed_ids},
        )
    else:
        return ApiResponse(message=f"已成功删除 {deleted_count} 条记录及相关图片文件")
