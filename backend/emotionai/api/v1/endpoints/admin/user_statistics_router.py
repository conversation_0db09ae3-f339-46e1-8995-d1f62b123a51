import logging
from datetime import date, timedelta
from typing import Optional

from fastapi import APIRouter, Depends, Query
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from emotionai.api import deps
from emotionai.models.user import User
from emotionai.schemas.admin.user_statistics import (
    UserStatisticsData,
    UserStatisticsResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get(
    "/",
    response_model=UserStatisticsResponse,
    summary="获取用户活动统计",
    dependencies=[Depends(deps.get_current_active_superuser)],
)
async def get_user_statistics(
    db: AsyncSession = Depends(deps.get_async_db),
    username: Optional[str] = Query(None, description="用户名筛选"),
    period: str = Query("day", description="统计周期：day/week/month"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    current_user: User = Depends(deps.get_current_active_superuser),
) -> UserStatisticsResponse:
    """
    获取用户活动统计数据，包括上传图片数量和标注数量
    """
    try:
        # 设置默认日期范围
        if not start_date:
            if period == "day":
                start_date = date.today() - timedelta(days=7)
            elif period == "week":
                start_date = date.today() - timedelta(weeks=4)
            elif period == "month":
                start_date = date.today() - timedelta(days=90)
            else:
                start_date = date.today() - timedelta(days=30)

        if not end_date:
            end_date = date.today()

        logger.info(
            f"查询用户统计数据，参数：username={username}, period={period}, start_date={start_date}, end_date={end_date}"
        )

        # 构建基础查询
        # 查询上传图片数量（基于emotion_analysis表）
        upload_query = """
        SELECT 
            u.username,
            COUNT(ea.id) as upload_count
        FROM users u
        LEFT JOIN emotion_analysis ea ON u.id = ea.user_id 
            AND ea.input_type = 'image'
            AND ea.created_at >= :start_date 
            AND ea.created_at <= :end_date
            AND ea.deleted_at IS NULL
        WHERE u.deleted_at IS NULL
        """

        # 查询标注数量（基于emotion_feedback表中的emotion字段）
        annotation_query = """
        SELECT 
            u.username,
            COUNT(ef.id) as annotation_count
        FROM users u
        LEFT JOIN emotion_feedback ef ON u.id = ef.created_by 
            AND ef.emotion IS NOT NULL
            AND ef.created_at >= :start_date 
            AND ef.created_at <= :end_date
            AND ef.deleted_at IS NULL
        WHERE u.deleted_at IS NULL
        """

        # 添加用户名筛选
        if username:
            upload_query += " AND u.username ILIKE :username_pattern"
            annotation_query += " AND u.username ILIKE :username_pattern"

        upload_query += " GROUP BY u.username ORDER BY upload_count DESC"
        annotation_query += " GROUP BY u.username ORDER BY annotation_count DESC"

        # 执行查询
        from datetime import datetime as dt

        # 转换日期为datetime对象，包含时间范围
        start_datetime = dt.combine(start_date, dt.min.time())
        end_datetime = dt.combine(end_date, dt.max.time())

        query_params = {
            "start_date": start_datetime,
            "end_date": end_datetime,
        }

        if username:
            query_params["username_pattern"] = f"%{username}%"

        logger.info(f"查询参数: start_date={start_datetime}, end_date={end_datetime}")

        # 获取上传数据
        upload_result = await db.execute(text(upload_query), query_params)
        upload_data = upload_result.fetchall()
        logger.info(f"上传数据查询结果: {len(upload_data)} 条记录")

        # 获取标注数据
        annotation_result = await db.execute(text(annotation_query), query_params)
        annotation_data = annotation_result.fetchall()
        logger.info(f"标注数据查询结果: {len(annotation_data)} 条记录")

        # 合并数据
        user_stats_dict = {}

        # 处理上传数据
        for row in upload_data:
            username_val, upload_count = row
            user_stats_dict[username_val] = {
                "username": username_val,
                "upload_count": upload_count or 0,
                "annotation_count": 0,
                "period": period,
            }

        # 处理标注数据
        for row in annotation_data:
            username_val, annotation_count = row
            if username_val in user_stats_dict:
                user_stats_dict[username_val]["annotation_count"] = (
                    annotation_count or 0
                )
            else:
                user_stats_dict[username_val] = {
                    "username": username_val,
                    "upload_count": 0,
                    "annotation_count": annotation_count or 0,
                    "period": period,
                }

        # 转换为列表并排序（按总活动量排序）
        statistics_list = []
        for stats in user_stats_dict.values():
            if (
                stats["upload_count"] > 0 or stats["annotation_count"] > 0
            ):  # 只包含有活动的用户
                statistics_list.append(UserStatisticsData(**stats))

        # 按总活动量排序
        statistics_list.sort(
            key=lambda x: x.upload_count + x.annotation_count, reverse=True
        )

        # 计算总计
        total_users = len(statistics_list)
        total_uploads = sum(item.upload_count for item in statistics_list)
        total_annotations = sum(item.annotation_count for item in statistics_list)

        logger.info(f"查询完成，返回 {total_users} 个用户的统计数据")

        return UserStatisticsResponse(
            data=statistics_list,
            total_users=total_users,
            total_uploads=total_uploads,
            total_annotations=total_annotations,
        )

    except Exception as e:
        logger.error(f"获取用户统计数据失败: {str(e)}")
        # 返回空数据而不是抛出异常
        return UserStatisticsResponse(
            data=[],
            total_users=0,
            total_uploads=0,
            total_annotations=0,
        )
