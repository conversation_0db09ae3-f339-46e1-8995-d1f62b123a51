"""
情绪分析API路由
"""

import logging
import os
import uuid
from datetime import datetime
from typing import Any

# 设置环境变量，确保使用系统安装的OpenCV库
os.environ["MEDIAPIPE_GPU"] = "0"  # 禁用MediaPipe的GPU加速
os.environ["MEDIAPIPE_USE_CUSTOM_OPENCV"] = (
    "0"  # 使用系统安装的OpenCV库，而不是MediaPipe内置的
)
os.environ["MEDIAPIPE_DISABLE_GPU"] = "1"  # 禁用GPU，使用CPU推理

import asyncio
import base64
import json

# 导入OpenCV
import cv2
import httpx
import numpy as np
from fastapi import (
    APIRouter,
    Depends,
    File,
    Form,
    HTTPException,
    Query,
    Request,
    UploadFile,
    status,
)
from fastapi.responses import FileResponse, StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession

from emotionai.api.deps import get_async_db, get_current_user
from emotionai.core.config.paths import MARKED_DIR, ORIGINAL_DIR
from emotionai.core.ml.model_loader import ModelRegistry
from emotionai.core.services.ml.emotion import emotion_service
from emotionai.models.user.user import User
from emotionai.schemas.ml.emotion import (
    DetectorType,
    EmotionRequest,
    EmotionResponse,
    ModelWeightsRequest,
    ModelWeightsResponse,
    YOLOv8FaceRequest,
)

# 创建日志对象
logger = logging.getLogger(__name__)

# 使用 Python 3.9+ 的新类型注解语法

# LM Studio API地址
LM_STUDIO_API_URL = "http://192.168.1.225:1234/v1/chat/completions"


router = APIRouter()


async def stream_multimodal_analysis(
    image_data: str, prompt: str, user_agent: str = None
):
    """
    流式传输多模态分析结果

    Args:
        image_data: Base64编码的图像数据
        prompt: 提示词
        user_agent: 用户代理字符串，用于检测浏览器类型

    Yields:
        流式响应数据
    """
    try:
        # 检测是否是微信浏览器
        is_wechat = user_agent and "MicroMessenger" in user_agent

        # 根据浏览器类型设置不同的延迟
        # 微信浏览器使用较长的延迟，普通浏览器使用较短的延迟
        delay_time = 0.08 if is_wechat else 0.04  # 微信浏览器80毫秒，普通浏览器40毫秒

        logger.info(
            f"多模态分析 - 浏览器类型: {'微信浏览器' if is_wechat else '普通浏览器'}, 使用延迟: {delay_time}秒"
        )

        # 构建请求数据 - 只传递必要参数，其他参数使用LM Studio中的设置
        payload = {
            "model": "local-model",  # LM Studio使用的模型名称
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_data}"
                            },
                        },
                    ],
                }
            ],
            "stream": True,  # 启用流式输出
            # 所有其他参数（max_tokens, temperature, top_p, top_k等）使用LM Studio中的设置
        }

        logger.info(f"发送到LM Studio的payload参数: {list(payload.keys())}")
        logger.info(
            "确认：没有传递max_tokens、temperature等限制参数，完全使用LM Studio本身的设置"
        )

        # 发送请求到LM Studio API，增加超时时间
        async with httpx.AsyncClient(timeout=120.0) as client:
            async with client.stream(
                "POST", LM_STUDIO_API_URL, json=payload
            ) as response:
                if response.status_code != 200:
                    error_msg = f"LM Studio API返回错误: {response.status_code}"
                    logger.error(error_msg)
                    yield f"data: {json.dumps({'error': error_msg})}\n\n"
                    return

                # 流式响应处理
                buffer = ""  # 用于缓存数据
                last_yield_time = 0  # 记录上次发送时间
                content_buffer = ""  # 用于累积content内容
                stream_ended = False  # 标记流是否正常结束

                try:
                    async for chunk in response.aiter_text():
                        if chunk:
                            # 将数据添加到缓冲区
                            buffer += chunk

                            # 检查是否有完整的数据行
                            lines = buffer.split("\n")
                            # 保留最后一行（可能不完整）
                            buffer = lines[-1]

                            # 处理完整的行
                            for line in lines[:-1]:
                                line = line.strip()
                                if line.startswith("data: "):
                                    data_part = line[6:]  # 移除 'data: ' 前缀

                                    # 检查是否是[DONE]标记
                                    if data_part == "[DONE]":
                                        logger.info("收到[DONE]标记，准备结束流")
                                        # 发送最后的内容缓冲区
                                        if content_buffer:
                                            logger.info(
                                                f"发送最后的缓冲区内容: {content_buffer}"
                                            )
                                            yield f"data: {json.dumps({'content': content_buffer})}\n\n"
                                            content_buffer = ""
                                        yield "data: [DONE]\n\n"
                                        stream_ended = True
                                        return

                                    # 尝试解析JSON
                                    try:
                                        json_data = json.loads(data_part)

                                        # 提取content字段
                                        if (
                                            "choices" in json_data
                                            and len(json_data["choices"]) > 0
                                        ):
                                            delta = json_data["choices"][0].get(
                                                "delta", {}
                                            )
                                            content = delta.get("content", "")

                                            if content:
                                                content_buffer += content
                                                # 只在缓冲区达到一定大小时记录日志，减少日志输出
                                                if len(content_buffer) % 100 == 0:
                                                    logger.debug(
                                                        f"缓冲区进度: 长度 {len(content_buffer)}"
                                                    )

                                                # 控制发送频率
                                                current_time = (
                                                    asyncio.get_event_loop().time()
                                                )
                                                if (
                                                    current_time - last_yield_time
                                                ) >= delay_time or len(
                                                    content_buffer
                                                ) >= 10:
                                                    # 只记录关键的发送事件，不记录具体内容
                                                    if (
                                                        len(content_buffer) > 50
                                                    ):  # 只有较长内容才记录
                                                        logger.debug(
                                                            f"发送缓冲区内容，长度: {len(content_buffer)}"
                                                        )
                                                    yield f"data: {json.dumps({'content': content_buffer})}\n\n"
                                                    content_buffer = ""
                                                    last_yield_time = current_time

                                    except json.JSONDecodeError:
                                        # 如果不是有效的JSON，跳过
                                        continue
                                    except Exception as e:
                                        logger.error(f"处理JSON数据时出错: {str(e)}")
                                        continue

                    # 流结束，检查是否有剩余内容需要发送
                    if not stream_ended:
                        logger.info("流异常结束，检查剩余内容")
                        # 处理可能剩余的缓冲区行
                        if buffer.strip():
                            line = buffer.strip()
                            if line.startswith("data: "):
                                data_part = line[6:]
                                if data_part == "[DONE]":
                                    if content_buffer:
                                        logger.info(
                                            f"发送最后的缓冲区内容: {content_buffer}"
                                        )
                                        yield f"data: {json.dumps({'content': content_buffer})}\n\n"
                                    yield "data: [DONE]\n\n"
                                    return
                                else:
                                    try:
                                        chunk_data = json.loads(data_part)
                                        if "choices" in chunk_data:
                                            for choice in chunk_data["choices"]:
                                                delta = choice.get("delta", {})
                                                content = delta.get("content", "")
                                                if content:
                                                    content_buffer += content
                                    except Exception as e:
                                        logger.error(f"处理流式响应块时出错: {str(e)}")
                                        # 继续处理其他块

                        # 发送剩余的内容
                        if content_buffer:
                            logger.info(f"发送剩余的缓冲区内容: {content_buffer}")
                            yield f"data: {json.dumps({'content': content_buffer})}\n\n"

                        # 如果没有收到[DONE]标记，手动发送
                        logger.info("手动发送[DONE]标记")
                        yield "data: [DONE]\n\n"

                except Exception as stream_error:
                    logger.error(f"流处理过程中出错: {str(stream_error)}")
                    # 即使出错，也要尝试发送剩余内容
                    if content_buffer:
                        logger.info(f"出错时发送剩余内容: {content_buffer}")
                        yield f"data: {json.dumps({'content': content_buffer})}\n\n"
                    yield f"data: {json.dumps({'error': f'流处理出错: {str(stream_error)}'})}\n\n"

    except Exception as e:
        logger.error(f"流式传输多模态分析时出错: {str(e)}")
        yield f"data: {json.dumps({'error': str(e)})}\n\n"


@router.post("/analyze", response_model=EmotionResponse)
async def analyze_emotion(
    request: EmotionRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    分析图片中的情绪
    """
    try:
        # 使用用户ID
        user_id = str(current_user.id)

        # 分析情绪
        result = await emotion_service.analyze_image(
            image_data=request.image,
            use_face_mesh=request.use_face_mesh,
            detector_type=request.detector_type,
            user_id=user_id,
            db=db if request.save_result else None,
        )

        if not result.get("success", False):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "分析情绪失败"),
            )

        # 移除年龄分析判断的相关代码
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析情绪时出错: {str(e)}",
        )


@router.post("/upload", response_model=EmotionResponse)
async def upload_and_analyze(
    file: UploadFile = File(...),
    use_face_mesh: bool = Form(False),  # 已弃用参数，保留仅为兼容性
    detector_type: str = Form(
        DetectorType.YOLO_MEDIAPIPE.value
    ),  # 检测器类型，可选值: yolov8, yolo_mediapipe, mediapipe
    save_result: bool = Form(True),
    analyze_now: bool = Form(False),  # 是否立即分析，默认为False
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    上传图片并返回图片URL，可选择是否立即分析情绪
    """
    try:
        # 读取上传的文件
        contents = await file.read()

        # 将文件内容转换为Base64编码
        import base64

        image_data = base64.b64encode(contents).decode("utf-8")

        # 使用用户ID
        user_id = str(current_user.id)

        # 打印请求参数
        logger.info(
            f"上传图片请求参数: use_face_mesh={use_face_mesh}, detector_type={detector_type}, save_result={save_result}, analyze_now={analyze_now}"
        )

        # 上传图片并检测特征点，但不进行情绪分析
        result = await emotion_service.upload_image(
            image_data=image_data,
            user_id=user_id,
            db=db,
        )

        # 打印响应结果
        logger.info(
            f"上传图片响应结果: success={result.get('success', False)}, image_id={result.get('image_id', None)}"
        )

        if not result.get("success", False):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "上传图片失败"),
            )

        # 转换文件系统路径为URL格式
        image_id = result.get("image_id")

        # 获取当前日期，用于构建URL
        today = datetime.now()
        year_month = today.strftime("%Y-%m")
        day = today.strftime("%d")

        # 构建URL
        result["original_image"] = (
            f"/api/v1/emotion/images/{year_month}/{day}/{image_id}.jpg"
        )
        result["marked_image"] = (
            f"/api/v1/emotion/images/{year_month}/{day}/{image_id}_marked.jpg"
        )

        # 确保结果中包含original_image字段
        if "original_image" not in result:
            logger.warning("结果中缺少original_image字段，手动添加")
            result["original_image"] = (
                f"/api/v1/emotion/images/{year_month}/{day}/{image_id}.jpg"
            )

        # 记录URL
        logger.info(
            f"构建图片URL: original_image={result.get('original_image')}, marked_image={result.get('marked_image')}"
        )

        # 检查文件是否存在
        from emotionai.core.config.paths import UPLOAD_DIR

        original_path = os.path.join(
            UPLOAD_DIR, "original", year_month, day, f"{image_id}.jpg"
        )
        marked_path = os.path.join(
            UPLOAD_DIR, "marked", year_month, day, f"{image_id}_marked.jpg"
        )

        # 确保目录存在
        os.makedirs(os.path.dirname(original_path), exist_ok=True)
        os.makedirs(os.path.dirname(marked_path), exist_ok=True)

        logger.info(
            f"检查文件是否存在: original_path={original_path}, marked_path={marked_path}"
        )
        logger.info(
            f"原始图片存在: {os.path.exists(original_path)}, 特征点图片存在: {os.path.exists(marked_path)}"
        )

        # 如果文件不存在，记录错误
        if not os.path.exists(original_path):
            logger.error(f"原始图片不存在: {original_path}")
        if not os.path.exists(marked_path):
            logger.error(f"特征点图片不存在: {marked_path}")

        # 如果需要立即分析，则进行情绪分析
        if analyze_now:
            # 获取图片ID
            image_id = result.get("image_id")
            if not image_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="缺少图片ID，无法进行分析",
                )

            # 分析情绪
            analysis_result = await emotion_service.analyze_emotion(
                image_id=image_id,
                user_id=user_id,
                db=db,
            )

            if not analysis_result.get("success", False):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=analysis_result.get("error", "分析情绪失败"),
                )

            # 合并结果
            result.update(analysis_result)

        # 移除年龄分析判断的相关代码
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析情绪时出错: {str(e)}",
        )


@router.post("/analyze/{image_id}", response_model=EmotionResponse)
async def analyze_emotion_by_id(
    image_id: str,
    use_ensemble: bool = Query(True, description="是否使用集成学习"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    根据图片ID分析情绪

    参数:
        image_id: 图片ID
        use_ensemble: 是否使用集成学习，默认为True

    返回:
        情绪分析结果
    """
    try:
        # 使用用户ID
        user_id = str(current_user.id)

        # 记录请求参数
        logger.info(f"分析情绪请求参数: image_id={image_id}, user_id={user_id}")

        # 检查图片是否存在
        import glob

        from emotionai.core.config.paths import ORIGINAL_DIR

        # 尝试多种方式查找原始图像
        # 1. 首先尝试在当前日期目录中查找
        today = datetime.now()
        year_month = today.strftime("%Y-%m")
        day = today.strftime("%d")

        # 构建原始图像路径
        original_path = os.path.join(ORIGINAL_DIR, year_month, day, f"{image_id}.jpg")
        logger.info(f"尝试在当前日期目录中查找原始图像: {original_path}")

        # 2. 如果找不到，尝试在所有日期目录中查找
        if not os.path.exists(original_path):
            logger.info("在当前日期目录中未找到原始图像，尝试在所有日期目录中查找")
            possible_paths = glob.glob(
                os.path.join(ORIGINAL_DIR, "**", f"{image_id}.jpg"), recursive=True
            )
            if possible_paths:
                original_path = possible_paths[0]
                logger.info(f"在日期目录中找到原始图像: {original_path}")

        # 3. 如果仍然找不到，尝试查找最新上传的图片
        if not os.path.exists(original_path):
            logger.info("在所有日期目录中未找到原始图像，尝试查找最新上传的图片")
            # 查找最新上传的图片
            all_images = glob.glob(
                os.path.join(ORIGINAL_DIR, "**", "*.jpg"), recursive=True
            )
            if all_images:
                # 按修改时间排序
                all_images.sort(key=os.path.getmtime, reverse=True)
                # 使用最新的图片
                original_path = all_images[0]
                logger.info(f"使用最新上传的图片: {original_path}")
                # 提取图片ID
                image_id = os.path.basename(original_path).replace(".jpg", "")

        # 如果找到了图像，直接读取并分析
        if os.path.exists(original_path):
            logger.info(f"找到原始图像: {original_path}")

            # 读取图像
            import cv2

            image = cv2.imread(str(original_path))

            if image is None or image.size == 0:
                logger.error(f"读取图像失败: {original_path}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"读取图像失败: {original_path}",
                )

            # 检测人脸特征点
            detection_result = await emotion_service.detect_face_features(
                image, detector_type=emotion_service.default_detector
            )

            if not detection_result.get("success", False):
                logger.error(
                    f"人脸特征点检测失败: {detection_result.get('error', '未知错误')}"
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=detection_result.get("error", "人脸特征点检测失败"),
                )

            # 获取人脸边界框
            bbox = detection_result.get("bbox", [0, 0, 0, 0])
            x1, y1, x2, y2 = bbox

            # 裁剪人脸区域
            face_image = image[y1:y2, x1:x2]

            if face_image.size == 0:
                logger.error("裁剪人脸区域失败")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="裁剪人脸区域失败",
                )

            # 分析情绪
            if use_ensemble and emotion_service.emotion_ensemble is not None:
                # 使用情绪模型融合器分析（集成学习）
                logger.info("使用情绪模型融合器进行集成学习分析")
                emotion_result = await emotion_service.emotion_ensemble.analyze(
                    face_image
                )

                # 记录各模型的预测结果，用于调试
                if "model_results" in emotion_result:
                    for model_name, result in emotion_result["model_results"].items():
                        if result.get("success", False):
                            logger.info(
                                f"模型 {model_name} 预测结果: {result.get('dominant_emotion')}, 置信度: {result.get('emotions', {}).get(result.get('dominant_emotion'), 0.0):.4f}"
                            )
                        else:
                            logger.warning(
                                f"模型 {model_name} 预测失败: {result.get('error', '未知错误')}"
                            )
            else:
                # 使用单个模型分析
                logger.info("使用单个模型分析")
                # 导入EmotionEnsemble
                from emotionai.core.ml.emotion_ensemble import EmotionEnsemble

                # 使用优化后的情绪集成模型
                logger.info("使用优化后的EmotionEnsemble进行情绪分析")
                emotion_ensemble = EmotionEnsemble()

                # 预测情绪（face_image是BGR格式，EmotionEnsemble会自动转换为RGB）
                emotion_result = await emotion_ensemble.analyze(face_image)

            if not emotion_result.get("success", False):
                logger.error(f"情绪分析失败: {emotion_result.get('error', '未知错误')}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=emotion_result.get("error", "情绪分析失败"),
                )

            # 获取情绪结果
            dominant_emotion = emotion_result.get("dominant_emotion", "neutral")
            emotions = emotion_result.get("emotions", {})
            confidence = emotions.get(dominant_emotion, 0.0)

            # 创建或更新情绪分析记录
            from sqlalchemy import select

            from emotionai.models.ml.emotion import EmotionAnalysis

            # 查询是否已存在情绪分析记录
            query = select(EmotionAnalysis).where(
                EmotionAnalysis.id == uuid.UUID(image_id),
                EmotionAnalysis.user_id == uuid.UUID(user_id),
                EmotionAnalysis.deleted_at.is_(None),
            )

            result = await db.execute(query)
            analysis = result.scalar_one_or_none()

            # 按置信度降序排列情绪
            # 确保 emotions 中的值是数值类型，以便正确排序
            valid_emotions_for_sorting = {
                k: v for k, v in emotions.items() if isinstance(v, (int, float))
            }
            sorted_emotions = sorted(
                valid_emotions_for_sorting.items(),
                key=lambda item: item[1],
                reverse=True,
            )
            logger.info(
                f"Router - 排序后的情绪列表 (sorted_emotions): {sorted_emotions}"
            )

            # 提取前三种情绪及其置信度
            primary_emotion_data = (
                sorted_emotions[0] if len(sorted_emotions) > 0 else ("neutral", 0.0)
            )
            secondary_emotion_data = (
                sorted_emotions[1] if len(sorted_emotions) > 1 else (None, 0.0)
            )
            third_emotion_data = (
                sorted_emotions[2] if len(sorted_emotions) > 2 else (None, 0.0)
            )

            logger.info(f"Router - 提取的主要情绪: {primary_emotion_data}")
            logger.info(f"Router - 提取的次要情绪: {secondary_emotion_data}")
            logger.info(f"Router - 提取的第三情绪: {third_emotion_data}")

            if analysis:
                # 更新现有记录
                analysis.primary_emotion = primary_emotion_data[0]
                analysis.confidence1 = (
                    float(primary_emotion_data[1])
                    if primary_emotion_data[1] is not None
                    else 0.0
                )
                analysis.secondary_emotion = secondary_emotion_data[0]
                analysis.confidence2 = (
                    float(secondary_emotion_data[1])
                    if secondary_emotion_data[1] is not None
                    else None
                )
                analysis.third_emotion = third_emotion_data[0]
                analysis.confidence3 = (
                    float(third_emotion_data[1])
                    if third_emotion_data[1] is not None
                    else None
                )
                analysis.emotion_details = emotions  # 保存原始的完整情绪详情
                analysis.updated_at = datetime.now()
            else:
                # 创建新记录
                analysis = EmotionAnalysis(
                    id=uuid.UUID(image_id),
                    user_id=uuid.UUID(user_id),
                    input_type="image",
                    input_path=str(original_path),
                    primary_emotion=primary_emotion_data[0],
                    confidence1=(
                        float(primary_emotion_data[1])
                        if primary_emotion_data[1] is not None
                        else 0.0
                    ),
                    secondary_emotion=secondary_emotion_data[0],
                    confidence2=(
                        float(secondary_emotion_data[1])
                        if secondary_emotion_data[1] is not None
                        else None
                    ),
                    third_emotion=third_emotion_data[0],
                    confidence3=(
                        float(third_emotion_data[1])
                        if third_emotion_data[1] is not None
                        else None
                    ),
                    emotion_details=emotions,  # 保存原始的完整情绪详情
                    model_version="v1.0",
                    created_by=uuid.UUID(user_id),
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                )
                db.add(analysis)

            # 保存到数据库
            await db.commit()
            await db.refresh(analysis)

            # 获取年龄和性别信息
            # 尝试直接使用AGR模型进行年龄和性别分析
            age = 28.0  # 默认年龄
            gender = "unknown"  # 默认性别

            try:
                # 导入需要的模块
                import cv2

                from emotionai.core.ml.deepface_wrapper import DeepFaceWrapper

                # 导入FaceAttributesEnsemble和DeepFaceWrapper
                from emotionai.core.ml.face_attributes_ensemble import (
                    FaceAttributesEnsemble,
                )

                try:
                    # 使用人脸属性集成进行年龄、性别和种族分析
                    logger.info("使用人脸属性集成进行年龄、性别和种族分析")

                    # 创建人脸属性集成实例
                    face_attributes_ensemble = FaceAttributesEnsemble()

                    # 转换为RGB格式
                    rgb_face = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)

                    # 分析人脸属性
                    ensemble_result = face_attributes_ensemble.analyze(rgb_face)

                    # 记录完整的人脸属性集成分析结果
                    logger.info(f"完整的人脸属性集成分析结果: {ensemble_result}")

                    # 如果人脸属性集成分析失败，回退到DeepFace
                    if not ensemble_result:
                        logger.warning("人脸属性集成分析失败，回退到DeepFace")
                        # 创建DeepFace包装器
                        deepface_wrapper = DeepFaceWrapper()
                        # 分析人脸
                        result = deepface_wrapper.analyze(rgb_face)
                        # 打印完整的DeepFace分析结果
                        logger.info(f"完整的DeepFace分析结果: {result}")
                    else:
                        # 使用人脸属性集成的结果
                        result = {
                            "using_deepface": True,  # 保持兼容性
                            "age": ensemble_result.get("age"),
                            "gender": ensemble_result.get("gender"),
                            "gender_confidence": ensemble_result.get(
                                "gender_confidence", 0.0
                            ),
                            "race": ensemble_result.get("race"),
                            "race_confidence": ensemble_result.get(
                                "race_confidence", 0.0
                            ),
                        }
                        logger.info(f"使用人脸属性集成结果: {result}")

                    # 提取结果
                    if (
                        result
                        and "using_deepface" in result
                        and result["using_deepface"]
                    ):
                        # 获取年龄和性别
                        age = result.get("age")
                        gender = result.get("gender")

                        # 获取种族信息
                        race = result.get("race", "asian")  # 默认设为asian
                        race_zh = result.get("race_zh", "亚裔")  # 默认设为亚裔

                        logger.info(
                            f"DeepFace分析结果: age={age}, gender={gender}, race={race}, race_zh={race_zh}"
                        )

                        # 确保种族信息有值，如果没有直接设置为默认值
                        if not race:
                            logger.warning("DeepFace分析没有返回race信息，使用默认值")
                            race = "asian"

                        if not race_zh:
                            logger.warning("DeepFace分析没有返回race_zh信息，尝试映射")
                            # 尝试从race映射
                            race_map = {
                                "asian": "亚裔",
                                "white": "白人",
                                "black": "黑人",
                                "indian": "印度裔",
                                "middle eastern": "中东",
                                "latino hispanic": "拉丁裔",
                            }
                            race_zh = race_map.get(
                                race.lower(), "亚裔"
                            )  # 默认为亚裔而不是未知
                            logger.info(f"从race={race}映射到race_zh={race_zh}")
                    else:
                        logger.warning("DeepFace未返回有效结果，使用默认值")
                        age = None
                        gender = "unknown"
                        race = "asian"  # 默认设为asian而不是unknown
                        race_zh = "亚裔"  # 默认设为亚裔而不是未知
                except Exception as e:
                    logger.error(f"DeepFace分析失败: {str(e)}")
                    age = 28  # 默认年龄
                    gender = "unknown"  # 默认性别
                    logger.info(f"使用默认值: age={age}, gender={gender}")
            except Exception as e:
                logger.error(f"分析人脸年龄和性别时出错: {str(e)}")
                logger.info(f"使用默认值: age={age}, gender={gender}")

            # 使用YOLO检测人脸，以获取多个人脸的分析结果
            try:
                # 导入ModelRegistry
                from emotionai.core.ml.model_loader import ModelRegistry

                # 使用YOLOv8分析多个人脸
                yolo_detector = ModelRegistry.get("yolo_face")
                if yolo_detector is None:
                    logger.error("YOLO人脸检测器未注册")
                    raise ValueError("YOLO人脸检测器未注册")

                # 确保YOLO模型已加载
                if not yolo_detector.loaded:
                    yolo_detector.load()

                # 使用YOLO检测人脸
                logger.info("使用YOLO检测所有人脸")
                yolo_result = yolo_detector.predict(image)

                # 检查YOLO检测结果
                if not yolo_result.get("success", False):
                    logger.error(
                        f"YOLO人脸检测失败: {yolo_result.get('error', '未知错误')}"
                    )
                    raise ValueError(yolo_result.get("error", "YOLO人脸检测失败"))

                # 获取YOLO检测到的人脸
                yolo_faces = yolo_result.get("faces", [])
                faces_count = len(yolo_faces)
                logger.info(f"YOLO检测到{faces_count}个人脸")

                # 增强日志：记录YOLO参数和每个检测到的人脸详情
                logger.info(
                    f"YOLO模型参数: conf_threshold={yolo_detector.conf_threshold if hasattr(yolo_detector, 'conf_threshold') else '未知'}"
                )
                for _, face in enumerate(yolo_faces):
                    bbox = face.get("bbox", [0, 0, 0, 0])
                    conf = face.get("confidence", 0)
                    logger.info(f"YOLO人脸: 边界框={bbox}, 置信度={conf:.4f}")

                # 如果没有检测到人脸，使用单人脸分析结果
                if faces_count == 0:
                    logger.warning("YOLO未检测到人脸，使用单人脸分析结果")
                    faces_data = [
                        {
                            "face_id": str(analysis.id),
                            "face_number": 1,  # 添加人脸编号
                            "dominant_emotion": analysis.primary_emotion,
                            "confidence": analysis.confidence,
                            "emotions": emotions,
                            "bbox": [0, 0, 0, 0],
                            "age": int(age) if age is not None else None,
                            "gender": gender,
                            "race": race,
                            "race_zh": race_zh,
                        }
                    ]
                else:
                    # 分析每个人脸
                    from emotionai.core.ml.deepface_wrapper import DeepFaceWrapper
                    from emotionai.core.ml.face_attributes_ensemble import (
                        FaceAttributesEnsemble,
                    )

                    # 创建人脸属性集成实例
                    face_attributes_ensemble = FaceAttributesEnsemble()
                    # 创建DeepFace包装器作为备用
                    deepface_wrapper = DeepFaceWrapper()

                    faces_data = []
                    logger.info(f"开始处理{len(yolo_faces)}个YOLO检测到的人脸")
                    for i, face_bbox in enumerate(yolo_faces):
                        # 获取边界框
                        bbox = face_bbox.get("bbox", [0, 0, 0, 0])
                        x1, y1, x2, y2 = bbox

                        # 添加人脸编号
                        face_number = i + 1  # 人脸编号从1开始
                        logger.info(f"开始处理人脸 #{face_number}, 边界框: {bbox}")

                        # 获取图像尺寸
                        img_height, img_width = image.shape[:2]

                        # 验证和修正边界框坐标
                        original_bbox = [x1, y1, x2, y2]  # 保存原始边界框用于日志

                        # 计算边界框的宽度和高度
                        bbox_width = x2 - x1
                        bbox_height = y2 - y1

                        # 获取人脸检测的置信度
                        confidence = face.get("confidence", 0.0)

                        # 定义人脸大小阈值（像素）
                        small_face_threshold = 100  # 小于100像素宽度的视为小人脸
                        large_face_threshold = 300  # 大于300像素宽度的视为大人脸

                        # 根据人脸大小动态调整扩展比例
                        if bbox_width < small_face_threshold:
                            # 小人脸需要更大的扩展比例
                            expansion_x_ratio = 0.30  # 水平方向扩展30%
                            expansion_y_top_ratio = 0.45  # 上方扩展45%
                            expansion_y_bottom_ratio = 0.30  # 下方扩展30%
                            logger.info(
                                f"人脸 #{face_number} 检测到小人脸 (宽度={bbox_width}px)，使用更大的扩展比例"
                            )
                        elif bbox_width > large_face_threshold:
                            # 大人脸可以使用较小的扩展比例
                            expansion_x_ratio = 0.20  # 水平方向扩展20%
                            expansion_y_top_ratio = 0.35  # 上方扩展35%
                            expansion_y_bottom_ratio = 0.20  # 下方扩展20%
                            logger.info(
                                f"人脸 #{face_number} 检测到大人脸 (宽度={bbox_width}px)，使用较小的扩展比例"
                            )
                        else:
                            # 中等大小的人脸使用默认扩展比例
                            expansion_x_ratio = 0.25  # 水平方向扩展25%
                            expansion_y_top_ratio = 0.40  # 上方扩展40%
                            expansion_y_bottom_ratio = 0.25  # 下方扩展25%
                            logger.info(
                                f"人脸 #{face_number} 检测到中等大小人脸 (宽度={bbox_width}px)，使用默认扩展比例"
                            )

                        # 根据置信度微调扩展比例
                        if confidence < 0.7 and confidence > 0:
                            # 低置信度的检测结果，增加扩展比例以提高捕获完整人脸的可能性
                            expansion_x_ratio += 0.05
                            expansion_y_top_ratio += 0.05
                            expansion_y_bottom_ratio += 0.05
                            logger.info(
                                f"人脸 #{face_number} 检测置信度较低 ({confidence:.2f})，增加扩展比例"
                            )

                        # 计算实际扩展像素数
                        expansion_x = int(bbox_width * expansion_x_ratio)
                        expansion_y_top = int(bbox_height * expansion_y_top_ratio)
                        expansion_y_bottom = int(bbox_height * expansion_y_bottom_ratio)

                        # 记录扩展比例
                        logger.info(
                            f"人脸 #{face_number} 边界框扩展比例: 水平={expansion_x_ratio:.2f}, 上方={expansion_y_top_ratio:.2f}, 下方={expansion_y_bottom_ratio:.2f}"
                        )

                        # 应用不同方向的扩展
                        x1 = x1 - expansion_x  # 左侧扩展
                        y1 = y1 - expansion_y_top  # 上方扩展（额头区域）
                        x2 = x2 + expansion_x  # 右侧扩展
                        y2 = y2 + expansion_y_bottom  # 下方扩展（下巴区域）

                        # 记录扩展后的边界框
                        expanded_bbox = [x1, y1, x2, y2]
                        logger.info(
                            f"人脸 #{face_number} 边界框已扩展: 原始={original_bbox}, 扩展后={expanded_bbox}"
                        )

                        # 确保坐标不小于0
                        x1 = max(0, x1)
                        y1 = max(0, y1)

                        # 确保坐标不大于图像尺寸
                        x2 = min(img_width, x2)
                        y2 = min(img_height, y2)

                        # 确保宽度和高度大于0
                        if x2 <= x1 or y2 <= y1:
                            logger.warning(
                                f"人脸 #{face_number} 边界框无效，宽度或高度小于等于0: 原始={original_bbox}, 修正后=[{x1}, {y1}, {x2}, {y2}]"
                            )
                            continue

                        # 记录边界框修正信息
                        if original_bbox != [x1, y1, x2, y2]:
                            logger.info(
                                f"人脸 #{face_number} 边界框已修正: 原始={original_bbox}, 修正后=[{x1}, {y1}, {x2}, {y2}]"
                            )

                        # 裁剪人脸区域
                        face_img = image[y1:y2, x1:x2]
                        if face_img.size == 0:
                            logger.warning(
                                f"裁剪人脸区域失败: 修正后的边界框=[{x1}, {y1}, {x2}, {y2}]"
                            )
                            continue

                        logger.info(
                            f"人脸 #{face_number} 裁剪成功, 尺寸: {face_img.shape}"
                        )

                        # 分析每个人脸
                        try:
                            # 使用人脸属性集成分析人脸属性
                            rgb_face = cv2.cvtColor(face_img, cv2.COLOR_BGR2RGB)

                            # 先尝试使用人脸属性集成
                            try:
                                logger.info(
                                    f"使用人脸属性集成分析人脸 #{face_number} 的属性"
                                )
                                ensemble_result = face_attributes_ensemble.analyze(
                                    rgb_face
                                )

                                if ensemble_result:
                                    logger.info(
                                        f"人脸属性集成成功分析人脸 #{face_number}"
                                    )

                                    # 提取结果
                                    face_age = ensemble_result.get("age")
                                    face_gender = ensemble_result.get("gender")
                                    # 移除未使用的变量 gender_confidence
                                    face_race = ensemble_result.get("race")
                                    # 移除未使用的变量 race_confidence

                                    # 获取中文种族名称
                                    race_map = {
                                        "asian": "亚裔",
                                        "white": "白人",
                                        "black": "黑人",
                                        "indian": "印度裔",
                                        "middle eastern": "中东",
                                        "latino hispanic": "拉丁裔",
                                    }
                                    face_race_zh = (
                                        race_map.get(face_race.lower(), "亚裔")
                                        if face_race
                                        else "亚裔"
                                    )

                                    logger.info(
                                        f"人脸 #{face_number} 属性集成分析结果: age={face_age}, gender={face_gender}, race={face_race}, race_zh={face_race_zh}"
                                    )
                                else:
                                    # 如果人脸属性集成分析失败，回退到DeepFace
                                    logger.warning(
                                        f"人脸属性集成分析人脸 #{face_number} 失败，回退到DeepFace"
                                    )
                                    face_result = deepface_wrapper.analyze(rgb_face)

                                    # 提取结果
                                    face_age = face_result.get("age", age)
                                    face_gender = face_result.get("gender", gender)
                                    face_race = face_result.get("race", race)
                                    face_race_zh = face_result.get("race_zh", race_zh)
                                    logger.info(
                                        f"DeepFace分析人脸 #{face_number} 结果: age={face_age}, gender={face_gender}, race={face_race}, race_zh={face_race_zh}"
                                    )
                            except Exception as e:
                                # 如果人脸属性集成分析失败，回退到DeepFace
                                logger.error(
                                    f"人脸属性集成分析人脸 #{face_number} 出错: {str(e)}，回退到DeepFace"
                                )
                                face_result = deepface_wrapper.analyze(rgb_face)

                                # 提取结果
                                face_age = face_result.get("age", age)
                                face_gender = face_result.get("gender", gender)
                                face_race = face_result.get("race", race)
                                face_race_zh = face_result.get("race_zh", race_zh)
                                logger.info(
                                    f"DeepFace分析人脸 #{face_number} 结果: age={face_age}, gender={face_gender}, race={face_race}, race_zh={face_race_zh}"
                                )

                            # 分析情绪
                            if emotion_service.emotion_ensemble is not None:
                                logger.info(
                                    f"使用情绪模型融合器分析人脸 #{face_number}"
                                )
                                face_emotion_result = (
                                    await emotion_service.emotion_ensemble.analyze(
                                        face_img
                                    )
                                )
                            else:
                                # 使用单个模型
                                # 导入EmotionEnsemble
                                from emotionai.core.ml.emotion_ensemble import (
                                    EmotionEnsemble,
                                )

                                emotion_ensemble = EmotionEnsemble()
                                face_emotion_result = await emotion_ensemble.analyze(
                                    face_img
                                )

                            face_emotions = face_emotion_result.get(
                                "emotions", emotions
                            )
                            face_dominant_emotion = face_emotion_result.get(
                                "dominant_emotion", "neutral"
                            )
                            face_confidence = face_emotions.get(
                                face_dominant_emotion, 0.0
                            )

                            # 添加到人脸数据中
                            face_data = {
                                "face_id": str(uuid.uuid4()),  # 随机生成一个新的face_id
                                "face_number": face_number,  # 添加人脸编号
                                "dominant_emotion": face_dominant_emotion,
                                "confidence": face_confidence,
                                "emotions": face_emotions,
                                "bbox": bbox,
                                "age": (
                                    int(face_age) if face_age is not None else None
                                ),  # 确保年龄是整数
                                "gender": face_gender,
                                "race": face_race,
                                "race_zh": face_race_zh,
                            }
                            faces_data.append(face_data)
                            logger.info(
                                f"成功分析人脸 #{face_number}, 主要情绪: {face_dominant_emotion}, 添加到faces_data"
                            )

                        except Exception as e:
                            logger.error(f"分析人脸 #{face_number} 失败: {str(e)}")
                            # 使用默认值
                            face_data = {
                                "face_id": str(uuid.uuid4()),  # 随机生成一个新的face_id
                                "face_number": face_number,  # 添加人脸编号
                                "dominant_emotion": analysis.primary_emotion,
                                "confidence": analysis.confidence,
                                "emotions": emotions,
                                "bbox": bbox,
                                "age": int(age) if age is not None else None,
                                "gender": gender,
                                "race": race,
                                "race_zh": race_zh,
                            }
                            faces_data.append(face_data)
                            logger.info(
                                f"使用默认值添加人脸 #{face_number} 到faces_data"
                            )

                    # 确保所有YOLO检测到的人脸都被处理
                    if len(faces_data) < len(yolo_faces):
                        logger.warning(
                            f"处理的人脸数量({len(faces_data)})少于YOLO检测到的人脸数量({len(yolo_faces)})"
                        )
                        # 检查哪些人脸没被处理
                        processed_indices = {
                            face.get("face_number", 0) - 1 for face in faces_data
                        }  # 使用集合推导式
                        missing_indices = [
                            i
                            for i in range(len(yolo_faces))
                            if i not in processed_indices
                        ]
                        logger.warning(f"未处理的人脸索引: {missing_indices}")

                        # 为未处理的人脸添加默认数据
                        for idx in missing_indices:
                            face_bbox = yolo_faces[idx]
                            face_number = idx + 1
                            bbox = face_bbox.get("bbox", [0, 0, 0, 0])
                            face_data = {
                                "face_id": str(uuid.uuid4()),
                                "face_number": face_number,
                                "dominant_emotion": "neutral",
                                "confidence": 1.0,
                                "emotions": {
                                    "neutral": 1.0,
                                    "happy": 0.0,
                                    "sad": 0.0,
                                    "angry": 0.0,
                                    "fear": 0.0,
                                    "surprise": 0.0,
                                    "disgust": 0.0,
                                },
                                "bbox": bbox,
                                "age": int(age) if age is not None else None,
                                "gender": gender,
                                "race": race,
                                "race_zh": race_zh,
                            }
                            faces_data.append(face_data)
                            logger.info(f"添加默认数据给未处理的人脸 #{face_number}")

                # 如果没有成功处理任何人脸，使用单人脸分析结果
                if not faces_data:
                    logger.warning("没有成功处理任何人脸，使用单人脸分析结果")
                    faces_data = [
                        {
                            "face_id": str(analysis.id),
                            "face_number": 1,  # 添加人脸编号
                            "dominant_emotion": analysis.emotion,
                            "confidence": analysis.confidence,
                            "emotions": emotions,
                            "bbox": [0, 0, 0, 0],
                            "age": int(age) if age is not None else None,
                            "gender": gender,
                            "race": race,
                            "race_zh": race_zh,
                        }
                    ]

            except Exception as e:
                logger.error(f"多人脸分析失败: {str(e)}")
                import traceback

                logger.error(f"错误详情: {traceback.format_exc()}")
                # 使用单人脸分析结果
                faces_data = [
                    {
                        "face_id": str(analysis.id),
                        "face_number": 1,  # 添加人脸编号
                        "dominant_emotion": analysis.primary_emotion,
                        "confidence": analysis.confidence,
                        "emotions": emotions,
                        "bbox": [0, 0, 0, 0],
                        "age": int(age) if age is not None else None,
                        "gender": gender,
                        "race": race,
                        "race_zh": race_zh,
                    }
                ]
                faces_count = 1

            # 如果是多人脸，计算群体统计信息
            group_analysis = None
            if len(faces_data) > 1:
                # 使用情绪服务的_calculate_group_statistics方法计算群体统计信息
                group_analysis = emotion_service._calculate_group_statistics(faces_data)
                logger.info(f"群体分析结果: {group_analysis}")

            # 返回结果
            # 构建返回给前端的 emotions 字典，只包含排序后的主要、次要、第三情绪
            # 以确保前端实时展示与数据库存储的层级一致
            final_emotions_for_response = {}
            if (
                primary_emotion_data[0] is not None
                and primary_emotion_data[1] is not None
            ):
                final_emotions_for_response[primary_emotion_data[0]] = float(
                    primary_emotion_data[1]
                )
            if (
                secondary_emotion_data[0] is not None
                and secondary_emotion_data[1] is not None
            ):
                final_emotions_for_response[secondary_emotion_data[0]] = float(
                    secondary_emotion_data[1]
                )
            if third_emotion_data[0] is not None and third_emotion_data[1] is not None:
                final_emotions_for_response[third_emotion_data[0]] = float(
                    third_emotion_data[1]
                )

            # 如果 faces_data 为空（例如单人脸且未进入YOLO流程），需要构建它
            if not faces_data and analysis:
                faces_data = [
                    {
                        "face_id": str(analysis.id),  # 或者使用一个新的uuid
                        "face_number": 1,
                        "dominant_emotion": analysis.primary_emotion,
                        "confidence": analysis.confidence1,
                        "emotions": final_emotions_for_response,  # 使用排序后的情绪
                        "bbox": (
                            bbox if "bbox" in locals() else [0, 0, 0, 0]
                        ),  # 从 detection_result 获取
                        "age": int(age) if age is not None else None,
                        "gender": gender,
                        "race": race,
                        "race_zh": race_zh,
                        "landmarks": (
                            detection_result.get("landmarks", [])
                            if "detection_result" in locals()
                            else []
                        ),
                        "landmarks_count": (
                            len(detection_result.get("landmarks", []))
                            if "detection_result" in locals()
                            else 0
                        ),
                    }
                ]
            elif faces_data:  # 如果 faces_data 已存在 (多人脸情况)
                # 更新 faces_data 中每个 face 的 emotions 字段
                for face_d in faces_data:
                    # 假设每个face的详细情绪分布也应该被限制为排序后的前三（或与该face dominant emotion相关的）
                    # 为了简化，这里我们让所有face的emotions字段都与整体排序后的top3一致
                    # 或者，如果每个face有自己的emotions_details，应该基于那个排序
                    # 当前后端逻辑似乎是为整个图片确定一个 primary/secondary/third
                    # 如果是单人脸，faces_data[0].emotions 将使用 final_emotions_for_response
                    if len(faces_data) == 1:
                        faces_data[0]["emotions"] = final_emotions_for_response
                        faces_data[0]["dominant_emotion"] = analysis.primary_emotion
                        faces_data[0]["confidence"] = analysis.confidence1

            # 构建 faces_data，确保其符合 FaceEmotionResult schema
            # 这里的 `emotions` 是从模型/ensemble直接获取的完整情绪详情
            # `primary_emotion_data`, `secondary_emotion_data`, `third_emotion_data` 是基于这个完整详情排序得到的

            processed_faces_data = []
            if faces_data:  # 通常是多人脸，每个 face 都有自己的分析结果
                for i, face_d_original in enumerate(faces_data):
                    # face_d_original 应该包含了该人脸的完整 emotions 字典
                    face_specific_emotions = face_d_original.get("emotions", {})
                    valid_face_emotions_for_sorting = {
                        k: v
                        for k, v in face_specific_emotions.items()
                        if isinstance(v, (int, float))
                    }
                    sorted_face_emotions_list = sorted(
                        valid_face_emotions_for_sorting.items(),
                        key=lambda item: item[1],
                        reverse=True,
                    )

                    p_emo = (
                        sorted_face_emotions_list[0]
                        if len(sorted_face_emotions_list) > 0
                        else (
                            face_d_original.get("dominant_emotion", "neutral"),
                            face_d_original.get("confidence", 0.0),
                        )
                    )
                    s_emo = (
                        sorted_face_emotions_list[1]
                        if len(sorted_face_emotions_list) > 1
                        else (None, None)
                    )
                    t_emo = (
                        sorted_face_emotions_list[2]
                        if len(sorted_face_emotions_list) > 2
                        else (None, None)
                    )

                    processed_face = {
                        "face_id": face_d_original.get("face_id", str(uuid.uuid4())),
                        "face_number": face_d_original.get("face_number", i + 1),
                        "bbox": face_d_original.get("bbox", [0, 0, 0, 0]),
                        "confidence": face_d_original.get(
                            "confidence", 0.0
                        ),  # 人脸检测置信度
                        "dominant_emotion": p_emo[0],
                        "primary_confidence": (
                            float(p_emo[1]) if p_emo[1] is not None else 0.0
                        ),
                        "secondary_dominant_emotion": s_emo[0],
                        "secondary_confidence": (
                            float(s_emo[1]) if s_emo[1] is not None else None
                        ),
                        "third_dominant_emotion": t_emo[0],
                        "third_confidence": (
                            float(t_emo[1]) if t_emo[1] is not None else None
                        ),
                        "emotions": face_specific_emotions,  # 完整的原始情绪详情
                        "landmarks": face_d_original.get("landmarks"),
                        "landmarks_count": face_d_original.get("landmarks_count"),
                        "age": face_d_original.get("age"),
                        "gender": face_d_original.get("gender"),
                        "race": face_d_original.get("race"),
                        "race_zh": face_d_original.get("race_zh"),
                    }
                    processed_faces_data.append(processed_face)
            elif (
                analysis
            ):  # 单人脸情况，从 analysis 对象（已保存排序结果）和原始 emotions 构建
                processed_faces_data = [
                    {
                        "face_id": str(analysis.id),
                        "face_number": 1,
                        "bbox": (
                            bbox if "bbox" in locals() else [0, 0, 0, 0]
                        ),  # 来自 detection_result
                        "confidence": (
                            detection_result.get("faces")[0].get("confidence")
                            if "detection_result" in locals()
                            and detection_result.get("faces")
                            else 0.0
                        ),
                        "dominant_emotion": analysis.primary_emotion,  # 已排序的主要情绪
                        "primary_confidence": analysis.confidence1,  # 已排序的主要情绪置信度
                        "secondary_dominant_emotion": analysis.secondary_emotion,
                        "secondary_confidence": analysis.confidence2,
                        "third_dominant_emotion": analysis.third_emotion,
                        "third_confidence": analysis.confidence3,
                        "emotions": emotions,  # 完整的原始情绪详情 (来自 emotion_result.get("emotions", {}))
                        "landmarks": (
                            detection_result.get("landmarks", [])
                            if "detection_result" in locals()
                            else []
                        ),
                        "landmarks_count": (
                            len(detection_result.get("landmarks", []))
                            if "detection_result" in locals()
                            else 0
                        ),
                        "age": int(age) if age is not None else None,
                        "gender": gender,
                        "race": race,
                        "race_zh": race_zh,
                    }
                ]

            # 构建最终的API响应
            response_payload = {
                "success": True,
                "image_id": str(analysis.id),
                "id": str(analysis.id),
                "faces": processed_faces_data,
                "faces_count": len(processed_faces_data),
                "created_at": analysis.created_at.isoformat(),
                "updated_at": analysis.updated_at.isoformat(),
                "original_image": f"/api/v1/emotion/images/{year_month}/{day}/{image_id}.jpg",
                "marked_image": f"/api/v1/emotion/images/{year_month}/{day}/{image_id}_marked.jpg?regenerate=true",
                "group_analysis": group_analysis,
                "processing_time": (
                    detection_result.get("processing_time")
                    if "detection_result" in locals()
                    else None
                ),
            }

            # 填充顶层 emotion, confidence_overall, age, gender, race, race_zh (通常基于第一个人脸)
            if processed_faces_data:
                first_face = processed_faces_data[0]
                response_payload["emotion"] = first_face["dominant_emotion"]
                response_payload["confidence_overall"] = first_face[
                    "primary_confidence"
                ]
                response_payload["age"] = first_face["age"]
                response_payload["gender"] = first_face["gender"]
                response_payload["race"] = first_face["race"]
                response_payload["race_zh"] = first_face["race_zh"]
            else:  # 如果没有检测到人脸，也提供默认值
                response_payload["emotion"] = "neutral"
                response_payload["confidence_overall"] = 0.0
                response_payload["age"] = None
                response_payload["gender"] = "unknown"
                response_payload["race"] = "unknown"
                response_payload["race_zh"] = "未知"

            # 增强日志：记录详细的返回数据
            logger.info(
                f"YOLO原始检测到的人脸数量: {faces_count}, 最终处理后的人脸数量: {len(faces_data)}"
            )
            logger.info(
                f"最终faces_data中每个人脸的face_number: {[face.get('face_number') for face in processed_faces_data]}"  # 使用 processed_faces_data
            )
            logger.info(
                f"最终返回的faces_count: {response_payload['faces_count']}"
            )  # 使用 response_payload

            # 输出详细日志，便于调试
            logger.info(
                f"返回给前端的完整结果: {response_payload}"
            )  # 使用 response_payload
            logger.info(
                f"检测到的人脸数量: {len(processed_faces_data)}, faces_count: {response_payload['faces_count']}"  # 使用 processed_faces_data 和 response_payload
            )

            return response_payload  # 返回 response_payload
        else:
            # 如果找不到图像，返回错误
            logger.error(f"未找到原始图像: {original_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到原始图像: {image_id}",
            )
    except Exception as e:
        logger.error(f"分析情绪时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析情绪时出错: {str(e)}",
        )


@router.post("/analyze-yolov8", response_model=EmotionResponse)
async def analyze_with_yolov8(
    request: YOLOv8FaceRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    使用 YOLOv8 分析图片中的人脸和情绪
    """
    try:
        # 使用用户ID
        user_id = str(current_user.id)

        # 分析情绪
        result = await emotion_service.analyze_image_with_yolov8(
            image_data=request.image,
            confidence_threshold=request.confidence_threshold,
            user_id=user_id if request.save_result else None,
            db=db if request.save_result else None,
        )

        if not result.get("success", False):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "分析情绪失败"),
            )

        # 移除年龄分析判断的相关代码
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析情绪时出错: {str(e)}",
        )


@router.post("/upload-yolov8", response_model=EmotionResponse)
async def upload_and_analyze_with_yolov8(
    file: UploadFile = File(...),
    confidence_threshold: float = Form(0.5),
    save_result: bool = Form(True),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    上传并使用 YOLOv8 分析图片中的人脸和情绪

    使用YOLOv8检测人脸，然后分析每个人脸的情绪、年龄和性别。

    参数:
        file: 上传的图片文件
        confidence_threshold: 人脸检测的置信度阈值，默认为0.5
        save_result: 是否保存分析结果，默认为True

    返回:
        情绪分析结果
    """
    try:
        # 读取上传的文件
        contents = await file.read()

        # 将文件内容转换为Base64编码
        import base64

        image_data = base64.b64encode(contents).decode("utf-8")

        # 使用用户ID
        user_id = str(current_user.id)

        # 打印请求参数
        logger.info(
            f"上传图片请求参数: confidence_threshold={confidence_threshold}, save_result={save_result}"
        )

        # 使用YOLOv8分析图像
        result = await emotion_service.analyze_image_with_yolov8(
            image_data=image_data,
            confidence_threshold=confidence_threshold,
            user_id=user_id if save_result else None,
            db=db if save_result else None,
        )

        # 打印响应结果
        logger.info(
            f"YOLOv8分析结果: success={result.get('success', False)}, faces_count={result.get('faces_count', 0)}"
        )

        if not result.get("success", False):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "分析图像失败"),
            )

        # 移除年龄分析判断的相关代码
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析情绪时出错: {str(e)}",
        )


@router.post("/analyze-ensemble/{image_id}")
async def analyze_emotion_ensemble(
    image_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    使用集成学习分析情绪，并返回详细的模型预测结果

    参数:
        image_id: 图片ID

    返回:
        包含各模型预测结果和融合结果的详细信息
    """
    try:
        # 使用用户ID
        user_id = str(current_user.id)

        # 记录请求参数
        logger.info(f"集成学习分析情绪请求参数: image_id={image_id}, user_id={user_id}")

        # 检查图片是否存在
        import glob

        from emotionai.core.config.paths import ORIGINAL_DIR

        # 尝试多种方式查找原始图像
        # 1. 首先尝试在当前日期目录中查找
        today = datetime.now()
        year_month = today.strftime("%Y-%m")
        day = today.strftime("%d")

        # 构建原始图像路径
        original_path = os.path.join(ORIGINAL_DIR, year_month, day, f"{image_id}.jpg")
        logger.info(f"尝试在当前日期目录中查找原始图像: {original_path}")

        # 2. 如果找不到，尝试在所有日期目录中查找
        if not os.path.exists(original_path):
            logger.info("在当前日期目录中未找到原始图像，尝试在所有日期目录中查找")
            possible_paths = glob.glob(
                os.path.join(ORIGINAL_DIR, "**", f"{image_id}.jpg"), recursive=True
            )
            if possible_paths:
                original_path = possible_paths[0]
                logger.info(f"在日期目录中找到原始图像: {original_path}")

        # 3. 如果仍然找不到，尝试查找最新上传的图片
        if not os.path.exists(original_path):
            logger.info("在所有日期目录中未找到原始图像，尝试查找最新上传的图片")
            # 查找最新上传的图片
            all_images = glob.glob(
                os.path.join(ORIGINAL_DIR, "**", "*.jpg"), recursive=True
            )
            if all_images:
                # 按修改时间排序
                all_images.sort(key=os.path.getmtime, reverse=True)
                # 使用最新的图片
                original_path = all_images[0]
                logger.info(f"使用最新上传的图片: {original_path}")
                # 提取图片ID
                image_id = os.path.basename(original_path).replace(".jpg", "")

        # 如果找到了图像，直接读取并分析
        if os.path.exists(original_path):
            logger.info(f"找到原始图像: {original_path}")

            # 读取图像
            import cv2

            image = cv2.imread(str(original_path))

            if image is None or image.size == 0:
                logger.error(f"读取图像失败: {original_path}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"读取图像失败: {original_path}",
                )

            # 检测人脸特征点
            detection_result = await emotion_service.detect_face_features(
                image, detector_type=emotion_service.default_detector
            )

            if not detection_result.get("success", False):
                logger.error(
                    f"人脸特征点检测失败: {detection_result.get('error', '未知错误')}"
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=detection_result.get("error", "人脸特征点检测失败"),
                )

            # 获取人脸边界框
            bbox = detection_result.get("bbox", [0, 0, 0, 0])
            x1, y1, x2, y2 = bbox

            # 裁剪人脸区域
            face_image = image[y1:y2, x1:x2]

            if face_image.size == 0:
                logger.error("裁剪人脸区域失败")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="裁剪人脸区域失败",
                )

            # 使用新版情绪模型融合器分析（真正的集成学习）
            if emotion_service.emotion_ensemble is not None:
                logger.info("使用新版情绪模型融合器进行集成学习分析")
                emotion_result = await emotion_service.emotion_ensemble.analyze(
                    face_image
                )

                # 返回详细的模型预测结果
                return {
                    "success": True,
                    "image_id": image_id,
                    "dominant_emotion": emotion_result.get(
                        "dominant_emotion", "neutral"
                    ),
                    "emotions": emotion_result.get("emotions", {}),
                    "model_results": emotion_result.get("model_results", {}),
                    "weights": emotion_service.emotion_ensemble.get_weights(),
                    "original_image": f"/api/v1/emotion/images/{year_month}/{day}/{image_id}.jpg",
                    "marked_image": f"/api/v1/emotion/images/{year_month}/{day}/{image_id}_marked.jpg?regenerate=true",
                }
            else:
                logger.error("新版情绪模型融合器未初始化")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="新版情绪模型融合器未初始化",
                )
        else:
            # 如果找不到图像，返回错误
            logger.error(f"未找到原始图像: {original_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到原始图像: {image_id}",
            )
    except Exception as e:
        logger.error(f"集成学习分析情绪时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"集成学习分析情绪时出错: {str(e)}",
        )


@router.post("/update-weights", response_model=ModelWeightsResponse)
async def update_model_weights(
    request: ModelWeightsRequest,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    更新情绪模型融合器的权重

    参数:
        request: 包含各模型权重的请求对象

    返回:
        更新后的权重信息
    """
    try:
        # 检查用户权限（可选）
        # 这里可以添加权限检查，例如只允许管理员更新权重

        # 构建权重字典
        weights = {
            "swin_fer": request.swin_fer,
            "hf_emotion": request.hf_emotion,
            "cnn_emotion": request.cnn_emotion,
        }

        # 更新权重
        result = await emotion_service.update_ensemble_weights(weights)

        if not result.get("success", False):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "更新权重失败"),
            )

        return result
    except Exception as e:
        logger.error(f"更新模型权重时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新模型权重时出错: {str(e)}",
        )


@router.get("/history", response_model=list)
async def get_emotion_history(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
) -> Any:
    """
    获取用户的情绪分析历史记录
    """
    try:
        # 使用用户ID
        user_id = str(current_user.id)

        # 获取历史记录
        history = await emotion_service.get_user_history(user_id, db, skip, limit)

        return history
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取情绪分析历史记录时出错: {str(e)}",
        )


@router.get("/images/{image_path:path}")
async def get_image(
    image_path: str,
    thumbnail: bool = Query(False, description="是否返回缩略图"),
    regenerate: bool = Query(
        True, description="是否重新生成特征点图像"
    ),  # 默认为True，每次都重新生成特征点图像
) -> Any:
    """
    获取图像

    支持多种路径格式:
    1. 旧格式: /api/v1/emotion/images/{uuid}.jpg
    2. 新格式: /api/v1/emotion/images/{year-month}/{day}/{uuid}.jpg
    3. 数据集样本: /api/v1/emotion/images/Training_*.jpg

    参数:
        thumbnail: 是否返回缩略图，默认为 False
        regenerate: 是否重新生成特征点图像，默认为 False
    """
    # 记录请求的图像路径
    logger.info(
        f"请求图像路径: {image_path}, 缩略图: {thumbnail}, 重新生成: {regenerate}"
    )
    try:
        # 先检查是否是数据集样本图片
        if image_path.startswith("Training_"):
            # 搜索所有可能的路径
            import glob

            # 获取项目根目录
            project_root = os.path.abspath(
                os.path.join(os.path.dirname(__file__), "../../../../../..")
            )
            logger.info(f"项目根目录: {project_root}")

            # 先检查数据集样本目录
            emotion_categories = [
                "happy",
                "sad",
                "angry",
                "fear",
                "disgust",
                "surprise",
                "neutral",
            ]

            # 尝试不同的目录结构
            base_paths = [
                # 项目根目录下的data目录
                os.path.join(project_root, "data", "emotion_samples"),
                # 当前目录下的data目录
                os.path.join("data", "emotion_samples"),
                # 绝对路径
                "/data/emotion_samples",
            ]

            # 尝试每个基础路径
            for base_path in base_paths:
                logger.info(f"检查基础路径: {base_path}")

                # 尝试每个情绪类别
                for category in emotion_categories:
                    full_path = os.path.join(base_path, category, image_path)
                    logger.info(f"检查路径: {full_path}")

                    if os.path.exists(full_path) and os.path.isfile(full_path):
                        logger.info(f"找到数据集样本图片: {full_path}")
                        return FileResponse(full_path)

            # 如果还是找不到，尝试使用glob搜索
            search_patterns = [
                os.path.join(project_root, "data", "emotion_samples", "*", image_path),
                os.path.join("data", "emotion_samples", "*", image_path),
                os.path.join("**", "emotion_samples", "*", image_path),
            ]

            for pattern in search_patterns:
                logger.info(f"使用模式搜索: {pattern}")
                possible_paths = glob.glob(pattern, recursive=True)
                if possible_paths:
                    logger.info(f"找到数据集样本图片: {possible_paths[0]}")
                    return FileResponse(possible_paths[0])

            # 如果还是找不到，尝试在根目录下搜索
            logger.info("在整个项目中搜索图片...")
            root_search = glob.glob(
                os.path.join(project_root, "**", image_path), recursive=True
            )
            if root_search:
                logger.info(f"在根目录下找到图片: {root_search[0]}")
                return FileResponse(root_search[0])

            # 如果还是找不到，返回404
            logger.warning(f"未找到数据集样本图片: {image_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"数据集样本图片不存在: {image_path}",
            )

        # 确定基础目录
        # 检查是否需要重新生成特征点图像
        if regenerate and "_marked" in image_path:
            logger.info("请求重新生成特征点图像")

            # 提取图像ID
            import re

            match = re.search(r"([a-f0-9-]{36})_marked\.jpg$", image_path)
            if match:
                image_id = match.group(1)
                logger.info(f"提取到图像ID: {image_id}")

                # 查找原始图像
                # 使用已导入的ORIGINAL_DIR

                # 尝试查找原始图像
                original_path = None

                # 如果是新格式路径，包含日期目录
                if "/" in image_path:
                    # 构建原始图像路径
                    original_image_path = image_path.replace("_marked.jpg", ".jpg")
                    original_path_full = os.path.join(ORIGINAL_DIR, original_image_path)
                    if os.path.exists(original_path_full):
                        original_path = original_path_full
                        logger.info(f"找到原始图像(新格式): {original_path}")

                # 如果没有找到，尝试搜索所有日期目录
                if not original_path:
                    import glob

                    possible_paths = glob.glob(
                        os.path.join(ORIGINAL_DIR, "**", f"{image_id}.jpg"),
                        recursive=True,
                    )
                    if possible_paths:
                        original_path = possible_paths[0]
                        logger.info(f"找到原始图像(搜索): {original_path}")

                # 如果找到原始图像，重新生成特征点图像
                if original_path:
                    # 读取原始图像
                    # 使用已导入的cv2、np和ModelRegistry

                    # 读取图像
                    image = cv2.imread(original_path)

                    if image is not None and image.size > 0:
                        # 使用统一的FaceDetector类
                        from emotionai.core.ml.face_detection import FaceDetector

                        try:
                            # 获取FaceDetector单例实例，设置不同的颜色
                            detector = FaceDetector.get_instance(
                                detector_type="yolo_mediapipe",
                                show_bounding_boxes=False,  # 禁用MediaPipe边界框，使用YOLO边界框
                                use_yolo=True,  # 启用YOLO支持
                                connection_color=(0, 255, 255),  # 青色
                                bbox_color=(0, 255, 0),  # 绿色
                                left_eye_color=(255, 0, 0),  # 蓝色
                                right_eye_color=(255, 0, 255),  # 紫色
                            )

                            # 使用优化后的方法，同时检测人脸和生成标记图像，避免重复调用YOLO模型
                            logger.info("使用统一的FaceDetector类生成特征点图像")
                            try:
                                # 确保图像数据类型正确
                                image = image.astype(np.uint8)

                                # 首先使用YOLO检测人脸
                                # 这里我们需要获取YOLO检测到的人脸信息
                                # 由于我们没有直接访问YOLO检测器的方法，我们需要使用ModelRegistry
                                # ModelRegistry已经在文件顶部导入

                                yolo_detector = ModelRegistry.get("yolo_face")
                                if yolo_detector is None:
                                    logger.warning(
                                        "YOLO人脸检测器未注册，使用MediaPipe直接检测"
                                    )
                                    # 使用优化后的方法，同时检测人脸和生成标记图像
                                    faces, marked_image = (
                                        detector.detect_faces_and_generate_image(image)
                                    )
                                else:
                                    # 确保YOLO模型已加载
                                    if not yolo_detector.loaded:
                                        yolo_detector.load()

                                    # 使用YOLO检测人脸
                                    logger.info("使用YOLO检测人脸")
                                    yolo_result = yolo_detector.predict(image)

                                    # 检查YOLO检测结果
                                    if not yolo_result.get(
                                        "success", False
                                    ) or not yolo_result.get("faces", []):
                                        logger.warning(
                                            "YOLO未检测到人脸，使用MediaPipe直接检测"
                                        )
                                        # 使用优化后的方法，同时检测人脸和生成标记图像
                                        faces, marked_image = (
                                            detector.detect_faces_and_generate_image(
                                                image
                                            )
                                        )
                                    else:
                                        # 获取YOLO检测到的人脸
                                        yolo_faces = yolo_result.get("faces", [])
                                        logger.info(
                                            f"YOLO检测到{len(yolo_faces)}个人脸"
                                        )

                                        # 打印YOLO检测到的人脸信息，用于调试
                                        for i, face in enumerate(yolo_faces):
                                            logger.debug(
                                                f"YOLO人脸 {i+1}: 边界框={face.get('bbox')}, 置信度={face.get('confidence')}"
                                            )

                                        # 使用YOLO检测到的人脸信息，让MediaPipe在这些区域上绘制面部网格
                                        faces, marked_image = (
                                            detector.process_with_yolo_faces(
                                                image, yolo_faces
                                            )
                                        )

                                # 如果没有检测到人脸，添加提示文字
                                if not faces:
                                    logger.warning("未检测到人脸，添加提示文字")
                                    # 创建标记图像的副本
                                    marked_image = image.copy()
                                    # 添加提示文字
                                    cv2.putText(
                                        marked_image,
                                        "No face detected",
                                        (50, 50),
                                        cv2.FONT_HERSHEY_SIMPLEX,
                                        1,
                                        (0, 0, 255),
                                        2,
                                    )
                            except Exception as e:
                                logger.error(f"检测人脸并生成标记图像时出错: {str(e)}")
                                # 创建标记图像的副本
                                marked_image = image.copy()
                                # 添加错误提示文字
                                cv2.putText(
                                    marked_image,
                                    f"Error: {str(e)}",
                                    (50, 50),
                                    cv2.FONT_HERSHEY_SIMPLEX,
                                    1,
                                    (0, 0, 255),
                                    2,
                                )

                            if marked_image is not None:
                                # 保存到临时文件
                                import tempfile

                                # 使用上下文管理器处理临时文件
                                with tempfile.NamedTemporaryFile(
                                    delete=False, suffix=".jpg"
                                ) as temp_file:
                                    temp_path = temp_file.name

                                # 在上下文管理器外写入文件
                                cv2.imwrite(temp_path, marked_image)

                                # 返回临时文件
                                logger.info(f"返回重新生成的特征点图像: {temp_path}")
                                return FileResponse(temp_path)
                            else:
                                logger.warning("FaceDetector的特征点绘制功能返回了None")
                        except Exception as e:
                            logger.error(f"使用FaceDetector绘制特征点时出错: {str(e)}")

                            # 尝试使用ModelRegistry获取检测器
                            try:
                                # 获取检测器模型
                                detector = ModelRegistry.get("yolo_mediapipe")

                                if detector and hasattr(
                                    detector, "get_feature_detection_image"
                                ):
                                    # 确保模型已加载
                                    if not detector.loaded:
                                        logger.info("检测器未加载，正在加载...")
                                        detector.load()

                                    # 使用检测器的特征点绘制功能生成网格图像
                                    logger.info(
                                        "使用ModelRegistry获取的检测器生成特征点图像"
                                    )
                                    marked_image = detector.get_feature_detection_image(
                                        image
                                    )

                                    if marked_image is not None:
                                        # 保存到临时文件
                                        import tempfile

                                        # 使用上下文管理器处理临时文件
                                        with tempfile.NamedTemporaryFile(
                                            delete=False, suffix=".jpg"
                                        ) as temp_file:
                                            temp_path = temp_file.name

                                        # 在上下文管理器外写入文件
                                        cv2.imwrite(temp_path, marked_image)

                                        # 返回临时文件
                                        logger.info(
                                            f"返回重新生成的特征点图像: {temp_path}"
                                        )
                                        return FileResponse(temp_path)
                                    else:
                                        logger.warning(
                                            "检测器的特征点绘制功能返回了None"
                                        )
                                else:
                                    logger.warning("检测器不存在或没有特征点绘制功能")
                            except Exception as e:
                                logger.error(
                                    f"使用ModelRegistry获取的检测器绘制特征点时出错: {str(e)}"
                                )
                    else:
                        logger.warning(f"无法读取原始图像: {original_path}")
                else:
                    logger.warning(f"未找到原始图像: {image_id}")
            else:
                logger.warning(f"无法从路径提取图像ID: {image_path}")

        # 如果不需要重新生成或重新生成失败，使用常规方式获取图像
        # 检查图片路径是否包含 "_marked"，如果包含则使用 marked 目录，否则使用 original 目录
        if "_marked" in image_path:
            base_dir = MARKED_DIR
            logger.info(f"图片路径包含_marked，使用marked目录: {base_dir}")
        else:
            # 不再使用缩略图目录，直接使用原始图像目录
            # 前端已经压缩了图像，所以原始图像目录中的图像已经是压缩过的
            base_dir = ORIGINAL_DIR
            logger.info(f"图片路径不包含_marked，使用原始图像目录: {base_dir}")
            # 忽略thumbnail参数，因为我们不再使用缩略图

        # 处理路径
        if "/" in image_path:  # 新格式路径，包含日期目录
            # 直接使用完整路径
            full_path = os.path.join(base_dir, image_path)
            logger.info(f"新格式路径: {full_path}")

            # 确保目录存在
            os.makedirs(os.path.dirname(full_path), exist_ok=True)

            # 如果请求的图片不存在，尝试使用其他目录
            if not os.path.exists(full_path):
                logger.warning(f"图片不存在: {full_path}")

                # 如果找不到原始图像，尝试使用标记图像
                marked_path = os.path.join(MARKED_DIR, image_path)
                logger.info(f"尝试使用标记图像: {marked_path}")

                # 确保目录存在
                os.makedirs(os.path.dirname(marked_path), exist_ok=True)

                if os.path.exists(marked_path):
                    full_path = marked_path
                    logger.info(f"使用标记图像: {full_path}")
                # 如果是原始图片请求，尝试在原始图片目录中查找
                elif "_marked" not in image_path:
                    original_path = os.path.join(ORIGINAL_DIR, image_path)
                    logger.info(f"尝试使用原始图像: {original_path}")

                    # 确保目录存在
                    os.makedirs(os.path.dirname(original_path), exist_ok=True)

                    if os.path.exists(original_path):
                        full_path = original_path
                        logger.info(f"使用原始图像: {full_path}")
                    else:
                        # 如果仍然找不到，尝试使用示例图片
                        logger.warning("无法找到图片，尝试使用示例图片")

                        # 使用示例图片
                        example_path = os.path.join(
                            os.path.dirname(os.path.dirname(__file__)),
                            "../../../../frontend/public/examples/original_example.jpg",
                        )

                        if os.path.exists(example_path):
                            logger.info(f"使用示例图片: {example_path}")
                            return FileResponse(example_path)
        else:  # 旧格式路径，只有文件名
            # 兼容旧版路径
            full_path = os.path.join(base_dir, image_path)
            logger.info(f"旧格式路径: {full_path}")

            # 确保目录存在
            os.makedirs(os.path.dirname(full_path), exist_ok=True)

            # 如果路径不存在，尝试搜索所有日期目录
            if not os.path.exists(full_path):
                # 搜索所有日期目录
                import glob

                # 在当前目录类型中搜索
                possible_paths = glob.glob(os.path.join(base_dir, "*", "*", image_path))
                if possible_paths:
                    full_path = possible_paths[0]  # 使用找到的第一个路径
                    logger.info(f"在日期目录中找到图片: {full_path}")
                # 如果找不到原始图像，尝试使用标记图像
                else:
                    marked_paths = glob.glob(
                        os.path.join(MARKED_DIR, "*", "*", image_path)
                    )
                    if marked_paths:
                        full_path = marked_paths[0]
                        logger.info(f"在日期目录中找到标记图像: {full_path}")
                    # 如果是原始图片请求且找不到标记图像，尝试在原始图片目录中查找
                    elif "_marked" not in image_path:
                        original_paths = glob.glob(
                            os.path.join(ORIGINAL_DIR, "*", "*", image_path)
                        )
                        if original_paths:
                            full_path = original_paths[0]
                            logger.info(f"在日期目录中找到原始图像: {full_path}")

        # 检查文件是否存在
        if not os.path.exists(full_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="图像不存在",
            )

        # 返回图像文件
        return FileResponse(full_path)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取图像时出错: {str(e)}",
        )


@router.post("/multimodal-analysis")
async def multimodal_analysis(
    request: Request,
    image_id: str = Form(...),
    emotion_data: str = Form(None),  # 添加情绪分析数据参数，可选
    current_user: User = Depends(get_current_user),
) -> StreamingResponse:
    """
    使用多模态大模型分析图片和情绪数据

    Args:
        image_id: 图片ID
        emotion_data: JSON格式的情绪分析数据（可选）
        current_user: 当前用户

    Returns:
        流式响应，包含多模态大模型的分析结果
    """
    try:
        # 获取用户代理
        user_agent = request.headers.get("user-agent", "")
        logger.info(f"多模态分析请求 - User-Agent: {user_agent}")

        # 记录请求参数
        logger.info(
            f"多模态分析请求参数: image_id={image_id}, user_id={current_user.id}"
        )

        # 检查图片是否存在
        import glob

        # 尝试多种方式查找原始图像
        # 1. 首先尝试在当前日期目录中查找
        today = datetime.now()
        year_month = today.strftime("%Y-%m")
        day = today.strftime("%d")

        # 构建原始图像路径
        original_path = os.path.join(ORIGINAL_DIR, year_month, day, f"{image_id}.jpg")
        logger.info(f"尝试在当前日期目录中查找原始图像: {original_path}")

        if not os.path.exists(original_path):
            # 2. 如果当前日期目录中没有找到，尝试在所有日期目录中查找
            logger.info("在当前日期目录中未找到原始图像，尝试在所有日期目录中查找")
            pattern = os.path.join(ORIGINAL_DIR, "*", "*", f"{image_id}.jpg")
            matches = glob.glob(pattern)

            if not matches:
                # 3. 如果还是没有找到，尝试在根目录中查找
                logger.info("在所有日期目录中未找到原始图像，尝试在根目录中查找")
                pattern = os.path.join(ORIGINAL_DIR, f"{image_id}.jpg")
                matches = glob.glob(pattern)

                if not matches:
                    # 如果还是没有找到，返回错误
                    logger.error(f"未找到图像ID为 {image_id} 的原始图像")
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"未找到图像ID为 {image_id} 的原始图像",
                    )

            # 使用找到的第一个匹配项
            original_path = matches[0]
            logger.info(f"找到原始图像: {original_path}")

        # 读取图像文件
        with open(original_path, "rb") as f:
            image_bytes = f.read()

        # 将图像转换为Base64编码
        image_base64 = base64.b64encode(image_bytes).decode("utf-8")

        # 构建提示词
        if emotion_data:
            try:
                # 解析情绪数据JSON
                emotion_json = json.loads(emotion_data)

                # 提取关键信息，避免传递过长的JSON
                main_emotion = emotion_json.get("emotion", "unknown")
                confidence = emotion_json.get("confidence", 0.0)
                age = emotion_json.get("age", "unknown")
                gender = emotion_json.get("gender", "unknown")
                race_zh = emotion_json.get("race_zh", "unknown")

                # 提取情绪分布（优先从faces数组中获取更详细的数据）
                emotions = {}
                faces = emotion_json.get("faces", [])
                if faces and len(faces) > 0:
                    # 使用第一个人脸的情绪数据
                    face_emotions = faces[0].get("emotions", {})
                    face_dominant = faces[0].get("dominant_emotion", main_emotion)
                    face_confidence = faces[0].get("confidence", confidence)
                    if face_emotions:
                        emotions = face_emotions
                        main_emotion = face_dominant
                        confidence = face_confidence  # 使用对应的置信度
                else:
                    # 回退到顶层情绪数据
                    emotions = emotion_json.get("emotions", {})

                emotion_summary = []
                if emotions:
                    # 按置信度排序，只取前3个
                    sorted_emotions = sorted(
                        emotions.items(), key=lambda x: x[1], reverse=True
                    )[:3]
                    for emotion, score in sorted_emotions:
                        emotion_summary.append(f"{emotion}: {score:.1%}")

                emotion_text = (
                    ", ".join(emotion_summary) if emotion_summary else "无详细数据"
                )

                # 确保数据不为空
                main_emotion = (
                    main_emotion
                    if main_emotion and main_emotion != "unknown"
                    else "Neutral"
                )
                age = age if age and age != "unknown" else "未知"
                gender = (
                    "男性"
                    if gender == "male"
                    else "女性" if gender == "female" else "未知"
                )
                race_zh = race_zh if race_zh and race_zh != "unknown" else "未知"

                # 构建简洁的提示词
                prompt = f"""好的，作为情绪分析专家，我将对照片进行分析并给出建议。

请首先独立分析照片中人物的情绪、性别、年龄和种族，给出你自己的判断。

然后，参考以下系统分析结果进行对比：
- 主要情绪: {main_emotion} (置信度: {confidence:.1%})
- 情绪分布: {emotion_text}
- 年龄: {age}岁
- 性别: {gender}
- 种族: {race_zh}

最后，请根据分析结果提出针对性的建议，帮助照片中的人物保持或改善情绪状态。"""

                logger.info("使用简化的情绪数据进行多模态分析:")
                logger.info(f"  - 原始JSON数据长度: {len(emotion_data)} 字符")
                logger.info(f"  - 是否有faces数组: {'是' if faces else '否'}")
                logger.info(f"  - faces数组长度: {len(faces) if faces else 0}")
                logger.info(f"  - 主要情绪: {main_emotion} (置信度: {confidence:.1%})")
                logger.info(f"  - 情绪分布: {emotion_text}")
                logger.info(f"  - 年龄: {age}")
                logger.info(f"  - 性别: {gender}")
                logger.info(f"  - 种族: {race_zh}")
                logger.info(f"构建的完整prompt长度: {len(prompt)} 字符")
                logger.info(f"完整prompt内容: {prompt}")
            except json.JSONDecodeError as e:
                logger.error(f"解析情绪数据JSON失败: {str(e)}")
                # 使用默认提示词
                emotion_data = None

        # 如果没有情绪数据或解析失败，使用默认提示词
        if not emotion_data:
            prompt = """好的，作为情绪分析专家，我将对照片进行分析并给出建议。请分析照片中人物的情绪、性别、年龄和种族，并提出一些保持好情绪的建议。"""

        # 返回流式响应
        return StreamingResponse(
            stream_multimodal_analysis(image_base64, prompt, user_agent),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",  # 禁用Nginx缓冲
            },
        )
    except Exception as e:
        logger.error(f"多模态分析时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"多模态分析时出错: {str(e)}",
        )


# 音频情绪分析API端点
@router.post("/audio-emotion-analysis")
async def analyze_audio_emotion(
    file: UploadFile = File(None),
    wechat_voice_id: str = Form(None),
    current_user: User = Depends(get_current_user),
) -> StreamingResponse:
    """
    使用多模态大模型分析音频中的情绪

    Args:
        file: 音频文件
        current_user: 当前用户

    Returns:
        流式响应，包含多模态大模型的分析结果
    """
    try:
        # 检查是否是微信录音
        if wechat_voice_id:
            logger.info(f"处理微信录音，voice_id: {wechat_voice_id}")
            # 这里应该添加从微信服务器下载录音的代码
            # 由于微信API的限制，这里只是一个示例
            # 实际项目中需要实现微信录音的下载

            # 模拟音频数据
            # 在实际项目中，这里应该是从微信服务器下载的音频数据
            audio_bytes = b"WeChat Voice Data"  # 这只是一个占位符
            mime_type = "audio/amr"  # 微信录音通常是AMR格式

            # 将音频转换为Base64编码
            audio_base64 = base64.b64encode(audio_bytes).decode("utf-8")

            # 在实际项目中，这里应该返回真实的音频数据
            # 由于我们无法实际下载微信录音，这里使用一个模拟的响应
            logger.warning("微信录音功能需要微信公众号或小程序支持，当前为模拟响应")

            # 模拟响应
            async def mock_wechat_audio_response():
                yield f"data: {json.dumps({'content': '由于微信浏览器的限制，无法直接处理微信录音。请使用其他浏览器或直接上传音频文件。'})}\n\n"

            return StreamingResponse(
                mock_wechat_audio_response(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no",  # 禁用Nginx缓冲
                },
            )

        # 处理普通上传的音频文件
        if not file:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="未提供音频文件",
            )

        # 读取上传的音频文件
        audio_bytes = await file.read()

        # 获取文件扩展名
        _, ext = os.path.splitext(file.filename)
        mime_type = f"audio/{ext[1:]}"  # 去掉点号

        # 支持的音频格式
        supported_formats = [
            "audio/mp3",
            "audio/wav",
            "audio/ogg",
            "audio/flac",
            "audio/aac",
            "audio/m4a",
            "audio/mp4",  # 添加mp4格式支持
            "audio/mpeg",  # 添加mpeg格式支持
            "audio/webm",  # 添加webm格式支持
        ]

        # 检查MIME类型
        if mime_type not in supported_formats:
            logger.warning(f"不支持的音频格式: {mime_type}，尝试使用通用音频MIME类型")
            # 尝试使用通用音频MIME类型
            if ext.lower() in [".mp3"]:
                mime_type = "audio/mpeg"
            elif ext.lower() in [".mp4", ".m4a"]:
                mime_type = "audio/mp4"
            elif ext.lower() in [".wav"]:
                mime_type = "audio/wav"
            elif ext.lower() in [".ogg"]:
                mime_type = "audio/ogg"
            elif ext.lower() in [".flac"]:
                mime_type = "audio/flac"
            elif ext.lower() in [".aac"]:
                mime_type = "audio/aac"
            elif ext.lower() in [".webm"]:
                mime_type = "audio/webm"
            else:
                mime_type = "audio/mpeg"  # 默认使用MP3格式

            logger.info(f"音频格式转换: {ext} -> {mime_type}")
        else:
            logger.info(f"音频格式支持: {mime_type}")

        # 记录文件信息
        logger.info(
            f"文件上传请求 | POST /api/v1/emotion/audio-emotion-analysis | 大小: {len(audio_bytes) / 1024 / 1024:.2f} MB | 格式: {mime_type}"
        )

        # 将音频转换为Base64编码
        audio_base64 = base64.b64encode(audio_bytes).decode("utf-8")

        # 构建提示词
        prompt = """
        请分析这段音频中说话者的情绪状态。

        请考虑以下因素:
        1. 语调和音高变化
        2. 语速和节奏
        3. 音量变化
        4. 停顿和犹豫
        5. 声音颤抖或其他情绪指标

        请提供详细分析，并给出主要情绪类别(如快乐、悲伤、愤怒、恐惧、惊讶、中性等)，
        以及情绪强度的估计(1-10分，其中1表示几乎不可察觉，10表示非常强烈)。

        如果音频中有多个说话者，请分别分析每个说话者的情绪。
        """

        # 创建音频部分 - 使用内联数据
        audio_part = {"inline_data": {"data": audio_base64, "mime_type": mime_type}}

        # 使用Gemini API进行分析
        async def stream_audio_analysis():
            try:
                # 导入Gemini API
                import google.generativeai as genai

                # 设置API密钥
                API_KEY = "AIzaSyALEVpVMStS25KRI4fLFHlxzvPYJd3GMdQ"
                genai.configure(api_key=API_KEY)

                # 创建生成模型
                model = genai.GenerativeModel("gemini-2.0-flash")

                # 生成内容
                response = model.generate_content(contents=[prompt, audio_part])

                # 返回分析结果
                yield f"data: {json.dumps({'content': response.text})}\n\n"

            except Exception as e:
                logger.error(f"音频情绪分析时出错: {str(e)}")
                yield f"data: {json.dumps({'error': str(e)})}\n\n"

        # 返回流式响应
        return StreamingResponse(
            stream_audio_analysis(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",  # 禁用Nginx缓冲
            },
        )
    except Exception as e:
        logger.error(f"音频情绪分析时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"音频情绪分析时出错: {str(e)}",
        )


# 移除年龄分析判断的相关代码


# 反馈相关API端点
@router.post("/feedback", response_model=dict)
async def save_feedback(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
) -> Any:
    """
    保存用户反馈

    Args:
        request: 请求对象，包含analysis_id、emotion、comment
        current_user: 当前用户
        db: 数据库会话

    Returns:
        保存结果
    """
    try:
        # 解析请求体
        body = await request.json()
        analysis_id_str = body.get("analysis_id")
        try:
            analysis_id = uuid.UUID(analysis_id_str)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="analysis_id 格式不正确，必须是有效的 UUID 字符串",
            )
        emotion = body.get("emotion")
        comment = body.get("comment", "")
        name = body.get("name")
        gender = body.get("gender")
        age = body.get("age")
        rating = body.get("rating")

        if not analysis_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少必要参数: analysis_id",
            )
        # 只有在没有 rating 时，才强制 emotion 必填
        if not rating and not emotion:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少必要参数: emotion 或 rating",
            )

        # 快捷反馈时 emotion 只要是 good/average/bad/null/空字符串，都传 None
        emotion_to_save = (
            emotion if emotion not in (None, "", "good", "bad", "average") else None
        )
        name_to_save = name if name not in (None, "") else None
        gender_to_save = gender if gender not in (None, "", "other") else None
        age_to_save = age if age not in (None, "", 0, "0") else None

        # 调用情绪分析服务保存反馈
        result = await emotion_service.save_feedback(
            analysis_id=str(analysis_id),  # 确保传递的是字符串
            user_id=str(current_user.id),
            emotion=emotion_to_save,
            comment=comment,
            db=db,
            name=name_to_save,
            gender=gender_to_save,
            age=age_to_save,
            rating=rating,
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存反馈API错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存反馈失败: {str(e)}",
        )


@router.get("/feedback/history/{analysis_id}", response_model=list)
async def get_feedback_history(
    analysis_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
) -> Any:
    """
    获取反馈历史

    Args:
        analysis_id: 分析ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        反馈历史列表
    """
    try:
        # 调用情绪分析服务获取反馈历史
        result = await emotion_service.get_feedback_history(
            analysis_id=analysis_id, user_id=str(current_user.id), db=db
        )

        return result

    except Exception as e:
        logger.error(f"获取反馈历史API错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取反馈历史失败: {str(e)}",
        )
