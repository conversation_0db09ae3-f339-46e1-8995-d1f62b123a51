from datetime import date
from typing import List, Optional

from pydantic import BaseModel, ConfigDict, Field


class UserStatisticsQuery(BaseModel):
    """用户统计查询参数"""

    username: Optional[str] = Field(None, description="用户名筛选")
    period: str = Field("day", description="统计周期：day/week/month")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")

    model_config = ConfigDict(from_attributes=True)


class UserStatisticsData(BaseModel):
    """用户统计数据"""

    username: str = Field(..., description="用户名")
    upload_count: int = Field(..., description="上传图片数量")
    annotation_count: int = Field(..., description="标注数量")
    period: str = Field(..., description="统计周期")

    model_config = ConfigDict(from_attributes=True)


class UserStatisticsResponse(BaseModel):
    """用户统计响应"""

    data: List[UserStatisticsData] = Field(..., description="统计数据列表")
    total_users: int = Field(..., description="总用户数")
    total_uploads: int = Field(..., description="总上传数")
    total_annotations: int = Field(..., description="总标注数")

    model_config = ConfigDict(from_attributes=True)
