from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


# 反馈相关的schema定义
class EmotionFeedbackBase(BaseModel):
    """
    情绪反馈基础模型

    系统支持两种反馈类型：
    1. 评价反馈 (rating字段): 用户对分析结果的主观评价 - good(好评)/average(一般)/bad(不好)
    2. 标注反馈 (emotion字段): 用户对情绪的具体标注 - happy/sad/angry/fear/surprise/disgust/neutral/contempt
    """

    emotion: Optional[str] = Field(
        None,
        max_length=20,
        description="标注反馈：8种具体情绪标注（happy/sad/angry/fear/surprise/disgust/neutral/contempt）",
    )
    comment: Optional[str] = Field(None, description="用户反馈的评论")
    name: Optional[str] = Field(None, max_length=64, description="用户姓名（可选）")
    gender: Optional[str] = Field(None, max_length=16, description="用户性别（必填）")
    age: Optional[int] = Field(None, description="用户年龄（必填）")
    rating: Optional[str] = Field(
        None,
        max_length=16,
        description="评价反馈：用户主观评价（good=好评/average=一般/bad=不好）",
    )

    model_config = ConfigDict(from_attributes=True)


class EmotionFeedback(EmotionFeedbackBase):
    """情绪反馈完整模型"""

    id: UUID = Field(..., description="反馈记录ID")
    analysis_id: UUID = Field(..., description="关联的分析记录ID")
    created_by: Optional[UUID] = Field(None, description="反馈用户ID")
    created_at: datetime = Field(..., description="反馈创建时间")
    updated_at: Optional[datetime] = Field(None, description="反馈更新时间")

    # 扩展字段
    username: Optional[str] = Field(None, description="反馈用户名")


# 共享的基础属性
class EmotionAnalysisBase(BaseModel):
    input_type: Optional[str] = Field(None, max_length=50, description="输入类型")
    input_path: Optional[str] = Field(None, max_length=255, description="输入路径")
    primary_emotion: Optional[str] = Field(None, max_length=50, description="主要情绪")
    confidence1: Optional[float] = Field(None, description="主要情绪置信度")
    secondary_emotion: Optional[str] = Field(
        None, max_length=50, description="次要情绪"
    )
    confidence2: Optional[float] = Field(None, description="次要情绪置信度")
    third_emotion: Optional[str] = Field(None, max_length=50, description="第三情绪")
    confidence3: Optional[float] = Field(None, description="第三情绪置信度")
    emotion_details: Optional[Dict[str, float]] = Field(
        None, description="所有情绪及其置信度"
    )
    gender: Optional[str] = Field(None, max_length=50, description="性别")
    age: Optional[int] = Field(None, description="年龄")
    race: Optional[str] = Field(None, max_length=50, description="种族")
    user_id: Optional[UUID] = Field(
        None, description="用户ID"
    )  # 保持 user_id 用于后端逻辑
    session_id: Optional[str] = Field(None, max_length=255, description="会话ID")
    device_info: Optional[Dict[str, Any]] = Field(None, description="设备信息")
    analysis_time: Optional[float] = Field(None, description="分析耗时(秒)")
    model_version: Optional[str] = Field(None, max_length=50, description="模型版本")
    additional_info: Optional[Dict[str, Any]] = Field(None, description="附加信息")

    # 新增字段
    username: Optional[str] = Field(None, description="用户名")
    image_thumbnail_url: Optional[str] = Field(None, description="图片缩略图URL")

    model_config = ConfigDict(from_attributes=True)


# 创建时所需的属性 (通常不由API直接创建，而是由系统内部记录)
class EmotionAnalysisCreate(EmotionAnalysisBase):
    input_type: str = Field(..., max_length=50, description="输入类型")
    input_path: str = Field(..., max_length=255, description="输入路径")
    primary_emotion: str = Field(..., max_length=50, description="主要情绪")
    confidence1: float = Field(..., description="主要情绪置信度")
    # secondary_emotion, confidence2, third_emotion, confidence3, emotion_details
    # 在创建时通常由分析服务填充，而不是直接由API创建时提供
    # 如果需要API创建时也支持这些字段，可以取消注释并添加
    # secondary_emotion: Optional[str] = Field(None, max_length=50, description="次要情绪")
    # confidence2: Optional[float] = Field(None, description="次要情绪置信度")
    # third_emotion: Optional[str] = Field(None, max_length=50, description="第三情绪")
    # confidence3: Optional[float] = Field(None, description="第三情绪置信度")
    # emotion_details: Optional[Dict[str, float]] = Field(None, description="所有情绪及其置信度")


# 更新时所需的属性 (通常不由API直接更新)
class EmotionAnalysisUpdate(EmotionAnalysisBase):
    pass


# 存储在数据库中的属性，继承自Base，并添加id和时间戳
class EmotionAnalysisInDBBase(EmotionAnalysisBase):
    id: UUID = Field(..., description="分析记录ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(None, description="删除时间")


# API返回的单个情绪分析记录模型
class EmotionAnalysis(EmotionAnalysisInDBBase):
    # 添加反馈信息字段
    feedbacks: Optional[List[EmotionFeedback]] = Field(
        default=[], description="用户反馈列表"
    )
    feedback_count: Optional[int] = Field(default=0, description="反馈数量")


# API返回的情绪分析记录列表模型 (用于分页)
class EmotionAnalysisListResponse(BaseModel):
    total: int = Field(..., description="总记录数")
    items: List[EmotionAnalysis] = Field(..., description="当前页记录列表")

    model_config = ConfigDict(from_attributes=True)
