# /Volumes/acasis/ema2_20250417/backend/emotionai/schemas/password_reset.py
import re

from pydantic import BaseModel, EmailStr, Field, validator

# 正则表达式：至少8位，包含大小写字母和数字 - We will use this for individual checks
PASSWORD_REGEX = (
    r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$"  # Keep for reference or remove
)


class ForgotPasswordRequest(BaseModel):
    email: EmailStr


class ResetPasswordRequest(BaseModel):
    token: str
    new_password: str = Field(
        ...,
        min_length=8,
        description="Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number.",
    )

    @validator("new_password")
    def validate_password_complexity(cls, value):
        if not re.search(r"[a-z]", value):
            raise ValueError("Password must contain at least one lowercase letter.")
        if not re.search(r"[A-Z]", value):
            raise ValueError("Password must contain at least one uppercase letter.")
        if not re.search(r"\d", value):
            raise ValueError("Password must contain at least one digit.")
        if len(value) < 8:
            raise ValueError("Password must be at least 8 characters long.")
        # You can also add a check for allowed characters if needed, e.g.,
        # if not re.fullmatch(r"[a-zA-Z\d@$!%*?&^_-]+", value): # Example: allow common special chars
        #     raise ValueError("Password contains invalid characters.")
        return value
