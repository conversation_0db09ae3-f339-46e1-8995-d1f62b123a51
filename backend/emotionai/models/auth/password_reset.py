# /Volumes/acasis/ema2_20250417/backend/emotionai/models/auth/password_reset.py
from datetime import datetime, timedelta
from uuid import UUID, uuid4

from sqlalchemy import Boolean, Column, DateTime, ForeignKey, String
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import relationship

from emotionai.core.database.base_unified import Base
from emotionai.core.utils.datetime_utils import get_beijing_now

# 建议从配置文件或环境变量读取过期时间，这里暂时硬编码为1小时
RESET_TOKEN_EXPIRE_MINUTES = 60


class PasswordResetToken(Base):
    __tablename__ = "password_reset_tokens"

    id: UUID = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    token: str = Column(String, unique=True, index=True, nullable=False)
    # ForeignKey 使用的是 users 表的 id 列
    user_id: UUID = Column(PGUUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at: datetime = Column(
        DateTime(timezone=True), nullable=False, default=get_beijing_now
    )
    expires_at: datetime = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: get_beijing_now()
        + timedelta(minutes=RESET_TOKEN_EXPIRE_MINUTES),
    )
    is_used: bool = Column(Boolean, default=False, nullable=False)

    # 关联关系: "User" 会被 SQLAlchemy 解析为导入的 User 类
    # back_populates 指向 User 模型中对应的关系属性名
    user = relationship("User", back_populates="password_reset_tokens")

    def __repr__(self) -> str:
        return f"<PasswordResetToken for user_id={self.user_id}>"
