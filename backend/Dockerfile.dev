FROM python:3.10-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgcc-s1 \
    libpq-dev \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements-core.txt requirements-dev.txt ./

# 安装Python依赖
RUN pip install --upgrade pip && \
    pip install -r requirements-core.txt && \
    pip install -r requirements-dev.txt && \
    pip check

# 创建必要目录
RUN mkdir -p logs uploads/original uploads/marked data

# 设置环境变量
ENV PYTHONPATH=/app \
    MEDIAPIPE_GPU=0 \
    MEDIAPIPE_USE_CUSTOM_OPENCV=0 \
    MEDIAPIPE_DISABLE_GPU=1 \
    HF_DATASETS_OFFLINE=1 \
    TRANSFORMERS_OFFLINE=1 \
    NO_GIT=1 \
    ENVIRONMENT=development

# 暴露端口
EXPOSE 8000

# 开发启动命令（支持热重载）
CMD ["uvicorn", "emotionai.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"] 