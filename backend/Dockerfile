# 使用 Mambaforge 作为基础镜像以加速环境创建
FROM condaforge/mambaforge:latest AS builder

# 设置工作目录
WORKDIR /opt/app
# 安装系统级编译工具，以确保 pip 构建 C/C++ 扩展时可用
RUN apt-get update && apt-get install -y --no-install-recommends build-essential && rm -rf /var/lib/apt/lists/*

# 复制生产环境定义文件
# 确保 environment-prod.yml 位于 backend/ 目录下 (即当前构建上下文)
COPY environment-prod.yml .

# 创建生产 Conda 环境
# 确保 environment-prod.yml 中的 name 字段是 ema-prod-env
RUN mamba env create -f environment-prod.yml && \
    mamba clean -afy

# ---- 最终生产镜像 ----
FROM debian:bullseye-slim AS production
# 或者其他您偏好的轻量级基础 Linux 镜像

# 安装一些基础工具，例如 curl (用于健康检查) 和其他可能的运行时依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    # 如果您的应用有其他非Python的系统级运行时依赖，在此处添加
    && rm -rf /var/lib/apt/lists/*

# 从 builder 阶段复制已创建的 Conda 环境
# Conda 环境通常安装在 /opt/conda/envs/ 目录下
COPY --from=builder /opt/conda/envs/ema-prod-env /opt/conda/envs/ema-prod-env

# 设置 PATH，以便可以直接调用 Conda 环境中的可执行文件
ENV PATH /opt/conda/envs/ema-prod-env/bin:$PATH

# 创建应用用户和组，以非 root 用户运行应用（更安全）
RUN groupadd -r appuser && useradd --no-log-init -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 复制应用代码到镜像中
# 确保 .dockerignore 文件排除了不必要的文件和目录 (如 .git, .vscode, __pycache__, etc.)
COPY . .

# 创建应用可能需要的目录，并设置权限
# 例如：uploads, logs。这些目录在 docker-compose.yml 中也通过卷挂载了，
# 但在镜像中创建它们并设置好权限是一个好习惯。
# 确保应用需要的根级别 /logs 目录存在且 appuser 有权限
RUN mkdir -p /logs && chown -R appuser:appuser /logs

RUN mkdir -p uploads logs data && \
    chown -R appuser:appuser /app/uploads /app/logs /app/data /opt/conda/envs/ema-prod-env
    # 注意：修改 Conda 环境目录的权限可能不是必需的，或者需要小心操作

# 切换到非 root 用户
USER appuser

# 设置必要的环境变量 (许多应该通过 docker-compose.yml 注入)
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    ENVIRONMENT=production \
    PYTHONPATH=/app
    # 其他如数据库连接、密钥等应通过 docker-compose.yml 的 environment 部分传入

# 暴露应用监听的端口
EXPOSE 8000

# 健康检查 (与 docker-compose.yml 中的保持一致或类似)
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 生产启动命令
# 使用 Conda 环境中的 gunicorn
CMD ["gunicorn", "emotionai.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "-b", "0.0.0.0:8000", "--timeout", "120", "--keep-alive", "2", "--max-requests", "1000", "--max-requests-jitter", "100"]
