-- 为邀请码表添加批次相关字段

-- 添加批次ID字段
ALTER TABLE invite_codes ADD COLUMN IF NOT EXISTS batch_id VARCHAR(50);

-- 添加批次颜色字段  
ALTER TABLE invite_codes ADD COLUMN IF NOT EXISTS batch_color VARCHAR(10);

-- 添加批次创建时间字段
ALTER TABLE invite_codes ADD COLUMN IF NOT EXISTS batch_created_at TIMESTAMP WITH TIME ZONE;

-- 为批次ID字段添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_invite_codes_batch_id ON invite_codes(batch_id);

-- 添加注释
COMMENT ON COLUMN invite_codes.batch_id IS '批次ID，用于标识同一批创建的邀请码';

COMMENT ON COLUMN invite_codes.batch_color IS '批次颜色，用于前端显示区分不同批次';

COMMENT ON COLUMN invite_codes.batch_created_at IS '批次创建时间';
