import asyncio

from sqlalchemy import text

from emotionai.core.database.session import get_async_db


async def check_data():
    async for db in get_async_db():
        # 检查emotion_analysis表的数据
        result = await db.execute(
            text(
                """
            SELECT 
                input_type, 
                COUNT(*) as count,
                MIN(created_at) as earliest,
                MAX(created_at) as latest
            FROM emotion_analysis 
            WHERE deleted_at IS NULL 
            GROUP BY input_type
        """
            )
        )
        print("emotion_analysis表数据统计:")
        for row in result.fetchall():
            print(
                f"  input_type: {row[0]}, count: {row[1]}, earliest: {row[2]}, latest: {row[3]}"
            )

        # 检查最近7天的上传数据
        result2 = await db.execute(
            text(
                """
            SELECT 
                u.username,
                COUNT(ea.id) as upload_count,
                ea.input_type
            FROM users u
            LEFT JOIN emotion_analysis ea ON u.id = ea.user_id 
                AND ea.created_at >= CURRENT_DATE - INTERVAL '7 days'
                AND ea.deleted_at IS NULL
            WHERE u.deleted_at IS NULL
            GROUP BY u.username, ea.input_type
            HAVING COUNT(ea.id) > 0
            ORDER BY upload_count DESC
            LIMIT 10
        """
            )
        )
        print("\n最近7天用户上传统计:")
        for row in result2.fetchall():
            print(f"  username: {row[0]}, upload_count: {row[1]}, input_type: {row[2]}")

        # 检查具体的查询条件
        result3 = await db.execute(
            text(
                """
            SELECT 
                u.username,
                COUNT(ea.id) as upload_count
            FROM users u
            LEFT JOIN emotion_analysis ea ON u.id = ea.user_id 
                AND ea.input_type = 'IMAGE'
                AND ea.created_at >= :start_date 
                AND ea.created_at <= :end_date
                AND ea.deleted_at IS NULL
            WHERE u.deleted_at IS NULL
            GROUP BY u.username
            HAVING COUNT(ea.id) > 0
            ORDER BY upload_count DESC
            LIMIT 10
        """
            ),
            {"start_date": "2025-05-22", "end_date": "2025-05-29"},
        )
        print("\n使用API查询条件的结果:")
        for row in result3.fetchall():
            print(f"  username: {row[0]}, upload_count: {row[1]}")
        break


if __name__ == "__main__":
    asyncio.run(check_data())
