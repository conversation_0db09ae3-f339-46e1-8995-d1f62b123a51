# ===================================
# 生产环境依赖 - 仅包含运行时必需的包
# ===================================

# Web框架
fastapi>=0.115.11,<1.0.0
uvicorn[standard]>=0.34.0,<1.0.0
starlette>=0.46.0,<1.0.0
gunicorn>=23.0.0,<24.0.0

# 数据库
sqlalchemy>=2.0.40,<3.0.0
alembic>=1.14.1,<2.0.0
psycopg2-binary>=2.9.10,<3.0.0
asyncpg>=0.30.0,<1.0.0

# 数据验证
pydantic>=2.11.0,<3.0.0
pydantic-settings>=2.2.1,<3.0.0
email-validator>=2.2.0,<3.0.0

# 安全认证
python-jose[cryptography]>=3.4.0,<4.0.0
passlib[bcrypt]>=1.7.4,<2.0.0
bcrypt>=4.3.0,<5.0.0

# 文件处理
aiofiles>=23.2.1,<24.0.0
python-multipart>=0.0.20,<1.0.0

# 缓存和性能
redis>=6.0.0,<7.0.0
slowapi>=0.1.9,<1.0.0

# HTTP客户端
httpx>=0.28.0,<1.0.0

# 环境配置
python-dotenv>=1.0.1,<2.0.0

# 工具库
tqdm>=4.67.0,<5.0.0
psutil>=7.0.0,<8.0.0
rich>=14.0.0,<15.0.0

# 监控
prometheus-client>=0.21.0,<1.0.0
prometheus-fastapi-instrumentator>=7.1.0,<8.0.0

# 深度学习核心依赖
torch>=2.6.0,<3.0.0
torchvision>=0.21.0,<1.0.0
transformers>=4.51.0,<5.0.0
safetensors>=0.5.3,<1.0.0

# 计算机视觉
mediapipe>=0.10.11,<1.0.0
opencv-python-headless>=4.11.0,<5.0.0
pillow>=11.2.0,<12.0.0

# 科学计算
numpy>=1.26.4,<2.0.0
scipy>=1.15.0,<2.0.0

# 人脸分析
deepface>=0.0.93,<1.0.0
ultralytics>=8.3.120,<9.0.0

# 图像处理
albumentations>=2.0.6,<3.0.0

# Apple Silicon 兼容性（仅在macOS上需要）
tensorflow-macos==2.16.2; sys_platform == "darwin"
tensorflow-metal==1.1.0; sys_platform == "darwin"
tf-keras==2.16.0; sys_platform == "darwin"
ml-dtypes>=0.5.1,<1.0.0 