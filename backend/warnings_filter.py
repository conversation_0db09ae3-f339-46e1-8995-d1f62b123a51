#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
警告过滤器

此脚本用于过滤Python的警告消息
"""
import os
import sys
import warnings

# 忽略特定的警告
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=UserWarning, module="torch")
warnings.filterwarnings("ignore", category=UserWarning, module="timm")
warnings.filterwarnings("ignore", message=".*torch.meshgrid.*")
warnings.filterwarnings("ignore", message=".*Importing from timm.models.layers.*")

# 设置环境变量以禁用特定警告
os.environ["PYTHONWARNINGS"] = "ignore::DeprecationWarning,ignore::UserWarning"

# 如果有命令行参数，则执行指定的命令
if len(sys.argv) > 1:
    import subprocess

    subprocess.run(sys.argv[1:])
