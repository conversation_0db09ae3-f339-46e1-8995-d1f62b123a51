#!/usr/bin/env python3
"""测试批量创建邀请码"""

import json
from datetime import datetime

import requests


def test_batch_creation():
    # 登录获取token
    login_url = "http://localhost:8000/api/v1/auth/login"
    login_data = {"username": "superadmin", "password": "admin123"}

    response = requests.post(login_url, data=login_data)
    if response.status_code != 200:
        print(f"登录失败: {response.text}")
        return

    token = response.json()["access_token"]
    print(f"登录成功")

    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}

    create_url = "http://localhost:8000/api/v1/invite-codes/admin/invite-codes"

    # 测试批量创建同一批次的邀请码
    batch_id = "batch-2025-05-27-001"
    batch_color = "#FF9800"
    batch_created_at = "2025-05-27T04:05:00+08:00"

    print(f"\n开始批量创建邀请码...")
    print(f"批次ID: {batch_id}")
    print(f"批次颜色: {batch_color}")
    print(f"批次创建时间: {batch_created_at}")

    created_codes = []

    for i in range(5):
        data = {
            "role": "user",
            "is_active": True,
            "expires_at": None,
            "batch_id": batch_id,
            "batch_color": batch_color,
            "batch_created_at": batch_created_at,
        }

        response = requests.post(create_url, headers=headers, json=data)

        if response.status_code == 200:
            result = response.json()
            created_codes.append(result["code"])
            print(f"✅ 创建邀请码 {i+1}: {result['code']}")
        else:
            print(f"❌ 创建邀请码 {i+1} 失败: {response.status_code}")
            try:
                error = response.json()
                print(f"   错误: {error.get('detail', error)}")
            except:
                print(f"   错误: {response.text}")

    print(f"\n批量创建完成！")
    print(f"成功创建 {len(created_codes)} 个邀请码:")
    for code in created_codes:
        print(f"  - {code}")

    # 测试查询批次邀请码
    print(f"\n查询批次邀请码...")
    query_url = f"http://localhost:8000/api/v1/invite-codes/admin/invite-codes?batch_id={batch_id}"
    response = requests.get(query_url, headers=headers)

    if response.status_code == 200:
        result = response.json()
        print(f"✅ 查询成功，找到 {len(result.get('items', []))} 个邀请码")
        for item in result.get("items", []):
            print(
                f"  - {item['code']} (批次: {item['batch_id']}, 颜色: {item['batch_color']}, 创建时间: {item['batch_created_at']})"
            )
    else:
        print(f"❌ 查询失败: {response.status_code}")


if __name__ == "__main__":
    test_batch_creation()
