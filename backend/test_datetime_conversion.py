#!/usr/bin/env python3
"""测试datetime转换"""

import json
from datetime import datetime

import pytz
import requests


def test_invite_code_creation():
    # 登录获取token
    login_url = "http://localhost:8000/api/v1/auth/login"
    login_data = {"username": "superadmin", "password": "admin123"}

    response = requests.post(login_url, data=login_data)
    if response.status_code != 200:
        print(f"登录失败: {response.text}")
        return

    token = response.json()["access_token"]
    print(f"登录成功，token: {token[:50]}...")

    # 测试不同的时间格式
    test_cases = [
        {
            "name": "无batch_created_at",
            "data": {
                "role": "user",
                "is_active": True,
                "expires_at": None,
                "batch_id": "test-batch-002",
                "batch_color": "#2196F3",
            },
        },
        {
            "name": "ISO格式时间字符串",
            "data": {
                "role": "user",
                "is_active": True,
                "expires_at": None,
                "batch_id": "test-batch-003",
                "batch_color": "#4CAF50",
                "batch_created_at": "2025-05-27T03:42:00+08:00",
            },
        },
        {
            "name": "UTC时间字符串",
            "data": {
                "role": "user",
                "is_active": True,
                "expires_at": None,
                "batch_id": "test-batch-004",
                "batch_color": "#9C27B0",
                "batch_created_at": "2025-05-26T19:42:00Z",
            },
        },
    ]

    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}

    create_url = "http://localhost:8000/api/v1/invite-codes/admin/invite-codes"

    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        print(f"数据: {json.dumps(test_case['data'], indent=2)}")

        response = requests.post(create_url, headers=headers, json=test_case["data"])

        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功创建邀请码: {result['code']}")
            print(f"   batch_id: {result.get('batch_id')}")
            print(f"   batch_color: {result.get('batch_color')}")
            print(f"   batch_created_at: {result.get('batch_created_at')}")
        else:
            print(f"❌ 创建失败: {response.status_code}")
            try:
                error = response.json()
                print(f"   错误: {error.get('detail', error)}")
            except:
                print(f"   错误: {response.text}")


if __name__ == "__main__":
    test_invite_code_creation()
