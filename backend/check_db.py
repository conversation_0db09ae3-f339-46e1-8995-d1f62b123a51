import asyncio
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from emotionai.core.deps.database import get_async_db
from emotionai.models.ml.emotion import EmotionAnalysis
from emotionai.models.user.user import User


async def check_database():
    """检查数据库中的用户和情绪分析记录"""

    # 获取数据库会话
    async for db in get_async_db():
        session: AsyncSession = db

        # 检查用户表
        print("\n=== 用户表信息 ===")
        user_count = await session.execute(select(func.count()).select_from(User))
        user_count = user_count.scalar()
        print(f"用户总数: {user_count}")

        if user_count > 0:
            # 获取前5个用户
            users = await session.execute(select(User).limit(5))
            users = users.scalars().all()

            print("\n前5个用户信息:")
            for user in users:
                print(
                    f"ID: {user.id}, 用户名: {user.username}, 邮箱: {user.email}, 是否活跃: {user.is_active}"
                )

        # 检查情绪分析表
        print("\n=== 情绪分析表信息 ===")
        emotion_count = await session.execute(
            select(func.count()).select_from(EmotionAnalysis)
        )
        emotion_count = emotion_count.scalar()
        print(f"情绪分析记录总数: {emotion_count}")

        if emotion_count > 0:
            # 获取前5条情绪分析记录
            emotions = await session.execute(select(EmotionAnalysis).limit(5))
            emotions = emotions.scalars().all()

            print("\n前5条情绪分析记录:")
            for emotion in emotions:
                print(f"ID: {emotion.id}")
                print(f"用户ID: {emotion.user_id}")
                print(f"输入类型: {emotion.input_type}")
                print(f"输入路径: {emotion.input_path}")
                print(f"情绪: {emotion.emotion}")
                print(f"置信度: {emotion.confidence}")
                print(f"创建时间: {emotion.created_at}")
                print("---")

        # 检查特定用户的情绪分析记录
        if user_count > 0 and emotion_count > 0:
            # 获取第一个用户的ID
            first_user = await session.execute(select(User).limit(1))
            first_user = first_user.scalar()

            if first_user:
                user_id = first_user.id
                print(
                    f"\n=== 用户 {first_user.username} (ID: {user_id}) 的情绪分析记录 ==="
                )

                # 查询该用户的情绪分析记录
                user_emotions = await session.execute(
                    select(EmotionAnalysis)
                    .where(EmotionAnalysis.user_id == user_id)
                    .limit(5)
                )
                user_emotions = user_emotions.scalars().all()

                print(f"记录数: {len(user_emotions)}")

                if user_emotions:
                    print("\n用户情绪分析记录:")
                    for emotion in user_emotions:
                        print(f"ID: {emotion.id}")
                        print(f"输入类型: {emotion.input_type}")
                        print(f"输入路径: {emotion.input_path}")
                        print(f"情绪: {emotion.emotion}")
                        print(f"置信度: {emotion.confidence}")
                        print(f"创建时间: {emotion.created_at}")
                        print("---")

        break  # 只需要一个会话


if __name__ == "__main__":
    asyncio.run(check_database())
