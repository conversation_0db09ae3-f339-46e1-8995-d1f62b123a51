#!/usr/bin/env python
"""
日志格式化脚本

此脚本用于过滤和美化日志输出
"""
import re
import sys
import time

# ANSI颜色代码
COLORS = {
    "RESET": "\033[0m",
    "BOLD": "\033[1m",
    "RED": "\033[31m",
    "GREEN": "\033[32m",
    "YELLOW": "\033[33m",
    "BLUE": "\033[34m",
    "MAGENTA": "\033[35m",
    "CYAN": "\033[36m",
    "WHITE": "\033[37m",
    "GRAY": "\033[90m",
    "BOLD_RED": "\033[1;31m",
    "BOLD_GREEN": "\033[1;32m",
    "BOLD_YELLOW": "\033[1;33m",
    "BOLD_BLUE": "\033[1;34m",
    "BOLD_MAGENTA": "\033[1;35m",
    "BOLD_CYAN": "\033[1;36m",
    "BOLD_WHITE": "\033[1;37m",
}

# 日志级别颜色
LEVEL_COLORS = {
    "DEBUG": COLORS["GRAY"],
    "INFO": COLORS["GREEN"],
    "WARNING": COLORS["YELLOW"],
    "ERROR": COLORS["RED"],
    "CRITICAL": COLORS["BOLD_RED"],
}

# 模块颜色
MODULE_COLORS = {
    "emotionai.core.ml": COLORS["MAGENTA"],
    "emotionai.services": COLORS["CYAN"],
    "emotionai.api": COLORS["BLUE"],
    "emotionai.core.database": COLORS["YELLOW"],
    "emotionai.core.cache": COLORS["GREEN"],
    "emotionai.core.config": COLORS["CYAN"],
    "emotionai.core.loggers": COLORS["GRAY"],
    "emotionai.app": COLORS["BOLD_GREEN"],
    "emotionai.routes": COLORS["BOLD_BLUE"],
    "emotionai.models": COLORS["BOLD_MAGENTA"],
    "uvicorn": COLORS["GRAY"],
    "fastapi": COLORS["BLUE"],
}

# 要忽略的警告消息
IGNORED_WARNINGS = [
    "Importing from timm.models.layers is deprecated",
    "torch.meshgrid: in an upcoming release",
]

# 要忽略的重复日志
IGNORED_DUPLICATES = [
    "使用MediaPipe Tasks API (推荐)",
    "找到模型文件:",
    "检查目录:",
    "在目录",
    "在子目录",
]

# 最近的日志消息，用于过滤重复
recent_messages: dict[str, float] = {}
# 重复消息计数
duplicate_counts: dict[str, int] = {}


def get_module_color(module_name: str) -> str:
    """获取模块的颜色"""
    for prefix, color in MODULE_COLORS.items():
        if module_name.startswith(prefix):
            return color
    return COLORS["WHITE"]


def format_log_line(line: str) -> str:
    """格式化日志行"""
    # 检查是否是警告消息
    for warning in IGNORED_WARNINGS:
        if warning in line:
            return ""

    # 标准日志格式: 2025-05-15 02:12:40,677 - emotionai.core.ml.model_loader - INFO - 消息
    log_pattern = r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - ([a-zA-Z0-9_.]+) - ([A-Z]+) - (.*)"
    match = re.match(log_pattern, line)

    if match:
        timestamp, module, level, message = match.groups()

        # 检查是否是重复消息
        if message in IGNORED_DUPLICATES:
            if message in recent_messages:
                if message in duplicate_counts:
                    duplicate_counts[message] += 1
                else:
                    duplicate_counts[message] = 1
                return ""
            else:
                recent_messages[message] = time.time()

        # 获取颜色
        level_color = LEVEL_COLORS.get(level, COLORS["WHITE"])
        module_color = get_module_color(module)

        # 格式化日志
        formatted_timestamp = f"{COLORS['GRAY']}{timestamp}{COLORS['RESET']}"
        formatted_module = f"{module_color}{module}{COLORS['RESET']}"
        formatted_level = f"{level_color}{level}{COLORS['RESET']}"

        # 根据日志级别格式化消息
        formatted_message = (
            f"{COLORS['RED']}{message}{COLORS['RESET']}"
            if level == "ERROR" or level == "CRITICAL"
            else f"{level_color}{message}{COLORS['RESET']}"
        )
        return f"{formatted_timestamp} {formatted_module} {formatted_level} {formatted_message}"

    # 非标准日志格式，直接返回原始行
    return line


def process_logs():
    """处理日志"""
    # 清理过期的消息
    now = time.time()
    expired_messages = [
        msg_key for msg_key, ts in recent_messages.items() if now - ts > 60
    ]
    for msg in expired_messages:
        del recent_messages[msg]

    try:
        # 读取标准输入
        for line in sys.stdin:
            try:
                # 格式化日志行
                formatted_line = format_log_line(line.rstrip())
                if formatted_line:
                    print(formatted_line, flush=True)
            except BrokenPipeError:
                # 如果在处理过程中管道断开，优雅地退出
                raise

        # 打印重复消息计数
        for msg, count in duplicate_counts.items():
            if count > 0:
                print(
                    f"{COLORS['GRAY']}已过滤 {count} 条重复消息: {msg}{COLORS['RESET']}",
                    flush=True,
                )
    except (BrokenPipeError, KeyboardInterrupt):
        # 捕获管道断开和键盘中断异常，向上传播
        raise


def handle_broken_pipe():
    """处理断开的管道错误"""
    # 重置标准输出和标准错误的文件描述符
    sys.stdout = None
    sys.stderr = None
    sys.exit(0)


if __name__ == "__main__":
    try:
        process_logs()
    except KeyboardInterrupt:
        # 优雅地处理Ctrl+C
        try:
            print(f"\n{COLORS['BOLD_RED']}日志处理已中断{COLORS['RESET']}", flush=True)
        except BrokenPipeError:
            handle_broken_pipe()
        sys.exit(0)
    except BrokenPipeError:
        # 处理断开的管道
        handle_broken_pipe()
    except Exception as e:
        try:
            print(f"{COLORS['BOLD_RED']}日志处理出错: {e}{COLORS['RESET']}", flush=True)
        except BrokenPipeError:
            handle_broken_pipe()
        sys.exit(1)
