#!/bin/bash

# 创建日志目录和文件
LOG_DIR="$(dirname "$0")/../logs"
LOG_FILE="$LOG_DIR/cron_execution.log"

# 确保日志目录存在
mkdir -p "$LOG_DIR"

# 记录脚本开始执行
echo "========================================" >> "$LOG_FILE"
echo "$(date): 开始执行模型使用统计更新脚本" >> "$LOG_FILE"

# 设置工作目录
cd "$(dirname "$0")/.." 
echo "$(date): 工作目录: $(pwd)" >> "$LOG_FILE"

# 运行 Python 脚本并捕获输出
echo "$(date): 开始运行 Python 脚本" >> "$LOG_FILE"
python -m scripts.update_model_usage_from_log 2>&1 | tee -a "$LOG_FILE"
RESULT=$?

# 记录执行结果
if [ $RESULT -eq 0 ]; then
    echo "$(date): 脚本执行成功完成" >> "$LOG_FILE"
else
    echo "$(date): 脚本执行失败，退出代码: $RESULT" >> "$LOG_FILE"
fi

echo "========================================" >> "$LOG_FILE"
