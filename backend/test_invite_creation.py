import asyncio
import json
from datetime import datetime

import httpx


async def test_invite_creation():
    """测试邀请码创建API"""

    # 首先登录获取token
    login_data = {"username": "admin", "password": "admin123"}

    async with httpx.AsyncClient() as client:
        # 登录
        login_response = await client.post(
            "http://localhost:8000/api/v1/auth/login", data=login_data
        )

        if login_response.status_code != 200:
            print(f"登录失败: {login_response.status_code}")
            print(login_response.text)
            return

        token_data = login_response.json()
        access_token = token_data["access_token"]

        print(f"登录成功，获取到token: {access_token[:20]}...")

        # 创建邀请码
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        invite_data = {
            "role": "user",
            "is_active": True,
            "expires_at": None,
            "batch_id": "test-batch-001",
            "batch_color": "#FF5722",
            "batch_created_at": datetime.now().isoformat(),
        }

        print(f"创建邀请码数据: {json.dumps(invite_data, indent=2)}")

        create_response = await client.post(
            "http://localhost:8000/api/v1/invite-codes/admin/invite-codes",
            headers=headers,
            json=invite_data,
        )

        print(f"创建邀请码响应状态: {create_response.status_code}")
        print(f"创建邀请码响应内容: {create_response.text}")

        if create_response.status_code == 200:
            result = create_response.json()
            print(f"创建成功！邀请码: {result.get('code')}")
            print(f"批次ID: {result.get('batch_id')}")
            print(f"批次颜色: {result.get('batch_color')}")
        else:
            print("创建失败！")


if __name__ == "__main__":
    asyncio.run(test_invite_creation())
