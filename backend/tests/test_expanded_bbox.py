#!/usr/bin/env python3

"""
测试扩展边界框对性别识别准确率的影响
"""

import argparse
import logging
import os
import sys
import time
from pathlib import Path
from typing import Any, Optional, Union

import cv2
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s - %(message)s",
    handlers=[logging.StreamHandler()],
)

logger = logging.getLogger("expanded_bbox_test")

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入DeepFace包装器
from emotionai.core.ml.deepface_wrapper import DeepFaceWrapper
from emotionai.core.ml.face_detection import FaceDetector


def test_with_original_and_expanded_bbox(image_path: str) -> dict[str, Any]:
    """
    测试原始边界框和扩展边界框对性别识别的影响

    Args:
        image_path: 图片路径

    Returns:
        包含测试结果的字典
    """
    try:
        # 加载图片
        logger.info(f"加载图片: {image_path}")
        image = cv2.imread(image_path)
        if image is None:
            logger.error(f"无法加载图片: {image_path}")
            return {"success": False, "error": "无法加载图片"}

        # 检测人脸
        logger.info("检测人脸...")
        detector = FaceDetector.get_instance()
        faces, marked_image = detector.detect_faces_and_generate_image(image)

        if not faces:
            logger.error("未检测到人脸")
            return {"success": False, "error": "未检测到人脸"}

        logger.info(f"检测到 {len(faces)} 个人脸")

        # 创建DeepFace包装器
        deepface_wrapper = DeepFaceWrapper()

        # 分析每个人脸
        results = []
        for _, face in enumerate(faces):
            logger.info(f"分析人脸 #{i+1}")

            # 获取人脸区域
            bbox = face.get("bbox", [0, 0, 0, 0])
            x1, y1, x2, y2 = bbox

            # 确保坐标有效
            x1 = max(0, x1)
            y1 = max(0, y1)
            x2 = min(image.shape[1], x2)
            y2 = min(image.shape[0], y2)

            # 裁剪原始人脸
            original_face_img = image[y1:y2, x1:x2]
            if original_face_img.size == 0:
                logger.warning(f"裁剪原始人脸区域失败: {bbox}")
                continue

            # 计算扩展边界框
            bbox_width = x2 - x1
            bbox_height = y2 - y1

            # 定义人脸大小阈值（像素）
            small_face_threshold = 100  # 小于100像素宽度的视为小人脸
            large_face_threshold = 300  # 大于300像素宽度的视为大人脸

            # 根据人脸大小动态调整扩展比例
            if bbox_width < small_face_threshold:
                # 小人脸需要更大的扩展比例
                expansion_x_ratio = 0.30  # 水平方向扩展30%
                expansion_y_top_ratio = 0.45  # 上方扩展45%
                expansion_y_bottom_ratio = 0.30  # 下方扩展30%
                logger.info(f"检测到小人脸 (宽度={bbox_width}px)，使用更大的扩展比例")
            elif bbox_width > large_face_threshold:
                # 大人脸可以使用较小的扩展比例
                expansion_x_ratio = 0.20  # 水平方向扩展20%
                expansion_y_top_ratio = 0.35  # 上方扩展35%
                expansion_y_bottom_ratio = 0.20  # 下方扩展20%
                logger.info(f"检测到大人脸 (宽度={bbox_width}px)，使用较小的扩展比例")
            else:
                # 中等大小的人脸使用默认扩展比例
                expansion_x_ratio = 0.25  # 水平方向扩展25%
                expansion_y_top_ratio = 0.40  # 上方扩展40%
                expansion_y_bottom_ratio = 0.25  # 下方扩展25%
                logger.info(
                    f"检测到中等大小人脸 (宽度={bbox_width}px)，使用默认扩展比例"
                )

            # 计算实际扩展像素数
            expansion_x = int(bbox_width * expansion_x_ratio)
            expansion_y_top = int(bbox_height * expansion_y_top_ratio)
            expansion_y_bottom = int(bbox_height * expansion_y_bottom_ratio)

            logger.info(
                f"边界框扩展比例: 水平={expansion_x_ratio:.2f}, 上方={expansion_y_top_ratio:.2f}, 下方={expansion_y_bottom_ratio:.2f}"
            )

            # 应用不同方向的扩展
            ex1 = max(0, x1 - expansion_x)  # 左侧扩展
            ey1 = max(0, y1 - expansion_y_top)  # 上方扩展（额头区域）
            ex2 = min(image.shape[1], x2 + expansion_x)  # 右侧扩展
            ey2 = min(image.shape[0], y2 + expansion_y_bottom)  # 下方扩展（下巴区域）

            # 裁剪扩展人脸
            expanded_face_img = image[ey1:ey2, ex1:ex2]
            if expanded_face_img.size == 0:
                logger.warning(f"裁剪扩展人脸区域失败: [{ex1}, {ey1}, {ex2}, {ey2}]")
                continue

            # 保存裁剪的人脸图像用于可视化
            original_face_path = (
                f"{os.path.splitext(image_path)[0]}_face_{i+1}_original.jpg"
            )
            expanded_face_path = (
                f"{os.path.splitext(image_path)[0]}_face_{i+1}_expanded.jpg"
            )
            cv2.imwrite(original_face_path, original_face_img)
            cv2.imwrite(expanded_face_path, expanded_face_img)

            # 转换为RGB格式
            original_rgb_face = cv2.cvtColor(original_face_img, cv2.COLOR_BGR2RGB)
            expanded_rgb_face = cv2.cvtColor(expanded_face_img, cv2.COLOR_BGR2RGB)

            # 分析原始人脸
            logger.info("分析原始人脸...")
            original_result = deepface_wrapper.analyze(original_rgb_face)

            # 分析扩展人脸
            logger.info("分析扩展人脸...")
            expanded_result = deepface_wrapper.analyze(expanded_rgb_face)

            # 提取性别信息
            original_gender = original_result.get("gender", "unknown")
            original_confidence = original_result.get("gender_confidence", 0.0)

            expanded_gender = expanded_result.get("gender", "unknown")
            expanded_confidence = expanded_result.get("gender_confidence", 0.0)

            logger.info(
                f"人脸 #{i+1} 原始边界框 - 性别: {original_gender}, 置信度: {original_confidence:.4f}"
            )
            logger.info(
                f"人脸 #{i+1} 扩展边界框 - 性别: {expanded_gender}, 置信度: {expanded_confidence:.4f}"
            )

            # 添加到结果列表
            results.append(
                {
                    "face_number": i + 1,
                    "bbox": bbox,
                    "expanded_bbox": [ex1, ey1, ex2, ey2],
                    "original": {
                        "gender": original_gender,
                        "confidence": original_confidence,
                        "face_path": original_face_path,
                    },
                    "expanded": {
                        "gender": expanded_gender,
                        "confidence": expanded_confidence,
                        "face_path": expanded_face_path,
                    },
                }
            )

        # 保存标记图像
        output_path = f"{os.path.splitext(image_path)[0]}_marked.jpg"
        cv2.imwrite(output_path, marked_image)
        logger.info(f"已保存标记图像到: {output_path}")

        return {
            "success": True,
            "faces_count": len(faces),
            "results": results,
            "marked_image_path": output_path,
        }

    except Exception as e:
        logger.error(f"分析失败: {str(e)}", exc_info=True)
        return {"success": False, "error": str(e)}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试扩展边界框对性别识别准确率的影响")
    parser.add_argument("--image", type=str, help="图片路径")

    args = parser.parse_args()

    if args.image:
        # 测试单张图片
        result = test_with_original_and_expanded_bbox(args.image)

        if result["success"]:
            # 打印结果摘要
            logger.info("\n=== 测试结果摘要 ===")
            logger.info(f"图片: {args.image}")
            logger.info(f"检测到的人脸数量: {result['faces_count']}")

            # 统计性别识别结果
            confidence_improved = 0
            gender_changed = 0

            for face_result in result["results"]:
                face_num = face_result["face_number"]
                original = face_result["original"]
                expanded = face_result["expanded"]

                # 检查置信度是否提高
                if expanded["confidence"] > original["confidence"]:
                    confidence_improved += 1
                    logger.info(
                        f"人脸 #{face_num} - 置信度提高: {original['confidence']:.4f} -> {expanded['confidence']:.4f} (+{expanded['confidence'] - original['confidence']:.4f})"
                    )

                # 检查性别是否改变
                if expanded["gender"] != original["gender"]:
                    gender_changed += 1
                    logger.info(
                        f"人脸 #{face_num} - 性别改变: {original['gender']} -> {expanded['gender']}"
                    )

            # 打印总结
            total_faces = len(result["results"])
            logger.info("\n总结:")
            logger.info(f"总共分析了 {total_faces} 个人脸")
            logger.info(
                f"置信度提高的人脸数量: {confidence_improved} ({confidence_improved/total_faces*100:.2f}%)"
            )
            logger.info(
                f"性别改变的人脸数量: {gender_changed} ({gender_changed/total_faces*100:.2f}%)"
            )

            # 提示查看生成的图像
            logger.info("\n生成的图像:")
            logger.info(f"标记图像: {result['marked_image_path']}")
            for face_result in result["results"]:
                logger.info(f"人脸 #{face_result['face_number']}:")
                logger.info(f"  原始人脸: {face_result['original']['face_path']}")
                logger.info(f"  扩展人脸: {face_result['expanded']['face_path']}")
        else:
            logger.error(f"测试失败: {result.get('error', '未知错误')}")
    else:
        logger.error("请提供--image参数")
        parser.print_help()


if __name__ == "__main__":
    main()
