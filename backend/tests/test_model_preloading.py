#!/usr/bin/env python
"""
测试模型预加载功能

此脚本用于测试模型预加载功能，包括异步加载和状态查询。
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path

# 设置日志级别
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

# 导入模型预加载模块
from emotionai.core.ml.model_preloader import (
    ModelStatus,
    get_preload_status,
    preload_all_models,
    start_preload_models_async,
    wait_for_models_loaded,
)


async def test_model_preloading():
    """测试模型预加载功能"""
    logger.info("开始测试模型预加载功能")

    # 启动异步预加载
    logger.info("启动异步预加载...")
    start_preload_models_async()

    # 等待一段时间，让预加载开始
    await asyncio.sleep(1)

    # 获取预加载状态
    status = get_preload_status()
    logger.info(f"预加载状态: {status}")

    # 等待所有模型加载完成，最多等待60秒
    logger.info("等待所有模型加载完成...")
    loaded = await wait_for_models_loaded(timeout=60.0)

    if loaded:
        logger.info("所有模型已加载完成")
    else:
        logger.warning("等待模型加载超时")

    # 获取最终状态
    final_status = get_preload_status()
    logger.info(f"最终预加载状态: {final_status}")

    # 检查每个模型的状态
    for _, model_info in final_status["models"].items():
        status = model_info["status"]
        error = model_info["error"]
        load_time = model_info.get("load_time")

        if status == ModelStatus.LOADED.value:
            logger.info(f"模型 {model_name} 加载成功，耗时: {load_time:.2f}秒")
        elif status == ModelStatus.FAILED.value:
            logger.error(f"模型 {model_name} 加载失败: {error}")
        else:
            logger.warning(f"模型 {model_name} 状态: {status}")

    # 统计信息
    loaded_count = final_status["loaded_models_count"]
    total_count = final_status["total_models_count"]
    failed_count = final_status["failed_models_count"]

    logger.info(
        f"加载统计: 成功 {loaded_count}/{total_count}，失败 {failed_count}/{total_count}"
    )

    return loaded_count, total_count, failed_count


async def test_model_preloading_with_specific_models():
    """测试特定模型的预加载功能"""
    logger.info("开始测试特定模型的预加载功能")

    # 启动预加载
    logger.info("启动预加载...")
    preload_all_models()

    # 等待特定模型加载完成
    specific_models = ["face_detector", "face_attributes_ensemble"]
    logger.info(f"等待特定模型加载完成: {specific_models}")

    loaded = await wait_for_models_loaded(models=specific_models, timeout=30.0)

    if loaded:
        logger.info(f"指定的模型 {specific_models} 已加载完成")
    else:
        logger.warning(f"等待指定模型 {specific_models} 加载超时")

    # 获取最终状态
    final_status = get_preload_status()

    # 检查特定模型的状态
    for model_name in specific_models:
        if model_name in final_status["models"]:
            model_info = final_status["models"][model_name]
            status = model_info["status"]
            error = model_info["error"]
            load_time = model_info.get("load_time")

            if status == ModelStatus.LOADED.value:
                logger.info(f"模型 {model_name} 加载成功，耗时: {load_time:.2f}秒")
            elif status == ModelStatus.FAILED.value:
                logger.error(f"模型 {model_name} 加载失败: {error}")
            else:
                logger.warning(f"模型 {model_name} 状态: {status}")
        else:
            logger.warning(f"模型 {model_name} 未在预加载列表中")

    return final_status


async def main():
    """主函数"""
    # 测试模型预加载功能
    await test_model_preloading()

    # 清理资源
    from emotionai.core.ml.model_preloader import cleanup_on_shutdown

    cleanup_on_shutdown()


if __name__ == "__main__":
    asyncio.run(main())
