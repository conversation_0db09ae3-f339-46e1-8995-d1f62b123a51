#!/usr/bin/env python

"""
情绪识别模型基准测试

比较不同情绪识别模型的性能，包括准确度、置信度和处理时间
"""

import asyncio
import json
import logging
import os
import sys
import time
from pathlib import Path
from typing import Any, Optional

import cv2
import matplotlib.font_manager as fm
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体支持
# 尝试加载系统中的中文字体
chinese_fonts = [
    "/System/Library/Fonts/PingFang.ttc",  # macOS
    "/System/Library/Fonts/STHeiti Light.ttc",  # macOS
    "/System/Library/Fonts/STHeiti Medium.ttc",  # macOS
    "/System/Library/Fonts/Hiragino Sans GB.ttc",  # macOS
    "/Library/Fonts/Arial Unicode.ttf",  # macOS
    "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",  # Linux
    "/usr/share/fonts/truetype/arphic/uming.ttc",  # Linux
    "C:/Windows/Fonts/simhei.ttf",  # Windows
    "C:/Windows/Fonts/msyh.ttf",  # Windows
]

# 查找可用的中文字体
chinese_font_path = None
for font_path in chinese_fonts:
    if os.path.exists(font_path):
        chinese_font_path = font_path
        break

# 如果找到中文字体，设置为matplotlib的默认字体
if chinese_font_path:
    plt.rcParams["font.family"] = fm.FontProperties(fname=chinese_font_path).get_name()
else:
    # 如果没有找到中文字体，使用英文标签
    logging.warning("未找到中文字体，将使用英文标签")

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志级别
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

from emotionai.core.ml.emotion_ensemble import EmotionEnsemble
from emotionai.core.ml.face_detection import FaceDetector
from emotionai.core.ml.loaders.emotion_loaders import register_emotion_models
from emotionai.core.ml.loaders.face_detection_loaders import (
    register_face_detection_models,
)

# 导入必要的模块
from emotionai.core.ml.model_loader import ModelRegistry

# 测试图像路径
TEST_IMAGES = [
    "/Volumes/acasis/ema2_20250417/frontend/public/examples/original_example.jpg",
    # 可以添加更多测试图像
]

# 情绪标签映射（用于标准化不同模型的输出）
EMOTION_MAP = {
    "angry": "Angry",
    "anger": "Angry",
    "disgust": "Disgust",
    "fear": "Fear",
    "happy": "Happy",
    "happiness": "Happy",
    "neutral": "Neutral",
    "sad": "Sad",
    "sadness": "Sad",
    "surprise": "Surprise",
    "surprised": "Surprise",
}

# 检查是否找到中文字体
use_english = chinese_font_path is None

# 模型配置
MODEL_CONFIGS = [
    {
        "name": "vit_fer",
        "display_name": "Vision Transformer (ViT)",
        "color": "blue",
        "marker": "o",
    },
    {
        "name": "swin_fer",
        "display_name": "Swin Transformer",
        "color": "red",
        "marker": "s",
    },
    {"name": "cnn_emotion", "display_name": "CNN", "color": "green", "marker": "^"},
    {
        "name": "trakov_vit",
        "display_name": "Trakov ViT",
        "color": "purple",
        "marker": "d",
    },
    {
        "name": "elena_ryumina",
        "display_name": "Elena Ryumina ResNet50",
        "color": "orange",
        "marker": "x",
    },
]

# 情绪集成器配置
ENSEMBLE_CONFIGS = [
    {
        "name": "default",
        "display_name": "Default Config" if use_english else "默认配置",
        "weights": {"swin_fer": 0.4, "cnn_emotion": 0.2, "trakov_vit": 0.4},
    },
    {
        "name": "vit_optimized",
        "display_name": "ViT Optimized Config" if use_english else "ViT优化配置",
        "weights": {"swin_fer": 0.1, "cnn_emotion": 0.2, "trakov_vit": 0.7},
    },
    {
        "name": "elena_optimized",
        "display_name": "Elena Optimized Config" if use_english else "Elena优化配置",
        "weights": {
            "swin_fer": 0.1,
            "cnn_emotion": 0.1,
            "trakov_vit": 0.4,
            "elena_ryumina": 0.4,
        },
    },
]


async def test_model(
    model_name: str, image: np.ndarray, face_image: np.ndarray, landmarks: list = None
) -> dict[str, Any]:
    """测试单个模型的性能"""
    model = ModelRegistry.get(model_name)
    if model is None:
        logger.error(f"模型 {model_name} 未注册")
        return {
            "model": model_name,
            "success": False,
            "error": f"模型 {model_name} 未注册",
        }

    if not model.loaded:
        model.load()

    # 测量处理时间
    start_time = time.time()

    # 使用特征点进行预测（如果提供）
    if landmarks is not None and hasattr(model, "predict_with_landmarks"):
        result = model.predict_with_landmarks(face_image, landmarks)
    else:
        result = model.predict(face_image)

    # 计算处理时间
    processing_time = time.time() - start_time

    # 添加处理时间到结果
    result["processing_time"] = processing_time
    result["model"] = model_name

    return result


async def test_ensemble(
    ensemble_config: dict[str, Any],
    image: np.ndarray,
    face_image: np.ndarray,
    landmarks: list = None,
) -> dict[str, Any]:
    """测试情绪集成器的性能"""
    # 创建情绪集成器
    ensemble = EmotionEnsemble()

    # 更新权重
    ensemble.update_weights(ensemble_config["weights"])

    # 测量处理时间
    start_time = time.time()

    # 预测情绪
    result = await ensemble.analyze(face_image)

    # 计算处理时间
    processing_time = time.time() - start_time

    # 添加处理时间和配置信息到结果
    result["processing_time"] = processing_time
    result["ensemble_config"] = ensemble_config["name"]

    return result


def standardize_emotion(emotion: str) -> str:
    """标准化情绪标签"""
    return EMOTION_MAP.get(emotion.lower(), emotion)


def plot_results(results: list[dict[str, Any]], output_path: str):
    """绘制结果图表"""
    # 检查是否找到中文字体
    use_english = chinese_font_path is None

    # 提取模型名称和处理时间
    model_names = []
    processing_times = []
    confidence_values = []

    for result in results:
        if "model" in result:
            # 单个模型的结果
            model_name = result["model"]
            model_config = next(
                (config for config in MODEL_CONFIGS if config["name"] == model_name),
                None,
            )
            display_name = model_config["display_name"] if model_config else model_name

            model_names.append(display_name)
            processing_times.append(result["processing_time"])

            # 获取主要情绪的置信度
            dominant_emotion = result.get("dominant_emotion", "unknown")
            confidence = result.get("emotions", {}).get(dominant_emotion, 0.0)
            confidence_values.append(confidence)
        elif "ensemble_config" in result:
            # 情绪集成器的结果
            ensemble_config = result["ensemble_config"]
            config = next(
                (
                    config
                    for config in ENSEMBLE_CONFIGS
                    if config["name"] == ensemble_config
                ),
                None,
            )
            if config:
                display_name = (
                    f"Ensemble ({config['display_name']})"
                    if use_english
                    else f"集成器 ({config['display_name']})"
                )
            else:
                display_name = (
                    f"Ensemble ({ensemble_config})"
                    if use_english
                    else f"集成器 ({ensemble_config})"
                )
            model_names.append(display_name)
            processing_times.append(result["processing_time"])

            # 获取主要情绪的置信度
            dominant_emotion = result.get("dominant_emotion", "unknown")
            confidence = result.get("emotions", {}).get(dominant_emotion, 0.0)
            confidence_values.append(confidence)

    # 创建子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 绘制处理时间柱状图
    ax1.bar(model_names, processing_times, color="skyblue")
    if use_english:
        ax1.set_title("Model Processing Time Comparison")
        ax1.set_xlabel("Model")
        ax1.set_ylabel("Processing Time (seconds)")
    else:
        ax1.set_title("模型处理时间对比")
        ax1.set_xlabel("模型")
        ax1.set_ylabel("处理时间 (秒)")
    ax1.set_ylim(0, max(processing_times) * 1.2)
    ax1.grid(axis="y", linestyle="--", alpha=0.7)

    # 旋转x轴标签，确保可读性
    plt.setp(ax1.get_xticklabels(), rotation=45, ha="right")

    # 绘制置信度柱状图
    ax2.bar(model_names, confidence_values, color="lightgreen")
    if use_english:
        ax2.set_title("Model Confidence Comparison")
        ax2.set_xlabel("Model")
        ax2.set_ylabel("Confidence")
    else:
        ax2.set_title("模型置信度对比")
        ax2.set_xlabel("模型")
        ax2.set_ylabel("置信度")
    ax2.set_ylim(0, 1.0)
    ax2.grid(axis="y", linestyle="--", alpha=0.7)

    # 旋转x轴标签，确保可读性
    plt.setp(ax2.get_xticklabels(), rotation=45, ha="right")

    # 调整布局
    plt.tight_layout()

    # 保存图表
    plt.savefig(output_path)
    logger.info(f"结果图表已保存到: {output_path}")


async def main():
    # 注册模型
    register_emotion_models()
    register_face_detection_models()

    # 创建人脸检测器
    detector = FaceDetector()

    # 存储所有结果
    all_results = []

    # 处理每个测试图像
    for image_path in TEST_IMAGES:
        logger.info(f"处理测试图像: {image_path}")

        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            logger.error(f"无法加载图像: {image_path}")
            continue

        # 检测人脸
        yolo_face_detector = ModelRegistry.get("yolo_face")
        if yolo_face_detector is None:
            logger.error("无法加载YOLO人脸检测器")
            continue

        if not yolo_face_detector.loaded:
            yolo_face_detector.load()

        # 检测人脸
        yolo_result = yolo_face_detector.predict(image)
        yolo_faces = yolo_result.get("faces", [])

        if not yolo_faces:
            logger.error(f"在图像 {image_path} 中未检测到人脸")
            continue

        logger.info(f"YOLO检测到{len(yolo_faces)}个人脸")

        # 使用MediaPipe处理人脸，获取特征点
        faces_with_landmarks, marked_image = detector.process_with_yolo_faces(
            image, yolo_faces
        )

        logger.info(f"MediaPipe处理了{len(faces_with_landmarks)}个人脸")

        # 对每个人脸进行情绪分析
        for i, face in enumerate(faces_with_landmarks):
            # 获取人脸边界框
            bbox = face.get("bbox")
            if not bbox:
                logger.warning(f"人脸 {i+1} 没有边界框")
                continue

            x1, y1, x2, y2 = bbox
            face_image = image[y1:y2, x1:x2].copy()

            # 获取特征点
            landmarks = face.get("landmarks")

            # 测试每个模型
            model_results = []
            for model_config in MODEL_CONFIGS:
                model_name = model_config["name"]
                logger.info(f"使用 {model_name} 模型分析人脸 {i+1}")

                result = await test_model(model_name, image, face_image, landmarks)
                if result.get("success", False) != False:
                    model_results.append(result)

                    # 标准化情绪标签
                    if "dominant_emotion" in result:
                        result["dominant_emotion"] = standardize_emotion(
                            result["dominant_emotion"]
                        )

                    # 记录结果
                    logger.info(
                        f"{model_config['display_name']} 模型预测结果: {result.get('dominant_emotion')}, 置信度: {result.get('emotions', {}).get(result.get('dominant_emotion'), 0.0):.4f}, 耗时: {result.get('processing_time', 0.0):.4f}秒"
                    )

            # 测试每个情绪集成器配置
            ensemble_results = []
            for ensemble_config in ENSEMBLE_CONFIGS:
                logger.info(
                    f"使用 {ensemble_config['display_name']} 配置的情绪集成器分析人脸 {i+1}"
                )

                result = await test_ensemble(
                    ensemble_config, image, face_image, landmarks
                )
                if result.get("success", False) != False:
                    ensemble_results.append(result)

                    # 标准化情绪标签
                    if "dominant_emotion" in result:
                        result["dominant_emotion"] = standardize_emotion(
                            result["dominant_emotion"]
                        )

                    # 记录结果
                    logger.info(
                        f"情绪集成器 ({ensemble_config['display_name']}) 预测结果: {result.get('dominant_emotion')}, 置信度: {result.get('emotions', {}).get(result.get('dominant_emotion'), 0.0):.4f}, 耗时: {result.get('processing_time', 0.0):.4f}秒"
                    )

            # 合并结果
            all_results.extend(model_results)
            all_results.extend(ensemble_results)

            # 绘制结果图表
            output_path = f"/tmp/emotion_benchmark_face_{i+1}.png"
            plot_results(model_results + ensemble_results, output_path)

            # 打印结果比较
            logger.info(f"人脸 {i+1} 的情绪分析结果比较:")
            for result in model_results:
                model_name = result.get("model", "unknown")
                model_config = next(
                    (
                        config
                        for config in MODEL_CONFIGS
                        if config["name"] == model_name
                    ),
                    None,
                )
                display_name = (
                    model_config["display_name"] if model_config else model_name
                )
                dominant_emotion = result.get("dominant_emotion", "unknown")
                confidence = result.get("emotions", {}).get(dominant_emotion, 0.0)
                processing_time = result.get("processing_time", 0.0)

                logger.info(
                    f"  {display_name}: {dominant_emotion}, 置信度: {confidence:.4f}, 耗时: {processing_time:.4f}秒"
                )

            for result in ensemble_results:
                ensemble_config = result.get("ensemble_config", "unknown")
                config = next(
                    (
                        config
                        for config in ENSEMBLE_CONFIGS
                        if config["name"] == ensemble_config
                    ),
                    None,
                )
                display_name = config["display_name"] if config else ensemble_config

                dominant_emotion = result.get("dominant_emotion", "unknown")
                confidence = result.get("emotions", {}).get(dominant_emotion, 0.0)
                processing_time = result.get("processing_time", 0.0)

                logger.info(
                    f"  情绪集成器 ({display_name}): {dominant_emotion}, 置信度: {confidence:.4f}, 耗时: {processing_time:.4f}秒"
                )

    # 保存所有结果到JSON文件
    with open("/tmp/emotion_benchmark_results.json", "w") as f:
        json.dump(all_results, f, indent=2)

    logger.info("基准测试完成，结果已保存到 /tmp/emotion_benchmark_results.json")


if __name__ == "__main__":
    asyncio.run(main())
