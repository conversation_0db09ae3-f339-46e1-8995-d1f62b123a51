#!/usr/bin/env python3

"""
比较不同性别识别模型的准确率
"""

import argparse
import logging
import os
import sys
import time
from pathlib import Path
from typing import Any, Optional, Union

import cv2
import numpy as np
import torch

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s - %(message)s",
    handlers=[logging.StreamHandler()],
)

logger = logging.getLogger("gender_models_comparison")

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入DeepFace包装器
from emotionai.core.ml.deepface_wrapper import DeepFaceWrapper
from emotionai.core.ml.face_detection import FaceDetector

# 尝试导入其他可能的性别识别模型
try:
    from deepface import DeepFace

    DEEPFACE_AVAILABLE = True
except ImportError:
    logger.warning("无法导入DeepFace库，将只使用项目内置的DeepFace包装器")
    DEEPFACE_AVAILABLE = False

# 尝试导入InsightFace
try:
    import insightface

    INSIGHTFACE_AVAILABLE = True
except ImportError:
    logger.warning("无法导入InsightFace库，将不使用InsightFace进行性别识别")
    INSIGHTFACE_AVAILABLE = False


class GenderModelTester:
    """性别识别模型测试器"""

    def __init__(self):
        """初始化测试器"""
        # 初始化人脸检测器
        self.face_detector = FaceDetector.get_instance()

        # 初始化DeepFace包装器
        self.deepface_wrapper = DeepFaceWrapper()

        # 初始化其他模型
        self.init_other_models()

    def init_other_models(self):
        """初始化其他性别识别模型"""
        self.models = {
            "deepface_wrapper": self.deepface_wrapper,
        }

        # 如果可用，添加原始DeepFace
        if DEEPFACE_AVAILABLE:
            logger.info("添加原始DeepFace模型")
            self.models["deepface_original"] = "deepface_original"

        # 如果可用，添加InsightFace
        if INSIGHTFACE_AVAILABLE:
            logger.info("添加InsightFace模型")
            try:
                # 初始化InsightFace模型
                self.insightface_model = insightface.app.FaceAnalysis()
                self.insightface_model.prepare(ctx_id=0, det_size=(640, 640))
                self.models["insightface"] = self.insightface_model
            except Exception as e:
                logger.error(f"初始化InsightFace模型失败: {str(e)}")

    def analyze_with_deepface_wrapper(self, face_img):
        """使用DeepFace包装器分析性别"""
        # 转换为RGB格式
        rgb_face = cv2.cvtColor(face_img, cv2.COLOR_BGR2RGB)

        # 分析人脸
        result = self.deepface_wrapper.analyze(rgb_face)

        # 提取性别信息
        gender = result.get("gender", "unknown")
        gender_confidence = result.get("gender_confidence", 0.0)

        return {"gender": gender, "confidence": gender_confidence}

    def analyze_with_deepface_original(self, face_img):
        """使用原始DeepFace分析性别"""
        if not DEEPFACE_AVAILABLE:
            return {"gender": "unknown", "confidence": 0.0}

        try:
            # 使用DeepFace进行分析
            result = DeepFace.analyze(
                img_path=face_img,
                actions=["gender"],
                detector_backend="opencv",
                enforce_detection=False,
                silent=True,
            )

            # 处理结果
            if isinstance(result, list) and len(result) > 0:
                face_data = result[0]
                gender = face_data.get("dominant_gender", "unknown").lower()
                gender_confidence = face_data.get("gender", {}).get(
                    face_data.get("dominant_gender", ""), 0.5
                )

                return {"gender": gender, "confidence": gender_confidence}
            else:
                return {"gender": "unknown", "confidence": 0.0}
        except Exception as e:
            logger.error(f"DeepFace分析失败: {str(e)}")
            return {"gender": "unknown", "confidence": 0.0}

    def analyze_with_insightface(self, face_img, full_image=None):
        """使用InsightFace分析性别"""
        if not INSIGHTFACE_AVAILABLE:
            return {"gender": "unknown", "confidence": 0.0}

        try:
            # InsightFace通常需要完整图像进行分析
            image_to_analyze = full_image if full_image is not None else face_img

            # 分析人脸
            faces = self.insightface_model.get(image_to_analyze)

            if len(faces) > 0:
                # 获取第一个人脸的性别
                face = faces[0]
                gender_idx = face.gender
                gender = "female" if gender_idx == 0 else "male"

                # InsightFace不提供置信度，使用默认值
                return {"gender": gender, "confidence": 0.9}  # 默认高置信度
            else:
                return {"gender": "unknown", "confidence": 0.0}
        except Exception as e:
            logger.error(f"InsightFace分析失败: {str(e)}")
            return {"gender": "unknown", "confidence": 0.0}

    def analyze_face(self, face_img, model_name, full_image=None):
        """使用指定模型分析人脸性别"""
        if model_name == "deepface_wrapper":
            return self.analyze_with_deepface_wrapper(face_img)
        elif model_name == "deepface_original":
            return self.analyze_with_deepface_original(face_img)
        elif model_name == "insightface":
            return self.analyze_with_insightface(face_img, full_image)
        else:
            logger.error(f"未知模型: {model_name}")
            return {"gender": "unknown", "confidence": 0.0}

    def test_image(self, image_path):
        """测试单张图片的性别识别准确率"""
        try:
            # 加载图片
            logger.info(f"加载图片: {image_path}")
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"无法加载图片: {image_path}")
                return {"success": False, "error": "无法加载图片"}

            # 检测人脸
            logger.info("检测人脸...")
            faces, marked_image = self.face_detector.detect_faces_and_generate_image(
                image
            )

            if not faces:
                logger.error("未检测到人脸")
                return {"success": False, "error": "未检测到人脸"}

            logger.info(f"检测到 {len(faces)} 个人脸")

            # 分析每个人脸
            results = []
            for _, face in enumerate(faces):
                logger.info(f"分析人脸 #{i+1}")

                # 获取人脸区域
                bbox = face.get("bbox", [0, 0, 0, 0])
                x1, y1, x2, y2 = bbox

                # 确保坐标有效
                x1 = max(0, x1)
                y1 = max(0, y1)
                x2 = min(image.shape[1], x2)
                y2 = min(image.shape[0], y2)

                # 裁剪人脸
                face_img = image[y1:y2, x1:x2]
                if face_img.size == 0:
                    logger.warning(f"裁剪人脸区域失败: {bbox}")
                    continue

                # 使用每个模型分析
                model_results = {}
                for model_name in self.models:  # 直接遍历字典
                    logger.info(f"使用 {model_name} 分析...")
                    result = self.analyze_face(face_img, model_name, image)
                    model_results[model_name] = result
                    logger.info(
                        f"{model_name} 结果: 性别={result['gender']}, 置信度={result['confidence']:.4f}"
                    )

                # 添加到结果列表
                results.append(
                    {"face_number": i + 1, "bbox": bbox, "model_results": model_results}
                )

            # 保存标记图像
            output_path = f"{os.path.splitext(image_path)[0]}_marked.jpg"
            cv2.imwrite(output_path, marked_image)
            logger.info(f"已保存标记图像到: {output_path}")

            return {
                "success": True,
                "faces_count": len(faces),
                "results": results,
                "marked_image_path": output_path,
            }

        except Exception as e:
            logger.error(f"分析失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}

    def test_directory(self, directory):
        """测试目录中所有图片的性别识别准确率"""
        # 获取目录中的所有图片
        image_extensions = [".jpg", ".jpeg", ".png", ".bmp"]
        image_paths = []

        for ext in image_extensions:
            image_paths.extend(list(Path(directory).glob(f"*{ext}")))
            image_paths.extend(list(Path(directory).glob(f"*{ext.upper()}")))

        if not image_paths:
            logger.error(f"目录中没有图片: {directory}")
            return {"success": False, "error": "目录中没有图片"}

        logger.info(f"找到 {len(image_paths)} 张图片")

        # 分析每张图片
        results = {}
        for image_path in image_paths:
            logger.info(f"分析图片: {image_path}")
            result = self.test_image(str(image_path))
            results[str(image_path)] = result

        # 计算统计信息
        stats = self.calculate_statistics(results)

        return {
            "success": True,
            "images_count": len(image_paths),
            "results": results,
            "statistics": stats,
        }

    def calculate_statistics(self, results):
        """计算统计信息"""
        stats = {}

        # 初始化统计数据
        for model_name in self.models:  # 直接遍历字典
            stats[model_name] = {
                "total_faces": 0,
                "male_count": 0,
                "female_count": 0,
                "unknown_count": 0,
                "high_confidence_count": 0,  # 置信度 > 0.8
            }

        # 统计每个模型的结果
        for _, image_result in results.items():  # 使用_表示未使用的变量
            if image_result["success"]:
                for face_result in image_result["results"]:
                    for model_name, model_result in face_result[
                        "model_results"
                    ].items():
                        stats[model_name]["total_faces"] += 1

                        if model_result["gender"] == "male":
                            stats[model_name]["male_count"] += 1
                        elif model_result["gender"] == "female":
                            stats[model_name]["female_count"] += 1
                        else:
                            stats[model_name]["unknown_count"] += 1

                        if model_result["confidence"] > 0.8:
                            stats[model_name]["high_confidence_count"] += 1

        # 计算百分比
        for _, model_stats in stats.items():  # 使用_表示未使用的变量
            total = model_stats["total_faces"]
            if total > 0:
                model_stats["male_percent"] = model_stats["male_count"] / total * 100
                model_stats["female_percent"] = (
                    model_stats["female_count"] / total * 100
                )
                model_stats["unknown_percent"] = (
                    model_stats["unknown_count"] / total * 100
                )
                model_stats["high_confidence_percent"] = (
                    model_stats["high_confidence_count"] / total * 100
                )

        return stats


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="比较不同性别识别模型的准确率")
    parser.add_argument("--image", type=str, help="单张图片路径")
    parser.add_argument("--dir", type=str, help="图片目录路径")

    args = parser.parse_args()

    # 创建测试器
    tester = GenderModelTester()

    if args.image:
        # 测试单张图片
        result = tester.test_image(args.image)
        logger.info(f"分析结果: {result}")
    elif args.dir:
        # 测试目录中的所有图片
        result = tester.test_directory(args.dir)
        logger.info(f"总结: 分析了 {result['images_count']} 张图片")

        # 打印统计信息
        for _, stats in result["statistics"].items():
            logger.info(f"\n{model_name} 统计信息:")
            logger.info(f"总共分析了 {stats['total_faces']} 个人脸")
            logger.info(f"男性: {stats['male_count']} ({stats['male_percent']:.2f}%)")
            logger.info(
                f"女性: {stats['female_count']} ({stats['female_percent']:.2f}%)"
            )
            logger.info(
                f"未知: {stats['unknown_count']} ({stats['unknown_percent']:.2f}%)"
            )
            logger.info(
                f"高置信度(>0.8): {stats['high_confidence_count']} ({stats['high_confidence_percent']:.2f}%)"
            )
    else:
        logger.error("请提供--image或--dir参数")
        parser.print_help()


if __name__ == "__main__":
    main()
