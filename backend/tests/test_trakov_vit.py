#!/usr/bin/env python

"""
测试Trakov VIT情绪识别模型
"""

import asyncio
import logging
import sys
from pathlib import Path

import cv2
import matplotlib.pyplot as plt
import numpy as np

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from emotionai.core.ml.emotion_ensemble import EmotionEnsemble
from emotionai.core.ml.loaders.emotion_loaders import register_emotion_models

# 导入必要的模块
from emotionai.core.ml.model_loader import ModelRegistry


async def test_trakov_vit_model():
    """测试Trakov VIT情绪识别模型"""
    # 注册所有情绪识别模型
    register_emotion_models()

    # 获取Trakov VIT模型
    trakov_vit_model = ModelRegistry.get("trakov_vit")

    if trakov_vit_model is None:
        logger.error("Trakov VIT模型未注册")
        return

    # 加载模型
    if not trakov_vit_model.loaded:
        trakov_vit_model.load()

    # 加载测试图像
    image_path = "/Volumes/acasis/ema2_20250417/frontend/public/examples/feature_detection_example.jpg"
    image = cv2.imread(image_path)

    if image is None:
        logger.error(f"无法读取图像: {image_path}")
        return

    # 预测情绪
    result = trakov_vit_model.predict(image)

    # 打印结果
    logger.info(f"预测成功: {result.get('success', False)}")
    if result.get("success", False):
        logger.info(f"主要情绪: {result.get('dominant_emotion', 'unknown')}")
        logger.info("情绪概率分布:")
        for _, prob in sorted(
            result.get("emotions", {}).items(), key=lambda x: x[1], reverse=True
        ):
            logger.info(f"  {emotion}: {prob:.4f}")
    else:
        logger.error(f"预测失败: {result.get('error', '未知错误')}")

    # 显示结果 (禁用图形显示)
    # plt.figure(figsize=(8, 6))
    # plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    # plt.title(f"情绪: {result.get('dominant_emotion', 'unknown')}")
    # plt.axis('off')
    # plt.show()


async def test_emotion_ensemble():
    """测试情绪集成器"""
    # 注册所有情绪识别模型
    register_emotion_models()

    # 创建情绪集成器
    ensemble = EmotionEnsemble()

    # 加载测试图像
    image_path = "/Volumes/acasis/ema2_20250417/frontend/public/examples/feature_detection_example.jpg"
    image = cv2.imread(image_path)

    if image is None:
        logger.error(f"无法读取图像: {image_path}")
        return

    # 分析情绪
    result = await ensemble.analyze(image)

    # 打印结果
    logger.info(f"分析成功: {result.get('success', False)}")
    if result.get("success", False):
        logger.info(f"主要情绪: {result.get('dominant_emotion', 'unknown')}")
        logger.info("情绪概率分布:")
        for _, prob in sorted(
            result.get("emotions", {}).items(), key=lambda x: x[1], reverse=True
        ):
            logger.info(f"  {emotion}: {prob:.4f}")

        # 打印各模型的结果
        logger.info("各模型的结果:")
        for _, model_result in result.get("model_results", {}).items():
            if model_result.get("success", False):
                logger.info(
                    f"  {model_name}: {model_result.get('dominant_emotion', 'unknown')}"
                )
            else:
                logger.info(
                    f"  {model_name}: 预测失败 - {model_result.get('error', '未知错误')}"
                )
    else:
        logger.error(f"分析失败: {result.get('error', '未知错误')}")

    # 显示结果 (禁用图形显示)
    # plt.figure(figsize=(10, 6))
    # plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    # plt.title(f"集成情绪: {result.get('dominant_emotion', 'unknown')}")
    # plt.axis('off')
    # plt.show()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_trakov_vit_model())
    asyncio.run(test_emotion_ensemble())
