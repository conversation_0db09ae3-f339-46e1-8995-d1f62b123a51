#!/usr/bin/env python3
"""
测试Redis服务器启动和连接

此脚本用于测试Redis服务器的启动和连接，以确保Redis服务器能够正常工作。
"""

import logging
import os
import sys
import time

# 添加项目根目录到 sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("redis-test")

try:
    from emotionai.core.config.paths import PROJECT_ROOT, path_to_str
    from emotionai.core.services.redis_manager import RedisManager
except ImportError as e:
    logger.error(f"导入依赖失败: {e}")
    logger.error("请确保已安装所有依赖，并且当前目录是项目根目录")
    sys.exit(1)


def test_redis_manager():
    """测试Redis管理器"""
    logger.info("开始测试Redis管理器...")

    # 创建Redis管理器实例
    redis_manager = RedisManager(path_to_str(PROJECT_ROOT))

    # 启动Redis服务器
    logger.info("正在启动Redis服务器...")
    if redis_manager.start_redis_server():
        logger.info("Redis服务器启动成功")

        # 获取Redis客户端
        redis_client = redis_manager.get_client()
        if redis_client:
            logger.info("获取Redis客户端成功")

            # 测试Redis连接
            try:
                redis_client.ping()
                logger.info("Redis连接测试成功")

                # 测试基本操作
                try:
                    # 设置键值对
                    redis_client.set("test_key", "test_value")
                    logger.info("设置键值对成功")

                    # 获取键值对
                    value = redis_client.get("test_key")
                    if isinstance(value, bytes):
                        value = value.decode("utf-8")
                    logger.info(f"获取键值对成功: {value}")

                    # 删除键值对
                    redis_client.delete("test_key")
                    logger.info("删除键值对成功")

                    logger.info("Redis基本操作测试成功")
                except Exception as e:
                    logger.error(f"Redis基本操作测试失败: {e}")
            except Exception as e:
                logger.error(f"Redis连接测试失败: {e}")
        else:
            logger.error("获取Redis客户端失败")

        # 停止Redis服务器
        logger.info("正在停止Redis服务器...")
        if redis_manager.stop_redis_server():
            logger.info("Redis服务器停止成功")
        else:
            logger.error("Redis服务器停止失败")
    else:
        logger.error("Redis服务器启动失败")


def main():
    """主函数"""
    logger.info("开始测试Redis服务器...")

    try:
        # 测试Redis管理器
        test_redis_manager()

        logger.info("Redis服务器测试完成")
    except Exception as e:
        logger.error(f"Redis服务器测试失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
