#!/usr/bin/env python

"""
测试MediaPipe API的可用性
"""

import logging
import os
import sys

import cv2
import numpy as np

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 设置环境变量，确保在macOS M芯片上禁用GPU加速
os.environ["MEDIAPIPE_GPU"] = "0"  # 禁用MediaPipe的GPU加速
os.environ["MEDIAPIPE_USE_CUSTOM_OPENCV"] = "0"  # 使用系统安装的OpenCV库
os.environ["MEDIAPIPE_DISABLE_GPU"] = "1"  # 禁用GPU，使用CPU推理

# 导入MediaPipe
import mediapipe as mp


def test_mediapipe_version():
    """测试MediaPipe版本"""
    try:
        logger.info(f"MediaPipe版本: {mp.__version__}")
    except AttributeError:
        logger.warning("MediaPipe没有__version__属性")


def test_mediapipe_modules():
    """测试MediaPipe模块结构"""
    # 检查mp对象的属性
    logger.info("MediaPipe模块属性:")
    for attr in dir(mp):
        if not attr.startswith("_"):  # 排除私有属性
            logger.info(f"- {attr}")


def test_mediapipe_tasks_api():
    """测试MediaPipe Tasks API"""
    try:
        # 尝试导入mediapipe.tasks
        logger.info("尝试导入mediapipe.tasks...")

        # 检查mp.tasks是否存在
        if hasattr(mp, "tasks"):
            logger.info("mp.tasks存在")

            # 检查mp.tasks.python是否存在
            if hasattr(mp.tasks, "python"):
                logger.info("mp.tasks.python存在")

                # 检查mp.tasks.python.vision是否存在
                if hasattr(mp.tasks.python, "vision"):
                    logger.info("mp.tasks.python.vision存在")

                    # 检查FaceLandmarker是否存在
                    if hasattr(mp.tasks.python.vision, "FaceLandmarker"):
                        logger.info("mp.tasks.python.vision.FaceLandmarker存在")
                    else:
                        logger.warning("mp.tasks.python.vision.FaceLandmarker不存在")
                else:
                    logger.warning("mp.tasks.python.vision不存在")
            else:
                logger.warning("mp.tasks.python不存在")
        else:
            logger.warning("mp.tasks不存在")

        # 尝试直接导入
        try:
            from mediapipe.tasks import python

            logger.info("成功导入mediapipe.tasks.python")

            from mediapipe.tasks.python import vision

            logger.info("成功导入mediapipe.tasks.python.vision")

            from mediapipe.tasks.python.vision import FaceLandmarker

            logger.info("成功导入mediapipe.tasks.python.vision.FaceLandmarker")
        except ImportError as e:
            logger.error(f"导入失败: {e}")
    except Exception as e:
        logger.error(f"测试Tasks API时出错: {e}")


def test_mediapipe_solutions_api():
    """测试MediaPipe Solutions API"""
    try:
        # 尝试导入mediapipe.solutions
        logger.info("尝试导入mediapipe.solutions...")

        # 检查mp.solutions是否存在
        if hasattr(mp, "solutions"):
            logger.info("mp.solutions存在")

            # 检查mp.solutions.face_mesh是否存在
            if hasattr(mp.solutions, "face_mesh"):
                logger.info("mp.solutions.face_mesh存在")
            else:
                logger.warning("mp.solutions.face_mesh不存在")
        else:
            logger.warning("mp.solutions不存在")

        # 尝试直接导入
        try:
            from mediapipe.solutions import face_mesh

            logger.info("成功导入mediapipe.solutions.face_mesh")
        except ImportError as e:
            logger.error(f"导入失败: {e}")
    except Exception as e:
        logger.error(f"测试Solutions API时出错: {e}")


def test_mediapipe_python_solutions_api():
    """测试MediaPipe Python Solutions API"""
    try:
        # 尝试导入mediapipe.python.solutions
        logger.info("尝试导入mediapipe.python.solutions...")

        # 检查mp.python是否存在
        if hasattr(mp, "python"):
            logger.info("mp.python存在")

            # 检查mp.python.solutions是否存在
            if hasattr(mp.python, "solutions"):
                logger.info("mp.python.solutions存在")

                # 检查mp.python.solutions.face_mesh是否存在
                if hasattr(mp.python.solutions, "face_mesh"):
                    logger.info("mp.python.solutions.face_mesh存在")
                else:
                    logger.warning("mp.python.solutions.face_mesh不存在")
            else:
                logger.warning("mp.python.solutions不存在")
        else:
            logger.warning("mp.python不存在")

        # 尝试直接导入
        try:
            from mediapipe.python.solutions import face_mesh

            logger.info("成功导入mediapipe.python.solutions.face_mesh")
        except ImportError as e:
            logger.error(f"导入失败: {e}")
    except Exception as e:
        logger.error(f"测试Python Solutions API时出错: {e}")


def test_mediapipe_framework():
    """测试MediaPipe Framework API"""
    try:
        # 尝试导入mediapipe.framework
        logger.info("尝试导入mediapipe.framework...")

        # 检查mp.framework是否存在
        if hasattr(mp, "framework"):
            logger.info("mp.framework存在")
        else:
            logger.warning("mp.framework不存在")

        # 尝试直接导入
        try:
            from mediapipe.framework import calculator_pb2

            logger.info("成功导入mediapipe.framework.calculator_pb2")
        except ImportError as e:
            logger.error(f"导入失败: {e}")
    except Exception as e:
        logger.error(f"测试Framework API时出错: {e}")


def test_mediapipe_image():
    """测试MediaPipe Image API"""
    try:
        # 创建一个测试图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)

        # 尝试创建MediaPipe图像对象
        logger.info("尝试创建MediaPipe图像对象...")

        # 检查mp.Image是否存在
        if hasattr(mp, "Image"):
            logger.info("mp.Image存在")

            # 尝试创建图像对象
            try:
                # 使用 _ 前缀表示变量未使用
                _mp_image = mp.Image(image_format=mp.ImageFormat.SRGB, data=test_image)
                logger.info("成功创建MediaPipe图像对象")
            except Exception as e:
                logger.error(f"创建图像对象失败: {e}")
        else:
            logger.warning("mp.Image不存在")
    except Exception as e:
        logger.error(f"测试Image API时出错: {e}")


def main():
    """主函数"""
    logger.info("开始测试MediaPipe API...")

    # 测试MediaPipe版本
    test_mediapipe_version()

    # 测试MediaPipe模块结构
    test_mediapipe_modules()

    # 测试MediaPipe Tasks API
    test_mediapipe_tasks_api()

    # 测试MediaPipe Solutions API
    test_mediapipe_solutions_api()

    # 测试MediaPipe Python Solutions API
    test_mediapipe_python_solutions_api()

    # 测试MediaPipe Framework API
    test_mediapipe_framework()

    # 测试MediaPipe Image API
    test_mediapipe_image()

    logger.info("MediaPipe API测试完成")


if __name__ == "__main__":
    main()
