#!/usr/bin/env python
"""
测试人脸属性集成

此脚本用于测试人脸属性集成类，该类集成了DeepFace和InsightFace的预测结果，
用于性别、年龄和种族预测。

权重配置:
- 性别预测: DeepFace (0.7), InsightFace (0.3)
- 年龄预测: DeepFace (0.5), InsightFace (0.5)
- 种族预测: DeepFace (1.0), InsightFace (不支持)
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path

import cv2
import numpy as np

# 设置日志级别
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

# 导入人脸属性集成类
from emotionai.core.ml.face_attributes_ensemble import FaceAttributesEnsemble

# 导入多任务人脸分析器
from emotionai.core.ml.pytorch_emotion_analyzer import MultiFaceAnalyzer


async def test_face_attributes_ensemble():
    """测试人脸属性集成类"""
    logger.info("开始测试人脸属性集成类")

    # 创建人脸属性集成实例
    ensemble = FaceAttributesEnsemble()
    logger.info("人脸属性集成实例创建成功")

    # 创建多任务人脸分析器实例
    multi_face_analyzer = MultiFaceAnalyzer.get_instance()
    logger.info("多任务人脸分析器实例创建成功")

    # 测试示例图像
    example_image_paths = [
        Path(
            "/Volumes/acasis/ema2_20250417/frontend/public/examples/original_example.jpg"
        ),
        Path(
            "/Volumes/acasis/ema2_20250417/test_face.jpg"
        ),  # 使用之前保存的裁剪人脸图像
    ]

    # 测试每个图像
    for image_path in example_image_paths:
        if not image_path.exists():
            logger.error(f"示例图像不存在: {image_path}")
            continue

        logger.info(f"\n\n========== 测试图像: {image_path} ==========\n")

        # 加载图像
        image = cv2.imread(str(image_path))
        if image is None:
            logger.error(f"无法加载图像: {image_path}")
            continue

        # 1. 使用人脸属性集成类直接分析
        logger.info("\n===== 使用人脸属性集成类直接分析 =====")
        start_time = time.time()
        ensemble_result = ensemble.analyze(image)
        ensemble_time = time.time() - start_time

        # 输出结果
        logger.info(f"人脸属性集成分析耗时: {ensemble_time:.4f}秒")
        logger.info(f"年龄: {ensemble_result.get('age')}")
        logger.info(f"性别: {ensemble_result.get('gender')}")
        logger.info(f"性别置信度: {ensemble_result.get('gender_confidence'):.4f}")
        logger.info(f"种族: {ensemble_result.get('race')}")
        logger.info(f"种族置信度: {ensemble_result.get('race_confidence'):.4f}")

        # 2. 使用多任务人脸分析器分析
        logger.info("\n===== 使用多任务人脸分析器分析 =====")
        start_time = time.time()
        multi_face_result = await multi_face_analyzer.analyze_async(image)
        multi_face_time = time.time() - start_time

        # 输出结果
        logger.info(f"多任务人脸分析器分析耗时: {multi_face_time:.4f}秒")
        logger.info(f"年龄: {multi_face_result.get('age')}")
        logger.info(f"性别: {multi_face_result.get('gender')}")
        logger.info(f"性别置信度: {multi_face_result.get('gender_confidence', 'N/A')}")
        logger.info(f"种族: {multi_face_result.get('race')}")
        logger.info(f"种族置信度: {multi_face_result.get('race_confidence', 'N/A')}")
        logger.info(f"主要情绪: {multi_face_result.get('dominant_emotion')}")

        # 3. 比较结果
        logger.info("\n===== 结果比较 =====")
        logger.info("人脸属性集成 vs 多任务人脸分析器:")
        logger.info(
            f"年龄: {ensemble_result.get('age')} vs {multi_face_result.get('age')}"
        )
        logger.info(
            f"性别: {ensemble_result.get('gender')} vs {multi_face_result.get('gender')}"
        )
        logger.info(
            f"种族: {ensemble_result.get('race')} vs {multi_face_result.get('race')}"
        )
        logger.info(f"分析耗时: {ensemble_time:.4f}秒 vs {multi_face_time:.4f}秒")

        # 4. 分析权重影响
        logger.info("\n===== 权重影响分析 =====")
        logger.info("当前权重配置:")
        logger.info("性别预测: DeepFace (0.7), InsightFace (0.3)")
        logger.info("年龄预测: DeepFace (0.5), InsightFace (0.5)")
        logger.info("种族预测: DeepFace (1.0), InsightFace (不支持)")

        # 保存分析后的图像
        output_path = f"test_ensemble_{image_path.stem}.jpg"
        cv2.imwrite(output_path, image)
        logger.info(f"已保存分析后的图像到 {output_path}")


async def main():
    """主函数"""
    await test_face_attributes_ensemble()


if __name__ == "__main__":
    asyncio.run(main())
