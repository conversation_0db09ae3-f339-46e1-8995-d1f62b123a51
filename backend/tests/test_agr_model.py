"""
测试年龄和性别分析模型
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

import cv2
import numpy as np
import torch

# 设置日志级别
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入需要测试的模块
from emotionai.core.ml.pytorch_face_models import (
    AGE_LABELS,
    GENDER_LABELS,
    AgeGenderRaceModel,
)


async def test_agr_model():
    """测试年龄和性别分析模型"""
    try:
        # 加载测试图像
        uploads_dir = Path("uploads/original")
        if not uploads_dir.exists():
            logger.error(f"上传目录不存在: {uploads_dir}")
            return

        # 查找最新的图像
        image_files = list(uploads_dir.glob("**/*.jpg"))
        if not image_files:
            logger.error("未找到测试图像")
            return

        # 使用最新的图像
        image_path = sorted(image_files, key=os.path.getmtime)[-1]
        logger.info(f"使用测试图像: {image_path}")

        # 加载图像
        image = cv2.imread(str(image_path))
        if image is None:
            logger.error(f"无法加载图像: {image_path}")
            return

        logger.info(f"图像大小: {image.shape}")

        # 直接使用AGR模型进行分析
        logger.info("直接使用AGR模型进行分析...")

        # 创建AGR模型
        device = torch.device("mps" if torch.backends.mps.is_available() else "cpu")
        logger.info(f"使用设备: {device}")

        agr_model = AgeGenderRaceModel()

        # 加载预训练模型
        model_path = "/Volumes/acasis/ema2_20250417/backend/emotionai/core/ml/models/face_attributes/age_gender_race_model.pt"
        logger.info(f"加载预训练模型: {model_path}")

        state_dict = torch.load(model_path, map_location=device)
        agr_model.load_state_dict(state_dict)

        # 将模型移动到设备并设置为评估模式
        agr_model = agr_model.to(device)
        agr_model.eval()

        logger.info("AGR模型加载完成")

        # 使用OpenCV进行人脸检测
        logger.info("使用OpenCV进行人脸检测...")
        face_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + "haarcascade_frontalface_default.xml"
        )
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)

        if len(faces) == 0:
            logger.error("未检测到人脸")
            return

        logger.info(f"检测到 {len(faces)} 个人脸")

        # 处理第一个人脸
        x, y, w, h = faces[0]

        # 裁剪人脸区域
        face_img = image[y : y + h, x : x + w]

        # 转换为RGB格式
        rgb_face = cv2.cvtColor(face_img, cv2.COLOR_BGR2RGB)

        # 调整大小为64x64（AGR模型的输入大小）
        resized_face = cv2.resize(rgb_face, (64, 64))

        # 归一化
        normalized_face = resized_face / 255.0

        # 转换为PyTorch张量
        tensor_face = (
            torch.from_numpy(normalized_face)
            .float()
            .permute(2, 0, 1)  # 将 HWC 转换为 CHW
            .unsqueeze(0)  # 添加批处理维度
        )

        # 移动到设备
        tensor_face = tensor_face.to(device)

        # 使用AGR模型预测
        logger.info("使用AGR模型预测...")
        with torch.no_grad():
            age_output, gender_output, race_output = agr_model(tensor_face)

        # 处理年龄预测
        age_probs = torch.softmax(age_output, dim=1).cpu().numpy()[0]
        age_idx = np.argmax(age_probs)

        # 使用年龄标签映射转换为实际年龄范围
        age_label = AGE_LABELS[age_idx]

        # 从年龄范围中提取中间值作为年龄估计
        if "-" in age_label:
            age_range = age_label.split("-")
            min_age = int(age_range[0])
            if age_range[1] == "+":  # 处理"70+"这种情况
                max_age = 85  # 假设上限为85岁
            else:
                max_age = int(age_range[1])
            age = float((min_age + max_age) / 2)  # 使用范围的中间值
        else:
            # 如果不是范围格式，尝试直接转换
            try:
                age = float(age_label)
            except ValueError:
                age = float(age_idx)  # 如果转换失败，使用索引作为备用

        logger.info(f"年龄预测: 索引={age_idx}, 标签={age_label}, 估计年龄={age}")

        # 处理性别预测
        gender_probs = torch.softmax(gender_output, dim=1).cpu().numpy()[0]
        gender_idx = np.argmax(gender_probs)
        gender = GENDER_LABELS[gender_idx]

        logger.info(f"性别预测: 索引={gender_idx}, 标签={gender}")

        # 输出最终结果
        logger.info(f"AGR模型分析结果: age={age}, gender={gender}")

    except Exception as e:
        logger.error(f"测试AGR模型时出错: {str(e)}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(test_agr_model())
