#!/usr/bin/env python

"""
测试Vision Transformer (ViT)情绪识别模型

比较Vision Transformer (ViT)模型与Swin-FER模型的性能
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path

import cv2
import numpy as np

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志级别
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

from emotionai.core.ml.emotion_ensemble import EmotionEnsemble
from emotionai.core.ml.face_detection import FaceDetector
from emotionai.core.ml.loaders.emotion_loaders import register_emotion_models
from emotionai.core.ml.loaders.face_detection_loaders import (
    register_face_detection_models,
)

# 导入必要的模块
from emotionai.core.ml.model_loader import ModelRegistry


async def main():
    # 注册模型
    register_emotion_models()
    register_face_detection_models()

    # 创建人脸检测器
    detector = FaceDetector()

    # 加载已注册的模型
    vit_fer = ModelRegistry.get("vit_fer")
    if vit_fer is None:
        logger.error("无法加载vit_fer模型")
        return

    if not vit_fer.loaded:
        vit_fer.load()

    swin_fer = ModelRegistry.get("swin_fer")
    if swin_fer is None:
        logger.error("无法加载swin_fer模型")
        return

    if not swin_fer.loaded:
        swin_fer.load()

    # 创建情绪集成器
    emotion_ensemble = EmotionEnsemble()

    # 更新权重，确保trakov_vit模型的权重最高
    emotion_ensemble.update_weights(
        {
            "trakov_vit": 0.7,
            "swin_fer": 0.1,
            "cnn_emotion": 0.2,
        }
    )

    # 打印当前权重
    logger.info(f"情绪集成器当前权重: {emotion_ensemble.get_weights()}")

    # 加载测试图像
    test_image_path = Path(
        "/Volumes/acasis/ema2_20250417/frontend/public/examples/original_example.jpg"
    )
    if not test_image_path.exists():
        logger.error(f"测试图像不存在: {test_image_path}")
        return

    # 打印测试图像信息
    logger.info(f"测试图像路径: {test_image_path}")

    image = cv2.imread(str(test_image_path))
    if image is None:
        logger.error(f"无法加载图像: {test_image_path}")
        return

    logger.info(f"加载图像: {test_image_path}, 尺寸: {image.shape}")

    # 使用YOLO检测人脸
    yolo_face_detector = ModelRegistry.get("yolo_face")
    if yolo_face_detector is None:
        logger.error("无法加载YOLO人脸检测器")
        return

    if not yolo_face_detector.loaded:
        yolo_face_detector.load()

    # 检测人脸
    yolo_result = yolo_face_detector.predict(image)
    yolo_faces = yolo_result.get("faces", [])

    if not yolo_faces:
        logger.error("未检测到人脸")
        return

    logger.info(f"YOLO检测到{len(yolo_faces)}个人脸")

    # 使用MediaPipe处理人脸，获取特征点
    faces_with_landmarks, marked_image = detector.process_with_yolo_faces(
        image, yolo_faces
    )

    logger.info(f"MediaPipe处理了{len(faces_with_landmarks)}个人脸")

    # 对每个人脸进行情绪分析
    for _, face in enumerate(faces_with_landmarks):
        # 获取人脸边界框
        bbox = face.get("bbox")
        if not bbox:
            logger.warning(f"人脸 {i+1} 没有边界框")
            continue

        x1, y1, x2, y2 = bbox
        face_image = image[y1:y2, x1:x2].copy()

        # 获取特征点（当前未使用）
        # landmarks = face.get("landmarks")

        # 测试Vision Transformer模型
        logger.info(f"使用Vision Transformer模型分析人脸 {i+1}")
        start_time = time.time()
        vit_fer_result = vit_fer.predict(face_image)
        vit_fer_time = time.time() - start_time
        logger.info(
            f"Vision Transformer模型预测结果: {vit_fer_result.get('dominant_emotion')}, 置信度: {vit_fer_result.get('emotions', {}).get(vit_fer_result.get('dominant_emotion'), 0.0):.4f}, 耗时: {vit_fer_time:.4f}秒"
        )

        # 测试Swin-FER模型
        logger.info(f"使用Swin-FER模型分析人脸 {i+1}")
        start_time = time.time()
        swin_fer_result = swin_fer.predict(face_image)
        swin_fer_time = time.time() - start_time
        logger.info(
            f"Swin-FER模型预测结果: {swin_fer_result.get('dominant_emotion')}, 置信度: {swin_fer_result.get('emotions', {}).get(swin_fer_result.get('dominant_emotion'), 0.0):.4f}, 耗时: {swin_fer_time:.4f}秒"
        )

        # 使用情绪集成器
        logger.info(f"使用情绪集成器分析人脸 {i+1}")
        ensemble_result = await emotion_ensemble.analyze(face_image)
        logger.info(
            f"情绪集成器预测结果: {ensemble_result.get('dominant_emotion')}, 置信度: {ensemble_result.get('emotions', {}).get(ensemble_result.get('dominant_emotion'), 0.0):.4f}"
        )

        # 打印各模型的结果
        logger.info("各模型预测结果:")
        for _, result in ensemble_result.get("model_results", {}).items():
            dominant_emotion = result.get("dominant_emotion", "unknown")
            confidence = result.get("emotions", {}).get(dominant_emotion, 0.0)
            logger.info(
                f"  模型 {model_name}: {dominant_emotion}, 置信度: {confidence:.4f}"
            )

    # 释放资源
    # detector.release_resources()  # FaceDetector没有release_resources方法，注释掉


if __name__ == "__main__":
    asyncio.run(main())
