#!/usr/bin/env python

"""
测试年龄和性别分析模型

这个脚本用于测试年龄和性别分析模型是否正常工作。
它会加载一个测试图像，然后使用MultiFaceAnalyzer进行分析，
并打印出分析结果。
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

import cv2
import numpy as np

# 设置日志级别
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入必要的模块
from emotionai.core.ml.pytorch_emotion_analyzer import MultiFaceAnalyzer


async def test_age_gender_model():
    """测试年龄和性别分析模型"""
    logger.info("开始测试年龄和性别分析模型")

    # 获取测试图像路径
    test_image_path = Path(
        "backend/uploads/original/2025-05/16/fb3c2fcd-dfd1-4ff3-80d7-cbf982073711.jpg"
    )
    if not test_image_path.exists():
        # 如果没有找到测试图像，使用uploads目录下的第一张图像
        uploads_dir = Path("backend/uploads")
        if uploads_dir.exists():
            # 递归查找所有jpg和png文件
            image_files = []
            for ext in ["*.jpg", "*.png"]:
                image_files.extend(list(uploads_dir.glob(f"**/{ext}")))

            if image_files:
                test_image_path = image_files[0]
                logger.info(f"使用上传的图像: {test_image_path}")
            else:
                logger.error("没有找到测试图像")
                return
        else:
            logger.error("没有找到uploads目录")
            return

    # 加载测试图像
    logger.info(f"加载测试图像: {test_image_path}")
    image = cv2.imread(str(test_image_path))
    if image is None:
        logger.error(f"无法加载图像: {test_image_path}")
        return

    # 获取MultiFaceAnalyzer实例
    logger.info("获取MultiFaceAnalyzer实例")
    analyzer = MultiFaceAnalyzer.get_instance()

    # 等待模型加载完成
    logger.info("等待模型加载完成...")
    # 检查模型是否已加载
    for i in range(10):  # 最多等待10秒
        if (
            hasattr(analyzer.analyzer, "agr_model")
            and analyzer.analyzer.agr_model is not None
        ):
            logger.info(f"模型已加载完成，耗时 {i} 秒")
            break
        await asyncio.sleep(1)
        logger.info(f"等待中... {i+1} 秒")
    else:
        logger.warning("等待超时，模型可能未完全加载")

    # 先进行人脸检测
    logger.info("开始进行人脸检测")
    from emotionai.core.ml.face_detection import FaceDetector

    # 获取FaceDetector实例
    detector = FaceDetector.get_instance()

    # 检测人脸
    faces, marked_image = detector.detect_faces_and_generate_image(image)
    logger.info(f"检测到 {len(faces)} 个人脸")

    if not faces:
        logger.error("未检测到人脸，无法进行年龄和性别分析")
        # 保存标记图像，以便查看
        cv2.imwrite("test_marked.jpg", marked_image)
        logger.info("已保存标记图像到 test_marked.jpg")
        return None

    # 获取第一个人脸
    face = faces[0]
    logger.info(f"人脸信息: {face}")

    # 裁剪人脸区域
    bbox = face.get("bbox", [0, 0, 0, 0])
    x1, y1, x2, y2 = bbox
    face_image = image[y1:y2, x1:x2]

    # 保存裁剪的人脸图像
    cv2.imwrite("test_face.jpg", face_image)
    logger.info("已保存裁剪的人脸图像到 test_face.jpg")

    # 分析人脸
    logger.info("开始分析人脸")

    # 直接使用PyTorchFaceAnalyzer的_analyze_age_gender_race方法
    logger.info("直接使用PyTorchFaceAnalyzer的_analyze_age_gender_race方法")

    # 获取PyTorchFaceAnalyzer实例
    pytorch_analyzer = analyzer.analyzer

    # 转换为RGB格式
    rgb_face = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)

    # 调整大小为64x64（AGR模型的输入大小）
    resized_face = cv2.resize(rgb_face, (64, 64))

    # 归一化
    normalized_face = resized_face / 255.0

    # 转换为PyTorch张量
    import torch

    tensor_face = (
        torch.from_numpy(normalized_face)
        .float()
        .permute(2, 0, 1)  # 将 HWC 转换为 CHW
        .unsqueeze(0)  # 添加批处理维度
    )
    tensor_face = tensor_face.to(pytorch_analyzer.device)

    # 使用AGR模型预测
    logger.info("使用AGR模型预测")
    with torch.no_grad():
        age_output, gender_output, race_output = pytorch_analyzer.agr_model(tensor_face)

    # 处理年龄输出
    age_probs = torch.softmax(age_output, dim=1).cpu().numpy()[0]

    # 导入实际使用的年龄标签和值
    from emotionai.core.ml.pytorch_face_models import AGE_LABELS

    # 定义与其他模块一致的年龄值映射
    age_values = {
        "0-2": 1,
        "3-5": 4,
        "6-9": 8,
        "10-14": 12,
        "15-19": 17,
        "20-29": 25,
        "30-39": 35,
        "40-49": 45,
        "50-59": 55,
        "60-69": 65,
        "70+": 75,
    }

    # 获取年龄标签
    age_idx = age_probs.argmax()
    age_label = AGE_LABELS[age_idx]
    logger.info(f"年龄标签: {age_label}")

    # 使用与其他模块一致的方法计算年龄
    if age_label in age_values:
        # 直接使用映射值
        age = age_values[age_label]
    else:
        # 从年龄范围中提取中间值作为年龄估计
        if "-" in age_label:
            age_range = age_label.split("-")
            min_age = int(age_range[0])
            if age_range[1] == "+":  # 处理"70+"这种情况
                max_age = 85  # 假设上限为85岁
            else:
                max_age = int(age_range[1])
            age = int((min_age + max_age) / 2)  # 使用范围的中间值
        else:
            # 如果不是范围格式，尝试直接转换
            try:
                age = int(float(age_label))
            except ValueError:
                age = int(float(age_idx))  # 如果转换失败，使用索引作为备用

    logger.info(f"计算得到的年龄: {age}")

    # 处理性别输出
    gender_probs = torch.softmax(gender_output, dim=1).cpu().numpy()[0]

    # 定义性别标签
    gender_labels = ["male", "female"]

    gender = gender_labels[gender_probs.argmax()]
    logger.info(f"性别: {gender}")

    # 处理种族输出
    race_probs = torch.softmax(race_output, dim=1).cpu().numpy()[0]

    # 定义种族标签
    race_labels = ["white", "black", "asian", "indian", "others"]

    race = race_labels[race_probs.argmax()]
    logger.info(f"种族: {race}")

    # 创建结果字典
    result = {
        "dominant_emotion": "neutral",  # 默认情绪
        "emotions": {},  # 空情绪字典
        "age": age,
        "gender": gender,
        "race": race,
    }

    # 打印分析结果
    logger.info(f"分析结果: {result}")

    # 检查年龄和性别
    age = result.get("age")
    gender = result.get("gender")
    logger.info(f"年龄: {age}")
    logger.info(f"性别: {gender}")

    # 检查年龄和性别是否为None
    if age is None:
        logger.warning("年龄为None")
    if gender is None:
        logger.warning("性别为None")

    # 返回结果
    return result


# 直接运行脚本
if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(test_age_gender_model())

    # 打印最终结果
    print("\n最终结果:")
    print(f"年龄: {result.get('age')}")
    print(f"性别: {result.get('gender')}")
    print(f"情绪: {result.get('dominant_emotion')}")
    print(f"情绪详情: {result.get('emotions')}")
