#!/usr/bin/env python
"""
测试InsightFace原生API的性别和年龄预测准确度

此脚本直接使用InsightFace的原生API，测试其在性别和年龄预测方面的准确度，
并输出原始的预测值和置信度。
"""

import argparse
import logging
import os
import sys
import time
from pathlib import Path

import cv2
import numpy as np

# 设置日志级别
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 检查是否可以导入InsightFace
try:
    import insightface
    from insightface.app import FaceAnalysis

    INSIGHTFACE_AVAILABLE = True
    logger.info("InsightFace可用")
except ImportError:
    INSIGHTFACE_AVAILABLE = False
    logger.error("InsightFace不可用，请安装InsightFace: pip install insightface")
    sys.exit(1)


class InsightFaceTester:
    """InsightFace测试类"""

    def __init__(self, det_size=(640, 640)):
        """初始化测试类

        Args:
            det_size: 检测尺寸，越大越准确但越慢
        """
        # 初始化InsightFace
        self.app = FaceAnalysis(name="buffalo_l")
        self.app.prepare(ctx_id=0, det_size=det_size)
        logger.info(f"InsightFace初始化完成，使用检测尺寸: {det_size}")

        # 记录模型信息
        self._log_model_info()

    def _log_model_info(self):
        """记录模型信息"""
        logger.info("InsightFace模型信息:")
        for _, model in self.app.models.items():
            if hasattr(model, "model_file"):
                logger.info(f"  - {name}: {model.model_file}")
            else:
                logger.info(f"  - {name}: (无模型文件信息)")

    def analyze_image(self, image_path):
        """分析图像中的人脸

        Args:
            image_path: 图像路径

        Returns:
            分析结果
        """
        logger.info(f"分析图像: {image_path}")

        # 加载图像
        image = cv2.imread(str(image_path))
        if image is None:
            logger.error(f"无法加载图像: {image_path}")
            return None

        # 转换为RGB格式
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # 记录图像信息
        logger.info(f"图像尺寸: {image.shape}")

        # 分析人脸
        start_time = time.time()
        faces = self.app.get(rgb_image)
        analysis_time = time.time() - start_time

        logger.info(f"分析耗时: {analysis_time:.4f}秒")
        logger.info(f"检测到 {len(faces)} 个人脸")

        # 返回结果
        return faces

    def test_multiple_sizes(self, image_path, sizes=None):
        """使用多种检测尺寸测试图像

        Args:
            image_path: 图像路径
            sizes: 检测尺寸列表，如果为None则使用默认尺寸

        Returns:
            各尺寸的分析结果
        """
        if sizes is None:
            sizes = [(224, 224), (320, 320), (640, 640)]

        results = {}

        for size in sizes:
            logger.info(f"\n===== 测试检测尺寸: {size} =====")

            # 重新初始化InsightFace
            self.app = FaceAnalysis(name="buffalo_l")
            self.app.prepare(ctx_id=0, det_size=size)

            # 分析图像
            faces = self.analyze_image(image_path)

            # 保存结果
            results[size] = faces

            # 输出结果
            if faces:
                for i, face in enumerate(faces):
                    logger.info(f"人脸 {i+1}:")
                    self._print_face_info(face)
            else:
                logger.warning(f"使用检测尺寸 {size} 未检测到人脸")

        return results

    def _print_face_info(self, face):
        """打印人脸信息

        Args:
            face: InsightFace人脸对象
        """
        # 获取基本信息
        bbox = face.bbox.astype(int)
        gender = face.gender
        age = face.age

        # 输出基本信息
        logger.info(f"  边界框: {bbox}")
        logger.info(f"  性别原始值: {gender}")
        logger.info(f"  性别标签: {'男性' if gender == 1 else '女性'}")
        logger.info(f"  年龄: {age}")

        # 输出置信度信息
        if hasattr(face, "det_score"):
            logger.info(f"  检测置信度: {face.det_score:.4f}")

        # 输出关键点信息
        if hasattr(face, "kps") and face.kps is not None:
            logger.info(f"  关键点数量: {len(face.kps)}")

        # 输出额外信息
        for attr_name in dir(face):
            if not attr_name.startswith("_") and attr_name not in [
                "bbox",
                "gender",
                "age",
                "det_score",
                "kps",
                "landmark_2d_106",
                "landmark_3d_68",
                "embedding",
            ]:
                attr_value = getattr(face, attr_name)
                if not callable(attr_value):
                    logger.info(f"  {attr_name}: {attr_value}")

    def test_with_different_models(self, image_path):
        """使用不同的模型测试图像

        Args:
            image_path: 图像路径
        """
        # 测试不同的模型名称
        model_names = ["buffalo_l", "buffalo_sc"]

        for model_name in model_names:
            try:
                logger.info(f"\n===== 测试模型: {model_name} =====")

                # 重新初始化InsightFace
                self.app = FaceAnalysis(name=model_name)
                self.app.prepare(ctx_id=0, det_size=(640, 640))

                # 分析图像
                faces = self.analyze_image(image_path)

                # 输出结果
                if faces:
                    for _, face in enumerate(faces):
                        logger.info(f"人脸 {i+1}:")
                        self._print_face_info(face)
                else:
                    logger.warning(f"使用模型 {model_name} 未检测到人脸")
            except Exception as e:
                logger.error(f"使用模型 {model_name} 测试失败: {str(e)}")


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description="测试InsightFace原生API的性别和年龄预测准确度"
    )
    parser.add_argument("--image", type=str, default=None, help="图像路径")
    parser.add_argument("--test-sizes", action="store_true", help="测试不同的检测尺寸")
    parser.add_argument("--test-models", action="store_true", help="测试不同的模型")
    args = parser.parse_args()

    # 如果未指定图像路径，使用默认图像
    if args.image is None:
        # 尝试使用项目中的示例图像
        default_images = [
            Path(
                "/Volumes/acasis/ema2_20250417/frontend/public/examples/original_example.jpg"
            ),
            Path("/Volumes/acasis/ema2_20250417/test_face.jpg"),
        ]

        for image_path in default_images:
            if image_path.exists():
                args.image = str(image_path)
                logger.info(f"使用默认图像: {args.image}")
                break

        if args.image is None:
            logger.error("未指定图像路径，且找不到默认图像")
            return 1

    # 创建测试类
    tester = InsightFaceTester()

    # 测试不同的检测尺寸
    if args.test_sizes:
        tester.test_multiple_sizes(args.image)
    # 测试不同的模型
    elif args.test_models:
        tester.test_with_different_models(args.image)
    # 默认测试
    else:
        faces = tester.analyze_image(args.image)
        if faces:
            for _, face in enumerate(faces):
                logger.info(f"人脸 {i+1}:")
                tester._print_face_info(face)
        else:
            logger.warning("未检测到人脸")

    return 0


if __name__ == "__main__":
    sys.exit(main())
