#!/usr/bin/env python

"""
测试示例图片的年龄和性别分析

使用示例图片测试AGR模型的年龄和性别识别准确性
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

import cv2
import numpy as np
import torch

# 设置日志级别
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from emotionai.core.ml.face_detection import FaceDetector

# 导入必要的模块
from emotionai.core.ml.pytorch_face_models import (
    AGE_LABELS,
    GENDER_LABELS,
    AgeGenderRaceModel,
)


async def test_example_image():
    """测试示例图片的年龄和性别分析"""
    logger.info("开始测试示例图片的年龄和性别分析")

    # 获取示例图像路径
    test_image_path = Path(
        "/Volumes/acasis/ema2_20250417/frontend/public/examples/original_example.jpg"
    )
    if not test_image_path.exists():
        logger.error(f"示例图像不存在: {test_image_path}")
        return

    # 加载测试图像
    logger.info(f"加载示例图像: {test_image_path}")
    image = cv2.imread(str(test_image_path))
    if image is None:
        logger.error(f"无法加载图像: {test_image_path}")
        return

    # 先进行人脸检测
    logger.info("开始进行人脸检测")
    detector = FaceDetector.get_instance()

    # 检测人脸
    faces, marked_image = detector.detect_faces_and_generate_image(image)
    logger.info(f"检测到 {len(faces)} 个人脸")

    if not faces:
        logger.error("未检测到人脸，无法进行年龄和性别分析")
        # 保存标记图像，以便查看
        cv2.imwrite("test_marked.jpg", marked_image)
        logger.info("已保存标记图像到 test_marked.jpg")
        return None

    # 处理每个检测到的人脸
    for _, face in enumerate(faces):
        logger.info(f"------- 分析人脸 {i+1} -------")

        # 获取人脸区域
        bbox = face.get("bbox", [0, 0, 0, 0])
        x1, y1, x2, y2 = bbox
        face_image = image[y1:y2, x1:x2]

        # 保存裁剪的人脸图像
        cv2.imwrite(f"test_face_{i}.jpg", face_image)
        logger.info(f"已保存裁剪的人脸图像到 test_face_{i}.jpg")

        # 分析人脸
        logger.info(f"开始分析人脸 {i+1}")

        # 获取PyTorchFaceAnalyzer实例
        pytorch_analyzer = AgeGenderRaceModel.get_instance()

        # 转换为RGB格式
        rgb_face = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)

        # 调整大小为64x64（AGR模型的输入大小）
        resized_face = cv2.resize(rgb_face, (64, 64))

        # 归一化
        normalized_face = resized_face / 255.0

        # 转换为PyTorch张量
        tensor_face = (
            torch.from_numpy(normalized_face)
            .float()
            .permute(2, 0, 1)  # 将 HWC 转换为 CHW
            .unsqueeze(0)  # 添加批处理维度
        )
        tensor_face = tensor_face.to(pytorch_analyzer.device)

        # 使用AGR模型预测
        logger.info("使用AGR模型预测")
        with torch.no_grad():
            age_output, gender_output, race_output = pytorch_analyzer.agr_model(
                tensor_face
            )

        # 处理年龄输出
        age_probs = torch.softmax(age_output, dim=1).cpu().numpy()[0]
        age_idx = np.argmax(age_probs)
        age_label = AGE_LABELS[age_idx]
        logger.info(f"年龄标签: {age_label}")

        # 输出所有年龄概率分布
        logger.info("年龄概率分布:")
        for idx, prob in enumerate(age_probs):
            logger.info(f"  {AGE_LABELS[idx]}: {prob:.4f}")

        # 定义与其他模块一致的年龄值映射
        age_values = {
            "0-2": 1,
            "3-5": 4,
            "6-9": 8,
            "10-14": 12,
            "15-19": 17,
            "20-29": 25,
            "30-39": 35,
            "40-49": 45,
            "50-59": 55,
            "60-69": 65,
            "70+": 75,
        }

        # 使用与其他模块一致的方法计算年龄
        if age_label in age_values:
            # 直接使用映射值
            age = age_values[age_label]
        else:
            # 从年龄范围中提取中间值作为年龄估计
            if "-" in age_label:
                age_range = age_label.split("-")
                min_age = int(age_range[0])
                max_age = (
                    85 if age_range[1] == "+" else int(age_range[1])
                )  # 处理"70+"这种情况
                age = int((min_age + max_age) / 2)  # 使用范围的中间值
            else:
                # 如果不是范围格式，尝试直接转换
                try:
                    age = int(float(age_label))
                except ValueError:
                    age = int(float(age_idx))  # 如果转换失败，使用索引作为备用

        logger.info(f"计算得到的年龄: {age}")

        # 应用年龄修正
        age_confidence = age_probs[age_idx] if age_idx < len(age_probs) else 0.2
        original_age = age
        age = correct_age_prediction(age, age_confidence)
        logger.info(f"应用年龄修正，原始年龄: {original_age}，修正后年龄: {age}")

        # 处理性别输出
        gender_probs = torch.softmax(gender_output, dim=1).cpu().numpy()[0]
        gender_idx = np.argmax(gender_probs)
        gender = GENDER_LABELS[gender_idx]
        logger.info(f"性别: {gender} (置信度: {gender_probs[gender_idx]:.4f})")

        # 处理种族输出
        race_probs = torch.softmax(race_output, dim=1).cpu().numpy()[0]
        race_idx = np.argmax(race_probs)
        race_labels = ["white", "black", "asian", "indian", "others"]
        race = race_labels[race_idx]
        logger.info(f"种族: {race} (置信度: {race_probs[race_idx]:.4f})")

        # 创建结果字典
        result = {
            "dominant_emotion": "neutral",  # 默认情绪
            "emotions": {},  # 空情绪字典
            "age": age,
            "gender": gender,
            "race": race,
        }

        # 打印分析结果
        logger.info(f"人脸 {i+1} 分析结果: {result}")

    # 保存标记图像用于可视化
    cv2.imwrite("example_marked.jpg", marked_image)
    logger.info("已保存标记图像到 example_marked.jpg")


def correct_age_prediction(age, confidence=0.2):
    """
    根据经验规则修正年龄预测

    由于当前模型对年龄的预测不准确，根据以下规则修正：
    1. 如果年龄小于15岁但置信度低，调整为更可能的年龄
    2. 提供更加合理的默认值

    Args:
        age: 预测的年龄值
        confidence: 预测的置信度

    Returns:
        修正后的年龄值
    """
    # 目前模型倾向于预测年轻年龄，特别是12岁左右
    if age < 15 and confidence < 0.3:
        # 如果预测的年龄小且置信度低，使用较为合理的年龄范围
        return 25  # 使用更合理的默认成人年龄

    # 限制年龄在合理范围内
    return max(15, min(age, 70))  # 保证年龄在15-70之间


# 直接运行脚本
if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_example_image())
