#!/usr/bin/env python
"""
测试DeepFace和InsightFace的年龄、性别和种族分析功能

此脚本用于比较DeepFace和InsightFace在年龄、性别和种族分析方面的性能和准确性，
并分析它们在项目中的权重配置。
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path

# 导入OpenCV
import cv2

# 导入NumPy
import numpy as np

# 导入PyTorch
import torch

# 设置日志级别
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

# 检查是否可以导入InsightFace
try:
    import insightface

    INSIGHTFACE_AVAILABLE = True
    logger.info("InsightFace可用")
except ImportError:
    INSIGHTFACE_AVAILABLE = False
    logger.warning("InsightFace不可用，将跳过相关测试")

# 导入DeepFace包装器
from emotionai.core.ml.deepface_wrapper import DeepFaceWrapper

# 导入人脸检测器
from emotionai.core.ml.face_detection import FaceDetector

# 导入多任务人脸分析器
from emotionai.core.ml.pytorch_emotion_analyzer import MultiFaceAnalyzer


class FaceAttributesTest:
    """人脸属性测试类"""

    def __init__(self):
        """初始化测试类"""
        # 初始化DeepFace包装器
        self.deepface_wrapper = DeepFaceWrapper()
        logger.info("DeepFace包装器初始化完成")

        # 初始化人脸检测器
        self.face_detector = FaceDetector.get_instance(
            detector_type="yolo_mediapipe",
            conf_threshold=0.5,
            show_bounding_boxes=True,
        )
        logger.info("人脸检测器初始化完成")

        # 初始化多任务人脸分析器
        self.multi_face_analyzer = MultiFaceAnalyzer.get_instance()
        logger.info("多任务人脸分析器初始化完成")

        # 初始化InsightFace（如果可用）
        if INSIGHTFACE_AVAILABLE:
            try:
                self.insightface_model = insightface.app.FaceAnalysis()
                self.insightface_model.prepare(ctx_id=0, det_size=(640, 640))
                logger.info("InsightFace模型初始化完成")
            except Exception as e:
                logger.error(f"InsightFace模型初始化失败: {str(e)}")
                self.insightface_model = None
        else:
            self.insightface_model = None

    async def test_face_attributes(self, image_path):
        """测试人脸属性分析"""
        logger.info(f"测试图像: {image_path}")

        # 加载图像
        image = cv2.imread(str(image_path))
        if image is None:
            logger.error(f"无法加载图像: {image_path}")
            return

        # 使用人脸检测器检测人脸
        faces = self.face_detector.detect_faces(image)
        logger.info(f"检测到 {len(faces)} 个人脸")

        if not faces:
            logger.error("未检测到人脸，测试终止")
            return

        # 处理第一个人脸
        face = faces[0]
        bbox = face.get("bbox", [0, 0, 0, 0])
        x1, y1, x2, y2 = bbox
        face_image = image[y1:y2, x1:x2]

        # 保存裁剪的人脸图像
        face_output_path = "test_face.jpg"
        cv2.imwrite(face_output_path, face_image)
        logger.info(f"已保存裁剪的人脸图像到 {face_output_path}")

        # 1. 使用DeepFace分析
        logger.info("\n===== DeepFace分析 =====")
        start_time = time.time()

        # 转换为RGB格式
        rgb_face = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)

        # 分析人脸
        deepface_result = self.deepface_wrapper.analyze(rgb_face)

        # 计算耗时
        deepface_time = time.time() - start_time

        # 输出结果
        logger.info(f"DeepFace分析耗时: {deepface_time:.4f}秒")
        logger.info(f"年龄: {deepface_result.get('age')}")
        logger.info(f"年龄标签: {deepface_result.get('age_label')}")
        logger.info(f"性别: {deepface_result.get('gender')}")
        logger.info(f"性别置信度: {deepface_result.get('gender_confidence'):.4f}")
        logger.info(f"种族: {deepface_result.get('race')}")
        logger.info(f"种族置信度: {deepface_result.get('race_confidence'):.4f}")

        # 2. 使用MultiFaceAnalyzer分析
        logger.info("\n===== MultiFaceAnalyzer分析 =====")
        start_time = time.time()

        # 分析人脸
        multi_face_result = await self.multi_face_analyzer.analyze_async(face_image)

        # 计算耗时
        multi_face_time = time.time() - start_time

        # 输出结果
        logger.info(f"MultiFaceAnalyzer分析耗时: {multi_face_time:.4f}秒")
        logger.info(f"年龄: {multi_face_result.get('age')}")
        logger.info(f"性别: {multi_face_result.get('gender')}")

        # 3. 使用InsightFace分析（如果可用）
        if self.insightface_model:
            logger.info("\n===== InsightFace分析 =====")

            # 尝试两种方式使用InsightFace
            # 方式1：使用包装器API
            start_time = time.time()

            # 分析人脸
            faces = self.insightface_model.get(rgb_face)

            # 计算耗时
            insightface_time = time.time() - start_time

            if faces:
                face = faces[0]
                # 获取年龄和性别
                age = face.age
                gender = "male" if face.gender == 1 else "female"
                gender_confidence = abs(face.gender - 0.5) * 2  # 转换为0-1的置信度

                # 输出结果
                logger.info(f"InsightFace分析耗时: {insightface_time:.4f}秒")
                logger.info(f"年龄: {age}")
                logger.info(f"性别: {gender}")
                logger.info(f"性别置信度: {gender_confidence:.4f}")
            else:
                logger.warning("InsightFace包装器API未检测到人脸")

            # 方式2：直接使用原始API
            logger.info("\n===== InsightFace原始API分析 =====")
            try:
                # 创建新的FaceAnalysis实例
                from insightface.app import FaceAnalysis

                app = FaceAnalysis(name="buffalo_l")
                app.prepare(ctx_id=0, det_size=(224, 224))  # 使用较小的检测尺寸

                # 调整图像大小以提高检测率
                resized_face = cv2.resize(rgb_face, (0, 0), fx=0.5, fy=0.5)

                start_time = time.time()

                # 分析人脸
                faces = app.get(resized_face)

                # 计算耗时
                insightface_raw_time = time.time() - start_time

                if faces:
                    face = faces[0]
                    # 获取年龄和性别
                    age = face.age
                    gender = "male" if face.gender == 1 else "female"
                    gender_confidence = abs(face.gender - 0.5) * 2  # 转换为0-1的置信度

                    # 输出结果
                    logger.info(
                        f"InsightFace原始API分析耗时: {insightface_raw_time:.4f}秒"
                    )
                    logger.info(f"年龄: {age}")
                    logger.info(f"性别: {gender}")
                    logger.info(f"性别置信度: {gender_confidence:.4f}")
                else:
                    logger.warning("InsightFace原始API未检测到人脸")
            except Exception as e:
                logger.error(f"InsightFace原始API分析失败: {str(e)}")

        # 4. 分析权重配置
        logger.info("\n===== 权重分析 =====")
        logger.info("当前项目中的权重配置:")
        logger.info("DeepFace (年龄、性别、种族): 权重 = 0.4")
        logger.info("InsightFace (年龄和性别): 权重较高，用于补充DeepFace")

        # 比较性能和准确性
        logger.info("\n===== 性能比较 =====")
        logger.info(f"DeepFace分析耗时: {deepface_time:.4f}秒")
        logger.info(f"MultiFaceAnalyzer分析耗时: {multi_face_time:.4f}秒")
        if self.insightface_model and faces:
            logger.info(f"InsightFace分析耗时: {insightface_time:.4f}秒")

        # 返回结果
        return {
            "deepface": deepface_result,
            "multi_face": multi_face_result,
            "insightface": faces[0] if self.insightface_model and faces else None,
        }


async def main():
    """主函数"""
    # 创建测试类
    test = FaceAttributesTest()

    # 测试示例图像
    example_image_paths = [
        Path(
            "/Volumes/acasis/ema2_20250417/frontend/public/examples/original_example.jpg"
        ),
        Path(
            "/Volumes/acasis/ema2_20250417/test_face.jpg"
        ),  # 使用之前保存的裁剪人脸图像
    ]

    # 测试每个图像
    for image_path in example_image_paths:
        if not image_path.exists():
            logger.error(f"示例图像不存在: {image_path}")
            continue

        logger.info(f"\n\n========== 测试图像: {image_path} ==========\n")

        # 测试人脸属性分析
        await test.test_face_attributes(image_path)


if __name__ == "__main__":
    asyncio.run(main())
