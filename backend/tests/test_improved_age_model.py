#!/usr/bin/env python

"""
测试改进版年龄、性别和种族预测模型 (基于FairFace) 与 DeepFace 对比
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

import cv2
import matplotlib.pyplot as plt
import numpy as np
import torch
from PIL import Image

# 添加项目根目录到Python路径
current_path = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_path))

from emotionai.core.ml.deepface_wrapper import DeepFaceWrapper
from emotionai.core.ml.face_detection import FaceDetector

# 导入改进的年龄模型和DeepFace包装器
from emotionai.core.ml.improved_age_model import ResNetAGModel, load_model
from emotionai.core.ml.pytorch_emotion_analyzer import correct_age_prediction

# 设置日志记录
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# 检查是否可用 GPU
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
if not torch.cuda.is_available():
    logger.info("CUDA不可用，尝试使用MPS")
    if torch.backends.mps.is_available():
        device = torch.device("mps")

logger.info(f"使用设备: {device}")


async def test_improved_age_model():
    """测试改进版年龄、性别和种族预测模型，并与DeepFace对比"""

    # 加载人脸检测器
    face_detector = FaceDetector()

    # 加载改进的AGR模型
    improved_model = ResNetAGModel(pretrained=True)
    improved_model = improved_model.to(device)
    improved_model.eval()

    # 加载DeepFace包装器
    deepface_wrapper = DeepFaceWrapper()

    # 测试图像路径
    test_image_path = os.path.join(current_path, "test_face.jpg")

    if not os.path.exists(test_image_path):
        logger.error(f"测试图像不存在: {test_image_path}")
        return

    # 记录测试开始
    logger.info(
        f"开始测试改进版年龄性别模型与DeepFace对比，测试图像: {test_image_path}"
    )

    # 读取测试图像
    image = cv2.imread(test_image_path)
    if image is None:
        logger.error(f"无法读取图像: {test_image_path}")
        return

    # 转换为RGB格式
    rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    # 检测人脸
    faces = face_detector.detect_faces(rgb_image)

    if not faces:
        logger.error("未检测到人脸")
        return

    # 获取第一个人脸
    bbox = faces[0]["bbox"]
    x1, y1, x2, y2 = bbox
    face_image = rgb_image[y1:y2, x1:x2]

    # 1. 使用改进的ResNet50模型
    logger.info("=== 使用改进的ResNet50模型 ===")
    face_tensor = ResNetAGModel.preprocess_image(face_image).to(device)

    # 使用模型预测
    with torch.no_grad():
        improved_results = improved_model.predict_age_gender(face_tensor)

    # 应用年龄修正
    original_age = improved_results["age"]
    corrected_age = correct_age_prediction(
        original_age, improved_results["age_confidence"]
    )

    # 输出改进模型结果
    logger.info("改进模型预测结果:")
    logger.info(f"原始年龄: {original_age}")
    logger.info(f"修正后年龄: {corrected_age}")
    logger.info(f"年龄标签: {improved_results['age_label']}")
    logger.info(f"年龄置信度: {improved_results['age_confidence']:.4f}")
    logger.info(f"性别: {improved_results['gender']}")
    logger.info(f"性别置信度: {improved_results['gender_confidence']:.4f}")
    logger.info(f"种族: {improved_results['race']}")
    logger.info(f"种族置信度: {improved_results['race_confidence']:.4f}")

    # 2. 使用DeepFace模型
    logger.info("\n=== 使用DeepFace模型 ===")
    deepface_results = deepface_wrapper.analyze(face_image)

    # 应用年龄修正到DeepFace结果
    deepface_age = deepface_results["age"]
    deepface_corrected_age = correct_age_prediction(
        deepface_age, 0.5
    )  # DeepFace不提供置信度，使用默认值

    # 输出DeepFace结果
    logger.info("DeepFace预测结果:")
    logger.info(f"原始年龄: {deepface_age}")
    logger.info(f"修正后年龄: {deepface_corrected_age}")
    logger.info(f"性别: {deepface_results['gender']}")
    logger.info(f"性别置信度: {deepface_results['gender_confidence']:.4f}")
    logger.info(f"种族: {deepface_results['race']}")
    logger.info(f"种族置信度: {deepface_results['race_confidence']:.4f}")
    logger.info(f"主要情绪: {deepface_results['dominant_emotion']}")

    # 3. 对比结果
    logger.info("\n=== 结果对比 ===")
    logger.info(
        f"年龄对比: 改进模型={corrected_age} vs DeepFace={deepface_corrected_age}"
    )
    logger.info(
        f"性别对比: 改进模型={improved_results['gender']} vs DeepFace={deepface_results['gender']}"
    )
    logger.info(
        f"种族对比: 改进模型={improved_results['race']} vs DeepFace={deepface_results['race']}"
    )

    # 可视化结果对比
    plt.figure(figsize=(15, 10))

    # 原始图像
    plt.subplot(2, 3, 1)
    plt.imshow(face_image)
    plt.title("原始人脸图像")
    plt.axis("off")

    # 改进模型年龄分布
    plt.subplot(2, 3, 2)
    age_labels = list(improved_results["age_distribution"].keys())
    age_probs = list(improved_results["age_distribution"].values())
    plt.bar(age_labels, age_probs)
    plt.xticks(rotation=45)
    plt.title("改进模型年龄预测分布")

    # DeepFace年龄分布 (从年龄映射函数创建)
    plt.subplot(2, 3, 3)
    age_distribution = deepface_wrapper._create_age_distribution(deepface_age)
    age_labels = list(age_distribution.keys())
    age_probs = list(age_distribution.values())
    plt.bar(age_labels, age_probs)
    plt.xticks(rotation=45)
    plt.title("DeepFace年龄预测分布")

    # 改进模型种族分布
    plt.subplot(2, 3, 5)
    race_labels = list(improved_results["race_distribution"].keys())
    race_probs = list(improved_results["race_distribution"].values())
    plt.bar(race_labels, race_probs)
    plt.xticks(rotation=45)
    plt.title("改进模型种族预测分布")

    # DeepFace种族分布
    plt.subplot(2, 3, 6)
    race_labels = list(deepface_results["race_distribution"].keys())
    race_probs = list(deepface_results["race_distribution"].values())
    plt.bar(race_labels, race_probs)
    plt.xticks(rotation=45)
    plt.title("DeepFace种族预测分布")

    plt.tight_layout()

    # 保存结果图
    comparison_image_path = os.path.join(current_path, "model_comparison_result.png")
    plt.savefig(comparison_image_path)
    logger.info(f"对比结果图像已保存至: {comparison_image_path}")

    # 将结果标注在图像上
    annotated_image = face_image.copy()
    h, w = annotated_image.shape[:2]

    # 创建标注文本
    results_text = [
        f"改进模型: 年龄={corrected_age}, 性别={improved_results['gender']}, 种族={improved_results['race']}",
        f"DeepFace: 年龄={deepface_corrected_age}, 性别={deepface_results['gender']}, 种族={deepface_results['race']}",
        f"DeepFace情绪: {deepface_results['dominant_emotion']}",
    ]

    # 创建新的图像用于标注
    result_img = np.zeros((h + 120, w, 3), dtype=np.uint8)
    result_img[:h, :w] = annotated_image

    # 添加文本
    for _, text in enumerate(results_text):
        cv2.putText(
            result_img,
            text,
            (10, h + 30 + i * 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.5,
            (255, 255, 255),
            1,
        )

    # 保存标注图像
    annotated_image_path = os.path.join(current_path, "model_comparison_annotated.jpg")
    cv2.imwrite(annotated_image_path, cv2.cvtColor(result_img, cv2.COLOR_RGB2BGR))
    logger.info(f"标注图像已保存至: {annotated_image_path}")


if __name__ == "__main__":
    asyncio.run(test_improved_age_model())
