#!/usr/bin/env python

"""
比较不同情绪识别模型的性能
包括:
1. swin_fer (当前使用的模型)
2. trpakov_vit_face_expression (已集成的模型)
3. <PERSON><PERSON>1996/FacialEmoRecog (未使用的备用模型)
4. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/swin-tiny-patch4-window7-224-finetuned-face-emotion-v12 (未使用的备用模型)
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path
from typing import Any, Optional

import cv2
import numpy as np
import torch
from PIL import Image
from transformers import AutoFeatureExtractor, AutoModelForImageClassification

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志级别
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

from emotionai.core.ml.face_detection import FaceDetector
from emotionai.core.ml.loaders.emotion_loaders import register_emotion_models
from emotionai.core.ml.loaders.face_detection_loaders import (
    register_face_detection_models,
)

# 导入必要的模块
from emotionai.core.ml.model_loader import ModelRegistry

# 定义模型路径
MODELS_DIR = Path(
    "/Volumes/acasis/ema2_20250417/backend/emotionai/core/ml/models/emotion_recognition"
)
HUGGINGFACE_DIR = MODELS_DIR / "huggingface"


# 定义模型类
class RajaramFacialEmoRecog:
    """Rajaram1996/FacialEmoRecog模型封装"""

    def __init__(self, model_path: Path):
        self.model_path = model_path
        self.device = (
            "mps"
            if torch.backends.mps.is_available()
            else "cuda" if torch.cuda.is_available() else "cpu"
        )
        logger.info(f"使用设备: {self.device}")

        # 加载模型
        try:
            self.feature_extractor = AutoFeatureExtractor.from_pretrained(
                str(model_path.parent)
            )
            self.model = AutoModelForImageClassification.from_pretrained(
                str(model_path.parent)
            )
            self.model = self.model.to(self.device)
            self.model.eval()

            # 获取标签映射
            self.id2label = (
                self.model.config.id2label
                if hasattr(self.model.config, "id2label")
                else {
                    0: "angry",
                    1: "disgust",
                    2: "fear",
                    3: "happy",
                    4: "neutral",
                    5: "sad",
                    6: "surprise",
                }
            )

            logger.info(f"Rajaram模型加载成功，支持的情绪类别: {self.id2label}")
        except Exception as e:
            logger.error(f"Rajaram模型加载失败: {str(e)}")
            raise

    def predict(self, image: np.ndarray) -> dict[str, Any]:
        """预测图像的情绪"""
        try:
            # 确保图像是RGB格式
            if len(image.shape) == 2:
                # 灰度图像转RGB
                image = np.stack([image] * 3, axis=-1)
            elif image.shape[2] == 4:
                # RGBA图像转RGB
                image = image[:, :, :3]

            # 转换为PIL图像
            pil_image = Image.fromarray(image.astype("uint8"))

            # 使用特征提取器处理图像
            inputs = self.feature_extractor(images=pil_image, return_tensors="pt")
            inputs = {k: v.to(self.device) for _, v in inputs.items()}

            # 预测
            with torch.no_grad():
                outputs = self.model(**inputs)

            # 获取预测结果
            logits = outputs.logits
            probabilities = torch.nn.functional.softmax(logits, dim=1)[0].cpu().numpy()

            # 构建结果字典
            result = {}
            for i, prob in enumerate(probabilities):
                emotion = self.id2label[i]
                result[emotion] = float(prob)

            # 找出最大概率的情绪
            max_emotion = max(result.items(), key=lambda x: x[1])[0]

            return {"emotions": result, "dominant_emotion": max_emotion}

        except Exception as e:
            logger.error(f"预测失败: {str(e)}")
            return {
                "emotions": dict.fromkeys(self.id2label.values(), 0.0),
                "dominant_emotion": "neutral",  # 默认返回中性情绪
            }


class MahmoudSwinFER:
    """MahmoudWSegni/swin-tiny模型封装"""

    def __init__(self, model_path: Path):
        self.model_path = model_path
        self.device = (
            "mps"
            if torch.backends.mps.is_available()
            else "cuda" if torch.cuda.is_available() else "cpu"
        )
        logger.info(f"使用设备: {self.device}")

        # 加载模型
        try:
            self.feature_extractor = AutoFeatureExtractor.from_pretrained(
                str(model_path.parent.parent)
            )
            self.model = AutoModelForImageClassification.from_pretrained(
                str(model_path.parent.parent)
            )
            self.model = self.model.to(self.device)
            self.model.eval()

            # 获取标签映射
            self.id2label = (
                self.model.config.id2label
                if hasattr(self.model.config, "id2label")
                else {
                    0: "angry",
                    1: "disgust",
                    2: "fear",
                    3: "happy",
                    4: "neutral",
                    5: "sad",
                    6: "surprise",
                }
            )

            logger.info(
                f"Mahmoud Swin-FER模型加载成功，支持的情绪类别: {self.id2label}"
            )
        except Exception as e:
            logger.error(f"Mahmoud Swin-FER模型加载失败: {str(e)}")
            raise

    def predict(self, image: np.ndarray) -> dict[str, Any]:
        """预测图像的情绪"""
        try:
            # 确保图像是RGB格式
            if len(image.shape) == 2:
                # 灰度图像转RGB
                image = np.stack([image] * 3, axis=-1)
            elif image.shape[2] == 4:
                # RGBA图像转RGB
                image = image[:, :, :3]

            # 转换为PIL图像
            pil_image = Image.fromarray(image.astype("uint8"))

            # 使用特征提取器处理图像
            inputs = self.feature_extractor(images=pil_image, return_tensors="pt")
            inputs = {k: v.to(self.device) for _, v in inputs.items()}

            # 预测
            with torch.no_grad():
                outputs = self.model(**inputs)

            # 获取预测结果
            logits = outputs.logits
            probabilities = torch.nn.functional.softmax(logits, dim=1)[0].cpu().numpy()

            # 构建结果字典
            result = {}
            for _, prob in enumerate(probabilities):
                emotion = self.id2label[i]
                result[emotion] = float(prob)

            # 找出最大概率的情绪
            max_emotion = max(result.items(), key=lambda x: x[1])[0]

            return {"emotions": result, "dominant_emotion": max_emotion}

        except Exception as e:
            logger.error(f"预测失败: {str(e)}")
            return {
                "emotions": dict.fromkeys(self.id2label.values(), 0.0),
                "dominant_emotion": "neutral",  # 默认返回中性情绪
            }


async def main():
    # 注册模型
    register_emotion_models()
    register_face_detection_models()

    # 创建人脸检测器
    detector = FaceDetector()

    # 加载已注册的模型
    swin_fer = ModelRegistry.get("swin_fer")
    if swin_fer is None:
        logger.error("无法加载swin_fer模型")
        return

    if not swin_fer.loaded:
        swin_fer.load()

    trakov_vit = ModelRegistry.get("trakov_vit")
    if trakov_vit is None:
        logger.error("无法加载trakov_vit模型")
        return

    if not trakov_vit.loaded:
        trakov_vit.load()

    # 加载未注册的模型
    rajaram_model_path = HUGGINGFACE_DIR / "rajaram_facial_emo" / "pytorch_model.bin"
    mahmoud_model_path = (
        HUGGINGFACE_DIR
        / "models--MahmoudWSegni--swin-tiny-patch4-window7-224-finetuned-face-emotion-v12"
        / "snapshots"
        / "b8767a951d111467a95cccc5573f90526176ac74"
        / "pytorch_model.bin"
    )

    # 检查模型文件是否存在
    if not rajaram_model_path.exists():
        logger.error(f"Rajaram模型文件不存在: {rajaram_model_path}")
    else:
        logger.info(f"Rajaram模型文件存在: {rajaram_model_path}")

    if not mahmoud_model_path.exists():
        logger.error(f"Mahmoud模型文件不存在: {mahmoud_model_path}")
    else:
        logger.info(f"Mahmoud模型文件存在: {mahmoud_model_path}")

    # 加载测试图像
    test_image_path = Path(
        "/Volumes/acasis/ema2_20250417/frontend/public/examples/original_example.jpg"
    )
    if not test_image_path.exists():
        logger.error(f"测试图像不存在: {test_image_path}")
        return

    # 打印测试图像信息
    logger.info(f"测试图像路径: {test_image_path}")

    image = cv2.imread(str(test_image_path))
    if image is None:
        logger.error(f"无法加载图像: {test_image_path}")
        return

    logger.info(f"加载图像: {test_image_path}, 尺寸: {image.shape}")

    # 使用YOLO检测人脸
    yolo_face_detector = ModelRegistry.get("yolo_face")
    if yolo_face_detector is None:
        logger.error("无法加载YOLO人脸检测器")
        return

    if not yolo_face_detector.loaded:
        yolo_face_detector.load()

    # 检测人脸
    yolo_result = yolo_face_detector.predict(image)
    yolo_faces = yolo_result.get("faces", [])

    if not yolo_faces:
        logger.error("未检测到人脸")
        return

    logger.info(f"YOLO检测到{len(yolo_faces)}个人脸")

    # 使用MediaPipe处理人脸，获取特征点
    faces_with_landmarks, marked_image = detector.process_with_yolo_faces(
        image, yolo_faces
    )

    logger.info(f"MediaPipe处理了{len(faces_with_landmarks)}个人脸")

    # 尝试加载Rajaram模型
    try:
        rajaram_model = RajaramFacialEmoRecog(rajaram_model_path)
        rajaram_loaded = True
    except Exception as e:
        logger.error(f"无法加载Rajaram模型: {str(e)}")
        rajaram_loaded = False

    # 尝试加载Mahmoud模型
    try:
        mahmoud_model = MahmoudSwinFER(mahmoud_model_path)
        mahmoud_loaded = True
    except Exception as e:
        logger.error(f"无法加载Mahmoud模型: {str(e)}")
        mahmoud_loaded = False

    # 对每个人脸进行情绪分析
    for _, face in enumerate(faces_with_landmarks):
        # 获取人脸边界框
        bbox = face.get("bbox")
        if not bbox:
            logger.warning(f"人脸 {i+1} 没有边界框")
            continue

        x1, y1, x2, y2 = bbox
        face_image = image[y1:y2, x1:x2].copy()

        # 获取特征点（当前未使用）
        # landmarks = face.get("landmarks")

        # 测试swin_fer模型
        logger.info(f"使用swin_fer模型分析人脸 {i+1}")
        start_time = time.time()
        swin_fer_result = swin_fer.predict(face_image)
        swin_fer_time = time.time() - start_time
        logger.info(
            f"swin_fer模型预测结果: {swin_fer_result.get('dominant_emotion')}, 置信度: {swin_fer_result.get('emotions', {}).get(swin_fer_result.get('dominant_emotion'), 0.0):.4f}, 耗时: {swin_fer_time:.4f}秒"
        )

        # 测试trakov_vit模型
        logger.info(f"使用trakov_vit模型分析人脸 {i+1}")
        start_time = time.time()
        trakov_vit_result = trakov_vit.predict(face_image)
        trakov_vit_time = time.time() - start_time
        logger.info(
            f"trakov_vit模型预测结果: {trakov_vit_result.get('dominant_emotion')}, 置信度: {trakov_vit_result.get('emotions', {}).get(trakov_vit_result.get('dominant_emotion'), 0.0):.4f}, 耗时: {trakov_vit_time:.4f}秒"
        )

        # 测试Rajaram模型
        if rajaram_loaded:
            logger.info(f"使用Rajaram模型分析人脸 {i+1}")
            start_time = time.time()
            rajaram_result = rajaram_model.predict(face_image)
            rajaram_time = time.time() - start_time
            logger.info(
                f"Rajaram模型预测结果: {rajaram_result.get('dominant_emotion')}, 置信度: {rajaram_result.get('emotions', {}).get(rajaram_result.get('dominant_emotion'), 0.0):.4f}, 耗时: {rajaram_time:.4f}秒"
            )

        # 测试Mahmoud模型
        if mahmoud_loaded:
            logger.info(f"使用Mahmoud模型分析人脸 {i+1}")
            start_time = time.time()
            mahmoud_result = mahmoud_model.predict(face_image)
            mahmoud_time = time.time() - start_time
            logger.info(
                f"Mahmoud模型预测结果: {mahmoud_result.get('dominant_emotion')}, 置信度: {mahmoud_result.get('emotions', {}).get(mahmoud_result.get('dominant_emotion'), 0.0):.4f}, 耗时: {mahmoud_time:.4f}秒"
            )

        # 打印所有模型的结果比较
        logger.info("所有模型预测结果比较:")
        logger.info(
            f"1. swin_fer: {swin_fer_result.get('dominant_emotion')}, 置信度: {swin_fer_result.get('emotions', {}).get(swin_fer_result.get('dominant_emotion'), 0.0):.4f}, 耗时: {swin_fer_time:.4f}秒"
        )
        logger.info(
            f"2. trakov_vit: {trakov_vit_result.get('dominant_emotion')}, 置信度: {trakov_vit_result.get('emotions', {}).get(trakov_vit_result.get('dominant_emotion'), 0.0):.4f}, 耗时: {trakov_vit_time:.4f}秒"
        )

        if rajaram_loaded:
            logger.info(
                f"3. Rajaram: {rajaram_result.get('dominant_emotion')}, 置信度: {rajaram_result.get('emotions', {}).get(rajaram_result.get('dominant_emotion'), 0.0):.4f}, 耗时: {rajaram_time:.4f}秒"
            )

        if mahmoud_loaded:
            logger.info(
                f"4. Mahmoud: {mahmoud_result.get('dominant_emotion')}, 置信度: {mahmoud_result.get('emotions', {}).get(mahmoud_result.get('dominant_emotion'), 0.0):.4f}, 耗时: {mahmoud_time:.4f}秒"
            )


if __name__ == "__main__":
    asyncio.run(main())
