#!/usr/bin/env python

"""
测试MediaPipe人脸检测功能
"""

import logging
import os
import sys

import cv2
import numpy as np

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 设置环境变量，确保在macOS M芯片上禁用GPU加速
os.environ["MEDIAPIPE_GPU"] = "0"  # 禁用MediaPipe的GPU加速
os.environ["MEDIAPIPE_USE_CUSTOM_OPENCV"] = "0"  # 使用系统安装的OpenCV库
os.environ["MEDIAPIPE_DISABLE_GPU"] = "1"  # 禁用GPU，使用CPU推理

# 导入MediaPipe
import mediapipe as mp


def test_face_detection():
    """测试MediaPipe人脸检测功能"""
    try:
        # 尝试导入mediapipe.tasks.python
        from mediapipe.tasks import python
        from mediapipe.tasks.python import vision

        logger.info("成功导入mediapipe.tasks.python和mediapipe.tasks.python.vision")

        # 检查可用的类和函数
        logger.info("mediapipe.tasks.python.vision中的属性:")
        for attr in dir(vision):
            if not attr.startswith("_"):  # 排除私有属性
                logger.info(f"- {attr}")

        # 检查mediapipe.tasks.python中的属性
        logger.info("mediapipe.tasks.python中的属性:")
        for attr in dir(python):
            if not attr.startswith("_"):  # 排除私有属性
                logger.info(f"- {attr}")

        # 检查mediapipe中的属性
        logger.info("mediapipe中的属性:")
        for attr in dir(mp):
            if not attr.startswith("_"):  # 排除私有属性
                logger.info(f"- {attr}")

        # 尝试使用FaceDetector
        if hasattr(vision, "FaceDetector"):
            logger.info("FaceDetector存在，尝试使用...")

            # 创建一个测试图像
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            cv2.rectangle(
                test_image, (30, 30), (70, 70), (255, 255, 255), -1
            )  # 绘制一个白色方块

            # 保存测试图像
            cv2.imwrite("test_face.jpg", test_image)
            logger.info("已保存测试图像: test_face.jpg")

            # 尝试加载模型
            model_path = os.path.join(
                os.path.dirname(os.path.abspath(__file__)),
                "emotionai",
                "core",
                "ml",
                "models",
                "face_landmarks",
                "mediapipe",
                "face_landmarker.task",
            )

            if os.path.exists(model_path):
                logger.info(f"模型文件存在: {model_path}")

                # 尝试创建BaseOptions
                if hasattr(python, "BaseOptions"):
                    base_options = python.BaseOptions(model_asset_path=model_path)
                    logger.info("成功创建BaseOptions")

                    # 尝试创建FaceDetectorOptions
                    if hasattr(vision, "FaceDetectorOptions"):
                        options = vision.FaceDetectorOptions(base_options=base_options)
                        logger.info("成功创建FaceDetectorOptions")

                        # 尝试创建FaceDetector
                        if hasattr(vision, "FaceDetector"):
                            detector = vision.FaceDetector.create_from_options(options)
                            logger.info("成功创建FaceDetector")

                            # 尝试检测人脸
                            # 需要创建MediaPipe图像对象
                            if hasattr(mp, "Image"):
                                mp_image = mp.Image(
                                    image_format=mp.ImageFormat.SRGB, data=test_image
                                )
                                logger.info("成功创建MediaPipe图像对象")

                                # 检测人脸
                                detection_result = detector.detect(mp_image)
                                logger.info(f"检测结果: {detection_result}")
                            else:
                                logger.warning(
                                    "mp.Image不存在，无法创建MediaPipe图像对象"
                                )
                        else:
                            logger.warning("vision.FaceDetector不存在")
                    else:
                        logger.warning("vision.FaceDetectorOptions不存在")
                else:
                    logger.warning("python.BaseOptions不存在")
            else:
                logger.error(f"模型文件不存在: {model_path}")
        else:
            logger.warning("FaceDetector不存在")

        # 尝试使用FaceLandmarker
        if hasattr(vision, "FaceLandmarker"):
            logger.info("FaceLandmarker存在，尝试使用...")

            # 创建一个测试图像
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            cv2.rectangle(
                test_image, (30, 30), (70, 70), (255, 255, 255), -1
            )  # 绘制一个白色方块

            # 尝试加载模型
            model_path = os.path.join(
                os.path.dirname(os.path.abspath(__file__)),
                "emotionai",
                "core",
                "ml",
                "models",
                "face_landmarks",
                "mediapipe",
                "face_landmarker.task",
            )

            if os.path.exists(model_path):
                logger.info(f"模型文件存在: {model_path}")

                # 尝试创建BaseOptions
                if hasattr(python, "BaseOptions"):
                    base_options = python.BaseOptions(model_asset_path=model_path)
                    logger.info("成功创建BaseOptions")

                    # 尝试创建FaceLandmarkerOptions
                    if hasattr(vision, "FaceLandmarkerOptions"):
                        options = vision.FaceLandmarkerOptions(
                            base_options=base_options
                        )
                        logger.info("成功创建FaceLandmarkerOptions")

                        # 尝试创建FaceLandmarker
                        if hasattr(vision, "FaceLandmarker"):
                            landmarker = vision.FaceLandmarker.create_from_options(
                                options
                            )
                            logger.info("成功创建FaceLandmarker")

                            # 尝试检测人脸特征点
                            # 需要创建MediaPipe图像对象
                            if hasattr(mp, "Image"):
                                mp_image = mp.Image(
                                    image_format=mp.ImageFormat.SRGB, data=test_image
                                )
                                logger.info("成功创建MediaPipe图像对象")

                                # 检测人脸特征点
                                detection_result = landmarker.detect(mp_image)
                                logger.info(f"检测结果: {detection_result}")
                            else:
                                logger.warning(
                                    "mp.Image不存在，无法创建MediaPipe图像对象"
                                )
                        else:
                            logger.warning("vision.FaceLandmarker不存在")
                    else:
                        logger.warning("vision.FaceLandmarkerOptions不存在")
                else:
                    logger.warning("python.BaseOptions不存在")
            else:
                logger.error(f"模型文件不存在: {model_path}")
        else:
            logger.warning("FaceLandmarker不存在")
    except Exception as e:
        logger.error(f"测试人脸检测功能时出错: {e}")


def main():
    """主函数"""
    logger.info("开始测试MediaPipe人脸检测功能...")

    # 测试人脸检测功能
    test_face_detection()

    logger.info("MediaPipe人脸检测功能测试完成")


if __name__ == "__main__":
    main()
