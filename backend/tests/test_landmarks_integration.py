#!/usr/bin/env python

"""
测试MediaPipe特征点与trakov_vit模型的集成
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

import cv2
import numpy as np

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志级别
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

from emotionai.core.ml.emotion_ensemble import EmotionEnsemble
from emotionai.core.ml.face_detection import FaceDetector
from emotionai.core.ml.loaders.emotion_loaders import register_emotion_models
from emotionai.core.ml.loaders.face_detection_loaders import (
    register_face_detection_models,
)

# 导入必要的模块
from emotionai.core.ml.model_loader import ModelRegistry


async def main():
    # 注册模型
    register_emotion_models()
    register_face_detection_models()

    # 创建人脸检测器
    detector = FaceDetector()

    # 创建情绪集成器
    emotion_ensemble = EmotionEnsemble()

    # 加载模型
    trakov_vit = ModelRegistry.get("trakov_vit")
    if trakov_vit is None:
        logger.error("无法加载trakov_vit模型")
        return

    if not trakov_vit.loaded:
        trakov_vit.load()

    # 加载测试图像
    test_image_path = Path(
        "/Volumes/acasis/ema2_20250417/frontend/public/examples/original_example.jpg"
    )
    if not test_image_path.exists():
        logger.error(f"测试图像不存在: {test_image_path}")
        return

    # 打印测试图像信息
    logger.info(f"测试图像路径: {test_image_path}")
    # 打开图像并显示
    import matplotlib.pyplot as plt

    plt.figure(figsize=(10, 8))
    plt.imshow(cv2.cvtColor(cv2.imread(str(test_image_path)), cv2.COLOR_BGR2RGB))
    plt.title("测试图像")
    plt.axis("off")
    plt.savefig("/tmp/test_image.png")
    logger.info("测试图像已保存到: /tmp/test_image.png")

    image = cv2.imread(str(test_image_path))
    if image is None:
        logger.error(f"无法加载图像: {test_image_path}")
        return

    logger.info(f"加载图像: {test_image_path}, 尺寸: {image.shape}")

    # 使用YOLO检测人脸
    yolo_face_detector = ModelRegistry.get("yolo_face")
    if yolo_face_detector is None:
        logger.error("无法加载YOLO人脸检测器")
        return

    if not yolo_face_detector.loaded:
        yolo_face_detector.load()

    # 检测人脸
    yolo_result = yolo_face_detector.predict(image)
    yolo_faces = yolo_result.get("faces", [])

    if not yolo_faces:
        logger.error("未检测到人脸")
        return

    logger.info(f"YOLO检测到{len(yolo_faces)}个人脸")

    # 使用MediaPipe处理人脸，获取特征点
    faces_with_landmarks, marked_image = detector.process_with_yolo_faces(
        image, yolo_faces
    )

    logger.info(f"MediaPipe处理了{len(faces_with_landmarks)}个人脸")

    # 检查是否有特征点
    for _, face in enumerate(faces_with_landmarks):
        landmarks = face.get("landmarks")
        if landmarks:
            logger.info(f"人脸 {i+1} 有 {len(landmarks)} 个特征点")
        else:
            logger.info(f"人脸 {i+1} 没有特征点")

    # 对每个人脸进行情绪分析
    for i, face in enumerate(faces_with_landmarks):
        # 获取人脸边界框
        bbox = face.get("bbox")
        if not bbox:
            logger.warning(f"人脸 {i+1} 没有边界框")
            continue

        x1, y1, x2, y2 = bbox
        face_image = image[y1:y2, x1:x2].copy()

        # 获取特征点
        landmarks = face.get("landmarks")

        # 单独测试swin_fer模型
        swin_fer = ModelRegistry.get("swin_fer")
        if not swin_fer.loaded:
            swin_fer.load()
        logger.info(f"使用swin_fer模型分析人脸 {i+1}")
        swin_fer_result = swin_fer.predict(face_image)
        logger.info(
            f"swin_fer模型预测结果: {swin_fer_result.get('dominant_emotion')}, 置信度: {swin_fer_result.get('emotions', {}).get(swin_fer_result.get('dominant_emotion'), 0.0):.4f}"
        )
        logger.info(
            f"swin_fer模型所有情绪置信度: {swin_fer_result.get('emotions', {})}"
        )

        # 使用trakov_vit模型进行情绪分析（不使用特征点）
        logger.info(f"使用trakov_vit模型分析人脸 {i+1}（不使用特征点）")
        result_without_landmarks = trakov_vit.predict(face_image)

        # 使用trakov_vit模型进行情绪分析（使用特征点）
        logger.info(f"使用trakov_vit模型分析人脸 {i+1}（使用特征点）")
        result_with_landmarks = trakov_vit.predict(face_image, landmarks=landmarks)

        # 使用情绪集成器进行情绪分析（不使用特征点）
        logger.info(f"使用情绪集成器分析人脸 {i+1}（不使用特征点）")
        result_ensemble_without_landmarks = await emotion_ensemble.analyze(face_image)

        # 使用情绪集成器进行情绪分析（使用特征点）
        logger.info(f"使用情绪集成器分析人脸 {i+1}（使用特征点）")
        result_ensemble_with_landmarks = await emotion_ensemble.analyze(
            face_image, landmarks=landmarks
        )

        # 打印结果
        logger.info(
            f"trakov_vit（不使用特征点）: {result_without_landmarks.get('dominant_emotion')}, 置信度: {result_without_landmarks.get('emotions', {}).get(result_without_landmarks.get('dominant_emotion'), 0.0):.4f}"
        )
        logger.info(
            f"trakov_vit（使用特征点）: {result_with_landmarks.get('dominant_emotion')}, 置信度: {result_with_landmarks.get('emotions', {}).get(result_with_landmarks.get('dominant_emotion'), 0.0):.4f}"
        )

        logger.info(
            f"情绪集成器（不使用特征点）: {result_ensemble_without_landmarks.get('dominant_emotion')}"
        )
        logger.info(
            f"情绪集成器（使用特征点）: {result_ensemble_with_landmarks.get('dominant_emotion')}"
        )

        # 打印各模型的结果
        if "model_results" in result_ensemble_without_landmarks:
            logger.info("情绪集成器（不使用特征点）各模型结果:")
            for _, result in result_ensemble_without_landmarks["model_results"].items():
                if result.get("success", False):
                    logger.info(
                        f"  模型 {model_name}: {result.get('dominant_emotion')}, 置信度: {result.get('emotions', {}).get(result.get('dominant_emotion'), 0.0):.4f}"
                    )

        if "model_results" in result_ensemble_with_landmarks:
            logger.info("情绪集成器（使用特征点）各模型结果:")
            for model_name, result in result_ensemble_with_landmarks[
                "model_results"
            ].items():
                if result.get("success", False):
                    logger.info(
                        f"  模型 {model_name}: {result.get('dominant_emotion')}, 置信度: {result.get('emotions', {}).get(result.get('dominant_emotion'), 0.0):.4f}"
                    )


if __name__ == "__main__":
    asyncio.run(main())
