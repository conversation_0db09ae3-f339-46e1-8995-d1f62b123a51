#!/usr/bin/env python3

"""
测试性别识别准确率
"""

import argparse
import logging
import os
import sys
import time
from pathlib import Path
from typing import Any, Optional, Union

import cv2
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s - %(message)s",
    handlers=[logging.StreamHandler()],
)

logger = logging.getLogger("gender_accuracy_test")

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入DeepFace包装器
from emotionai.core.ml.deepface_wrapper import DeepFaceWrapper
from emotionai.core.ml.face_detection import FaceDetector


def test_gender_accuracy(image_path: str) -> dict[str, Any]:
    """
    测试单张图片的性别识别准确率

    Args:
        image_path: 图片路径

    Returns:
        包含性别识别结果的字典
    """
    try:
        # 加载图片
        logger.info(f"加载图片: {image_path}")
        image = cv2.imread(image_path)
        if image is None:
            logger.error(f"无法加载图片: {image_path}")
            return {"success": False, "error": "无法加载图片"}

        # 检测人脸
        logger.info("检测人脸...")
        detector = FaceDetector.get_instance()
        faces, marked_image = detector.detect_faces_and_generate_image(image)

        if not faces:
            logger.error("未检测到人脸")
            return {"success": False, "error": "未检测到人脸"}

        logger.info(f"检测到 {len(faces)} 个人脸")

        # 创建DeepFace包装器
        deepface_wrapper = DeepFaceWrapper()

        # 分析每个人脸
        results = []
        for _, face in enumerate(faces):
            logger.info(f"分析人脸 #{i+1}")

            # 获取人脸区域
            bbox = face.get("bbox", [0, 0, 0, 0])
            x1, y1, x2, y2 = bbox

            # 确保坐标有效
            x1 = max(0, x1)
            y1 = max(0, y1)
            x2 = min(image.shape[1], x2)
            y2 = min(image.shape[0], y2)

            # 裁剪人脸
            face_img = image[y1:y2, x1:x2]
            if face_img.size == 0:
                logger.warning(f"裁剪人脸区域失败: {bbox}")
                continue

            # 转换为RGB格式
            rgb_face = cv2.cvtColor(face_img, cv2.COLOR_BGR2RGB)

            # 分析人脸
            result = deepface_wrapper.analyze(rgb_face)

            # 提取性别信息
            gender = result.get("gender", "unknown")
            gender_confidence = result.get("gender_confidence", 0.0)

            logger.info(f"人脸 #{i+1} 性别: {gender}, 置信度: {gender_confidence:.4f}")

            # 添加到结果列表
            results.append(
                {
                    "face_number": i + 1,
                    "gender": gender,
                    "gender_confidence": gender_confidence,
                    "bbox": bbox,
                }
            )

        # 保存标记图像
        output_path = f"{os.path.splitext(image_path)[0]}_marked.jpg"
        cv2.imwrite(output_path, marked_image)
        logger.info(f"已保存标记图像到: {output_path}")

        return {
            "success": True,
            "faces_count": len(faces),
            "results": results,
            "marked_image_path": output_path,
        }

    except Exception as e:
        logger.error(f"分析失败: {str(e)}", exc_info=True)
        return {"success": False, "error": str(e)}


def test_multiple_images(directory: str) -> dict[str, Any]:
    """
    测试目录中所有图片的性别识别准确率

    Args:
        directory: 图片目录

    Returns:
        包含所有图片性别识别结果的字典
    """
    # 获取目录中的所有图片
    image_extensions = [".jpg", ".jpeg", ".png", ".bmp"]
    image_paths = []

    for ext in image_extensions:
        image_paths.extend(list(Path(directory).glob(f"*{ext}")))
        image_paths.extend(list(Path(directory).glob(f"*{ext.upper()}")))

    if not image_paths:
        logger.error(f"目录中没有图片: {directory}")
        return {"success": False, "error": "目录中没有图片"}

    logger.info(f"找到 {len(image_paths)} 张图片")

    # 分析每张图片
    results = {}
    for image_path in image_paths:
        logger.info(f"分析图片: {image_path}")
        result = test_gender_accuracy(str(image_path))
        results[str(image_path)] = result

    return {"success": True, "images_count": len(image_paths), "results": results}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试性别识别准确率")
    parser.add_argument("--image", type=str, help="单张图片路径")
    parser.add_argument("--dir", type=str, help="图片目录路径")

    args = parser.parse_args()

    if args.image:
        # 测试单张图片
        result = test_gender_accuracy(args.image)
        logger.info(f"分析结果: {result}")
    elif args.dir:
        # 测试目录中的所有图片
        result = test_multiple_images(args.dir)
        logger.info(f"总结: 分析了 {result['images_count']} 张图片")

        # 计算准确率统计
        total_faces = 0
        male_count = 0
        female_count = 0
        unknown_count = 0
        high_confidence_count = 0  # 置信度 > 0.8

        for _, image_result in result["results"].items():  # 使用_表示未使用的变量
            if image_result["success"]:
                for face_result in image_result["results"]:
                    total_faces += 1
                    if face_result["gender"] == "male":
                        male_count += 1
                    elif face_result["gender"] == "female":
                        female_count += 1
                    else:
                        unknown_count += 1

                    if face_result["gender_confidence"] > 0.8:
                        high_confidence_count += 1

        logger.info(f"总共分析了 {total_faces} 个人脸")
        logger.info(f"男性: {male_count} ({male_count/total_faces*100:.2f}%)")
        logger.info(f"女性: {female_count} ({female_count/total_faces*100:.2f}%)")
        logger.info(f"未知: {unknown_count} ({unknown_count/total_faces*100:.2f}%)")
        logger.info(
            f"高置信度(>0.8): {high_confidence_count} ({high_confidence_count/total_faces*100:.2f}%)"
        )
    else:
        logger.error("请提供--image或--dir参数")
        parser.print_help()


if __name__ == "__main__":
    main()
