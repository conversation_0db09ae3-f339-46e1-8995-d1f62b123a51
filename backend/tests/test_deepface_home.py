#!/usr/bin/env python
"""
测试DeepFace环境变量设置

此脚本用于测试DeepFace环境变量设置是否正确，以及模型文件是否能被正确加载。
"""

import logging
import os
import sys
from pathlib import Path

# 设置日志级别
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

# 导入DeepFace包装器
from emotionai.core.ml.deepface_wrapper import DeepFaceWrapper


def test_deepface_home():
    """测试DeepFace环境变量设置"""
    logger.info("开始测试DeepFace环境变量设置")

    # 获取当前的DEEPFACE_HOME环境变量
    original_deepface_home = os.environ.get("DEEPFACE_HOME")
    logger.info(f"原始DEEPFACE_HOME环境变量: {original_deepface_home}")

    # 创建DeepFaceWrapper实例
    logger.info("创建DeepFaceWrapper实例...")
    wrapper = DeepFaceWrapper()

    # 获取设置后的DEEPFACE_HOME环境变量
    new_deepface_home = os.environ.get("DEEPFACE_HOME")
    logger.info(f"设置后的DEEPFACE_HOME环境变量: {new_deepface_home}")

    # 获取模型目录
    models_dir = wrapper.models_dir
    logger.info(f"模型目录: {models_dir}")

    # 检查.deepface目录
    deepface_dir = os.path.join(models_dir, ".deepface")
    logger.info(f".deepface目录: {deepface_dir}")
    logger.info(f".deepface目录存在: {os.path.exists(deepface_dir)}")

    # 检查weights目录
    weights_dir = os.path.join(deepface_dir, "weights")
    logger.info(f"weights目录: {weights_dir}")
    logger.info(f"weights目录存在: {os.path.exists(weights_dir)}")

    # 列出weights目录中的文件
    if os.path.exists(weights_dir):
        files = os.listdir(weights_dir)
        logger.info(f"weights目录中的文件: {files}")
    else:
        logger.warning("weights目录不存在")

    # 测试DeepFace分析
    logger.info("测试DeepFace分析...")

    # 使用示例图像
    example_image_path = Path(
        "/Volumes/acasis/ema2_20250417/frontend/public/examples/original_example.jpg"
    )
    if not example_image_path.exists():
        logger.error(f"示例图像不存在: {example_image_path}")
        return

    try:
        # 分析图像
        result = wrapper.analyze(str(example_image_path))

        # 输出结果
        logger.info("DeepFace分析结果:")
        logger.info(f"年龄: {result.get('age')}")
        logger.info(f"性别: {result.get('gender')}")
        logger.info(f"种族: {result.get('race')}")
        logger.info(f"主要情绪: {result.get('dominant_emotion')}")

        logger.info("DeepFace分析成功")
    except Exception as e:
        logger.error(f"DeepFace分析失败: {str(e)}")

    # 测试DeepFace情绪分析
    logger.info("测试DeepFace情绪分析...")

    try:
        # 分析情绪
        emotion_result = wrapper.analyze_emotion(str(example_image_path))

        # 输出结果
        logger.info("DeepFace情绪分析结果:")
        logger.info(f"主要情绪: {emotion_result.get('dominant_emotion')}")
        logger.info(f"情绪分布: {emotion_result.get('emotions')}")

        logger.info("DeepFace情绪分析成功")
    except Exception as e:
        logger.error(f"DeepFace情绪分析失败: {str(e)}")

    logger.info("DeepFace环境变量测试完成")


if __name__ == "__main__":
    test_deepface_home()
