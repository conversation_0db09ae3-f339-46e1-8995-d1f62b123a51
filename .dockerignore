# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile # If Dockerfile is in root and context is root, it's fine.
docker-compose.yml
docker-compose.dev.yml
# .dockerignore itself should not be ignored if context is root

# Node_modules (global, if any)
node_modules/

# Python virtual environments
venv/
.venv/
env/
*.pyc
__pycache__/

# IDE and editor specific files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
*.project
*.tmproj

# OS generated files
.DS_Store
Thumbs.db
ehthumbs.db

# Log files
*.log
logs/

# Temporary files
*.tmp
*.bak
*.swp

# Build artifacts / Distribution
dist/
build/
target/ # Common for Java/Scala

# Secrets / Sensitive data
.env # Local .env should not be in image
*.pem
*.key
secrets/
credentials.*

# Test reports and coverage
coverage/
.nyc_output/
junit.xml

# Specific project files/folders to ignore if context is root
# For example, if backend/ and frontend/ are handled by their own Dockerfiles
# and their contexts, you might ignore them here if the root context is ever used
# for a "meta" Dockerfile.
# backend/
# frontend/

# Documentation (if not needed in image)
# docs/

# Data / Models (if large and managed separately)
# data/
# models/
# media/

# Reports
# reports/

# Scripts (if only for local execution)
# scripts/

# Specific files
dump.rdb # Redis dump
postgresql.conf # Example config, should be mounted
redis.db.settings # Example config