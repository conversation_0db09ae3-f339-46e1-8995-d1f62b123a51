#!/bin/bash

# Cloudflare隧道测试脚本
# 尝试不同的连接方法来解决TLS握手问题

echo "🔧 Cloudflare隧道连接测试"
echo "=========================="

# 检查网络连接
echo "1. 检查基础网络连接..."
if ping -c 2 1.1.1.1 > /dev/null 2>&1; then
    echo "✅ 网络连接正常"
else
    echo "❌ 网络连接异常"
    exit 1
fi

# 检查DNS解析
echo "2. 检查DNS解析..."
if nslookup region1.v2.argotunnel.com > /dev/null 2>&1; then
    echo "✅ DNS解析正常"
else
    echo "❌ DNS解析异常"
fi

# 检查系统时间
echo "3. 检查系统时间..."
echo "   当前时间: $(date)"

# 检查cloudflared版本
echo "4. 检查cloudflared版本..."
cloudflared version

echo ""
echo "🚀 尝试不同的连接方法..."
echo ""

# 方法1: 使用简化配置
echo "方法1: 使用简化配置文件"
echo "按 Ctrl+C 停止，然后尝试下一个方法"
echo "----------------------------------------"
timeout 30 cloudflared tunnel --config "$(pwd)/cloudflare-config.yml" run

echo ""
echo "方法1失败，尝试方法2..."
echo ""

# 方法2: 直接使用隧道名称，不使用配置文件
echo "方法2: 直接连接，不使用配置文件"
echo "按 Ctrl+C 停止，然后尝试下一个方法"
echo "----------------------------------------"
timeout 30 cloudflared tunnel --url http://localhost:3000 run ema-dev

echo ""
echo "方法2失败，尝试方法3..."
echo ""

# 方法3: 使用HTTP协议
echo "方法3: 强制使用HTTP协议"
echo "按 Ctrl+C 停止"
echo "----------------------------------------"
cloudflared tunnel --protocol http2 --url http://localhost:3000 run ema-dev
