#!/usr/bin/env python
# -*- coding: utf-8 -*-

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
import sys
import time

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def test_qq_smtp_basic():
    """最基本的QQ邮箱SMTP测试"""
    logger.info("开始最基本的QQ邮箱SMTP测试...")
    
    # 配置信息 - 使用最简单的设置
    smtp_host = "smtp.qq.com"
    smtp_port = 465  # SSL端口
    sender = "<EMAIL>"
    password = "qweamijhnidebaji"  # 授权码
    receiver = "<EMAIL>"  # 收件人邮箱
    
    # 创建一个简单的文本邮件
    msg = MIMEMultipart()
    msg['From'] = sender
    msg['To'] = receiver
    msg['Subject'] = "QQ邮箱SMTP测试"
    
    # 简单的纯文本内容
    text = "这是一封测试邮件，用于验证QQ邮箱SMTP服务是否正常工作。"
    msg.attach(MIMEText(text, 'plain', 'utf-8'))
    
    try:
        # 创建SSL上下文
        context = ssl.create_default_context()
        
        # 连接到SMTP服务器
        logger.info(f"连接到SMTP服务器: {smtp_host}:{smtp_port}")
        with smtplib.SMTP_SSL(smtp_host, smtp_port, context=context) as server:
            # 登录
            logger.info(f"尝试登录...")
            server.login(sender, password)
            logger.info(f"登录成功")
            
            # 发送邮件
            logger.info(f"发送邮件到: {receiver}")
            server.sendmail(sender, receiver, msg.as_string())
            logger.info(f"邮件发送成功")
            
        return True
    except Exception as e:
        logger.error(f"邮件发送失败: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    if test_qq_smtp_basic():
        print("\n✅ 测试成功！邮件已发送。")
    else:
        print("\n❌ 测试失败！请检查错误信息。")
