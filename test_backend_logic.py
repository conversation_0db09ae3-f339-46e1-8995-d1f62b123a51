#!/usr/bin/env python3
"""
直接测试后端逻辑，验证分析详情API是否正确返回反馈信息
"""

import asyncio
import sys
import os
from uuid import UUID

# 添加backend目录到Python路径
sys.path.append('./backend')

from sqlalchemy.ext.asyncio import AsyncSession
from emotionai.api.v1.endpoints.admin.analysis_router import read_emotion_analysis
from emotionai.api import deps
from emotionai.core.database.session import get_async_db

async def test_analysis_detail_logic():
    """直接测试分析详情逻辑"""
    
    # 测试的分析ID
    analysis_id = UUID("4b8c8cee-f731-4414-8829-4d7766591489")
    
    print(f"🔍 测试分析详情逻辑")
    print(f"🆔 分析ID: {analysis_id}")
    print("-" * 50)
    
    try:
        # 获取数据库会话
        async for db in get_async_db():
            print("✅ 数据库连接成功")
            
            # 调用分析详情函数
            result = await read_emotion_analysis(analysis_id, db)
            
            print("✅ API逻辑调用成功!")
            print(f"📝 返回数据结构:")
            
            # 检查基本信息
            print(f"  - 分析ID: {result.id}")
            print(f"  - 用户名: {result.username}")
            print(f"  - 情绪: {result.emotion}")
            print(f"  - 置信度: {result.confidence}")
            print(f"  - 创建时间: {result.created_at}")
            
            # 检查反馈信息
            feedback_count = getattr(result, 'feedback_count', 0)
            feedbacks = getattr(result, 'feedbacks', [])
            
            print(f"\n💬 反馈信息:")
            print(f"  - 反馈数量: {feedback_count}")
            print(f"  - 反馈列表长度: {len(feedbacks) if feedbacks else 0}")
            
            if feedbacks:
                print(f"\n📋 反馈详情:")
                for i, feedback in enumerate(feedbacks, 1):
                    print(f"  反馈 #{i}:")
                    print(f"    - ID: {feedback.id}")
                    print(f"    - 情绪评价: {feedback.emotion}")
                    print(f"    - 评论: {feedback.comment}")
                    print(f"    - 反馈用户: {getattr(feedback, 'username', 'N/A')}")
                    print(f"    - 创建时间: {feedback.created_at}")
                    print()
            else:
                print("  ⚠️  没有反馈数据")
            
            # 检查图片信息
            image_url = getattr(result, 'image_thumbnail_url', None)
            if image_url:
                print(f"🖼️  图片缩略图: {image_url}")
            else:
                print("🖼️  无图片信息")
            
            print("\n" + "=" * 50)
            print("🎉 测试完成！后端逻辑正常工作")
            
            break  # 只需要一个会话
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始测试后端分析详情逻辑")
    print()
    
    # 运行测试
    asyncio.run(test_analysis_detail_logic()) 