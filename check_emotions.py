import os
import sys
import asyncio
from sqlalchemy import text

# 添加项目根目录到 sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "backend")))

try:
    from emotionai.core.db.session import async_session_maker
except ImportError:
    print("无法导入 async_session_maker，尝试其他导入路径")
    try:
        from emotionai.db.session import async_session_maker
    except ImportError:
        print("无法导入 async_session_maker，请检查项目结构")
        sys.exit(1)

async def check_emotions():
    try:
        async with async_session_maker() as session:
            # 查询不同的情绪值
            query = text("SELECT DISTINCT emotion FROM emotion_analysis WHERE deleted_at IS NULL LIMIT 10")
            result = await session.execute(query)
            emotions = result.scalars().all()
            print(f"数据库中的情绪值: {emotions}")
            
            # 查询记录总数
            count_query = text("SELECT COUNT(*) FROM emotion_analysis WHERE deleted_at IS NULL")
            count_result = await session.execute(count_query)
            count = count_result.scalar()
            print(f"总记录数: {count}")
            
            # 查询 happy 情绪的记录数
            happy_query = text("SELECT COUNT(*) FROM emotion_analysis WHERE emotion = 'happy' AND deleted_at IS NULL")
            happy_result = await session.execute(happy_query)
            happy_count = happy_result.scalar()
            print(f"'happy' 情绪的记录数: {happy_count}")
            
            # 查询 Happy 情绪的记录数（大写）
            happy_cap_query = text("SELECT COUNT(*) FROM emotion_analysis WHERE emotion = 'Happy' AND deleted_at IS NULL")
            happy_cap_result = await session.execute(happy_cap_query)
            happy_cap_count = happy_cap_result.scalar()
            print(f"'Happy' 情绪的记录数: {happy_cap_count}")
            
            # 查询包含 happy 的记录数（不区分大小写）
            happy_ilike_query = text("SELECT COUNT(*) FROM emotion_analysis WHERE emotion ILIKE '%happy%' AND deleted_at IS NULL")
            happy_ilike_result = await session.execute(happy_ilike_query)
            happy_ilike_count = happy_ilike_result.scalar()
            print(f"包含 'happy'（不区分大小写）的记录数: {happy_ilike_count}")
            
    except Exception as e:
        print(f"查询出错: {e}")

if __name__ == "__main__":
    asyncio.run(check_emotions())
